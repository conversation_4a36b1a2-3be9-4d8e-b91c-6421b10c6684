import { ref, computed, reactive } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from './useBusinessForm'
import type { AssemblyForm, AssemblyMaterial, AssemblyProduct, AssemblyType } from '@/types/inventory'
import type { FormConfig } from '@/types/business'

/**
 * 组装类型选项
 */
export const assemblyTypeOptions = [
  { label: '简单组装', value: 'simple' },
  { label: '复杂组装', value: 'complex' },
  { label: '批量组装', value: 'batch' }
]

/**
 * 材料库存警告接口
 */
interface MaterialStockWarning {
  materialId: string | number
  materialName: string
  requiredQuantity: number
  availableQuantity: number
  message: string
}

/**
 * 组装单表单配置
 */
const assemblyFormConfig: FormConfig = {
  documentType: 'ASSEMBLY',
  title: '组装单',
  apiEndpoint: '/depot/assembly',
  
  // 字段映射：AssemblyForm -> BaseBusinessForm
  fieldMapping: {
    assemblyNo: 'number',
    assemblyDate: 'operTime',
    warehouseId: 'depotId',
    warehouseName: 'depotName',
    assemblyType: 'otherField1',
    operator: 'otherField2',
    operatorId: 'otherField3',
    totalMaterialCost: 'otherField4',
    totalProductCost: 'otherField5',
    laborCost: 'otherField6',
    overheadCost: 'otherField7',
    totalCost: 'totalPrice',
    remark: 'remark'
  },
  
  // 必填字段验证
  requiredFields: [
    'assemblyNo',
    'assemblyDate',
    'warehouseId',
    'assemblyType',
    'operator',
    'materials',
    'products'
  ],
  
  // 业务规则
  businessRules: {
    allowNegativeStock: false,
    requireStockValidation: true,
    autoGenerateNumber: true,
    numberPrefix: 'ZZPD'
  }
}

/**
 * 组装单表单Hook
 */
export function useAssemblyForm() {
  // 使用通用业务表单
  const businessForm = useBusinessForm(assemblyFormConfig)
  
  // 组装单特有状态
  const assemblyForm = reactive<AssemblyForm>({
    id: '',
    assemblyNo: '',
    assemblyDate: new Date().toISOString().split('T')[0],
    operator: '',
    operatorId: '',
    warehouseId: '',
    warehouseName: '',
    assemblyType: 'simple' as AssemblyType,
    materials: [],
    products: [],
    totalMaterialCost: 0,
    totalProductCost: 0,
    laborCost: 0,
    overheadCost: 0,
    totalCost: 0,
    remark: ''
  })
  
  // 材料库存警告列表
  const materialStockWarnings = ref<MaterialStockWarning[]>([])
  
  // 选择器状态
  const showWarehouseSelector = ref(false)
  const showAssemblyTypeSelector = ref(false)
  const showMaterialSelector = ref(false)
  const showProductSelector = ref(false)
  
  // 计算属性
  const isFormValid = computed(() => {
    return assemblyForm.assemblyNo &&
           assemblyForm.assemblyDate &&
           assemblyForm.warehouseId &&
           assemblyForm.operator &&
           assemblyForm.materials.length > 0 &&
           assemblyForm.products.length > 0 &&
           assemblyForm.materials.every(m => m.quantity > 0) &&
           assemblyForm.products.every(p => p.quantity > 0) &&
           materialStockWarnings.value.length === 0
  })
  
  const totalMaterialQuantity = computed(() => {
    return assemblyForm.materials.reduce((sum, material) => sum + material.quantity, 0)
  })
  
  const totalProductQuantity = computed(() => {
    return assemblyForm.products.reduce((sum, product) => sum + product.quantity, 0)
  })
  
  const hasMaterialStockWarnings = computed(() => materialStockWarnings.value.length > 0)
  
  // 生成组装单号
  const generateAssemblyNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `ZZPD${year}${month}${day}${random}`
  }
  
  // 验证材料库存
  const validateMaterialStock = (materialId: string | number, requiredQuantity: number): boolean => {
    const detail = businessForm.formData.details.find(d => d.productId === materialId)
    if (!detail) return false
    
    const availableStock = Number(detail.otherField5) || 0
    return requiredQuantity <= availableStock
  }
  
  // 检查材料库存并生成警告
  const checkMaterialStockAndGenerateWarnings = () => {
    const warnings: MaterialStockWarning[] = []
    
    assemblyForm.materials.forEach(material => {
      if (!validateMaterialStock(material.id, material.quantity)) {
        const detail = businessForm.formData.details.find(d => d.productId === material.id)
        const availableStock = Number(detail?.otherField5) || 0
        
        warnings.push({
          materialId: material.id,
          materialName: material.name,
          requiredQuantity: material.quantity,
          availableQuantity: availableStock,
          message: `${material.name} 需要数量 ${material.quantity} 超过库存 ${availableStock}`
        })
      }
    })
    
    materialStockWarnings.value = warnings
  }
  
  // 计算成本
  const calculateCosts = () => {
    // 计算材料总成本
    assemblyForm.totalMaterialCost = assemblyForm.materials.reduce((sum, material) => {
      return sum + (material.quantity * material.unitPrice)
    }, 0)
    
    // 计算成品总成本
    assemblyForm.totalProductCost = assemblyForm.products.reduce((sum, product) => {
      return sum + (product.quantity * product.unitPrice)
    }, 0)
    
    // 计算总成本
    assemblyForm.totalCost = assemblyForm.totalMaterialCost + 
                            (assemblyForm.laborCost || 0) + 
                            (assemblyForm.overheadCost || 0)
  }
  
  // 选择仓库
  const selectWarehouse = (warehouse: any) => {
    assemblyForm.warehouseId = warehouse.id
    assemblyForm.warehouseName = warehouse.name
    showWarehouseSelector.value = false
    
    // 重新检查材料库存
    checkMaterialStockAndGenerateWarnings()
  }
  
  // 选择组装类型
  const selectAssemblyType = (type: AssemblyType) => {
    assemblyForm.assemblyType = type
    showAssemblyTypeSelector.value = false
  }
  
  // 添加材料
  const addMaterial = (material: AssemblyMaterial) => {
    const existingIndex = assemblyForm.materials.findIndex(m => m.id === material.id)
    
    if (existingIndex >= 0) {
      assemblyForm.materials[existingIndex].quantity += material.quantity
    } else {
      assemblyForm.materials.push({ ...material })
    }
    
    calculateCosts()
    checkMaterialStockAndGenerateWarnings()
  }
  
  // 移除材料
  const removeMaterial = (materialId: string | number) => {
    const index = assemblyForm.materials.findIndex(m => m.id === materialId)
    if (index >= 0) {
      assemblyForm.materials.splice(index, 1)
      calculateCosts()
      checkMaterialStockAndGenerateWarnings()
    }
  }
  
  // 更新材料数量
  const updateMaterialQuantity = (materialId: string | number, quantity: number) => {
    const material = assemblyForm.materials.find(m => m.id === materialId)
    if (material) {
      material.quantity = quantity
      calculateCosts()
      checkMaterialStockAndGenerateWarnings()
    }
  }
  
  // 添加成品
  const addProduct = (product: AssemblyProduct) => {
    const existingIndex = assemblyForm.products.findIndex(p => p.id === product.id)
    
    if (existingIndex >= 0) {
      assemblyForm.products[existingIndex].quantity += product.quantity
    } else {
      assemblyForm.products.push({ ...product })
    }
    
    calculateCosts()
  }
  
  // 移除成品
  const removeProduct = (productId: string | number) => {
    const index = assemblyForm.products.findIndex(p => p.id === productId)
    if (index >= 0) {
      assemblyForm.products.splice(index, 1)
      calculateCosts()
    }
  }
  
  // 更新成品数量
  const updateProductQuantity = (productId: string | number, quantity: number) => {
    const product = assemblyForm.products.find(p => p.id === productId)
    if (product) {
      product.quantity = quantity
      calculateCosts()
    }
  }
  
  // 提交组装单
  const submitAssembly = async () => {
    if (!isFormValid.value) {
      showToast('请完善组装单信息')
      return false
    }
    
    if (hasMaterialStockWarnings.value) {
      const confirmed = await showConfirmDialog({
        title: '材料库存警告',
        message: '存在材料库存不足，是否继续提交？'
      }).catch(() => false)
      
      if (!confirmed) return false
    }
    
    try {
      // 映射数据到通用表单格式
      businessForm.formData.number = assemblyForm.assemblyNo || generateAssemblyNo()
      businessForm.formData.operTime = assemblyForm.assemblyDate
      businessForm.formData.depotId = assemblyForm.warehouseId
      businessForm.formData.depotName = assemblyForm.warehouseName
      businessForm.formData.otherField1 = assemblyForm.assemblyType
      businessForm.formData.otherField2 = assemblyForm.operator
      businessForm.formData.otherField3 = assemblyForm.operatorId
      businessForm.formData.otherField4 = assemblyForm.totalMaterialCost
      businessForm.formData.otherField5 = assemblyForm.totalProductCost
      businessForm.formData.otherField6 = assemblyForm.laborCost
      businessForm.formData.otherField7 = assemblyForm.overheadCost
      businessForm.formData.totalPrice = assemblyForm.totalCost
      businessForm.formData.remark = assemblyForm.remark
      
      // 映射材料和成品数据
      businessForm.formData.details = [
        // 材料数据（负数表示消耗）
        ...assemblyForm.materials.map(material => ({
          productId: material.id,
          productName: material.name,
          productModel: material.code,
          productUnit: material.unit,
          quantity: -material.quantity, // 负数表示消耗
          unitPrice: material.unitPrice,
          totalPrice: -(material.quantity * material.unitPrice),
          remark: `材料: ${material.name}`
        })),
        // 成品数据（正数表示产出）
        ...assemblyForm.products.map(product => ({
          productId: product.id,
          productName: product.name,
          productModel: product.code,
          productUnit: product.unit,
          quantity: product.quantity, // 正数表示产出
          unitPrice: product.unitPrice,
          totalPrice: product.quantity * product.unitPrice,
          remark: `成品: ${product.name}`
        }))
      ]
      
      const result = await businessForm.submitForm()
      
      if (result) {
        showToast('组装单提交成功')
        resetForm()
      }
      
      return result
    } catch (error) {
      console.error('组装单提交失败:', error)
      showToast('组装单提交失败')
      return false
    }
  }
  
  // 重置表单
  const resetForm = () => {
    Object.assign(assemblyForm, {
      id: '',
      assemblyNo: '',
      assemblyDate: new Date().toISOString().split('T')[0],
      operator: '',
      operatorId: '',
      warehouseId: '',
      warehouseName: '',
      assemblyType: 'simple' as AssemblyType,
      materials: [],
      products: [],
      totalMaterialCost: 0,
      totalProductCost: 0,
      laborCost: 0,
      overheadCost: 0,
      totalCost: 0,
      remark: ''
    })
    
    materialStockWarnings.value = []
    businessForm.resetForm()
  }
  
  // 初始化
  const initForm = () => {
    assemblyForm.assemblyNo = generateAssemblyNo()
  }
  
  return {
    // 表单数据
    assemblyForm,
    businessForm,
    
    // 状态
    materialStockWarnings,
    showWarehouseSelector,
    showAssemblyTypeSelector,
    showMaterialSelector,
    showProductSelector,
    
    // 计算属性
    isFormValid,
    totalMaterialQuantity,
    totalProductQuantity,
    hasMaterialStockWarnings,
    
    // 选项
    assemblyTypeOptions,
    
    // 方法
    generateAssemblyNo,
    validateMaterialStock,
    checkMaterialStockAndGenerateWarnings,
    calculateCosts,
    selectWarehouse,
    selectAssemblyType,
    addMaterial,
    removeMaterial,
    updateMaterialQuantity,
    addProduct,
    removeProduct,
    updateProductQuantity,
    submitAssembly,
    resetForm,
    initForm
  }
}
