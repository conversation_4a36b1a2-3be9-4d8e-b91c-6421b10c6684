import { ref, computed, reactive } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from './useBusinessForm'
import type { TransferOutForm, InventoryProduct } from '@/types/inventory'
import type { FormConfig } from '@/types/business'

/**
 * 调拨原因枚举
 */
export enum TransferReason {
  BALANCE = 'balance',           // 库存平衡
  EMERGENCY = 'emergency',       // 紧急调拨
  SEASONAL = 'seasonal',         // 季节性调整
  MAINTENANCE = 'maintenance',   // 维护调拨
  OPTIMIZATION = 'optimization', // 库存优化
  OTHER = 'other'               // 其他原因
}

/**
 * 调拨原因选项
 */
export const transferReasonOptions = [
  { label: '库存平衡', value: TransferReason.BALANCE },
  { label: '紧急调拨', value: TransferReason.EMERGENCY },
  { label: '季节性调整', value: TransferReason.SEASONAL },
  { label: '维护调拨', value: TransferReason.MAINTENANCE },
  { label: '库存优化', value: TransferReason.OPTIMIZATION },
  { label: '其他原因', value: TransferReason.OTHER }
]

/**
 * 库存警告接口
 */
interface StockWarning {
  productId: string | number
  productName: string
  requestedQuantity: number
  availableQuantity: number
  message: string
}

/**
 * 调拨出库表单配置
 */
const transferOutFormConfig: FormConfig = {
  documentType: 'TRANSFER_OUT',
  title: '调拨出库',
  apiEndpoint: '/depot/transferOut',
  
  // 字段映射：TransferOutForm -> BaseBusinessForm
  fieldMapping: {
    transferNo: 'number',
    transferDate: 'operTime',
    fromWarehouseId: 'depotId',
    fromWarehouseName: 'depotName',
    toWarehouseId: 'otherField1',
    toWarehouseName: 'otherField2',
    transferReason: 'otherField3',
    operator: 'otherField4',
    operatorId: 'otherField5',
    transferInNo: 'otherField6',
    transferInId: 'otherField7',
    totalQuantity: 'totalQuantity',
    remark: 'remark'
  },
  
  // 必填字段验证
  requiredFields: [
    'transferNo',
    'transferDate', 
    'fromWarehouseId',
    'toWarehouseId',
    'operator',
    'products'
  ],
  
  // 业务规则
  businessRules: {
    allowNegativeStock: false,
    requireStockValidation: true,
    autoGenerateNumber: true,
    numberPrefix: 'DBCK'
  }
}

/**
 * 调拨出库表单Hook
 */
export function useTransferOutForm() {
  // 使用通用业务表单
  const businessForm = useBusinessForm(transferOutFormConfig)
  
  // 调拨出库特有状态
  const transferOutForm = reactive<TransferOutForm>({
    id: '',
    transferNo: '',
    transferDate: new Date().toISOString().split('T')[0],
    operator: '',
    operatorId: '',
    fromWarehouseId: '',
    fromWarehouseName: '',
    toWarehouseId: '',
    toWarehouseName: '',
    transferInNo: '',
    transferInId: '',
    products: [],
    totalQuantity: 0,
    remark: ''
  })
  
  // 库存警告列表
  const stockWarnings = ref<StockWarning[]>([])
  
  // 仓库选择状态
  const showFromWarehouseSelector = ref(false)
  const showToWarehouseSelector = ref(false)
  const showTransferReasonSelector = ref(false)
  
  // 计算属性
  const isFormValid = computed(() => {
    return transferOutForm.transferNo &&
           transferOutForm.transferDate &&
           transferOutForm.fromWarehouseId &&
           transferOutForm.toWarehouseId &&
           transferOutForm.fromWarehouseId !== transferOutForm.toWarehouseId &&
           transferOutForm.operator &&
           transferOutForm.products.length > 0 &&
           transferOutForm.products.every(p => p.quantity > 0) &&
           stockWarnings.value.length === 0
  })
  
  const totalAmount = computed(() => {
    return transferOutForm.products.reduce((sum, product) => {
      return sum + (product.quantity * product.unitPrice)
    }, 0)
  })
  
  const hasStockWarnings = computed(() => stockWarnings.value.length > 0)
  
  // 生成调拨出库单号
  const generateTransferOutNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `DBCK${year}${month}${day}${random}`
  }
  
  // 验证仓库不同
  const validateWarehouseDifferent = (): boolean => {
    if (transferOutForm.fromWarehouseId && transferOutForm.toWarehouseId) {
      if (transferOutForm.fromWarehouseId === transferOutForm.toWarehouseId) {
        showToast('源仓库和目标仓库不能相同')
        return false
      }
    }
    return true
  }
  
  // 验证源仓库库存
  const validateSourceStock = (productId: string | number, requestedQuantity: number): boolean => {
    const detail = businessForm.formData.details.find(d => d.productId === productId)
    if (!detail) return false
    
    const availableStock = Number(detail.otherField5) || 0
    return requestedQuantity <= availableStock
  }
  
  // 检查库存并生成警告
  const checkStockAndGenerateWarnings = () => {
    const warnings: StockWarning[] = []
    
    transferOutForm.products.forEach(product => {
      if (!validateSourceStock(product.id, product.quantity)) {
        const detail = businessForm.formData.details.find(d => d.productId === product.id)
        const availableStock = Number(detail?.otherField5) || 0
        
        warnings.push({
          productId: product.id,
          productName: product.name,
          requestedQuantity: product.quantity,
          availableQuantity: availableStock,
          message: `${product.name} 调拨数量 ${product.quantity} 超过源仓库库存 ${availableStock}`
        })
      }
    })
    
    stockWarnings.value = warnings
  }
  
  // 选择源仓库
  const selectFromWarehouse = (warehouse: any) => {
    transferOutForm.fromWarehouseId = warehouse.id
    transferOutForm.fromWarehouseName = warehouse.name
    showFromWarehouseSelector.value = false
    
    // 验证仓库不同
    validateWarehouseDifferent()
    
    // 重新检查库存
    checkStockAndGenerateWarnings()
  }
  
  // 选择目标仓库
  const selectToWarehouse = (warehouse: any) => {
    transferOutForm.toWarehouseId = warehouse.id
    transferOutForm.toWarehouseName = warehouse.name
    showToWarehouseSelector.value = false
    
    // 验证仓库不同
    validateWarehouseDifferent()
  }
  
  // 选择调拨原因
  const selectTransferReason = (reason: TransferReason) => {
    // 通过字段映射设置调拨原因
    businessForm.formData.otherField3 = reason
    showTransferReasonSelector.value = false
  }
  
  // 添加商品
  const addProduct = (product: InventoryProduct) => {
    const existingIndex = transferOutForm.products.findIndex(p => p.id === product.id)
    
    if (existingIndex >= 0) {
      transferOutForm.products[existingIndex].quantity += product.quantity
    } else {
      transferOutForm.products.push({ ...product })
    }
    
    // 更新总数量
    transferOutForm.totalQuantity = transferOutForm.products.reduce((sum, p) => sum + p.quantity, 0)
    
    // 检查库存
    checkStockAndGenerateWarnings()
  }
  
  // 移除商品
  const removeProduct = (productId: string | number) => {
    const index = transferOutForm.products.findIndex(p => p.id === productId)
    if (index >= 0) {
      transferOutForm.products.splice(index, 1)
      transferOutForm.totalQuantity = transferOutForm.products.reduce((sum, p) => sum + p.quantity, 0)
      checkStockAndGenerateWarnings()
    }
  }
  
  // 更新商品数量
  const updateProductQuantity = (productId: string | number, quantity: number) => {
    const product = transferOutForm.products.find(p => p.id === productId)
    if (product) {
      product.quantity = quantity
      transferOutForm.totalQuantity = transferOutForm.products.reduce((sum, p) => sum + p.quantity, 0)
      checkStockAndGenerateWarnings()
    }
  }
  
  // 提交调拨出库
  const submitTransferOut = async () => {
    if (!isFormValid.value) {
      showToast('请完善调拨出库信息')
      return false
    }
    
    if (hasStockWarnings.value) {
      const confirmed = await showConfirmDialog({
        title: '库存警告',
        message: '存在库存不足的商品，是否继续提交？'
      }).catch(() => false)
      
      if (!confirmed) return false
    }
    
    try {
      // 映射数据到通用表单格式
      businessForm.formData.number = transferOutForm.transferNo || generateTransferOutNo()
      businessForm.formData.operTime = transferOutForm.transferDate
      businessForm.formData.depotId = transferOutForm.fromWarehouseId
      businessForm.formData.depotName = transferOutForm.fromWarehouseName
      businessForm.formData.otherField1 = transferOutForm.toWarehouseId
      businessForm.formData.otherField2 = transferOutForm.toWarehouseName
      businessForm.formData.otherField4 = transferOutForm.operator
      businessForm.formData.otherField5 = transferOutForm.operatorId
      businessForm.formData.totalQuantity = transferOutForm.totalQuantity
      businessForm.formData.remark = transferOutForm.remark
      
      // 映射商品数据
      businessForm.formData.details = transferOutForm.products.map(product => ({
        productId: product.id,
        productName: product.name,
        productModel: product.model,
        productUnit: product.unit,
        quantity: product.quantity,
        unitPrice: product.unitPrice,
        totalPrice: product.quantity * product.unitPrice,
        remark: product.remark
      }))
      
      const result = await businessForm.submitForm()
      
      if (result) {
        showToast('调拨出库提交成功')
        resetForm()
      }
      
      return result
    } catch (error) {
      console.error('调拨出库提交失败:', error)
      showToast('调拨出库提交失败')
      return false
    }
  }
  
  // 重置表单
  const resetForm = () => {
    Object.assign(transferOutForm, {
      id: '',
      transferNo: '',
      transferDate: new Date().toISOString().split('T')[0],
      operator: '',
      operatorId: '',
      fromWarehouseId: '',
      fromWarehouseName: '',
      toWarehouseId: '',
      toWarehouseName: '',
      transferInNo: '',
      transferInId: '',
      products: [],
      totalQuantity: 0,
      remark: ''
    })
    
    stockWarnings.value = []
    businessForm.resetForm()
  }
  
  // 初始化
  const initForm = () => {
    transferOutForm.transferNo = generateTransferOutNo()
  }
  
  return {
    // 表单数据
    transferOutForm,
    businessForm,
    
    // 状态
    stockWarnings,
    showFromWarehouseSelector,
    showToWarehouseSelector,
    showTransferReasonSelector,
    
    // 计算属性
    isFormValid,
    totalAmount,
    hasStockWarnings,
    
    // 选项
    transferReasonOptions,
    
    // 方法
    generateTransferOutNo,
    validateWarehouseDifferent,
    validateSourceStock,
    checkStockAndGenerateWarnings,
    selectFromWarehouse,
    selectToWarehouse,
    selectTransferReason,
    addProduct,
    removeProduct,
    updateProductQuantity,
    submitTransferOut,
    resetForm,
    initForm
  }
}
