/**
 * 其他入库表单业务逻辑Hook
 * 
 * 基于通用业务表单逻辑，扩展其他入库特定功能：
 * - 入库类型管理
 * - 操作员选择
 * - 仓库选择
 * - 商品库存管理
 * - 金额计算
 */

import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from '@/composables/business/useBusinessForm'
import { DocumentType } from '@/types/business'
import type { 
  OtherInForm, 
  InventoryProduct, 
  Operator,
  InType
} from '@/types/inventory'
import type { FormConfig } from '@/types/business'
import type { Depot } from '@/types/business'

/**
 * 其他入库表单Hook
 */
export function useOtherInForm(initialData?: Partial<OtherInForm>) {
  // 其他入库配置
  const otherInConfig: FormConfig = {
    formType: DocumentType.OTHER_IN,
    showPartnerSelector: false,
    partnerType: '',
    showDepotSelector: true,
    showAccountSelector: false,
    showLinkDocument: true,
    editable: true,
    defaultValues: {
      number: '',
      operTime: new Date().toISOString().slice(0, 16),
      ...initialData
    }
  }

  // 使用通用业务表单逻辑
  const businessForm = useBusinessForm(otherInConfig)
  
  // 选择器显示状态
  const showOperatorPicker = ref<boolean>(false)
  const showDatePicker = ref<boolean>(false)
  const showWarehousePicker = ref<boolean>(false)
  const showProductPicker = ref<boolean>(false)
  const showSourceDocumentPicker = ref<boolean>(false)
  const dateValue = ref<string[]>([])
  
  // 其他入库特定的计算属性
  const otherInForm = computed(() => ({
    // 映射通用字段到其他入库字段
    inNo: businessForm.formData.number,
    inDate: businessForm.formData.operTime,
    operator: businessForm.formData.otherField1 || '',
    operatorId: businessForm.formData.otherField2 || '',
    
    // 仓库信息
    warehouseId: businessForm.formData.depotId || '',
    warehouseName: businessForm.formData.depotName || '',
    
    // 入库信息
    inType: businessForm.formData.otherField3 as InType || 'other',
    sourceDocument: businessForm.formData.linkNumber || '',
    sourceDocumentId: businessForm.formData.linkId || '',
    
    // 商品信息
    products: businessForm.formData.details.map(detail => ({
      id: detail.productId,
      name: detail.productName,
      code: detail.otherField1 || '',
      spec: detail.otherField2 || '',
      unit: detail.otherField3 || '个',
      currentStock: detail.otherField4 ? Number(detail.otherField4) : 0,
      availableStock: detail.otherField5 ? Number(detail.otherField5) : 0,
      lockedStock: 0,
      costPrice: detail.unitPrice || 0,
      averagePrice: detail.unitPrice || 0,
      lastInPrice: detail.unitPrice || 0,
      lastOutPrice: detail.unitPrice || 0,
      warehouseId: businessForm.formData.depotId || '',
      warehouseName: businessForm.formData.depotName || '',
      locationCode: detail.otherField6 || ''
    })) as InventoryProduct[],
    
    // 数量和金额信息
    totalQuantity: businessForm.formData.details.reduce((total, detail) => total + (detail.basicNumber || 0), 0),
    totalAmount: businessForm.formData.totalPrice || 0,
    
    // 附加信息
    remark: businessForm.formData.remark || '',
    attachments: []
  }))

  // 计算属性
  const isFormValid = computed(() => {
    const form = otherInForm.value
    return form.inDate && 
           form.operator && 
           form.warehouseName &&
           form.products.length > 0 &&
           form.totalQuantity > 0
  })

  const totalInQuantity = computed(() => {
    return otherInForm.value.totalQuantity
  })

  const totalInAmount = computed(() => {
    return otherInForm.value.totalAmount
  })

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number): string => {
    return amount.toFixed(2)
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 生成其他入库编号
   */
  const generateInNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `QTRK${year}${month}${day}${random}`
  }

  /**
   * 获取入库类型文本
   */
  const getInTypeText = (type: InType): string => {
    switch (type) {
      case 'production': return '生产入库'
      case 'transfer': return '调拨入库'
      case 'inventory_gain': return '盘盈入库'
      case 'return': return '退货入库'
      case 'gift': return '赠品入库'
      default: return '其他入库'
    }
  }

  /**
   * 获取入库类型选项
   */
  const getInTypeOptions = () => [
    { text: '生产入库', value: 'production' },
    { text: '调拨入库', value: 'transfer' },
    { text: '盘盈入库', value: 'inventory_gain' },
    { text: '退货入库', value: 'return' },
    { text: '赠品入库', value: 'gift' },
    { text: '其他入库', value: 'other' }
  ]

  /**
   * 计算金额
   */
  const calculateAmount = (): void => {
    const totalAmount = businessForm.formData.details.reduce((total, detail) => {
      return total + ((detail.unitPrice || 0) * (detail.basicNumber || 0))
    }, 0)
    
    businessForm.formData.totalPrice = totalAmount
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const form = otherInForm.value
    const errors: string[] = []

    // 验证日期
    if (!form.inDate) {
      errors.push('请选择入库日期')
    }

    // 验证操作员
    if (!form.operator) {
      errors.push('请选择操作员')
    }

    // 验证仓库
    if (!form.warehouseName) {
      errors.push('请选择入库仓库')
    }

    // 验证商品
    if (form.products.length === 0) {
      errors.push('请至少添加一个入库商品')
    }

    // 验证入库数量
    for (const product of form.products) {
      const detail = businessForm.formData.details.find(d => d.productId === product.id)
      if (!detail || detail.basicNumber <= 0) {
        errors.push(`商品"${product.name}"的入库数量必须大于0`)
        break
      }
    }

    // 验证总数量
    if (form.totalQuantity <= 0) {
      errors.push('入库总数量必须大于0')
    }

    if (errors.length > 0) {
      showToast({ type: 'fail', message: errors[0] })
      return false
    }

    return true
  }

  /**
   * 日期确认处理
   */
  const handleDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    businessForm.formData.operTime = formatDate(date)
    showDatePicker.value = false
  }

  /**
   * 操作员选择处理
   */
  const handleOperatorSelect = (operator: Operator): void => {
    businessForm.formData.otherField1 = operator.name
    businessForm.formData.otherField2 = operator.id.toString()
    showOperatorPicker.value = false
  }

  /**
   * 仓库选择处理
   */
  const handleWarehouseSelect = (warehouse: Depot): void => {
    businessForm.formData.depotName = warehouse.name
    businessForm.formData.depotId = warehouse.id
    
    // 更新所有商品的仓库信息
    businessForm.formData.details.forEach(detail => {
      detail.otherField7 = warehouse.id.toString()
      detail.otherField8 = warehouse.name
    })
    
    showWarehousePicker.value = false
  }

  /**
   * 入库类型选择处理
   */
  const handleInTypeSelect = (type: InType): void => {
    businessForm.formData.otherField3 = type
  }

  /**
   * 来源单据选择处理
   */
  const handleSourceDocumentSelect = (document: any): void => {
    businessForm.formData.linkNumber = document.documentNo
    businessForm.formData.linkId = document.id
    showSourceDocumentPicker.value = false
  }

  /**
   * 添加入库商品
   */
  const addInProduct = (product: InventoryProduct): void => {
    businessForm.addDetailItem({
      productId: product.id,
      productName: product.name,
      unitPrice: product.costPrice,
      basicNumber: 1,
      otherField1: product.code || '',
      otherField2: product.spec || '',
      otherField3: product.unit || '个',
      otherField4: product.currentStock.toString(),
      otherField5: product.availableStock.toString(),
      otherField6: product.locationCode || '',
      otherField7: product.warehouseId.toString(),
      otherField8: product.warehouseName
    })
    
    calculateAmount()
  }

  /**
   * 移除入库商品
   */
  const removeInProduct = async (index: number): Promise<void> => {
    try {
      await showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个入库商品吗？'
      })
      
      businessForm.removeDetailItem(index)
      calculateAmount()
      showToast({ type: 'success', message: '商品已删除' })
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 入库数量变化处理
   */
  const handleInQuantityChange = (index: number, quantity: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].basicNumber = quantity
      calculateAmount()
    }
  }

  /**
   * 入库单价变化处理
   */
  const handleInPriceChange = (index: number, price: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].unitPrice = price
      calculateAmount()
    }
  }

  /**
   * 库位变化处理
   */
  const handleLocationChange = (index: number, location: string): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].otherField6 = location
    }
  }

  /**
   * 重置表单
   */
  const resetForm = (): void => {
    businessForm.resetForm()
    businessForm.formData.number = generateInNo()
    businessForm.formData.operTime = formatDate(new Date())
    businessForm.formData.otherField3 = 'other'
  }

  /**
   * 初始化表单
   */
  const initializeForm = (data?: Partial<OtherInForm>): void => {
    if (data) {
      // 映射数据到业务表单
      businessForm.formData.number = data.inNo || generateInNo()
      businessForm.formData.operTime = data.inDate || formatDate(new Date())
      businessForm.formData.otherField1 = data.operator || ''
      businessForm.formData.otherField2 = data.operatorId?.toString() || ''
      businessForm.formData.depotName = data.warehouseName || ''
      businessForm.formData.depotId = data.warehouseId || ''
      businessForm.formData.otherField3 = data.inType || 'other'
      businessForm.formData.linkNumber = data.sourceDocument || ''
      businessForm.formData.linkId = data.sourceDocumentId || ''
      businessForm.formData.remark = data.remark || ''
      
      // 映射商品数据
      if (data.products) {
        businessForm.formData.details = data.products.map(product => ({
          productId: product.id,
          productName: product.name,
          unitPrice: product.costPrice,
          basicNumber: 1,
          otherField1: product.code || '',
          otherField2: product.spec || '',
          otherField3: product.unit || '个',
          otherField4: product.currentStock.toString(),
          otherField5: product.availableStock.toString(),
          otherField6: product.locationCode || '',
          otherField7: product.warehouseId.toString(),
          otherField8: product.warehouseName
        }))
      }
    } else {
      businessForm.formData.number = generateInNo()
      businessForm.formData.operTime = formatDate(new Date())
      businessForm.formData.otherField3 = 'other'
    }
  }

  return {
    // 响应式数据
    loading: businessForm.loading,
    submitting: businessForm.submitting,
    otherInForm,
    showOperatorPicker,
    showDatePicker,
    showWarehousePicker,
    showProductPicker,
    showSourceDocumentPicker,
    dateValue,
    
    // 计算属性
    isFormValid,
    totalInQuantity,
    totalInAmount,
    
    // 业务表单方法
    ...businessForm,
    
    // 其他入库特定方法
    formatCurrency,
    formatDate,
    generateInNo,
    getInTypeText,
    getInTypeOptions,
    calculateAmount,
    validateForm,
    handleDateConfirm,
    handleOperatorSelect,
    handleWarehouseSelect,
    handleInTypeSelect,
    handleSourceDocumentSelect,
    addInProduct,
    removeInProduct,
    handleInQuantityChange,
    handleInPriceChange,
    handleLocationChange,
    resetForm,
    initializeForm
  }
}
