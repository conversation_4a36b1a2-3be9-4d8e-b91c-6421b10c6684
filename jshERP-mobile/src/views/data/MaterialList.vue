<template>
  <div class="material-list">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索商品名称、条码、规格、型号"
        @search="handleSearch"
        @clear="handleClearSearch"
        show-action
      >
        <template #action>
          <van-button 
            type="primary" 
            size="small" 
            @click="showFilterPopup = true"
            icon="filter-o"
          >
            筛选
          </van-button>
        </template>
      </van-search>
    </div>

    <!-- 分类筛选标签 -->
    <div class="category-tags" v-if="selectedCategories.length > 0">
      <van-tag
        v-for="category in selectedCategories"
        :key="category.id"
        closeable
        type="primary"
        @close="removeCategory(category)"
        class="category-tag"
      >
        {{ category.name }}
      </van-tag>
    </div>

    <!-- 商品列表 -->
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="loadMore"
      class="material-list-container"
    >
      <div
        v-for="material in materialList"
        :key="material.id"
        class="material-item"
        @click="handleItemClick(material)"
      >
        <van-card
          :num="material.stock || 0"
          :price="material.wholesalePrice || 0"
          :desc="material.standard || material.model"
          :title="material.name"
          :thumb="material.imgUrl || defaultImage"
          :lazy-load="true"
        >
          <template #tags>
            <van-tag 
              v-if="material.categoryName" 
              type="default" 
              size="small"
            >
              {{ material.categoryName }}
            </van-tag>
            <van-tag 
              v-if="material.unitName" 
              type="default" 
              size="small"
            >
              {{ material.unitName }}
            </van-tag>
          </template>
          
          <template #footer>
            <div class="material-footer">
              <div class="stock-info">
                <span class="stock-label">库存:</span>
                <span 
                  class="stock-value"
                  :class="{ 'low-stock': (material.stock || 0) < 10 }"
                >
                  {{ material.stock || 0 }}
                </span>
              </div>
              <div class="price-info">
                <span class="price-label">批发价:</span>
                <span class="price-value">¥{{ material.wholesalePrice || 0 }}</span>
              </div>
            </div>
          </template>
        </van-card>
      </div>
    </van-list>

    <!-- 筛选弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="filter-popup">
        <div class="filter-header">
          <van-button 
            type="default" 
            size="small" 
            @click="resetFilter"
          >
            重置
          </van-button>
          <h3>筛选条件</h3>
          <van-button 
            type="primary" 
            size="small" 
            @click="applyFilter"
          >
            确定
          </van-button>
        </div>

        <div class="filter-content">
          <!-- 商品分类 -->
          <div class="filter-section">
            <h4>商品分类</h4>
            <div class="category-list">
              <van-checkbox-group v-model="tempSelectedCategoryIds">
                <van-checkbox
                  v-for="category in categories"
                  :key="category.id"
                  :name="category.id"
                  class="category-checkbox"
                >
                  {{ category.name }}
                </van-checkbox>
              </van-checkbox-group>
            </div>
          </div>

          <!-- 启用状态 -->
          <div class="filter-section">
            <h4>商品状态</h4>
            <van-radio-group v-model="tempEnabledFilter">
              <van-radio name="">全部</van-radio>
              <van-radio name="1">启用</van-radio>
              <van-radio name="0">禁用</van-radio>
            </van-radio-group>
          </div>

          <!-- 库存状态 -->
          <div class="filter-section">
            <h4>库存状态</h4>
            <van-radio-group v-model="tempStockFilter">
              <van-radio name="">全部</van-radio>
              <van-radio name="inStock">有库存</van-radio>
              <van-radio name="lowStock">库存不足</van-radio>
              <van-radio name="outOfStock">无库存</van-radio>
            </van-radio-group>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 加载状态 -->
    <van-loading 
      v-if="initialLoading" 
      class="loading-overlay"
      type="spinner"
      color="#3B82F6"
    >
      加载中...
    </van-loading>

    <!-- 空状态 -->
    <van-empty 
      v-if="!initialLoading && materialList.length === 0"
      description="暂无商品数据"
      image="search"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { materialAdapter } from '@/api/adapters/material'
import type { 
  MaterialVo4Unit, 
  MaterialQuery, 
  MaterialCategory 
} from '@/types/material'

// 路由
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const materialList = ref<MaterialVo4Unit[]>([])
const categories = ref<MaterialCategory[]>([])
const selectedCategories = ref<MaterialCategory[]>([])
const loading = ref(false)
const finished = ref(false)
const initialLoading = ref(true)
const showFilterPopup = ref(false)

// 分页参数
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 筛选条件
const filterParams = reactive<MaterialQuery>({
  current: 1,
  size: 20
})

// 临时筛选条件（弹窗中使用）
const tempSelectedCategoryIds = ref<number[]>([])
const tempEnabledFilter = ref('')
const tempStockFilter = ref('')

// 默认图片
const defaultImage = 'https://via.placeholder.com/100x100?text=商品'

/**
 * 组件挂载时初始化
 */
onMounted(async () => {
  await Promise.all([
    loadCategories(),
    loadMaterialList(true)
  ])
  initialLoading.value = false
})

/**
 * 加载商品分类
 */
const loadCategories = async () => {
  try {
    const response = await materialAdapter.getCategories()
    if (response.success) {
      categories.value = response.data || []
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

/**
 * 加载商品列表
 */
const loadMaterialList = async (reset = false) => {
  try {
    if (reset) {
      pagination.current = 1
      materialList.value = []
      finished.value = false
    }

    const params: MaterialQuery = {
      ...filterParams,
      current: pagination.current,
      size: pagination.size,
      materialParam: searchKeyword.value || undefined,
      categoryId: selectedCategories.value.map(c => c.id).join(',') || undefined
    }

    const response = await materialAdapter.getList(params)
    
    if (reset) {
      materialList.value = response.records
    } else {
      materialList.value.push(...response.records)
    }
    
    pagination.total = response.total
    pagination.current++
    
    // 检查是否还有更多数据
    if (materialList.value.length >= response.total) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('加载商品列表失败:', error)
    showToast('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 加载更多
 */
const loadMore = () => {
  if (!finished.value) {
    loading.value = true
    loadMaterialList()
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  loadMaterialList(true)
}

/**
 * 清除搜索
 */
const handleClearSearch = () => {
  searchKeyword.value = ''
  loadMaterialList(true)
}

/**
 * 移除分类筛选
 */
const removeCategory = (category: MaterialCategory) => {
  const index = selectedCategories.value.findIndex(c => c.id === category.id)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
    loadMaterialList(true)
  }
}

/**
 * 重置筛选条件
 */
const resetFilter = () => {
  tempSelectedCategoryIds.value = []
  tempEnabledFilter.value = ''
  tempStockFilter.value = ''
}

/**
 * 应用筛选条件
 */
const applyFilter = () => {
  // 更新选中的分类
  selectedCategories.value = categories.value.filter(c => 
    tempSelectedCategoryIds.value.includes(c.id!)
  )
  
  // 更新筛选参数
  filterParams.enabled = tempEnabledFilter.value
  
  // 关闭弹窗并重新加载
  showFilterPopup.value = false
  loadMaterialList(true)
}

/**
 * 商品项点击处理
 */
const handleItemClick = (material: MaterialVo4Unit) => {
  router.push({
    name: 'MaterialDetail',
    params: { id: material.id }
  })
}
</script>

<style scoped lang="less">
.material-list {
  background-color: #f7f8fa;
  min-height: 100vh;

  .search-section {
    background: white;
    padding: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .category-tags {
    padding: 8px 12px;
    background: white;
    border-bottom: 1px solid #eee;

    .category-tag {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }

  .material-list-container {
    padding: 12px;
  }

  .material-item {
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .material-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      font-size: 12px;

      .stock-info, .price-info {
        display: flex;
        align-items: center;
      }

      .stock-label, .price-label {
        color: #666;
        margin-right: 4px;
      }

      .stock-value {
        color: #07c160;
        font-weight: bold;

        &.low-stock {
          color: #ff976a;
        }
      }

      .price-value {
        color: #3B82F6;
        font-weight: bold;
      }
    }
  }

  .filter-popup {
    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #eee;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .filter-content {
      padding: 16px;
      max-height: calc(60vh - 60px);
      overflow-y: auto;

      .filter-section {
        margin-bottom: 24px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .category-list {
          .category-checkbox {
            display: block;
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  .loading-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }
}
</style>
