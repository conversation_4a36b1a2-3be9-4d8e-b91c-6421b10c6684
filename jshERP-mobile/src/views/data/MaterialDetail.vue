<template>
  <div class="material-detail">
    <!-- 导航栏 -->
    <van-nav-bar
      title="商品详情"
      left-arrow
      @click-left="handleBack"
      @click-right="handleEdit"
    >
      <template #right>
        <van-icon name="edit" size="18" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading 
      v-if="loading" 
      class="loading-overlay"
      type="spinner"
      color="#3B82F6"
    >
      加载中...
    </van-loading>

    <!-- 商品信息 -->
    <div v-else-if="material" class="material-content">
      <!-- 商品图片和基本信息 -->
      <div class="material-header">
        <div class="material-image">
          <van-image
            :src="material.imgUrl || defaultImage"
            fit="cover"
            round
            width="120"
            height="120"
            :lazy-load="true"
          />
        </div>
        <div class="material-basic">
          <h2 class="material-name">{{ material.name }}</h2>
          <div class="material-tags">
            <van-tag 
              v-if="material.categoryName" 
              type="primary" 
              size="small"
            >
              {{ material.categoryName }}
            </van-tag>
            <van-tag 
              v-if="material.unitName" 
              type="default" 
              size="small"
            >
              {{ material.unitName }}
            </van-tag>
            <van-tag 
              v-if="material.enabled" 
              type="success" 
              size="small"
            >
              启用
            </van-tag>
            <van-tag 
              v-else 
              type="danger" 
              size="small"
            >
              禁用
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 商品详细信息 -->
      <div class="detail-sections">
        <!-- 基本信息 -->
        <van-cell-group title="基本信息" inset>
          <van-cell title="商品编码" :value="material.id" />
          <van-cell title="商品名称" :value="material.name" />
          <van-cell title="规格" :value="material.standard || '-'" />
          <van-cell title="型号" :value="material.model || '-'" />
          <van-cell title="颜色" :value="material.color || '-'" />
          <van-cell title="品牌" :value="material.brand || '-'" />
          <van-cell title="制造商" :value="material.mfrs || '-'" />
          <van-cell title="单位" :value="material.unitName || '-'" />
          <van-cell title="重量" :value="material.weight ? `${material.weight}kg` : '-'" />
        </van-cell-group>

        <!-- 价格信息 -->
        <van-cell-group title="价格信息" inset>
          <van-cell title="采购价" :value="material.purchasePrice ? `¥${material.purchasePrice}` : '-'" />
          <van-cell title="批发价" :value="material.wholesalePrice ? `¥${material.wholesalePrice}` : '-'" />
          <van-cell title="最低售价" :value="material.lowPrice ? `¥${material.lowPrice}` : '-'" />
        </van-cell-group>

        <!-- 库存信息 -->
        <van-cell-group title="库存信息" inset>
          <van-cell title="当前库存" :value="material.stock || 0" />
          <van-cell title="条码" :value="material.mBarCode || '-'" />
        </van-cell-group>

        <!-- 扩展信息 -->
        <van-cell-group title="扩展信息" inset>
          <van-cell title="助记码" :value="material.mnemonic || '-'" />
          <van-cell title="位置" :value="material.position || '-'" />
          <van-cell title="保质期" :value="material.expiryNum ? `${material.expiryNum}天` : '-'" />
          <van-cell title="序列号管理" :value="material.enableSerialNumber === '1' ? '启用' : '禁用'" />
          <van-cell title="批次号管理" :value="material.enableBatchNumber === '1' ? '启用' : '禁用'" />
          <van-cell title="备注" :value="material.remark || '-'" />
        </van-cell-group>

        <!-- 扩展字段 -->
        <van-cell-group 
          v-if="material.otherField1 || material.otherField2 || material.otherField3"
          title="自定义字段" 
          inset
        >
          <van-cell 
            v-if="material.otherField1" 
            title="扩展字段1" 
            :value="material.otherField1" 
          />
          <van-cell 
            v-if="material.otherField2" 
            title="扩展字段2" 
            :value="material.otherField2" 
          />
          <van-cell 
            v-if="material.otherField3" 
            title="扩展字段3" 
            :value="material.otherField3" 
          />
        </van-cell-group>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button 
          type="primary" 
          size="large" 
          @click="handleEdit"
          block
        >
          编辑商品
        </van-button>
      </div>
    </div>

    <!-- 错误状态 -->
    <van-empty 
      v-else
      description="商品信息加载失败"
      image="error"
    >
      <van-button 
        type="primary" 
        size="small" 
        @click="loadMaterialDetail"
      >
        重新加载
      </van-button>
    </van-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { materialAdapter } from '@/api/adapters/material'
import type { MaterialVo4Unit } from '@/types/material'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const material = ref<MaterialVo4Unit | null>(null)

// 默认图片
const defaultImage = 'https://via.placeholder.com/120x120?text=商品'

/**
 * 组件挂载时加载商品详情
 */
onMounted(() => {
  loadMaterialDetail()
})

/**
 * 加载商品详情
 */
const loadMaterialDetail = async () => {
  try {
    loading.value = true
    const materialId = Number(route.params.id)
    
    if (!materialId) {
      showToast('商品ID无效')
      handleBack()
      return
    }

    const response = await materialAdapter.getById(materialId)
    
    if (response.success && response.data) {
      material.value = response.data as MaterialVo4Unit
    } else {
      showToast(response.message || '加载商品详情失败')
    }
  } catch (error) {
    console.error('加载商品详情失败:', error)
    showToast('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 返回上一页
 */
const handleBack = () => {
  router.back()
}

/**
 * 编辑商品
 */
const handleEdit = () => {
  if (material.value?.id) {
    router.push(`/data/material/edit/${material.value.id}`)
  }
}
</script>

<style scoped lang="less">
.material-detail {
  background-color: #f7f8fa;
  min-height: 100vh;

  .loading-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
  }

  .material-content {
    padding-bottom: 80px; // 为底部按钮留出空间
  }

  .material-header {
    background: white;
    padding: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .material-image {
      margin-right: 16px;
    }

    .material-basic {
      flex: 1;

      .material-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px 0;
        line-height: 1.4;
      }

      .material-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
      }
    }
  }

  .detail-sections {
    .van-cell-group {
      margin-bottom: 12px;
    }

    .van-cell {
      font-size: 14px;

      :deep(.van-cell__title) {
        color: #666;
        font-weight: 500;
      }

      :deep(.van-cell__value) {
        color: #333;
        font-weight: 400;
      }
    }
  }

  .action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background: white;
    border-top: 1px solid #eee;
    z-index: 100;
  }
}
</style>
