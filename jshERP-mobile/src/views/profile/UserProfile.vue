<!--
  个人中心页面

  包含用户信息卡片、功能菜单和底部提示
-->
<template>
  <div class="erp-page erp-page--no-padding">
    <!-- 用户信息卡片 -->
    <ERPUserCard :user-info="userInfo" :trial-info="trialInfo" />

    <!-- 功能菜单列表 -->
    <div class="profile-menu">
      <ERPMenuList :items="menuItems" @item-click="handleMenuClick" />
    </div>

    <!-- 底部提示已移除 -->
  </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useAuthStore } from '@/stores'
import ERPUserCard from '@/components/erp/ERPUserCard.vue'
import { userAdapter } from '@/api/adapters/user'
import { encryptPassword } from '@/utils/crypto'
import ERPMenuList from '@/components/erp/ERPMenuList.vue'
import type { UserInfo, TrialInfo, MenuItem } from '@/types/erp'

const router = useRouter()
const authStore = useAuthStore()

/**
 * 用户信息 - 从认证store获取真实数据
 */
const userInfo = computed<UserInfo>(() => {
  const user = authStore.userInfo
  console.log('Current user from store:', user) // 调试日志

  return {
    username: user?.realname || user?.username || '未知用户',
    userType: getUserType(),
    avatar: user?.avatar || '' // 可以设置头像URL
  }
})

/**
 * 获取用户类型描述
 */
const getUserType = (): string => {
  // 从localStorage获取完整的用户数据
  try {
    const accessToken = localStorage.getItem('ACCESS_TOKEN')
    if (!accessToken) return '用户'

    // 从token中解析用户信息，或者使用存储的用户数据
    const rawUserInfo = localStorage.getItem('USER_INFO')
    if (rawUserInfo) {
      const rawUser = JSON.parse(rawUserInfo)
      console.log('Raw user data:', rawUser) // 调试日志

      // 根据用户角色返回描述
      if (rawUser.ismanager === 1) return '管理员'
      if (rawUser.isystem === 1) return '系统管理员'
      if (rawUser.position) return rawUser.position
      if (rawUser.department) return rawUser.department

      // 根据用户名判断（admin通常是管理员）
      if (rawUser.loginName === 'admin' || rawUser.username === 'admin') {
        return '系统管理员'
      }
    }
  } catch (error) {
    console.error('Parse user info error:', error)
  }

  return '员工'
}

/**
 * 试用信息 - 移除硬编码，根据实际需要可连接API
 */
const trialInfo = reactive<TrialInfo>({
  isTrialUser: false, // 默认不显示试用信息
  endDate: '',
  currentUsers: 0,
  maxUsers: 0
})

/**
 * 功能菜单配置
 */
const menuItems: MenuItem[] = [
  { id: 'about', icon: 'info-o', label: '关于我们' },
  { id: 'password', icon: 'lock', label: '登录密码' },
  { id: 'cache', icon: 'delete-o', label: '缓存链接' },
  { id: 'profile', icon: 'contact', label: '个人信息' },
  { id: 'logout', icon: 'sign-out', label: '退出登录', danger: true }
]

/**
 * 处理菜单项点击
 */
const handleMenuClick = async (item: MenuItem): Promise<void> => {
  console.log('Menu item clicked:', item)

  switch (item.id) {
    case 'about':
      await handleAbout()
      break

    case 'password':
      await handleChangePassword()
      break

    case 'cache':
      await handleClearCache()
      break

    case 'profile':
      await handleEditProfile()
      break

    case 'logout':
      await handleLogout()
      break

    default:
      showToast(`点击了${item.label}`)
      break
  }
}

/**
 * 关于我们
 */
const handleAbout = async (): Promise<void> => {
  try {
    // 显示系统信息
    await showDialog({
      title: '关于聆花文化ERP系统',
      message: `
        <div style="text-align: center; padding: 20px; line-height: 1.6;">
          <div style="margin-bottom: 24px;">
            <div style="font-size: 18px; font-weight: bold; color: #3B82F6; margin-bottom: 8px;">
              🎨 聆花文化ERP系统
            </div>
            <div style="font-size: 14px; color: #666; margin-bottom: 4px;">版本 V3.5 移动端</div>
            <div style="font-size: 12px; color: #999;">聆花文化团队倾心打造</div>
          </div>

          <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                      border-radius: 12px; padding: 16px; margin-bottom: 20px;
                      border-left: 4px solid #3B82F6;">
            <div style="font-size: 14px; color: #374151; text-align: left;">
              <div style="margin-bottom: 8px;">🏛️ <strong>专业定位</strong></div>
              <div style="margin-bottom: 12px; color: #6B7280; font-size: 13px;">
                专为文化艺术企业打造的综合管理平台
              </div>

              <div style="margin-bottom: 8px;">💎 <strong>核心业务</strong></div>
              <div style="margin-bottom: 12px; color: #6B7280; font-size: 13px;">
                珐琅工艺品、艺术收藏品、文化创意产品
              </div>

              <div style="margin-bottom: 8px;">⚡ <strong>管理功能</strong></div>
              <div style="color: #6B7280; font-size: 13px;">
                进销存管理、生产工艺、客户关系、财务报表
              </div>
            </div>
          </div>

          <div style="font-size: 12px; color: #9CA3AF;">
            让传统工艺与现代管理完美融合 ✨
          </div>
        </div>
      `,
      allowHtml: true,
      confirmButtonText: '我知道了'
    })
  } catch (error) {
    console.log('About dialog cancelled')
  }
}

/**
 * 修改密码
 */
const handleChangePassword = async (): Promise<void> => {
  try {
    // 显示密码修改对话框
    await showDialog({
      title: '🔒 修改密码',
      message: `
        <div style="padding: 12px 0;">
          <div style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #374151;">当前密码</label>
            <input id="oldPassword" type="password" placeholder="请输入当前密码"
                   style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;
                          font-size: 14px; background: #f9fafb;" />
          </div>
          <div style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #374151;">新密码</label>
            <input id="newPassword" type="password" placeholder="请输入新密码"
                   style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;
                          font-size: 14px; background: #f9fafb;" />
          </div>
          <div>
            <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #374151;">确认新密码</label>
            <input id="confirmPassword" type="password" placeholder="请再次输入新密码"
                   style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;
                          font-size: 14px; background: #f9fafb;" />
          </div>
        </div>
      `,
      allowHtml: true,
      confirmButtonText: '确定修改',
      cancelButtonText: '取消'
    })

    // 获取输入的密码
    const oldPasswordEl = document.getElementById('oldPassword') as HTMLInputElement
    const newPasswordEl = document.getElementById('newPassword') as HTMLInputElement
    const confirmPasswordEl = document.getElementById('confirmPassword') as HTMLInputElement

    const oldPassword = oldPasswordEl?.value?.trim()
    const newPassword = newPasswordEl?.value?.trim()
    const confirmPassword = confirmPasswordEl?.value?.trim()

    // 验证输入
    if (!oldPassword || !newPassword || !confirmPassword) {
      showToast('请填写完整的密码信息')
      return
    }

    if (newPassword !== confirmPassword) {
      showToast('两次输入的新密码不一致')
      return
    }

    if (newPassword.length < 6) {
      showToast('新密码长度不能少于6位')
      return
    }

    // 获取当前用户ID
    const currentUser = authStore.userInfo
    if (!currentUser?.id) {
      showToast('获取用户信息失败')
      return
    }

    // 调用API修改密码
    showToast('正在修改密码...')
    await userAdapter.changePassword({
      userId: parseInt(currentUser.id),
      oldpassword: encryptPassword(oldPassword),
      password: encryptPassword(newPassword)
    })

    showToast('密码修改成功')

  } catch (error: any) {
    console.error('Change password error:', error)
    showToast(error.message || '密码修改失败')
  }
}

/**
 * 清理缓存
 */
const handleClearCache = async (): Promise<void> => {
  try {
    await showDialog({
      title: '清理缓存',
      message: '确定要清理应用缓存吗？这将清除本地存储的数据。',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 清理localStorage中的缓存数据
    const keysToKeep = ['auth_token', 'user_info'] // 保留认证信息
    const allKeys = Object.keys(localStorage)

    allKeys.forEach(key => {
      if (!keysToKeep.includes(key)) {
        localStorage.removeItem(key)
      }
    })

    showToast({ type: 'success', message: '缓存清理完成' })
  } catch (error) {
    console.log('Clear cache cancelled')
  }
}

/**
 * 编辑个人信息
 */
const handleEditProfile = async (): Promise<void> => {
  try {
    const currentUser = authStore.userInfo
    if (!currentUser) {
      showToast('获取用户信息失败')
      return
    }

    // 显示个人信息编辑对话框
    await showDialog({
      title: '👤 编辑个人信息',
      message: `
        <div style="padding: 12px 0;">
          <div style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #374151;">真实姓名</label>
            <input id="realname" type="text" placeholder="请输入真实姓名"
                   value="${currentUser.realname || ''}"
                   style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;
                          font-size: 14px; background: #f9fafb;" />
          </div>
          <div style="margin-bottom: 10px;">
            <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #374151;">邮箱地址</label>
            <input id="email" type="email" placeholder="请输入邮箱地址"
                   value="${currentUser.email || ''}"
                   style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;
                          font-size: 14px; background: #f9fafb;" />
          </div>
          <div>
            <label style="display: block; margin-bottom: 4px; font-size: 13px; color: #374151;">手机号码</label>
            <input id="phonenum" type="tel" placeholder="请输入手机号"
                   value="${currentUser.phone || ''}"
                   style="width: 100%; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;
                          font-size: 14px; background: #f9fafb;" />
          </div>
        </div>
      `,
      allowHtml: true,
      confirmButtonText: '保存修改',
      cancelButtonText: '取消'
    })

    // 获取输入的信息
    const realnameEl = document.getElementById('realname') as HTMLInputElement
    const emailEl = document.getElementById('email') as HTMLInputElement
    const phonenumEl = document.getElementById('phonenum') as HTMLInputElement

    const realname = realnameEl?.value?.trim()
    const email = emailEl?.value?.trim()
    const phonenum = phonenumEl?.value?.trim()

    // 验证必填项
    if (!realname) {
      showToast('请输入真实姓名')
      return
    }

    // 验证邮箱格式
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      showToast('邮箱格式不正确')
      return
    }

    // 验证手机号格式
    if (phonenum && !/^1[3-9]\d{9}$/.test(phonenum)) {
      showToast('手机号格式不正确')
      return
    }

    // 调用API更新用户信息
    showToast('正在保存...')
    await userAdapter.updateUserInfo({
      id: parseInt(currentUser.id),
      realname,
      email: email || undefined,
      phonenum: phonenum || undefined
    })

    // 更新本地存储的用户信息
    const updatedUser = {
      ...currentUser,
      realname,
      email,
      phone: phonenum
    }
    localStorage.setItem('USER_INFO', JSON.stringify(updatedUser))

    // 刷新认证store中的用户信息
    authStore.userInfo = updatedUser

    showToast('个人信息修改成功')

  } catch (error: any) {
    console.error('Edit profile error:', error)
    showToast(error.message || '个人信息编辑失败')
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async (): Promise<void> => {
  try {
    await showDialog({
      title: '退出登录',
      message: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    // 执行退出登录
    await authStore.logout()
    showToast({ type: 'success', message: '已退出登录' })

    // 跳转到登录页面
    router.push('/auth/login')
  } catch (error) {
    // 用户取消或退出失败
    console.log('Logout cancelled or failed:', error)
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';
@import '@/styles/common.less';

.erp-page--no-padding {
  padding-bottom: 60px; // Tab栏高度

  .erp-header {
    margin: 0;
  }
}

.profile-menu {
  margin-top: var(--erp-spacing-lg);
}

.profile-footer {
  margin: var(--erp-spacing-xl) var(--erp-spacing-md) var(--erp-spacing-lg);
  text-align: center;

  &__text {
    font-size: var(--erp-font-size-xs);
    color: var(--erp-text-tertiary);
    line-height: var(--erp-line-height-normal);
    margin: 0;
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .profile-footer {
    margin: var(--erp-spacing-lg) var(--erp-spacing-sm) var(--erp-spacing-md);

    &__text {
      font-size: 10px;
    }
  }
}
</style>
