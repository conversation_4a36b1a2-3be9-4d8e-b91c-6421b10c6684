<template>
  <div class="transfer-out-add">
    <!-- 头部导航 -->
    <van-nav-bar
      title="调拨出库"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 库存警告 -->
    <div v-if="stockWarnings.length > 0" class="stock-warnings">
      <van-notice-bar
        v-for="warning in stockWarnings"
        :key="warning.productId"
        type="danger"
        :text="warning.message"
        left-icon="warning-o"
        scrollable
      />
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <van-field
          v-model="transferOutForm.transferNo"
          label="调拨单号"
          placeholder="系统自动生成"
          readonly
          right-icon="refresh"
          @click-right-icon="transferOutForm.transferNo = generateTransferOutNo()"
        />
        
        <van-field
          v-model="transferOutForm.transferDate"
          label="调拨日期"
          placeholder="请选择调拨日期"
          readonly
          is-link
          @click="showDatePicker = true"
        />
        
        <van-field
          v-model="transferOutForm.operator"
          label="操作员"
          placeholder="请输入操作员"
          required
        />
      </van-cell-group>

      <!-- 仓库信息 -->
      <van-cell-group title="仓库信息" inset>
        <van-field
          v-model="transferOutForm.fromWarehouseName"
          label="源仓库"
          placeholder="请选择源仓库"
          readonly
          is-link
          required
          @click="showFromWarehouseSelector = true"
        />
        
        <van-field
          v-model="transferOutForm.toWarehouseName"
          label="目标仓库"
          placeholder="请选择目标仓库"
          readonly
          is-link
          required
          @click="showToWarehouseSelector = true"
        />
        
        <van-field
          :value="getTransferReasonLabel()"
          label="调拨原因"
          placeholder="请选择调拨原因"
          readonly
          is-link
          @click="showTransferReasonSelector = true"
        />
      </van-cell-group>

      <!-- 商品信息 -->
      <van-cell-group title="商品信息" inset>
        <van-cell
          title="添加商品"
          is-link
          @click="showProductSelector = true"
        >
          <template #icon>
            <van-icon name="plus" class="add-icon" />
          </template>
        </van-cell>
        
        <!-- 商品列表 -->
        <div v-if="transferOutForm.products.length > 0" class="product-list">
          <div
            v-for="(product, index) in transferOutForm.products"
            :key="product.id"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-details">
                <span class="product-model">{{ product.model }}</span>
                <span class="product-unit">{{ product.unit }}</span>
              </div>
              
              <!-- 库存状态指示器 -->
              <div class="stock-status">
                <van-tag 
                  v-if="isProductStockSufficient(index)"
                  type="success" 
                  size="small"
                >
                  库存充足
                </van-tag>
                <van-tag 
                  v-else
                  type="danger" 
                  size="small"
                >
                  库存不足
                </van-tag>
              </div>
            </div>
            
            <div class="product-actions">
              <van-stepper
                v-model="product.quantity"
                min="0"
                step="1"
                @change="updateProductQuantity(product.id, $event)"
              />
              <van-button
                type="danger"
                size="mini"
                @click="removeProduct(product.id)"
              >
                删除
              </van-button>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <van-empty
          v-else
          description="暂无商品"
          image="search"
        />
      </van-cell-group>

      <!-- 汇总信息 -->
      <van-cell-group title="汇总信息" inset>
        <van-cell title="总数量" :value="transferOutForm.totalQuantity" />
        <van-cell title="总金额" :value="`¥${totalAmount.toFixed(2)}`" />
      </van-cell-group>

      <!-- 备注信息 -->
      <van-cell-group title="备注信息" inset>
        <van-field
          v-model="transferOutForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入备注信息"
          rows="3"
          autosize
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="primary"
        size="large"
        :disabled="!isFormValid"
        :loading="businessForm.loading"
        @click="submitTransferOut"
      >
        提交调拨出库
      </van-button>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        title="选择调拨日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 源仓库选择器 -->
    <van-popup v-model:show="showFromWarehouseSelector" position="bottom">
      <van-picker
        title="选择源仓库"
        :columns="warehouseOptions"
        @confirm="onFromWarehouseConfirm"
        @cancel="showFromWarehouseSelector = false"
      />
    </van-popup>

    <!-- 目标仓库选择器 -->
    <van-popup v-model:show="showToWarehouseSelector" position="bottom">
      <van-picker
        title="选择目标仓库"
        :columns="warehouseOptions"
        @confirm="onToWarehouseConfirm"
        @cancel="showToWarehouseSelector = false"
      />
    </van-popup>

    <!-- 调拨原因选择器 -->
    <van-popup v-model:show="showTransferReasonSelector" position="bottom">
      <van-picker
        title="选择调拨原因"
        :columns="transferReasonOptions"
        @confirm="onTransferReasonConfirm"
        @cancel="showTransferReasonSelector = false"
      />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup v-model:show="showProductSelector" position="bottom" style="height: 80%">
      <div class="product-selector">
        <van-nav-bar
          title="选择商品"
          left-text="取消"
          @click-left="showProductSelector = false"
        />
        
        <van-search
          v-model="productSearchKeyword"
          placeholder="搜索商品"
          @search="searchProducts"
        />
        
        <div class="product-selector-list">
          <van-cell
            v-for="product in availableProducts"
            :key="product.id"
            :title="product.name"
            :label="`型号: ${product.model} | 库存: ${product.stock}`"
            is-link
            @click="selectProduct(product)"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useTransferOutForm } from '@/composables/useTransferOutForm'
import type { InventoryProduct } from '@/types/inventory'

const router = useRouter()

// 使用调拨出库表单Hook
const {
  transferOutForm,
  businessForm,
  stockWarnings,
  showFromWarehouseSelector,
  showToWarehouseSelector,
  showTransferReasonSelector,
  isFormValid,
  totalAmount,
  transferReasonOptions,
  generateTransferOutNo,
  selectFromWarehouse,
  selectToWarehouse,
  selectTransferReason,
  addProduct,
  removeProduct,
  updateProductQuantity,
  submitTransferOut,
  initForm
} = useTransferOutForm()

// 本地状态
const showDatePicker = ref(false)
const showProductSelector = ref(false)
const currentDate = ref(new Date())
const productSearchKeyword = ref('')

// 模拟数据
const warehouseOptions = ref([
  { text: '主仓库', value: '1' },
  { text: '分仓库A', value: '2' },
  { text: '分仓库B', value: '3' },
  { text: '临时仓库', value: '4' }
])

const availableProducts = ref<InventoryProduct[]>([
  {
    id: '1',
    name: '商品A',
    model: 'A001',
    unit: '件',
    quantity: 10,
    unitPrice: 100,
    stock: 50,
    remark: ''
  },
  {
    id: '2',
    name: '商品B',
    model: 'B001',
    unit: '个',
    quantity: 5,
    unitPrice: 200,
    stock: 30,
    remark: ''
  }
])

// 计算属性
const isProductStockSufficient = (index: number): boolean => {
  const product = transferOutForm.products[index]
  if (!product) return false
  
  const availableProduct = availableProducts.value.find(p => p.id === product.id)
  return availableProduct ? product.quantity <= availableProduct.stock : false
}

// 获取调拨原因标签
const getTransferReasonLabel = (): string => {
  const reason = businessForm.formData.otherField3
  const option = transferReasonOptions.find(opt => opt.value === reason)
  return option ? option.label : ''
}

// 事件处理
const onDateConfirm = (value: Date) => {
  transferOutForm.transferDate = value.toISOString().split('T')[0]
  showDatePicker.value = false
}

const onFromWarehouseConfirm = ({ selectedOptions }: any) => {
  const warehouse = selectedOptions[0]
  selectFromWarehouse({
    id: warehouse.value,
    name: warehouse.text
  })
}

const onToWarehouseConfirm = ({ selectedOptions }: any) => {
  const warehouse = selectedOptions[0]
  selectToWarehouse({
    id: warehouse.value,
    name: warehouse.text
  })
}

const onTransferReasonConfirm = ({ selectedOptions }: any) => {
  const reason = selectedOptions[0]
  selectTransferReason(reason.value)
}

const selectProduct = (product: InventoryProduct) => {
  addProduct({
    ...product,
    quantity: 1
  })
  showProductSelector.value = false
  showToast(`已添加 ${product.name}`)
}

const searchProducts = () => {
  // 实现商品搜索逻辑
  console.log('搜索商品:', productSearchKeyword.value)
}

// 生命周期
onMounted(() => {
  initForm()
})
</script>

<style scoped lang="less">
.transfer-out-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .stock-warnings {
    padding: 16px;
    
    .van-notice-bar {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;
    }

    .add-icon {
      color: #1890ff;
      margin-right: 8px;
    }

    .product-list {
      .product-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #ebedf0;
        background-color: #fff;

        &:last-child {
          border-bottom: none;
        }

        .product-info {
          flex: 1;

          .product-name {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 4px;
          }

          .product-details {
            font-size: 14px;
            color: #969799;
            margin-bottom: 8px;

            .product-model {
              margin-right: 12px;
            }
          }

          .stock-status {
            .van-tag {
              font-size: 12px;
            }
          }
        }

        .product-actions {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      width: 100%;
      background-color: #1890ff;
      border-color: #1890ff;

      &:disabled {
        background-color: #c8c9cc;
        border-color: #c8c9cc;
      }
    }
  }

  .product-selector {
    height: 100%;
    display: flex;
    flex-direction: column;

    .product-selector-list {
      flex: 1;
      overflow-y: auto;
      padding: 0 16px;
    }
  }
}
</style>
