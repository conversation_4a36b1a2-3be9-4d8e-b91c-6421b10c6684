<!--
  其他出库添加页面
  
  基于通用业务表单组件，实现其他出库的添加功能
  包含出库类型选择、操作员选择、仓库选择、库存验证、商品管理等
-->

<template>
  <div class="other-out-add">
    <!-- 页面头部 -->
    <van-nav-bar
      title="新增其他出库"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 库存警告 -->
    <div v-if="stockWarnings.length > 0" class="stock-warnings">
      <van-notice-bar
        v-for="warning in stockWarnings"
        :key="warning.productId"
        type="danger"
        :text="warning.message"
        left-icon="warning-o"
        scrollable
      />
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <!-- 出库编号 -->
        <van-field
          v-model="otherOutForm.outNo"
          label="出库编号"
          placeholder="系统自动生成"
          readonly
          :border="false"
        />
        
        <!-- 出库日期 -->
        <van-field
          v-model="otherOutForm.outDate"
          label="出库日期"
          placeholder="请选择出库日期"
          readonly
          is-link
          required
          :border="false"
          @click="showDatePicker = true"
        />
        
        <!-- 操作员 -->
        <van-field
          v-model="otherOutForm.operator"
          label="操作员"
          placeholder="请选择操作员"
          readonly
          is-link
          required
          :border="false"
          @click="showOperatorPicker = true"
        />
        
        <!-- 出库仓库 -->
        <van-field
          v-model="otherOutForm.warehouseName"
          label="出库仓库"
          placeholder="请选择出库仓库"
          readonly
          is-link
          required
          :border="false"
          @click="showWarehousePicker = true"
        />
      </van-cell-group>

      <!-- 出库信息 -->
      <van-cell-group title="出库信息" inset>
        <!-- 出库类型 -->
        <van-field
          label="出库类型"
          :border="false"
        >
          <template #input>
            <van-radio-group
              v-model="otherOutForm.outType"
              direction="horizontal"
              @change="handleOutTypeSelect"
            >
              <van-radio name="loss">损耗出库</van-radio>
              <van-radio name="transfer">调拨出库</van-radio>
              <van-radio name="sample">样品出库</van-radio>
              <van-radio name="other">其他出库</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        
        <!-- 目标单据 -->
        <van-field
          v-model="otherOutForm.targetDocument"
          label="目标单据"
          placeholder="请选择目标单据（可选）"
          readonly
          is-link
          :border="false"
          @click="showTargetDocumentPicker = true"
        />
      </van-cell-group>

      <!-- 商品信息 -->
      <van-cell-group title="出库商品" inset>
        <!-- 商品列表 -->
        <div v-if="otherOutForm.products.length > 0" class="product-list">
          <div
            v-for="(product, index) in otherOutForm.products"
            :key="index"
            class="product-item"
            :class="{ 'stock-insufficient': !isProductStockSufficient(index) }"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-code" v-if="product.code">编码: {{ product.code }}</div>
              <div class="product-spec" v-if="product.spec">规格: {{ product.spec }}</div>
              
              <!-- 库存信息 -->
              <div class="stock-info">
                <span class="current-stock">当前库存: {{ product.currentStock }}</span>
                <span 
                  class="available-stock"
                  :class="{ 'insufficient': !isProductStockSufficient(index) }"
                >
                  可用: {{ product.availableStock }}
                </span>
                <span 
                  v-if="getProductQuantity(index) > 0"
                  class="remaining-stock"
                  :class="{ 'insufficient': !isProductStockSufficient(index) }"
                >
                  剩余: {{ product.availableStock - getProductQuantity(index) }}
                </span>
              </div>
              
              <!-- 库位信息 -->
              <div class="location-info" v-if="product.locationCode">
                <van-tag type="primary" size="small">{{ product.locationCode }}</van-tag>
              </div>
              
              <!-- 库存状态指示器 -->
              <div class="stock-status">
                <van-tag 
                  v-if="isProductStockSufficient(index)"
                  type="success" 
                  size="small"
                >
                  库存充足
                </van-tag>
                <van-tag 
                  v-else
                  type="danger" 
                  size="small"
                >
                  库存不足
                </van-tag>
              </div>
            </div>
            
            <div class="product-controls">
              <div class="quantity-control">
                <van-stepper
                  :model-value="getProductQuantity(index)"
                  min="1"
                  :max="product.availableStock"
                  @change="handleOutQuantityChange(index, $event)"
                />
              </div>
              
              <div class="price-control">
                <van-field
                  :model-value="getProductPrice(index)"
                  type="number"
                  placeholder="出库单价"
                  @blur="handleOutPriceChange(index, Number($event.target.value))"
                />
              </div>
              
              <div class="amount">
                ¥{{ formatCurrency(getProductPrice(index) * getProductQuantity(index)) }}
              </div>
              
              <!-- 库位按钮 -->
              <van-button
                size="mini"
                type="primary"
                @click="handleLocationEdit(index)"
              >
                库位
              </van-button>
              
              <van-icon
                name="delete-o"
                class="delete-btn"
                @click="removeOutProduct(index)"
              />
            </div>
          </div>
        </div>
        
        <!-- 添加商品按钮 -->
        <van-cell
          title="添加出库商品"
          is-link
          :border="false"
          @click="handleAddProduct"
        >
          <template #icon>
            <van-icon name="plus" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 汇总信息 -->
      <van-cell-group title="汇总信息" inset>
        <!-- 出库总数量 -->
        <van-field
          :model-value="totalOutQuantity.toString()"
          label="出库总数量"
          readonly
          :border="false"
        />
        
        <!-- 出库总金额 -->
        <van-field
          :model-value="formatCurrency(totalOutAmount)"
          label="出库总金额"
          readonly
          class="total-amount"
          :border="false"
        />
      </van-cell-group>

      <!-- 其他信息 -->
      <van-cell-group title="其他信息" inset>
        <!-- 备注 -->
        <van-field
          v-model="otherOutForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入出库备注"
          rows="3"
          autosize
          :border="false"
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="default"
        size="large"
        @click="handleSaveDraft"
        :loading="loading"
      >
        保存草稿
      </van-button>
      
      <van-button
        type="warning"
        size="large"
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!isFormValid"
      >
        确认出库
      </van-button>
    </div>

    <!-- 操作员选择器 -->
    <van-popup
      v-model:show="showOperatorPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <OperatorPicker @select="handleOperatorSelect" />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="dateValue"
        title="选择出库日期"
        @confirm="handleDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 仓库选择器 -->
    <van-popup
      v-model:show="showWarehousePicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <WarehousePicker @select="handleWarehouseSelect" />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup
      v-model:show="showProductPicker"
      position="bottom"
      :style="{ height: '80%' }"
    >
      <InventoryProductPicker @select="handleProductSelect" />
    </van-popup>

    <!-- 目标单据选择器 -->
    <van-popup
      v-model:show="showTargetDocumentPicker"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <TargetDocumentPicker @select="handleTargetDocumentSelect" />
    </van-popup>

    <!-- 库位编辑弹窗 -->
    <van-popup
      v-model:show="showLocationDialog"
      position="center"
      :style="{ width: '90%', borderRadius: '12px' }"
    >
      <div class="location-dialog">
        <div class="dialog-header">
          <h3>编辑库位</h3>
          <van-icon name="cross" @click="showLocationDialog = false" />
        </div>
        
        <div class="dialog-content">
          <div class="product-info">
            <div class="product-name">{{ currentLocationProduct?.name }}</div>
            <div class="product-code">{{ currentLocationProduct?.code }}</div>
          </div>
          
          <van-field
            v-model="currentLocation"
            label="库位编码"
            placeholder="请输入库位编码"
            clearable
          />
        </div>
        
        <div class="dialog-actions">
          <van-button
            type="default"
            @click="showLocationDialog = false"
          >
            取消
          </van-button>
          
          <van-button
            type="primary"
            @click="handleLocationConfirm"
          >
            确认
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { useOtherOutForm } from '@/composables/useOtherOutForm'
import type { InventoryProduct, Operator, OutType } from '@/types/inventory'
import type { Depot } from '@/types/business'

// 组件引入
import OperatorPicker from '@/components/picker/OperatorPicker.vue'
import WarehousePicker from '@/components/picker/WarehousePicker.vue'
import InventoryProductPicker from '@/components/picker/InventoryProductPicker.vue'
import TargetDocumentPicker from '@/components/picker/TargetDocumentPicker.vue'

// 路由
const router = useRouter()

// 表单逻辑
const {
  loading,
  submitting,
  otherOutForm,
  showOperatorPicker,
  showDatePicker,
  showWarehousePicker,
  showProductPicker,
  showTargetDocumentPicker,
  dateValue,
  stockWarnings,
  isFormValid,
  totalOutQuantity,
  totalOutAmount,
  isStockSufficient,
  formatCurrency,
  validateForm,
  validateStockQuantity,
  handleDateConfirm,
  handleOperatorSelect,
  handleWarehouseSelect,
  handleOutTypeSelect,
  handleTargetDocumentSelect,
  addOutProduct,
  removeOutProduct,
  handleOutQuantityChange,
  handleOutPriceChange,
  handleLocationChange,
  resetForm,
  initializeForm,
  formData
} = useOtherOutForm()

// 库位编辑相关状态
const showLocationDialog = ref(false)
const currentLocationIndex = ref(-1)
const currentLocationProduct = ref<InventoryProduct | null>(null)
const currentLocation = ref('')

/**
 * 获取商品数量
 */
const getProductQuantity = (index: number): number => {
  return formData.details[index]?.basicNumber || 1
}

/**
 * 获取商品单价
 */
const getProductPrice = (index: number): number => {
  return formData.details[index]?.unitPrice || 0
}

/**
 * 检查商品库存是否充足
 */
const isProductStockSufficient = (index: number): boolean => {
  const product = otherOutForm.value.products[index]
  const quantity = getProductQuantity(index)
  return product && quantity <= product.availableStock
}

/**
 * 返回处理
 */
const handleBack = async () => {
  // 检查是否有未保存的更改
  if (otherOutForm.value.products.length > 0) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的更改，确定要离开吗？'
      })
    } catch {
      return
    }
  }

  router.back()
}

/**
 * 添加商品处理
 */
const handleAddProduct = () => {
  if (!otherOutForm.value.warehouseName) {
    showToast({ type: 'fail', message: '请先选择出库仓库' })
    return
  }

  showProductPicker.value = true
}

/**
 * 商品选择处理
 */
const handleProductSelect = (product: InventoryProduct) => {
  // 检查库存
  if (product.availableStock <= 0) {
    showToast({ type: 'fail', message: `商品"${product.name}"库存不足，无法出库` })
    return
  }

  addOutProduct(product)
  showProductPicker.value = false
  showToast({ type: 'success', message: '出库商品已添加' })
}

/**
 * 库位编辑处理
 */
const handleLocationEdit = (index: number) => {
  const product = otherOutForm.value.products[index]
  currentLocationIndex.value = index
  currentLocationProduct.value = product
  currentLocation.value = product.locationCode || ''
  showLocationDialog.value = true
}

/**
 * 库位确认
 */
const handleLocationConfirm = () => {
  if (currentLocationIndex.value >= 0) {
    handleLocationChange(currentLocationIndex.value, currentLocation.value)
  }

  showLocationDialog.value = false
  showToast({ type: 'success', message: '库位已保存' })
}

/**
 * 保存草稿
 */
const handleSaveDraft = async () => {
  try {
    loading.value = true

    // 这里调用API保存草稿
    // await saveOtherOutDraft(otherOutForm.value)

    showToast({ type: 'success', message: '草稿已保存' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 确认出库
 */
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  // 再次检查库存
  if (!isStockSufficient) {
    showToast({ type: 'fail', message: '存在库存不足的商品，请检查后重试' })
    return
  }

  try {
    submitting.value = true

    // 这里调用API确认出库
    // await submitOtherOut(otherOutForm.value)

    showToast({ type: 'success', message: '出库成功' })

    // 返回列表页
    router.replace('/inventory/other-out/list')
  } catch (error) {
    showToast({ type: 'fail', message: '出库失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  initializeForm()
})
</script>

<style lang="less" scoped>
.other-out-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .stock-warnings {
    padding: 8px 16px;

    .van-notice-bar {
      margin-bottom: 8px;
      border-radius: 6px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .product-list {
    .product-item {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid #ebedf0;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &.stock-insufficient {
        background-color: #fef2f2;
        border-left: 4px solid #ef4444;
      }

      .product-info {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-code,
        .product-spec {
          font-size: 12px;
          color: #969799;
          margin-bottom: 4px;
        }

        .stock-info {
          display: flex;
          gap: 12px;
          margin-bottom: 6px;
          flex-wrap: wrap;

          .current-stock,
          .available-stock,
          .remaining-stock {
            font-size: 11px;
            color: #646566;
            background-color: #f2f3f5;
            padding: 2px 6px;
            border-radius: 4px;

            &.insufficient {
              background-color: #fee2e2;
              color: #dc2626;
            }
          }
        }

        .location-info {
          margin-bottom: 6px;

          .van-tag {
            font-size: 10px;
          }
        }

        .stock-status {
          .van-tag {
            font-size: 10px;
          }
        }
      }

      .product-controls {
        display: flex;
        align-items: center;
        gap: 6px;
        flex-wrap: wrap;

        .quantity-control {
          .van-stepper {
            --van-stepper-button-width: 22px;
            --van-stepper-button-height: 22px;
            --van-stepper-input-width: 32px;
            --van-stepper-input-height: 22px;
          }
        }

        .price-control {
          width: 65px;

          .van-field {
            padding: 0;

            :deep(.van-field__control) {
              text-align: center;
              font-size: 10px;
            }
          }
        }

        .amount {
          font-size: 11px;
          font-weight: 500;
          color: #f56500;
          min-width: 45px;
          text-align: right;
        }

        .van-button {
          --van-button-mini-height: 22px;
          --van-button-mini-padding: 0 6px;
          --van-button-mini-font-size: 9px;
        }

        .delete-btn {
          color: #ee0a24;
          font-size: 14px;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }

  .total-amount {
    :deep(.van-field__label) {
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-field__control) {
      font-weight: 600;
      color: #f56500;
      font-size: 16px;
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    gap: 12px;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-weight: 500;

      &--default {
        background-color: #f7f8fa;
        border-color: #f7f8fa;
        color: #646566;
      }

      &--warning {
        background: linear-gradient(135deg, #f56500 0%, #e55100 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(245, 101, 0, 0.4);

        &:active {
          transform: translateY(1px);
          box-shadow: 0 2px 8px rgba(245, 101, 0, 0.4);
        }

        &:disabled {
          background: #c8c9cc;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }

  .location-dialog {
    padding: 20px;

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #323233;
      }

      .van-icon {
        font-size: 18px;
        color: #969799;
        cursor: pointer;
      }
    }

    .dialog-content {
      margin-bottom: 20px;

      .product-info {
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 8px;
        margin-bottom: 16px;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-code {
          font-size: 12px;
          color: #969799;
        }
      }

      .van-field {
        margin-bottom: 12px;
      }
    }

    .dialog-actions {
      display: flex;
      gap: 12px;

      .van-button {
        flex: 1;
        height: 40px;
        border-radius: 20px;
      }
    }
  }

  // 弹窗样式优化
  :deep(.van-popup) {
    border-radius: 16px 16px 0 0;
  }

  :deep(.van-picker) {
    background-color: #fff;
  }

  :deep(.van-picker__toolbar) {
    border-bottom: 1px solid #ebedf0;
  }

  :deep(.van-radio-group) {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .other-out-add {
    .product-list {
      .product-item {
        .product-controls {
          gap: 4px;

          .price-control {
            width: 55px;
          }

          .amount {
            min-width: 40px;
            font-size: 10px;
          }

          .van-button {
            --van-button-mini-padding: 0 4px;
          }
        }
      }
    }
  }
}
</style>
