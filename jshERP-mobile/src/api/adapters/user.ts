import { httpClient } from '@/utils/request'
import { BaseAdapter } from './base'

/**
 * 用户信息接口
 */
export interface UserProfile {
  id: number
  username: string
  loginName: string
  realname: string
  email?: string
  phonenum?: string
  avatar?: string
  position?: string
  department?: string
  ismanager?: number
  isystem?: number
  tenantId?: number
}

/**
 * 密码修改请求
 */
export interface PasswordChangeRequest {
  userId: number
  oldpassword: string
  password: string
}

/**
 * 用户信息修改请求
 */
export interface UserUpdateRequest {
  id: number
  realname?: string
  email?: string
  phonenum?: string
  position?: string
  department?: string
}

/**
 * 用户管理API适配器
 */
class UserAdapter extends BaseAdapter {
  protected endpoint = '/user'

  /**
   * 获取当前用户会话信息
   */
  async getCurrentUser(): Promise<UserProfile> {
    try {
      const response = await httpClient.get(`${this.endpoint}/getUserSession`)
      
      if (response.code === 200 && response.data?.user) {
        return response.data.user
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error: any) {
      console.error('Get current user failed:', error)
      throw new Error(error.message || '获取用户信息失败')
    }
  }

  /**
   * 根据ID获取用户信息
   */
  async getUserById(id: number): Promise<UserProfile> {
    try {
      const response = await httpClient.get(`${this.endpoint}/info`, {
        params: { id }
      })
      
      if (response.code === 200 && response.data?.info) {
        return response.data.info
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error: any) {
      console.error('Get user by id failed:', error)
      throw new Error(error.message || '获取用户信息失败')
    }
  }

  /**
   * 修改密码
   */
  async changePassword(request: PasswordChangeRequest): Promise<boolean> {
    try {
      const response = await httpClient.put(`${this.endpoint}/updatePwd`, request)
      
      if (response.code === 200) {
        return true
      } else {
        // 处理特定的错误情况
        if (response.data?.status === 2) {
          throw new Error('原始密码输入错误')
        } else if (response.data?.status === 3) {
          throw new Error('新密码格式不正确')
        } else {
          throw new Error(response.data?.message || '密码修改失败')
        }
      }
    } catch (error: any) {
      console.error('Change password failed:', error)
      throw new Error(error.message || '密码修改失败')
    }
  }

  /**
   * 修改用户信息
   */
  async updateUserInfo(request: UserUpdateRequest): Promise<boolean> {
    try {
      const response = await httpClient.put(`${this.endpoint}/updateUser`, request)
      
      if (response.code === 200) {
        return true
      } else {
        throw new Error(response.message || '用户信息修改失败')
      }
    } catch (error: any) {
      console.error('Update user info failed:', error)
      throw new Error(error.message || '用户信息修改失败')
    }
  }
}

export const userAdapter = new UserAdapter()
