<!--
  基础按钮组件

  基于Vant Button组件封装的基础按钮，提供统一的按钮样式和交互

  @example
  <BaseButton type="primary" @click="handleClick">
    点击按钮
  </BaseButton>
-->
<template>
  <van-button
    :type="buttonType"
    :size="size"
    :disabled="disabled || loading"
    :loading="loading"
    :block="block"
    :round="round"
    :square="square"
    @click="handleClick"
  >
    <slot />
  </van-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
/**
 * 按钮组件属性接口
 */
interface Props {
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'default'
  /** 按钮尺寸 */
  size?: 'large' | 'normal' | 'small' | 'mini'
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示加载状态 */
  loading?: boolean
  /** 是否为块级元素 */
  block?: boolean
  /** 是否为圆角按钮 */
  round?: boolean
  /** 是否为方形按钮 */
  square?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'normal',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  square: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonType = computed(() => {
  const typeMap: Record<string, string> = {
    primary: 'primary',
    success: 'success',
    warning: 'warning',
    danger: 'danger',
    default: 'default'
  }
  return typeMap[props.type] as any
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>