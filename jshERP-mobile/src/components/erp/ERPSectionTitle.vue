<!--
  ERP分组标题组件
  
  用于显示功能模块的分组标题，带有蓝色圆点装饰
-->
<template>
  <div class="erp-section-title" :class="{ 'erp-section-title--clickable': clickable }" @click="handleClick">
    <div class="erp-section-title__content">
      <div class="erp-section-title__dot"></div>
      <div class="erp-section-title__text">{{ title }}</div>
      <div v-if="subtitle" class="erp-section-title__subtitle">{{ subtitle }}</div>
    </div>
    
    <div v-if="extra || $slots.extra" class="erp-section-title__extra">
      <slot name="extra">{{ extra }}</slot>
    </div>
    
    <div v-if="expandable" class="erp-section-title__arrow" :class="{ 'erp-section-title__arrow--expanded': expanded }">
      <van-icon name="arrow-down" />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 组件属性接口
 */
interface Props {
  /** 主标题 */
  title: string
  /** 副标题 */
  subtitle?: string
  /** 额外内容 */
  extra?: string
  /** 是否可点击 */
  clickable?: boolean
  /** 是否可展开 */
  expandable?: boolean
  /** 是否已展开 */
  expanded?: boolean
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 点击事件 */
  click: []
  /** 展开/收起事件 */
  toggle: [expanded: boolean]
}

const props = withDefaults(defineProps<Props>(), {
  clickable: false,
  expandable: false,
  expanded: false
})

const emit = defineEmits<Emits>()

/**
 * 处理点击事件
 */
const handleClick = (): void => {
  if (props.clickable) {
    emit('click')
  }
  
  if (props.expandable) {
    emit('toggle', !props.expanded)
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--erp-spacing-md);
  transition: all var(--erp-duration-fast) var(--erp-ease-out);
  
  &--clickable {
    cursor: pointer;
    
    &:hover {
      background: var(--erp-bg-tertiary);
    }
    
    &:active {
      background: var(--erp-border-light);
    }
  }
  
  &__content {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  &__dot {
    width: 4px;
    height: 16px;
    background: var(--erp-primary);
    border-radius: var(--erp-radius-xs);
    margin-right: var(--erp-spacing-sm);
    flex-shrink: 0;
  }
  
  &__text {
    font-size: var(--erp-font-size-lg);
    font-weight: var(--erp-font-weight-semibold);
    color: var(--erp-text-primary);
    line-height: var(--erp-line-height-normal);
    margin-right: var(--erp-spacing-sm);
  }
  
  &__subtitle {
    font-size: var(--erp-font-size-sm);
    color: var(--erp-text-secondary);
    line-height: var(--erp-line-height-normal);
  }
  
  &__extra {
    font-size: var(--erp-font-size-sm);
    color: var(--erp-text-tertiary);
    margin-right: var(--erp-spacing-sm);
  }
  
  &__arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: var(--erp-text-tertiary);
    transition: all var(--erp-duration-normal) var(--erp-ease-out);
    
    &--expanded {
      transform: rotate(180deg);
    }
    
    .van-icon {
      font-size: 16px;
    }
  }
}
</style>
