<!--
  ERP用户信息卡片组件
  
  用于个人中心页面显示用户信息和试用信息
-->
<template>
  <div class="erp-user-card">
    <div class="erp-user-card__header">
      <div class="erp-user-card__avatar">
        <van-image
          v-if="userInfo.avatar"
          :src="userInfo.avatar"
          round
          width="60"
          height="60"
          fit="cover"
        />
        <div v-else class="erp-user-card__avatar-default">
          <van-icon name="contact" size="30" />
        </div>
      </div>
      
      <div class="erp-user-card__info">
        <h3 class="erp-user-card__name">{{ userInfo.username }}</h3>
        <p class="erp-user-card__type">{{ userInfo.userType }}</p>
      </div>
    </div>
    
    <!-- 试用信息已移除 -->
  </div>
</template>

<script setup lang="ts">
/**
 * 用户信息接口
 */
interface UserInfo {
  username: string
  userType: string
  avatar?: string
}

/**
 * 试用信息接口
 */
interface TrialInfo {
  isTrialUser: boolean
  endDate: string
  currentUsers: number
  maxUsers: number
}

/**
 * 组件属性接口
 */
interface Props {
  /** 用户信息 */
  userInfo: UserInfo
  /** 试用信息 */
  trialInfo: TrialInfo
}

const props = defineProps<Props>()
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-user-card {
  background: var(--erp-gradient);
  color: var(--erp-text-white);
  padding: var(--erp-spacing-xl) var(--erp-spacing-md);
  margin: 0;
  
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: var(--erp-spacing-md);
  }
  
  &__avatar {
    margin-right: var(--erp-spacing-md);
    
    &-default {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--erp-text-white);
    }
  }
  
  &__info {
    flex: 1;
  }
  
  &__name {
    font-size: var(--erp-font-size-xl);
    font-weight: var(--erp-font-weight-semibold);
    margin: 0 0 var(--erp-spacing-xs) 0;
    color: var(--erp-text-white);
  }
  
  &__type {
    font-size: var(--erp-font-size-md);
    margin: 0;
    opacity: 0.9;
    color: var(--erp-text-white);
  }
  
  &__trial {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--erp-radius-md);
    padding: var(--erp-spacing-sm) var(--erp-spacing-md);
    
    &-text {
      font-size: var(--erp-font-size-sm);
      color: var(--erp-success);
      text-align: center;
      line-height: var(--erp-line-height-normal);
    }
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .erp-user-card {
    padding: var(--erp-spacing-lg) var(--erp-spacing-md);
    
    &__avatar {
      &-default {
        width: 50px;
        height: 50px;
      }
    }
    
    &__name {
      font-size: var(--erp-font-size-lg);
    }
    
    &__type {
      font-size: var(--erp-font-size-sm);
    }
  }
}
</style>
