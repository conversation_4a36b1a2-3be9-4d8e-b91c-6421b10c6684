<!--
  ERP数据卡片组件
  
  用于首页显示各种业务数据，如销售额、订单数量等
-->
<template>
  <div 
    class="erp-data-card" 
    :class="{ 
      'erp-data-card--clickable': clickable,
      'erp-data-card--refresh': showRefresh 
    }"
    @click="handleClick"
  >
    <div v-if="showRefresh" class="erp-data-card__refresh" @click.stop="handleRefresh">
      <van-icon name="replay" :class="{ 'erp-data-card__refresh--loading': refreshing }" />
    </div>
    
    <div class="erp-data-card__value">{{ formattedValue }}</div>
    <div class="erp-data-card__label">{{ label }}</div>
    
    <div v-if="trend" class="erp-data-card__trend" :class="`erp-data-card__trend--${trend.type}`">
      <van-icon :name="trendIcon" />
      <span>{{ trend.value }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

/**
 * 趋势数据接口
 */
interface TrendData {
  type: 'up' | 'down' | 'flat'
  value: string
}

/**
 * 组件属性接口
 */
interface Props {
  /** 数据值 */
  value: number | string
  /** 标签文字 */
  label: string
  /** 数据类型 */
  type?: 'number' | 'currency' | 'percentage'
  /** 是否显示刷新按钮 */
  showRefresh?: boolean
  /** 是否可点击 */
  clickable?: boolean
  /** 是否正在刷新 */
  refreshing?: boolean
  /** 趋势数据 */
  trend?: TrendData
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 点击事件 */
  click: []
  /** 刷新事件 */
  refresh: []
}

const props = withDefaults(defineProps<Props>(), {
  type: 'number',
  showRefresh: false,
  clickable: false,
  refreshing: false
})

const emit = defineEmits<Emits>()

/**
 * 格式化显示值
 */
const formattedValue = computed(() => {
  const { value, type } = props
  
  switch (type) {
    case 'currency':
      return `¥${value}`
    case 'percentage':
      return `${value}%`
    default:
      return value
  }
})

/**
 * 趋势图标
 */
const trendIcon = computed(() => {
  if (!props.trend) return ''
  
  switch (props.trend.type) {
    case 'up':
      return 'arrow-up'
    case 'down':
      return 'arrow-down'
    default:
      return 'minus'
  }
})

/**
 * 处理点击事件
 */
const handleClick = (): void => {
  if (props.clickable) {
    emit('click')
  }
}

/**
 * 处理刷新事件
 */
const handleRefresh = (): void => {
  if (!props.refreshing) {
    emit('refresh')
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-data-card {
  position: relative;
  background: var(--erp-bg-card);
  border-radius: var(--erp-radius-lg);
  padding: var(--erp-spacing-md);
  text-align: center;
  transition: all var(--erp-duration-fast) var(--erp-ease-out);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  &--clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--erp-shadow-medium);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &--refresh {
    .erp-data-card__refresh {
      position: absolute;
      top: var(--erp-spacing-sm);
      right: var(--erp-spacing-sm);
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: var(--erp-text-tertiary);
      transition: all var(--erp-duration-fast) var(--erp-ease-out);
      
      &:hover {
        color: var(--erp-primary);
        transform: scale(1.1);
      }
      
      &--loading {
        animation: spin 1s linear infinite;
      }
    }
  }
  
  &__value {
    font-size: var(--erp-font-size-2xl);
    font-weight: var(--erp-font-weight-bold);
    color: var(--erp-text-primary);
    line-height: var(--erp-line-height-tight);
    margin-bottom: var(--erp-spacing-xs);
  }
  
  &__label {
    font-size: var(--erp-font-size-sm);
    color: var(--erp-text-secondary);
    line-height: var(--erp-line-height-normal);
  }
  
  &__trend {
    position: absolute;
    bottom: var(--erp-spacing-sm);
    right: var(--erp-spacing-sm);
    display: flex;
    align-items: center;
    font-size: var(--erp-font-size-xs);
    
    .van-icon {
      margin-right: 2px;
      font-size: 12px;
    }
    
    &--up {
      color: var(--erp-success);
    }
    
    &--down {
      color: var(--erp-error);
    }
    
    &--flat {
      color: var(--erp-text-tertiary);
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
