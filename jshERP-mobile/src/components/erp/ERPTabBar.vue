<!--
  ERP底部Tab导航组件
  
  提供5个主要功能模块的导航：首页、资料、开单、报表、我的
-->
<template>
  <van-tabbar 
    v-model="activeTab" 
    class="erp-tabbar"
    fixed
    placeholder
    @change="handleTabChange"
  >
    <van-tabbar-item 
      v-for="tab in tabs" 
      :key="tab.name"
      :name="tab.name"
      :icon="tab.icon"
      :badge="getBadge(tab.name)"
    >
      {{ tab.label }}
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

/**
 * Tab项配置接口
 */
interface TabItem {
  name: string
  label: string
  icon: string
  path: string
}

/**
 * Tab配置数据
 */
const tabs: TabItem[] = [
  { name: 'home', label: '首页', icon: 'wap-home-o', path: '/home' },
  { name: 'data', label: '资料', icon: 'apps-o', path: '/data' },
  { name: 'orders', label: '开单', icon: 'add-o', path: '/orders' },
  { name: 'reports', label: '报表', icon: 'chart-trending-o', path: '/reports' },
  { name: 'profile', label: '我的', icon: 'manager-o', path: '/profile' }
]

const router = useRouter()
const route = useRoute()

/**
 * 当前激活的Tab
 */
const activeTab = computed(() => {
  const currentPath = route.path
  const tab = tabs.find(t => t.path === currentPath)
  return tab?.name || 'home'
})

/**
 * 处理Tab切换
 */
const handleTabChange = (name: string): void => {
  const tab = tabs.find(t => t.name === name)
  if (tab && tab.path !== route.path) {
    router.push(tab.path)
  }
}

/**
 * 获取Tab角标
 */
const getBadge = (tabName: string): number | undefined => {
  // TODO: 根据业务需求返回角标数量
  // 例如：消息数量、待处理任务数量等
  switch (tabName) {
    case 'home':
      // 可以显示待处理任务数量
      return undefined
    case 'profile':
      // 可以显示未读消息数量
      return undefined
    default:
      return undefined
  }
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-tabbar {
  background: var(--erp-bg-card);
  border-top: 1px solid var(--erp-border-light);
  
  :deep(.van-tabbar-item) {
    color: var(--erp-text-tertiary);
    
    &--active {
      color: var(--erp-primary);
    }
    
    .van-tabbar-item__text {
      font-size: var(--erp-font-size-xs);
      margin-top: var(--erp-spacing-xs);
    }
    
    .van-tabbar-item__icon {
      font-size: 20px;
      margin-bottom: 2px;
    }
    
    .van-badge {
      .van-badge__wrapper {
        .van-badge--fixed {
          top: -2px;
          right: -8px;
        }
      }
    }
  }
}
</style>
