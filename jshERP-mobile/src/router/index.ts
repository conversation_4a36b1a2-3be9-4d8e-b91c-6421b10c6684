import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  // 登录页面
  {
    path: '/auth',
    component: () => import('@/layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: {
          requiresAuth: false,
          title: '用户登录'
        }
      }
    ]
  },

  // 主应用（包含Tab导航）
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/home'
      },
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          requiresAuth: true,
          title: '首页',
          tab: 'home'
        }
      },
      {
        path: 'data',
        name: 'Data',
        component: () => import('@/views/data/DataManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '资料',
          tab: 'data'
        }
      },
      {
        path: 'orders',
        name: 'Orders',
        component: () => import('@/views/orders/OrderManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '开单',
          tab: 'orders'
        }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/views/reports/ReportsManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '报表',
          tab: 'reports'
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/UserProfile.vue'),
        meta: {
          requiresAuth: true,
          title: '我的',
          tab: 'profile'
        }
      }
    ]
  },

  // 销售订单相关页面（独立路由）
  {
    path: '/orders/sales-order',
    name: 'SalesOrderList',
    component: () => import('@/views/orders/SalesOrderList.vue'),
    meta: { requiresAuth: true, title: '销售订单' }
  },
  {
    path: '/orders/sales-order/add',
    name: 'SalesOrderAdd',
    component: () => import('@/views/orders/SalesOrderAdd.vue'),
    meta: { requiresAuth: true, title: '新增订单' }
  },
  {
    path: '/orders/sales-order/edit/:id',
    name: 'SalesOrderEdit',
    component: () => import('@/views/orders/SalesOrderEdit.vue'),
    meta: { requiresAuth: true, title: '编辑订单' }
  },
  {
    path: '/orders/sales-order/:id',
    name: 'SalesOrderDetail',
    component: () => import('@/views/orders/SalesOrderDetail.vue'),
    meta: { requiresAuth: true, title: '订单详情' }
  },

  // 采购管理相关页面（独立路由）
  {
    path: '/purchase/order/add',
    name: 'PurchaseOrderAdd',
    component: () => import('@/views/purchase/PurchaseOrderAdd.vue'),
    meta: { requiresAuth: true, title: '新增采购订单' }
  },
  {
    path: '/purchase/in/add',
    name: 'PurchaseInAdd',
    component: () => import('@/views/purchase/PurchaseInAdd.vue'),
    meta: { requiresAuth: true, title: '采购入库' }
  },
  {
    path: '/purchase/return/add',
    name: 'PurchaseReturnAdd',
    component: () => import('@/views/purchase/PurchaseReturnAdd.vue'),
    meta: { requiresAuth: true, title: '采购退货' }
  },

  // 库存管理相关页面（独立路由）
  {
    path: '/inventory/other-in/add',
    name: 'OtherInAdd',
    component: () => import('@/views/inventory/OtherInAdd.vue'),
    meta: { requiresAuth: true, title: '其他入库' }
  },
  {
    path: '/inventory/other-out/add',
    name: 'OtherOutAdd',
    component: () => import('@/views/inventory/OtherOutAdd.vue'),
    meta: { requiresAuth: true, title: '其他出库' }
  },
  {
    path: '/inventory/transfer-out/add',
    name: 'TransferOutAdd',
    component: () => import('@/views/inventory/TransferOutAdd.vue'),
    meta: { requiresAuth: true, title: '调拨出库' }
  },
  {
    path: '/inventory/assembly/add',
    name: 'AssemblyAdd',
    component: () => import('@/views/inventory/AssemblyAdd.vue'),
    meta: { requiresAuth: true, title: '组装单' }
  },

  // 商品管理相关页面（独立路由）
  {
    path: '/data/material',
    name: 'MaterialList',
    component: () => import('@/views/data/MaterialList.vue'),
    meta: { requiresAuth: true, title: '商品管理' }
  },
  {
    path: '/data/material/:id',
    name: 'MaterialDetail',
    component: () => import('@/views/data/MaterialDetail.vue'),
    meta: { requiresAuth: true, title: '商品详情' }
  },
  {
    path: '/data/material/add',
    name: 'MaterialAdd',
    component: () => import('@/views/data/MaterialAdd.vue'),
    meta: { requiresAuth: true, title: '新增商品' }
  },
  {
    path: '/data/material/edit/:id',
    name: 'MaterialEdit',
    component: () => import('@/views/data/MaterialEdit.vue'),
    meta: { requiresAuth: true, title: '编辑商品' }
  },

  // 404重定向
  {
    path: '/:pathMatch(.*)*',
    redirect: '/home'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router