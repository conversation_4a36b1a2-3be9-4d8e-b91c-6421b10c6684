/**
 * 移动端财务管理路由
 * 暂时注释掉所有未创建的页面
 */

export default [
  // 暂时注释掉所有财务管理页面，等后续开发
]
  {
    path: 'finance',
    name: 'MobileFinanceList',
    component: () => import('@/mobile/views/finance/FinanceList.vue'),
    meta: {
      title: '财务管理',
      icon: 'balance-list-o',
      tab: 'finance',
      showHeader: true,
      showTabBar: true,
      keepAlive: true,
      requiresAuth: true,
      permission: 'finance:view'
    }
  },
  {
    path: 'finance/accounts',
    name: 'MobileAccountList',
    component: () => import('@/mobile/views/finance/AccountList.vue'),
    meta: {
      title: '账户管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'finance:account'
    }
  },
  {
    path: 'finance/accounts/:id',
    name: 'MobileAccountDetail',
    component: () => import('@/mobile/views/finance/AccountDetail.vue'),
    meta: {
      title: '账户详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'finance:account'
    }
  },
  {
    path: 'finance/income',
    name: 'MobileIncomeList',
    component: () => import('@/mobile/views/finance/IncomeList.vue'),
    meta: {
      title: '收入管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'finance:income'
    }
  },
  {
    path: 'finance/expense',
    name: 'MobileExpenseList',
    component: () => import('@/mobile/views/finance/ExpenseList.vue'),
    meta: {
      title: '支出管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'finance:expense'
    }
  },
  {
    path: 'finance/reports',
    name: 'MobileFinanceReports',
    component: () => import('@/mobile/views/finance/FinanceReports.vue'),
    meta: {
      title: '财务报表',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'finance:report'
    }
  },
  {
    path: 'finance/reports/:type',
    name: 'MobileFinanceReportDetail',
    component: () => import('@/mobile/views/finance/FinanceReportDetail.vue'),
    meta: {
      title: '报表详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'finance:report'
    }
  }
]
