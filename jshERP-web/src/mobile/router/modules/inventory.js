/**
 * 移动端库存管理路由
 * 暂时注释掉所有未创建的页面
 */

export default [
  // 暂时注释掉所有库存管理页面，等后续开发
]
  {
    path: 'inventory/stock/:id',
    name: 'MobileStockDetail',
    component: () => import('@/mobile/views/inventory/StockDetail.vue'),
    meta: {
      title: '库存详情',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'inventory:view'
    }
  },
  {
    path: 'inventory/check',
    name: 'MobileStockCheck',
    component: () => import('@/mobile/views/inventory/StockCheck.vue'),
    meta: {
      title: '库存盘点',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'inventory:check'
    }
  },
  {
    path: 'inventory/check/scan',
    name: 'MobileStockCheckScan',
    component: () => import('@/mobile/views/inventory/StockCheckScan.vue'),
    meta: {
      title: '扫码盘点',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'inventory:check'
    }
  },
  {
    path: 'inventory/transfer',
    name: 'MobileStockTransfer',
    component: () => import('@/mobile/views/inventory/StockTransfer.vue'),
    meta: {
      title: '库存调拨',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'inventory:transfer'
    }
  },
  {
    path: 'inventory/transfer/create',
    name: 'MobileStockTransferCreate',
    component: () => import('@/mobile/views/inventory/StockTransferCreate.vue'),
    meta: {
      title: '新增调拨',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true,
      permission: 'inventory:transfer'
    }
  },
  {
    path: 'inventory/in',
    name: 'MobileStockIn',
    component: () => import('@/mobile/views/inventory/StockIn.vue'),
    meta: {
      title: '入库管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'inventory:in'
    }
  },
  {
    path: 'inventory/out',
    name: 'MobileStockOut',
    component: () => import('@/mobile/views/inventory/StockOut.vue'),
    meta: {
      title: '出库管理',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: true,
      permission: 'inventory:out'
    }
  }
]
