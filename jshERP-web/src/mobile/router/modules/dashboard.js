/**
 * 移动端仪表盘路由
 */

export default [
  {
    path: 'dashboard',
    name: 'MobileDashboard',
    component: () => import('@/mobile/views/dashboard/Dashboard.vue'),
    meta: {
      title: '首页',
      icon: 'home-o',
      tab: 'dashboard',
      showHeader: true,
      showTabBar: true,
      keepAlive: true,
      requiresAuth: true
    }
  },
  // 暂时注释掉未创建的页面
  // {
  //   path: 'statistics',
  //   name: 'MobileStatistics',
  //   component: () => import('@/mobile/views/dashboard/Statistics.vue'),
  //   meta: {
  //     title: '数据统计',
  //     showHeader: true,
  //     showBack: true,
  //     showTabBar: false,
  //     keepAlive: true,
  //     requiresAuth: true
  //   }
  // },
  // {
  //   path: 'notifications',
  //   name: 'MobileNotifications',
  //   component: () => import('@/mobile/views/dashboard/Notifications.vue'),
  //   meta: {
  //     title: '消息通知',
  //     showHeader: true,
  //     showBack: true,
  //     showTabBar: false,
  //     keepAlive: false,
  //     requiresAuth: true
  //   }
  // }
]
