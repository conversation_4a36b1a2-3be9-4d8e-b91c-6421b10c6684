/**
 * 移动端商品管理路由
 * 暂时注释掉所有未创建的页面
 */

export default [
  // 暂时注释掉所有商品管理页面，等后续开发
  // {
  //   path: 'products',
  //   name: 'MobileProductList',
  //   component: () => import('@/mobile/views/product/ProductList.vue'),
  //   meta: {
  //     title: '商品管理',
  //     icon: 'goods-collect-o',
  //     tab: 'products',
  //     showHeader: true,
  //     showTabBar: true,
  //     keepAlive: true,
  //     requiresAuth: true,
  //     permission: 'product:view'
  //   }
  // },

]
