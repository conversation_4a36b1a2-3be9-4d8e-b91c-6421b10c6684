/**
 * 移动端个人中心路由
 * 暂时注释掉所有未创建的页面
 */

export default [
  // 暂时注释掉所有个人中心页面，等后续开发
]
  {
    path: 'profile',
    name: 'MobileProfile',
    component: () => import('@/mobile/views/profile/Profile.vue'),
    meta: {
      title: '个人中心',
      icon: 'user-o',
      tab: 'profile',
      showHeader: true,
      showTabBar: true,
      keepAlive: true,
      requiresAuth: true
    }
  },
  {
    path: 'profile/settings',
    name: 'MobileSettings',
    component: () => import('@/mobile/views/profile/Settings.vue'),
    meta: {
      title: '设置',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true
    }
  },
  {
    path: 'profile/edit',
    name: 'MobileProfileEdit',
    component: () => import('@/mobile/views/profile/ProfileEdit.vue'),
    meta: {
      title: '编辑资料',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true
    }
  },
  {
    path: 'profile/password',
    name: 'MobileChangePassword',
    component: () => import('@/mobile/views/profile/ChangePassword.vue'),
    meta: {
      title: '修改密码',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true
    }
  },
  {
    path: 'profile/about',
    name: 'MobileAbout',
    component: () => import('@/mobile/views/profile/About.vue'),
    meta: {
      title: '关于我们',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: false
    }
  },
  {
    path: 'profile/help',
    name: 'MobileHelp',
    component: () => import('@/mobile/views/profile/Help.vue'),
    meta: {
      title: '帮助中心',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: true,
      requiresAuth: false
    }
  },
  {
    path: 'profile/feedback',
    name: 'MobileFeedback',
    component: () => import('@/mobile/views/profile/Feedback.vue'),
    meta: {
      title: '意见反馈',
      showHeader: true,
      showBack: true,
      showTabBar: false,
      keepAlive: false,
      requiresAuth: true
    }
  }
]
