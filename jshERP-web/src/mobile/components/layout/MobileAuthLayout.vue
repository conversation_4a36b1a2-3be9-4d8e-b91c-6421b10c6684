<template>
  <div class="mobile-auth-layout">
    <!-- 认证页面内容 -->
    <div class="mobile-auth-content">
      <router-view />
    </div>
    
    <!-- 背景装饰 -->
    <div class="mobile-auth-background">
      <div class="auth-bg-circle auth-bg-circle-1"></div>
      <div class="auth-bg-circle auth-bg-circle-2"></div>
      <div class="auth-bg-circle auth-bg-circle-3"></div>
    </div>
  </div>
</template>

<script>
/**
 * 移动端认证页面布局组件
 * 用于登录、注册、忘记密码等认证相关页面
 */
export default {
  name: 'MobileAuthLayout',
  
  created() {
    // 设置页面标题
    document.title = 'jshERP Mobile'
    
    // 隐藏桌面端的侧边栏（如果存在）
    if (this.$store) {
      this.$store.dispatch('setSidebar', false)
    }
  },
  
  mounted() {
    // 设置状态栏样式（如果是PWA环境）
    this.setStatusBarStyle()
  },
  
  methods: {
    /**
     * 设置状态栏样式
     */
    setStatusBarStyle() {
      // 设置状态栏为浅色内容（适配深色背景）
      if (window.navigator && window.navigator.standalone) {
        const metaThemeColor = document.querySelector('meta[name="theme-color"]')
        if (metaThemeColor) {
          metaThemeColor.content = '#3B82F6'
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-auth-layout {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
  overflow: hidden;
  
  // 安全区域适配
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  
  .mobile-auth-content {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
  }
  
  .mobile-auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    
    .auth-bg-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      
      &.auth-bg-circle-1 {
        width: 200px;
        height: 200px;
        top: -100px;
        right: -100px;
        animation: float 6s ease-in-out infinite;
      }
      
      &.auth-bg-circle-2 {
        width: 150px;
        height: 150px;
        bottom: 20%;
        left: -75px;
        animation: float 8s ease-in-out infinite reverse;
      }
      
      &.auth-bg-circle-3 {
        width: 100px;
        height: 100px;
        top: 30%;
        left: 20%;
        animation: float 10s ease-in-out infinite;
      }
    }
  }
}

// 浮动动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-auth-layout {
    .mobile-auth-content {
      padding: 16px;
    }
  }
}

@media (min-height: 812px) {
  .mobile-auth-layout {
    .mobile-auth-content {
      padding: 40px 20px;
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-auth-layout {
    .mobile-auth-content {
      justify-content: flex-start;
      padding-top: 20px;
    }
    
    .mobile-auth-background {
      .auth-bg-circle {
        &.auth-bg-circle-1 {
          width: 120px;
          height: 120px;
          top: -60px;
          right: -60px;
        }
        
        &.auth-bg-circle-2 {
          width: 100px;
          height: 100px;
          bottom: 10%;
          left: -50px;
        }
        
        &.auth-bg-circle-3 {
          width: 80px;
          height: 80px;
          top: 20%;
          left: 15%;
        }
      }
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .mobile-auth-layout {
    background: linear-gradient(135deg, #1E293B 0%, #0F172A 100%);
    
    .mobile-auth-background {
      .auth-bg-circle {
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }
}
</style>
