/**
 * 移动端组件样式
 * 自定义组件和Vant组件的样式增强
 */

.mobile-app {
  /**
   * 移动端布局组件样式
   */
  .mobile-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: @background-color-light;
  }
  
  .mobile-header {
    flex-shrink: 0;
    background: @background-color;
    border-bottom: 1px solid @border-color;
    z-index: @z-index-fixed;
  }
  
  .mobile-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }
  
  .mobile-footer {
    flex-shrink: 0;
    background: @background-color;
    border-top: 1px solid @border-color;
    z-index: @z-index-fixed;
  }
  
  /**
   * 移动端卡片组件
   */
  .mobile-card {
    background: @background-color;
    border-radius: @border-radius-lg;
    box-shadow: @shadow-sm;
    margin: @margin-sm @margin-md @margin-md;
    overflow: hidden;
    
    &:first-child {
      margin-top: @margin-md;
    }
    
    &:last-child {
      margin-bottom: @margin-md;
    }
    
    .mobile-card-header {
      padding: @padding-md;
      border-bottom: 1px solid @border-color-light;
      
      .mobile-card-title {
        font-size: @font-size-lg;
        font-weight: @font-weight-semibold;
        color: @text-color;
        margin: 0;
      }
      
      .mobile-card-extra {
        color: @text-color-secondary;
        font-size: @font-size-sm;
      }
    }
    
    .mobile-card-body {
      padding: @padding-md;
    }
    
    .mobile-card-footer {
      padding: @padding-md;
      border-top: 1px solid @border-color-light;
      background: @background-color-light;
    }
  }
  
  /**
   * 移动端列表组件
   */
  .mobile-list {
    background: @background-color;
    border-radius: @border-radius-lg;
    margin: @margin-sm @margin-md @margin-md;
    overflow: hidden;
    
    &:first-child {
      margin-top: @margin-md;
    }
    
    &:last-child {
      margin-bottom: @margin-md;
    }
    
    .mobile-list-item {
      padding: @padding-md;
      border-bottom: 1px solid @border-color-light;
      display: flex;
      align-items: center;
      min-height: @touch-target-size;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: @background-color-light;
      }
      
      .mobile-list-item-icon {
        margin-right: @margin-sm;
        color: @text-color-secondary;
      }
      
      .mobile-list-item-content {
        flex: 1;
        
        .mobile-list-item-title {
          font-size: @font-size-md;
          color: @text-color;
          margin-bottom: @margin-xs;
        }
        
        .mobile-list-item-desc {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
      
      .mobile-list-item-extra {
        color: @text-color-secondary;
        font-size: @font-size-sm;
      }
      
      .mobile-list-item-arrow {
        margin-left: @margin-sm;
        color: @text-color-placeholder;
      }
    }
  }
  
  /**
   * 移动端表单组件增强
   */
  .mobile-form {
    .van-cell-group {
      margin: @margin-md;
      border-radius: @border-radius-lg;
      overflow: hidden;
      
      .van-field {
        padding: @padding-md;
        
        &__label {
          font-weight: @font-weight-medium;
          color: @text-color;
        }
        
        &__control {
          color: @text-color;
          
          &::placeholder {
            color: @text-color-placeholder;
          }
        }
      }
    }
    
    .mobile-form-actions {
      padding: @padding-lg @padding-md;
      
      .van-button {
        width: 100%;
        height: 44px;
        border-radius: @border-radius-md;
        font-weight: @font-weight-medium;
        
        & + .van-button {
          margin-top: @margin-md;
        }
      }
    }
  }
  
  /**
   * 移动端按钮组件增强
   */
  .van-button {
    border-radius: @border-radius-md;
    font-weight: @font-weight-medium;
    transition: all @animation-duration-fast @animation-timing-function;
    
    &--primary {
      background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
      border: none;
      box-shadow: @shadow-sm;
      
      &:active {
        transform: translateY(1px);
        box-shadow: @shadow-xs;
      }
    }
    
    &--success {
      background: linear-gradient(135deg, @success-color 0%, #059669 100%);
      border: none;
      box-shadow: @shadow-sm;
    }
    
    &--warning {
      background: linear-gradient(135deg, @warning-color 0%, #D97706 100%);
      border: none;
      box-shadow: @shadow-sm;
    }
    
    &--danger {
      background: linear-gradient(135deg, @error-color 0%, #DC2626 100%);
      border: none;
      box-shadow: @shadow-sm;
    }
    
    &--default {
      background: @background-color;
      border: 1px solid @border-color;
      color: @text-color;
      
      &:active {
        background: @background-color-light;
      }
    }
  }
  
  /**
   * 移动端导航栏增强
   */
  .van-nav-bar {
    background: @background-color;
    box-shadow: @shadow-sm;
    
    &__title {
      font-weight: @font-weight-semibold;
      font-size: @font-size-lg;
      color: @text-color;
    }
    
    &__left,
    &__right {
      .van-icon {
        color: @text-color;
      }
    }
  }
  
  /**
   * 移动端标签栏增强
   */
  .van-tabbar {
    background: @background-color;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    border-top: 1px solid @border-color;
    
    &-item {
      &__text {
        font-size: @font-size-xs;
        margin-top: @margin-xs;
      }
      
      &--active {
        .van-tabbar-item__text {
          color: @primary-color;
        }
        
        .van-tabbar-item__icon {
          color: @primary-color;
        }
      }
    }
  }
  
  /**
   * 移动端弹窗增强
   */
  .van-popup {
    border-radius: @border-radius-xl @border-radius-xl 0 0;
    
    &--bottom {
      .van-popup__close-icon {
        top: @padding-md;
        right: @padding-md;
      }
    }
  }
  
  /**
   * 移动端加载组件
   */
  .mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: @padding-xxl;
    color: @text-color-secondary;
    
    .van-loading {
      margin-bottom: @margin-md;
    }
    
    .mobile-loading-text {
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
  }
  
  /**
   * 移动端空状态组件
   */
  .mobile-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: @padding-xxl;
    
    .mobile-empty-icon {
      font-size: 64px;
      color: @text-color-placeholder;
      margin-bottom: @margin-lg;
    }
    
    .mobile-empty-text {
      font-size: @font-size-md;
      color: @text-color-secondary;
      text-align: center;
      line-height: @line-height-lg;
    }
    
    .mobile-empty-action {
      margin-top: @margin-lg;
    }
  }
  
  /**
   * 移动端搜索组件增强
   */
  .van-search {
    background: @background-color;
    padding: @padding-sm @padding-md;
    
    &__content {
      background: @background-color-light;
      border-radius: @border-radius-lg;
    }
    
    &__field {
      &__control {
        font-size: @font-size-md;
      }
    }
  }
  
  /**
   * 移动端单元格增强
   */
  .van-cell {
    padding: @padding-md;
    
    &__title {
      font-weight: @font-weight-medium;
      color: @text-color;
    }
    
    &__value {
      color: @text-color-secondary;
    }
    
    &__right-icon {
      color: @text-color-placeholder;
    }
  }
}
