/**
 * 移动端样式变量
 * 基于jshERP设计系统的移动端适配
 */

// ========== 主题色彩 ==========
@primary-color: #3B82F6;           // 主色调（蓝色）
@success-color: #10B981;           // 成功色（绿色）
@warning-color: #F59E0B;           // 警告色（橙色）
@error-color: #EF4444;             // 错误色（红色）
@info-color: #6B7280;              // 信息色（灰色）

// 辅助色彩
@primary-light: #93C5FD;           // 主色浅色
@primary-dark: #1E40AF;            // 主色深色
@success-light: #6EE7B7;           // 成功色浅色
@warning-light: #FCD34D;           // 警告色浅色
@error-light: #FCA5A5;             // 错误色浅色

// ========== 中性色彩 ==========
@text-color: #111827;              // 主文本色
@text-color-secondary: #6B7280;    // 次要文本色
@text-color-placeholder: #9CA3AF;  // 占位符文本色
@text-color-disabled: #D1D5DB;     // 禁用文本色

@background-color: #FFFFFF;        // 背景色
@background-color-light: #F9FAFB;  // 浅背景色
@background-color-dark: #F3F4F6;   // 深背景色

@border-color: #E5E7EB;            // 边框色
@border-color-light: #F3F4F6;      // 浅边框色
@border-color-dark: #D1D5DB;       // 深边框色

// ========== 字体设置 ==========
@font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;

// 字体大小
@font-size-xs: 10px;               // 极小字体
@font-size-sm: 12px;               // 小字体
@font-size-md: 14px;               // 中等字体（基准）
@font-size-lg: 16px;               // 大字体
@font-size-xl: 18px;               // 超大字体
@font-size-xxl: 20px;              // 特大字体

// 字体粗细
@font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// 行高
@line-height-xs: 1.2;
@line-height-sm: 1.4;
@line-height-md: 1.5;
@line-height-lg: 1.6;
@line-height-xl: 1.8;

// ========== 间距设置 ==========
@spacing-xs: 4px;                  // 极小间距
@spacing-sm: 8px;                  // 小间距
@spacing-md: 16px;                 // 中等间距
@spacing-lg: 24px;                 // 大间距
@spacing-xl: 32px;                 // 超大间距
@spacing-xxl: 48px;                // 特大间距

// 内边距
@padding-xs: @spacing-xs;
@padding-sm: @spacing-sm;
@padding-md: @spacing-md;
@padding-lg: @spacing-lg;
@padding-xl: @spacing-xl;
@padding-xxl: @spacing-xxl;

// 外边距
@margin-xs: @spacing-xs;
@margin-sm: @spacing-sm;
@margin-md: @spacing-md;
@margin-lg: @spacing-lg;
@margin-xl: @spacing-xl;
@margin-xxl: @spacing-xxl;

// ========== 圆角设置 ==========
@border-radius-xs: 2px;            // 极小圆角
@border-radius-sm: 4px;            // 小圆角
@border-radius-md: 6px;            // 中等圆角
@border-radius-lg: 8px;            // 大圆角
@border-radius-xl: 12px;           // 超大圆角
@border-radius-round: 50%;         // 圆形

// ========== 阴影设置 ==========
@shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
@shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
@shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
@shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
@shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

// ========== 移动端特定 ==========
// 安全区域
@safe-area-inset-top: env(safe-area-inset-top);
@safe-area-inset-right: env(safe-area-inset-right);
@safe-area-inset-bottom: env(safe-area-inset-bottom);
@safe-area-inset-left: env(safe-area-inset-left);

// 布局尺寸
@header-height: 44px;              // 头部高度
@tab-bar-height: 50px;             // 底部导航高度
@navbar-height: 46px;              // 导航栏高度

// 最小触摸区域
@touch-target-size: 44px;          // 最小触摸目标尺寸

// ========== 动画设置 ==========
@animation-duration-fast: 0.2s;    // 快速动画
@animation-duration-base: 0.3s;    // 基础动画
@animation-duration-slow: 0.5s;    // 慢速动画

@animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); // 动画缓动函数

// ========== 层级设置 ==========
@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal-backdrop: 1040;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;
@z-index-toast: 1080;

// ========== 断点设置 ==========
@screen-xs: 320px;                 // 超小屏幕
@screen-sm: 375px;                 // 小屏幕
@screen-md: 414px;                 // 中等屏幕
@screen-lg: 768px;                 // 大屏幕（平板）

// ========== Vant 组件定制 ==========
// 覆盖 Vant 默认变量
@van-primary-color: @primary-color;
@van-success-color: @success-color;
@van-warning-color: @warning-color;
@van-danger-color: @error-color;
@van-info-color: @info-color;

@van-text-color: @text-color;
@van-text-color-2: @text-color-secondary;
@van-text-color-3: @text-color-placeholder;

@van-background-color: @background-color;
@van-background-color-light: @background-color-light;

@van-border-color: @border-color;
@van-border-radius-sm: @border-radius-sm;
@van-border-radius-md: @border-radius-md;
@van-border-radius-lg: @border-radius-lg;

@van-font-size-xs: @font-size-xs;
@van-font-size-sm: @font-size-sm;
@van-font-size-md: @font-size-md;
@van-font-size-lg: @font-size-lg;

@van-padding-xs: @padding-xs;
@van-padding-sm: @padding-sm;
@van-padding-md: @padding-md;
@van-padding-lg: @padding-lg;

// ========== 业务相关变量 ==========
// 状态色彩
@status-pending: #F59E0B;          // 待处理
@status-processing: #3B82F6;       // 处理中
@status-completed: #10B981;        // 已完成
@status-cancelled: #6B7280;        // 已取消
@status-failed: #EF4444;           // 失败

// 业务色彩
@color-sales: #10B981;             // 销售
@color-purchase: #3B82F6;          // 采购
@color-inventory: #F59E0B;         // 库存
@color-finance: #8B5CF6;           // 财务
