/**
 * 移动端基础样式
 * 包含重置样式、基础元素样式等
 */

/**
 * 移动端样式重置
 */
.mobile-app {
  // 基础重置
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  // 移除默认样式
  ul, ol {
    list-style: none;
  }
  
  a {
    text-decoration: none;
    color: inherit;
    
    &:hover, &:active {
      text-decoration: none;
    }
  }
  
  button {
    border: none;
    outline: none;
    background: none;
    cursor: pointer;
  }
  
  input, textarea {
    border: none;
    outline: none;
    background: none;
    resize: none;
    
    &::-webkit-input-placeholder {
      color: @text-color-placeholder;
    }
    
    &::-moz-placeholder {
      color: @text-color-placeholder;
    }
    
    &:-ms-input-placeholder {
      color: @text-color-placeholder;
    }
  }
  
  // 移动端特有的样式重置
  html, body {
    height: 100%;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  // 禁用选择和拖拽
  img {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
  }
  
  // 移除点击高亮
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }
  
  // 滚动条样式
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }
  
  /**
   * 基础文本样式
   */
  h1, h2, h3, h4, h5, h6 {
    font-weight: @font-weight-semibold;
    line-height: @line-height-sm;
    margin: 0;
  }
  
  h1 {
    font-size: @font-size-xxl;
  }
  
  h2 {
    font-size: @font-size-xl;
  }
  
  h3 {
    font-size: @font-size-lg;
  }
  
  h4 {
    font-size: @font-size-md;
  }
  
  h5 {
    font-size: @font-size-sm;
  }
  
  h6 {
    font-size: @font-size-xs;
  }
  
  p {
    margin: 0;
    line-height: @line-height-md;
  }
  
  small {
    font-size: @font-size-sm;
    color: @text-color-secondary;
  }
  
  strong {
    font-weight: @font-weight-semibold;
  }
  
  /**
   * 基础布局样式
   */
  .flex {
    display: flex;
  }
  
  .flex-column {
    flex-direction: column;
  }
  
  .flex-row {
    flex-direction: row;
  }
  
  .flex-wrap {
    flex-wrap: wrap;
  }
  
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  
  .flex-1 {
    flex: 1;
  }
  
  .flex-auto {
    flex: auto;
  }
  
  .flex-none {
    flex: none;
  }
  
  .justify-start {
    justify-content: flex-start;
  }
  
  .justify-end {
    justify-content: flex-end;
  }
  
  .justify-center {
    justify-content: center;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .justify-around {
    justify-content: space-around;
  }
  
  .items-start {
    align-items: flex-start;
  }
  
  .items-end {
    align-items: flex-end;
  }
  
  .items-center {
    align-items: center;
  }
  
  .items-baseline {
    align-items: baseline;
  }
  
  .items-stretch {
    align-items: stretch;
  }
  
  /**
   * 基础定位样式
   */
  .relative {
    position: relative;
  }
  
  .absolute {
    position: absolute;
  }
  
  .fixed {
    position: fixed;
  }
  
  .sticky {
    position: sticky;
  }
  
  /**
   * 基础显示样式
   */
  .block {
    display: block;
  }
  
  .inline {
    display: inline;
  }
  
  .inline-block {
    display: inline-block;
  }
  
  .hidden {
    display: none;
  }
  
  .visible {
    visibility: visible;
  }
  
  .invisible {
    visibility: hidden;
  }
  
  /**
   * 基础文本对齐
   */
  .text-left {
    text-align: left;
  }
  
  .text-center {
    text-align: center;
  }
  
  .text-right {
    text-align: right;
  }
  
  .text-justify {
    text-align: justify;
  }
  
  /**
   * 基础文本样式
   */
  .text-xs {
    font-size: @font-size-xs;
  }
  
  .text-sm {
    font-size: @font-size-sm;
  }
  
  .text-md {
    font-size: @font-size-md;
  }
  
  .text-lg {
    font-size: @font-size-lg;
  }
  
  .text-xl {
    font-size: @font-size-xl;
  }
  
  .text-xxl {
    font-size: @font-size-xxl;
  }
  
  .font-light {
    font-weight: @font-weight-light;
  }
  
  .font-normal {
    font-weight: @font-weight-normal;
  }
  
  .font-medium {
    font-weight: @font-weight-medium;
  }
  
  .font-semibold {
    font-weight: @font-weight-semibold;
  }
  
  .font-bold {
    font-weight: @font-weight-bold;
  }
  
  /**
   * 基础颜色样式
   */
  .text-primary {
    color: @primary-color;
  }
  
  .text-success {
    color: @success-color;
  }
  
  .text-warning {
    color: @warning-color;
  }
  
  .text-error {
    color: @error-color;
  }
  
  .text-info {
    color: @info-color;
  }
  
  .text-secondary {
    color: @text-color-secondary;
  }
  
  .text-placeholder {
    color: @text-color-placeholder;
  }
  
  .text-disabled {
    color: @text-color-disabled;
  }
}
