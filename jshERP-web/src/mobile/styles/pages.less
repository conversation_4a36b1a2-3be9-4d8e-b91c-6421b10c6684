/**
 * 移动端页面样式
 * 各个业务页面的专用样式
 */

.mobile-app {
  /**
   * 登录页面样式
   */
  .mobile-login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: @padding-xl @padding-lg;
    
    .mobile-login-logo {
      text-align: center;
      margin-bottom: @margin-xxl;
      
      .mobile-login-logo-img {
        width: 80px;
        height: 80px;
        border-radius: @border-radius-xl;
        background: @background-color;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: @primary-color;
        margin-bottom: @margin-lg;
      }
      
      .mobile-login-title {
        color: @background-color;
        font-size: @font-size-xl;
        font-weight: @font-weight-semibold;
        margin-bottom: @margin-sm;
      }
      
      .mobile-login-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: @font-size-sm;
      }
    }
    
    .mobile-login-form {
      background: @background-color;
      border-radius: @border-radius-xl;
      padding: @padding-xl;
      box-shadow: @shadow-lg;
      
      .van-field {
        margin-bottom: @margin-md;
        border-radius: @border-radius-md;
        background: @background-color-light;
        
        &__control {
          font-size: @font-size-md;
        }
      }
      
      .mobile-login-actions {
        margin-top: @margin-lg;
        
        .van-button {
          width: 100%;
          height: 48px;
          border-radius: @border-radius-md;
          font-size: @font-size-md;
          font-weight: @font-weight-semibold;
        }
      }
      
      .mobile-login-extra {
        margin-top: @margin-lg;
        text-align: center;
        
        .mobile-login-link {
          color: @primary-color;
          font-size: @font-size-sm;
          text-decoration: none;
          
          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }
  
  /**
   * 仪表盘页面样式
   */
  .mobile-dashboard-page {
    background: @background-color-light;
    min-height: 100vh;
    
    .mobile-dashboard-header {
      background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
      padding: @padding-lg @padding-md @padding-xl;
      color: @background-color;
      
      .mobile-dashboard-greeting {
        font-size: @font-size-lg;
        font-weight: @font-weight-semibold;
        margin-bottom: @margin-sm;
      }
      
      .mobile-dashboard-date {
        font-size: @font-size-sm;
        opacity: 0.9;
      }
    }
    
    .mobile-dashboard-stats {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: @margin-md;
      padding: @padding-md;
      margin-top: -@margin-lg;
      
      .mobile-stat-card {
        background: @background-color;
        border-radius: @border-radius-lg;
        padding: @padding-lg;
        box-shadow: @shadow-sm;
        text-align: center;
        
        .mobile-stat-icon {
          width: 40px;
          height: 40px;
          border-radius: @border-radius-md;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          margin-bottom: @margin-md;
          font-size: 20px;
          
          &--sales {
            background: rgba(16, 185, 129, 0.1);
            color: @color-sales;
          }
          
          &--purchase {
            background: rgba(59, 130, 246, 0.1);
            color: @color-purchase;
          }
          
          &--inventory {
            background: rgba(245, 158, 11, 0.1);
            color: @color-inventory;
          }
          
          &--finance {
            background: rgba(139, 92, 246, 0.1);
            color: @color-finance;
          }
        }
        
        .mobile-stat-value {
          font-size: @font-size-xl;
          font-weight: @font-weight-bold;
          color: @text-color;
          margin-bottom: @margin-xs;
        }
        
        .mobile-stat-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
    
    .mobile-dashboard-quick-actions {
      padding: 0 @padding-md @padding-md;
      
      .mobile-quick-action-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: @margin-md;
        background: @background-color;
        border-radius: @border-radius-lg;
        padding: @padding-lg;
        box-shadow: @shadow-sm;
        
        .mobile-quick-action {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-decoration: none;
          color: @text-color;
          
          &:active {
            opacity: 0.7;
          }
          
          .mobile-quick-action-icon {
            width: 36px;
            height: 36px;
            border-radius: @border-radius-md;
            background: @background-color-light;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: @margin-sm;
            font-size: 18px;
            color: @primary-color;
          }
          
          .mobile-quick-action-text {
            font-size: @font-size-xs;
            text-align: center;
            line-height: @line-height-sm;
          }
        }
      }
    }
  }
  
  /**
   * 商品页面样式
   */
  .mobile-product-page {
    .mobile-product-search {
      background: @background-color;
      padding: @padding-md;
      border-bottom: 1px solid @border-color;
      
      .van-search {
        background: transparent;
        padding: 0;
      }
    }
    
    .mobile-product-filters {
      background: @background-color;
      padding: @padding-sm @padding-md;
      border-bottom: 1px solid @border-color;
      display: flex;
      gap: @margin-sm;
      
      .mobile-filter-tag {
        padding: @padding-xs @padding-sm;
        background: @background-color-light;
        border-radius: @border-radius-sm;
        font-size: @font-size-xs;
        color: @text-color-secondary;
        border: 1px solid @border-color;
        
        &--active {
          background: @primary-color;
          color: @background-color;
          border-color: @primary-color;
        }
      }
    }
    
    .mobile-product-list {
      padding: @padding-md;
      
      .mobile-product-item {
        background: @background-color;
        border-radius: @border-radius-lg;
        padding: @padding-md;
        margin-bottom: @margin-md;
        box-shadow: @shadow-sm;
        display: flex;
        
        .mobile-product-image {
          width: 80px;
          height: 80px;
          border-radius: @border-radius-md;
          background: @background-color-light;
          margin-right: @margin-md;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .mobile-product-info {
          flex: 1;
          
          .mobile-product-name {
            font-size: @font-size-md;
            font-weight: @font-weight-medium;
            color: @text-color;
            margin-bottom: @margin-xs;
            line-height: @line-height-sm;
          }
          
          .mobile-product-code {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-bottom: @margin-sm;
          }
          
          .mobile-product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .mobile-product-price {
              font-size: @font-size-lg;
              font-weight: @font-weight-semibold;
              color: @primary-color;
            }
            
            .mobile-product-stock {
              font-size: @font-size-sm;
              color: @text-color-secondary;
              
              &--low {
                color: @warning-color;
              }
              
              &--out {
                color: @error-color;
              }
            }
          }
        }
      }
    }
  }
  
  /**
   * 订单页面样式
   */
  .mobile-order-page {
    .mobile-order-tabs {
      background: @background-color;
      border-bottom: 1px solid @border-color;
      
      .van-tabs__nav {
        background: transparent;
      }
      
      .van-tab {
        font-size: @font-size-sm;
        
        &--active {
          color: @primary-color;
        }
      }
    }
    
    .mobile-order-list {
      padding: @padding-md;
      
      .mobile-order-item {
        background: @background-color;
        border-radius: @border-radius-lg;
        margin-bottom: @margin-md;
        box-shadow: @shadow-sm;
        overflow: hidden;
        
        .mobile-order-header {
          padding: @padding-md;
          border-bottom: 1px solid @border-color-light;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .mobile-order-number {
            font-size: @font-size-md;
            font-weight: @font-weight-medium;
            color: @text-color;
          }
          
          .mobile-order-status {
            padding: @padding-xs @padding-sm;
            border-radius: @border-radius-sm;
            font-size: @font-size-xs;
            
            &--pending {
              background: rgba(245, 158, 11, 0.1);
              color: @status-pending;
            }
            
            &--processing {
              background: rgba(59, 130, 246, 0.1);
              color: @status-processing;
            }
            
            &--completed {
              background: rgba(16, 185, 129, 0.1);
              color: @status-completed;
            }
            
            &--cancelled {
              background: rgba(107, 114, 128, 0.1);
              color: @status-cancelled;
            }
          }
        }
        
        .mobile-order-content {
          padding: @padding-md;
          
          .mobile-order-customer {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-bottom: @margin-sm;
          }
          
          .mobile-order-amount {
            font-size: @font-size-lg;
            font-weight: @font-weight-semibold;
            color: @text-color;
            margin-bottom: @margin-sm;
          }
          
          .mobile-order-date {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }
        
        .mobile-order-actions {
          padding: @padding-md;
          border-top: 1px solid @border-color-light;
          display: flex;
          gap: @margin-sm;
          
          .van-button {
            flex: 1;
            height: 32px;
            font-size: @font-size-sm;
          }
        }
      }
    }
  }
}
