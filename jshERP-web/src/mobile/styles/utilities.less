/**
 * 移动端工具类样式
 * 常用的原子化CSS类
 */

.mobile-app {
  /**
   * 间距工具类
   */
  // 内边距
  .p-0 { padding: 0; }
  .p-xs { padding: @padding-xs; }
  .p-sm { padding: @padding-sm; }
  .p-md { padding: @padding-md; }
  .p-lg { padding: @padding-lg; }
  .p-xl { padding: @padding-xl; }
  
  .px-0 { padding-left: 0; padding-right: 0; }
  .px-xs { padding-left: @padding-xs; padding-right: @padding-xs; }
  .px-sm { padding-left: @padding-sm; padding-right: @padding-sm; }
  .px-md { padding-left: @padding-md; padding-right: @padding-md; }
  .px-lg { padding-left: @padding-lg; padding-right: @padding-lg; }
  .px-xl { padding-left: @padding-xl; padding-right: @padding-xl; }
  
  .py-0 { padding-top: 0; padding-bottom: 0; }
  .py-xs { padding-top: @padding-xs; padding-bottom: @padding-xs; }
  .py-sm { padding-top: @padding-sm; padding-bottom: @padding-sm; }
  .py-md { padding-top: @padding-md; padding-bottom: @padding-md; }
  .py-lg { padding-top: @padding-lg; padding-bottom: @padding-lg; }
  .py-xl { padding-top: @padding-xl; padding-bottom: @padding-xl; }
  
  .pt-0 { padding-top: 0; }
  .pt-xs { padding-top: @padding-xs; }
  .pt-sm { padding-top: @padding-sm; }
  .pt-md { padding-top: @padding-md; }
  .pt-lg { padding-top: @padding-lg; }
  .pt-xl { padding-top: @padding-xl; }
  
  .pr-0 { padding-right: 0; }
  .pr-xs { padding-right: @padding-xs; }
  .pr-sm { padding-right: @padding-sm; }
  .pr-md { padding-right: @padding-md; }
  .pr-lg { padding-right: @padding-lg; }
  .pr-xl { padding-right: @padding-xl; }
  
  .pb-0 { padding-bottom: 0; }
  .pb-xs { padding-bottom: @padding-xs; }
  .pb-sm { padding-bottom: @padding-sm; }
  .pb-md { padding-bottom: @padding-md; }
  .pb-lg { padding-bottom: @padding-lg; }
  .pb-xl { padding-bottom: @padding-xl; }
  
  .pl-0 { padding-left: 0; }
  .pl-xs { padding-left: @padding-xs; }
  .pl-sm { padding-left: @padding-sm; }
  .pl-md { padding-left: @padding-md; }
  .pl-lg { padding-left: @padding-lg; }
  .pl-xl { padding-left: @padding-xl; }
  
  // 外边距
  .m-0 { margin: 0; }
  .m-xs { margin: @margin-xs; }
  .m-sm { margin: @margin-sm; }
  .m-md { margin: @margin-md; }
  .m-lg { margin: @margin-lg; }
  .m-xl { margin: @margin-xl; }
  .m-auto { margin: auto; }
  
  .mx-0 { margin-left: 0; margin-right: 0; }
  .mx-xs { margin-left: @margin-xs; margin-right: @margin-xs; }
  .mx-sm { margin-left: @margin-sm; margin-right: @margin-sm; }
  .mx-md { margin-left: @margin-md; margin-right: @margin-md; }
  .mx-lg { margin-left: @margin-lg; margin-right: @margin-lg; }
  .mx-xl { margin-left: @margin-xl; margin-right: @margin-xl; }
  .mx-auto { margin-left: auto; margin-right: auto; }
  
  .my-0 { margin-top: 0; margin-bottom: 0; }
  .my-xs { margin-top: @margin-xs; margin-bottom: @margin-xs; }
  .my-sm { margin-top: @margin-sm; margin-bottom: @margin-sm; }
  .my-md { margin-top: @margin-md; margin-bottom: @margin-md; }
  .my-lg { margin-top: @margin-lg; margin-bottom: @margin-lg; }
  .my-xl { margin-top: @margin-xl; margin-bottom: @margin-xl; }
  
  .mt-0 { margin-top: 0; }
  .mt-xs { margin-top: @margin-xs; }
  .mt-sm { margin-top: @margin-sm; }
  .mt-md { margin-top: @margin-md; }
  .mt-lg { margin-top: @margin-lg; }
  .mt-xl { margin-top: @margin-xl; }
  .mt-auto { margin-top: auto; }
  
  .mr-0 { margin-right: 0; }
  .mr-xs { margin-right: @margin-xs; }
  .mr-sm { margin-right: @margin-sm; }
  .mr-md { margin-right: @margin-md; }
  .mr-lg { margin-right: @margin-lg; }
  .mr-xl { margin-right: @margin-xl; }
  .mr-auto { margin-right: auto; }
  
  .mb-0 { margin-bottom: 0; }
  .mb-xs { margin-bottom: @margin-xs; }
  .mb-sm { margin-bottom: @margin-sm; }
  .mb-md { margin-bottom: @margin-md; }
  .mb-lg { margin-bottom: @margin-lg; }
  .mb-xl { margin-bottom: @margin-xl; }
  .mb-auto { margin-bottom: auto; }
  
  .ml-0 { margin-left: 0; }
  .ml-xs { margin-left: @margin-xs; }
  .ml-sm { margin-left: @margin-sm; }
  .ml-md { margin-left: @margin-md; }
  .ml-lg { margin-left: @margin-lg; }
  .ml-xl { margin-left: @margin-xl; }
  .ml-auto { margin-left: auto; }
  
  /**
   * 尺寸工具类
   */
  .w-full { width: 100%; }
  .w-auto { width: auto; }
  .w-0 { width: 0; }
  
  .h-full { height: 100%; }
  .h-auto { height: auto; }
  .h-0 { height: 0; }
  .h-screen { height: 100vh; }
  
  .min-w-0 { min-width: 0; }
  .min-w-full { min-width: 100%; }
  
  .min-h-0 { min-height: 0; }
  .min-h-full { min-height: 100%; }
  .min-h-screen { min-height: 100vh; }
  
  .max-w-full { max-width: 100%; }
  .max-w-none { max-width: none; }
  
  .max-h-full { max-height: 100%; }
  .max-h-screen { max-height: 100vh; }
  
  /**
   * 圆角工具类
   */
  .rounded-none { border-radius: 0; }
  .rounded-xs { border-radius: @border-radius-xs; }
  .rounded-sm { border-radius: @border-radius-sm; }
  .rounded-md { border-radius: @border-radius-md; }
  .rounded-lg { border-radius: @border-radius-lg; }
  .rounded-xl { border-radius: @border-radius-xl; }
  .rounded-full { border-radius: @border-radius-round; }
  
  .rounded-t-none { border-top-left-radius: 0; border-top-right-radius: 0; }
  .rounded-t-sm { border-top-left-radius: @border-radius-sm; border-top-right-radius: @border-radius-sm; }
  .rounded-t-md { border-top-left-radius: @border-radius-md; border-top-right-radius: @border-radius-md; }
  .rounded-t-lg { border-top-left-radius: @border-radius-lg; border-top-right-radius: @border-radius-lg; }
  .rounded-t-xl { border-top-left-radius: @border-radius-xl; border-top-right-radius: @border-radius-xl; }
  
  .rounded-b-none { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
  .rounded-b-sm { border-bottom-left-radius: @border-radius-sm; border-bottom-right-radius: @border-radius-sm; }
  .rounded-b-md { border-bottom-left-radius: @border-radius-md; border-bottom-right-radius: @border-radius-md; }
  .rounded-b-lg { border-bottom-left-radius: @border-radius-lg; border-bottom-right-radius: @border-radius-lg; }
  .rounded-b-xl { border-bottom-left-radius: @border-radius-xl; border-bottom-right-radius: @border-radius-xl; }
  
  /**
   * 阴影工具类
   */
  .shadow-none { box-shadow: none; }
  .shadow-xs { box-shadow: @shadow-xs; }
  .shadow-sm { box-shadow: @shadow-sm; }
  .shadow-md { box-shadow: @shadow-md; }
  .shadow-lg { box-shadow: @shadow-lg; }
  .shadow-xl { box-shadow: @shadow-xl; }
  
  /**
   * 边框工具类
   */
  .border-0 { border: 0; }
  .border { border: 1px solid @border-color; }
  .border-2 { border: 2px solid @border-color; }
  
  .border-t-0 { border-top: 0; }
  .border-t { border-top: 1px solid @border-color; }
  .border-t-2 { border-top: 2px solid @border-color; }
  
  .border-r-0 { border-right: 0; }
  .border-r { border-right: 1px solid @border-color; }
  .border-r-2 { border-right: 2px solid @border-color; }
  
  .border-b-0 { border-bottom: 0; }
  .border-b { border-bottom: 1px solid @border-color; }
  .border-b-2 { border-bottom: 2px solid @border-color; }
  
  .border-l-0 { border-left: 0; }
  .border-l { border-left: 1px solid @border-color; }
  .border-l-2 { border-left: 2px solid @border-color; }
  
  /**
   * 背景色工具类
   */
  .bg-transparent { background-color: transparent; }
  .bg-white { background-color: @background-color; }
  .bg-light { background-color: @background-color-light; }
  .bg-dark { background-color: @background-color-dark; }
  .bg-primary { background-color: @primary-color; }
  .bg-success { background-color: @success-color; }
  .bg-warning { background-color: @warning-color; }
  .bg-error { background-color: @error-color; }
  .bg-info { background-color: @info-color; }
  
  /**
   * 文本颜色工具类
   */
  .text-white { color: @background-color; }
  .text-black { color: @text-color; }
  .text-primary { color: @primary-color; }
  .text-success { color: @success-color; }
  .text-warning { color: @warning-color; }
  .text-error { color: @error-color; }
  .text-info { color: @info-color; }
  .text-secondary { color: @text-color-secondary; }
  .text-placeholder { color: @text-color-placeholder; }
  .text-disabled { color: @text-color-disabled; }
  
  /**
   * 溢出处理工具类
   */
  .overflow-hidden { overflow: hidden; }
  .overflow-visible { overflow: visible; }
  .overflow-scroll { overflow: scroll; }
  .overflow-auto { overflow: auto; }
  
  .overflow-x-hidden { overflow-x: hidden; }
  .overflow-x-visible { overflow-x: visible; }
  .overflow-x-scroll { overflow-x: scroll; }
  .overflow-x-auto { overflow-x: auto; }
  
  .overflow-y-hidden { overflow-y: hidden; }
  .overflow-y-visible { overflow-y: visible; }
  .overflow-y-scroll { overflow-y: scroll; }
  .overflow-y-auto { overflow-y: auto; }
  
  /**
   * 文本处理工具类
   */
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .text-ellipsis {
    text-overflow: ellipsis;
  }
  
  .text-clip {
    text-overflow: clip;
  }
  
  .whitespace-normal { white-space: normal; }
  .whitespace-nowrap { white-space: nowrap; }
  .whitespace-pre { white-space: pre; }
  .whitespace-pre-line { white-space: pre-line; }
  .whitespace-pre-wrap { white-space: pre-wrap; }
  
  .break-normal { word-break: normal; overflow-wrap: normal; }
  .break-words { overflow-wrap: break-word; }
  .break-all { word-break: break-all; }
  
  /**
   * 层级工具类
   */
  .z-0 { z-index: 0; }
  .z-10 { z-index: 10; }
  .z-20 { z-index: 20; }
  .z-30 { z-index: 30; }
  .z-40 { z-index: 40; }
  .z-50 { z-index: 50; }
  .z-auto { z-index: auto; }
  
  /**
   * 透明度工具类
   */
  .opacity-0 { opacity: 0; }
  .opacity-25 { opacity: 0.25; }
  .opacity-50 { opacity: 0.5; }
  .opacity-75 { opacity: 0.75; }
  .opacity-100 { opacity: 1; }
  
  /**
   * 指针事件工具类
   */
  .pointer-events-none { pointer-events: none; }
  .pointer-events-auto { pointer-events: auto; }
  
  /**
   * 用户选择工具类
   */
  .select-none { user-select: none; }
  .select-text { user-select: text; }
  .select-all { user-select: all; }
  .select-auto { user-select: auto; }
  
  /**
   * 移动端专用工具类
   */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  .touch-pan-x {
    touch-action: pan-x;
  }
  
  .touch-pan-y {
    touch-action: pan-y;
  }
  
  .touch-none {
    touch-action: none;
  }
  
  .scroll-smooth {
    scroll-behavior: smooth;
  }
  
  .scroll-auto {
    scroll-behavior: auto;
  }
  
  // 移动端安全区域工具类
  .safe-top {
    padding-top: @safe-area-inset-top;
  }
  
  .safe-bottom {
    padding-bottom: @safe-area-inset-bottom;
  }
  
  .safe-left {
    padding-left: @safe-area-inset-left;
  }
  
  .safe-right {
    padding-right: @safe-area-inset-right;
  }
}
