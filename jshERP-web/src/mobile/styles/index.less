/**
 * 移动端样式入口文件
 * 统一管理移动端的所有样式
 */

// 引入变量
@import './variables.less';

// 引入 Vant 样式（如果使用自定义主题）
// @import '~vant/lib/index.less';

// 引入基础样式
@import './base.less';

// 引入组件样式
@import './components.less';

// 引入页面样式
@import './pages.less';

// 引入工具类样式
@import './utilities.less';

/**
 * 移动端全局样式作用域
 * 确保移动端样式不影响桌面端
 */
.mobile-app {
  font-family: @font-family;
  font-size: @font-size-md;
  line-height: @line-height-md;
  color: @text-color;
  background-color: @background-color-light;
  
  // 移动端特有的样式重置
  * {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
  }
  
  // 移动端滚动优化
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
  
  // 移动端安全区域适配
  .safe-area-top {
    padding-top: @safe-area-inset-top;
  }
  
  .safe-area-bottom {
    padding-bottom: @safe-area-inset-bottom;
  }
  
  .safe-area-left {
    padding-left: @safe-area-inset-left;
  }
  
  .safe-area-right {
    padding-right: @safe-area-inset-right;
  }
  
  // 移动端布局容器
  .mobile-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .mobile-header {
    flex-shrink: 0;
    height: @header-height;
    background: @background-color;
    border-bottom: 1px solid @border-color;
  }
  
  .mobile-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: @background-color-light;
  }
  
  .mobile-footer {
    flex-shrink: 0;
    height: @tab-bar-height;
    background: @background-color;
    border-top: 1px solid @border-color;
  }
  
  // 移动端页面内边距
  .mobile-page {
    padding: @padding-md;
    min-height: 100%;
  }
  
  .mobile-page-no-padding {
    padding: 0;
    min-height: 100%;
  }
  
  // 移动端卡片样式
  .mobile-card {
    background: @background-color;
    border-radius: @border-radius-lg;
    box-shadow: @shadow-sm;
    margin-bottom: @margin-md;
    overflow: hidden;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  // 移动端列表样式
  .mobile-list {
    background: @background-color;
    border-radius: @border-radius-lg;
    overflow: hidden;
    
    .mobile-list-item {
      padding: @padding-md;
      border-bottom: 1px solid @border-color-light;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: @background-color-light;
      }
    }
  }
  
  // 移动端按钮样式增强
  .van-button {
    border-radius: @border-radius-md;
    font-weight: @font-weight-medium;
    
    &--primary {
      background: linear-gradient(135deg, @primary-color 0%, @primary-dark 100%);
      border: none;
    }
    
    &--success {
      background: linear-gradient(135deg, @success-color 0%, #059669 100%);
      border: none;
    }
    
    &--warning {
      background: linear-gradient(135deg, @warning-color 0%, #D97706 100%);
      border: none;
    }
    
    &--danger {
      background: linear-gradient(135deg, @error-color 0%, #DC2626 100%);
      border: none;
    }
  }
  
  // 移动端表单样式增强
  .van-field {
    border-radius: @border-radius-md;
    
    &__control {
      font-size: @font-size-md;
    }
  }
  
  // 移动端导航栏样式
  .van-nav-bar {
    background: @background-color;
    box-shadow: @shadow-sm;
    
    &__title {
      font-weight: @font-weight-semibold;
      font-size: @font-size-lg;
    }
  }
  
  // 移动端标签栏样式
  .van-tabbar {
    background: @background-color;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    
    &-item {
      &__text {
        font-size: @font-size-xs;
      }
      
      &--active {
        .van-tabbar-item__text {
          color: @primary-color;
        }
      }
    }
  }
  
  // 移动端弹窗样式
  .van-popup {
    border-radius: @border-radius-xl @border-radius-xl 0 0;
  }
  
  // 移动端加载状态
  .mobile-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: @padding-xl;
    color: @text-color-secondary;
  }
  
  // 移动端空状态
  .mobile-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: @padding-xxl;
    color: @text-color-secondary;
    
    .mobile-empty-icon {
      font-size: 48px;
      margin-bottom: @margin-md;
      opacity: 0.5;
    }
    
    .mobile-empty-text {
      font-size: @font-size-md;
      text-align: center;
    }
  }
  
  // 移动端错误状态
  .mobile-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: @padding-xxl;
    
    .mobile-error-icon {
      font-size: 48px;
      color: @error-color;
      margin-bottom: @margin-md;
    }
    
    .mobile-error-text {
      font-size: @font-size-md;
      color: @text-color-secondary;
      text-align: center;
      margin-bottom: @margin-lg;
    }
  }
}
