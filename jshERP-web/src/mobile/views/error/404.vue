<template>
  <div class="mobile-404">
    <van-empty
      class="custom-empty"
      image="error"
      description="页面不存在"
    >
      <template #image>
        <div class="error-icon">
          <van-icon name="warning-o" size="80" color="#ff6b6b" />
        </div>
      </template>
      
      <div class="error-content">
        <h3 class="error-title">页面走丢了</h3>
        <p class="error-desc">抱歉，您访问的页面不存在或已被删除</p>
        
        <div class="error-actions">
          <van-button 
            type="primary" 
            size="large"
            @click="goHome"
          >
            返回首页
          </van-button>
          
          <van-button 
            type="default" 
            size="large"
            @click="goBack"
          >
            返回上页
          </van-button>
        </div>
      </div>
    </van-empty>
  </div>
</template>

<script>
export default {
  name: 'Mobile404',
  methods: {
    goHome() {
      this.$router.push('/m/dashboard')
    },
    goBack() {
      if (window.history.length > 1) {
        this.$router.go(-1)
      } else {
        this.goHome()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-404 {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f8fa;
  
  .custom-empty {
    padding: 60px 20px;
  }
  
  .error-icon {
    margin-bottom: 20px;
  }
  
  .error-content {
    text-align: center;
    
    .error-title {
      font-size: 20px;
      font-weight: 600;
      color: #323233;
      margin: 0 0 8px 0;
    }
    
    .error-desc {
      font-size: 14px;
      color: #969799;
      margin: 0 0 30px 0;
      line-height: 1.6;
    }
    
    .error-actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .van-button {
        width: 200px;
        margin: 0 auto;
      }
    }
  }
}
</style>
