<template>
  <div class="mobile-forgot-password">
    <div class="forgot-content">
      <div class="forgot-header">
        <h2>忘记密码</h2>
        <p>请输入您的用户名，我们将帮助您重置密码</p>
      </div>

      <van-form @submit="handleSubmit">
        <van-field
          v-model="form.username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请输入用户名' }]"
          left-icon="user-o"
          clearable
        />

        <div class="forgot-actions">
          <van-button 
            round 
            block 
            type="primary" 
            native-type="submit"
            :loading="loading"
            loading-text="处理中..."
          >
            重置密码
          </van-button>
          
          <van-button 
            round 
            block 
            type="default" 
            @click="$router.go(-1)"
            style="margin-top: 16px;"
          >
            返回登录
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileForgotPassword',
  
  data() {
    return {
      form: {
        username: ''
      },
      loading: false
    }
  },
  
  methods: {
    async handleSubmit() {
      this.loading = true
      
      try {
        // 这里应该调用重置密码的API
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        this.$toast.success('重置密码邮件已发送')
        
        setTimeout(() => {
          this.$router.push('/m/auth/login')
        }, 1500)
        
      } catch (error) {
        console.error('重置密码失败:', error)
        this.$toast('重置密码失败，请重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-forgot-password {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
  
  .forgot-content {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 16px;
    padding: 32px 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    .forgot-header {
      text-align: center;
      margin-bottom: 32px;
      
      h2 {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin: 0 0 8px 0;
      }
      
      p {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
    
    :deep(.van-field) {
      margin-bottom: 16px;
      border-radius: 8px;
      background: #f7f8fa;
      
      &__control {
        font-size: 16px;
      }
      
      &__left-icon {
        color: #3B82F6;
      }
    }
    
    .forgot-actions {
      margin-top: 24px;
      
      .van-button {
        height: 48px;
        font-size: 16px;
        font-weight: 600;
        
        &--primary {
          background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
          border: none;
        }
      }
    }
  }
}
</style>
