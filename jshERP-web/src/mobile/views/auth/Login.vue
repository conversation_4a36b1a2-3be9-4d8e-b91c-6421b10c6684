<template>
  <div class="mobile-login">
    <!-- Logo区域 -->
    <div class="login-logo">
      <div class="logo-icon">
        <van-icon name="shop-o" size="48" />
      </div>
      <h1 class="logo-title">jshERP Mobile</h1>
      <p class="logo-subtitle">企业资源管理系统</p>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <van-form @submit="handleLogin">
        <van-field
          v-model="loginForm.username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请输入用户名' }]"
          left-icon="user-o"
          clearable
        />
        
        <van-field
          v-model="loginForm.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
          left-icon="lock"
          clearable
        />
        
        <!-- 验证码 -->
        <van-field
          v-if="needCaptcha"
          v-model="loginForm.captcha"
          name="captcha"
          label="验证码"
          placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
          left-icon="shield-o"
          clearable
        >
          <template #button>
            <img 
              :src="captchaImage" 
              class="captcha-image"
              @click="refreshCaptcha"
              alt="验证码"
            />
          </template>
        </van-field>

        <div class="login-actions">
          <van-button 
            round 
            block 
            type="primary" 
            native-type="submit"
            :loading="loading"
            loading-text="登录中..."
          >
            登录
          </van-button>
        </div>
      </van-form>

      <!-- 其他操作 -->
      <div class="login-extra">
        <van-button 
          type="default" 
          size="small" 
          hairline
          @click="$router.push('/m/auth/forgot-password')"
        >
          忘记密码？
        </van-button>
      </div>
    </div>

    <!-- 版本信息 -->
    <div class="login-footer">
      <p>© 2024 jshERP. All rights reserved.</p>
      <p>Version {{ version }}</p>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: 'MobileLogin',
  
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        captcha: '',
        checkKey: ''
      },
      loading: false,
      needCaptcha: false,
      captchaImage: '',
      version: '1.0.0'
    }
  },
  
  created() {
    // 检查是否需要验证码
    this.checkCaptchaRequired()
    
    // 如果已经登录，直接跳转
    if (this.$store.getters.token) {
      this.redirectAfterLogin()
    }
  },
  
  methods: {
    ...mapActions(['Login', 'getCaptcha']),
    
    /**
     * 检查是否需要验证码
     */
    async checkCaptchaRequired() {
      // 这里可以调用API检查是否需要验证码
      // 暂时设为false，简化登录流程
      this.needCaptcha = false
    },
    
    /**
     * 刷新验证码
     */
    async refreshCaptcha() {
      if (!this.needCaptcha) return
      
      try {
        const response = await this.getCaptcha()
        if (response.success) {
          this.captchaImage = response.data.image
          this.loginForm.checkKey = response.data.checkKey
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
        this.$toast('获取验证码失败')
      }
    },
    
    /**
     * 处理登录
     */
    async handleLogin() {
      this.loading = true
      
      try {
        const loginParams = {
          username: this.loginForm.username,
          password: this.loginForm.password
        }
        
        // 如果需要验证码，添加验证码参数
        if (this.needCaptcha) {
          loginParams.captcha = this.loginForm.captcha
          loginParams.checkKey = this.loginForm.checkKey
        }
        
        const response = await this.Login(loginParams)
        
        if (response.success) {
          this.$toast.success('登录成功')
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.redirectAfterLogin()
          }, 1000)
        } else {
          this.$toast(response.message || '登录失败')
          
          // 登录失败后刷新验证码
          if (this.needCaptcha) {
            this.refreshCaptcha()
          }
        }
      } catch (error) {
        console.error('登录失败:', error)
        this.$toast('登录失败，请重试')
        
        // 登录失败后刷新验证码
        if (this.needCaptcha) {
          this.refreshCaptcha()
        }
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 登录成功后重定向
     */
    redirectAfterLogin() {
      const redirect = this.$route.query.redirect || '/m/dashboard'
      this.$router.replace(redirect)
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-login {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 32px 24px;
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
  
  .login-logo {
    text-align: center;
    margin-bottom: 48px;
    color: white;
    
    .logo-icon {
      margin-bottom: 16px;
      
      .van-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 16px;
        backdrop-filter: blur(10px);
      }
    }
    
    .logo-title {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }
    
    .logo-subtitle {
      font-size: 14px;
      opacity: 0.9;
      margin: 0;
    }
  }
  
  .login-form {
    background: white;
    border-radius: 16px;
    padding: 32px 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    
    :deep(.van-field) {
      margin-bottom: 16px;
      border-radius: 8px;
      background: #f7f8fa;
      
      &__control {
        font-size: 16px;
      }
      
      &__left-icon {
        color: #3B82F6;
      }
    }
    
    .captcha-image {
      width: 80px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid #e5e7eb;
    }
    
    .login-actions {
      margin-top: 32px;
      
      .van-button {
        height: 48px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
        border: none;
      }
    }
    
    .login-extra {
      margin-top: 24px;
      text-align: center;
      
      .van-button {
        color: #666;
        font-size: 14px;
      }
    }
  }
  
  .login-footer {
    margin-top: 32px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    
    p {
      margin: 4px 0;
      font-size: 12px;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-login {
    padding: 24px 16px;
    
    .login-logo {
      margin-bottom: 32px;
      
      .logo-title {
        font-size: 24px;
      }
    }
    
    .login-form {
      padding: 24px 20px;
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-login {
    padding: 16px 24px;
    
    .login-logo {
      margin-bottom: 24px;
      
      .logo-icon .van-icon {
        padding: 12px;
      }
      
      .logo-title {
        font-size: 20px;
      }
    }
    
    .login-form {
      padding: 20px;
      
      :deep(.van-field) {
        margin-bottom: 12px;
      }
      
      .login-actions {
        margin-top: 20px;
        
        .van-button {
          height: 40px;
        }
      }
    }
  }
}
</style>
