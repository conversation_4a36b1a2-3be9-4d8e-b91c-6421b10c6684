<template>
  <div class="mobile-test">
    <!-- 移动端测试页面 -->
    <van-nav-bar
      title="移动端测试"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />
    
    <div class="test-content">
      <van-cell-group title="系统信息">
        <van-cell title="设备类型" :value="deviceInfo.type" />
        <van-cell title="屏幕尺寸" :value="deviceInfo.screen" />
        <van-cell title="用户代理" :value="deviceInfo.userAgent" />
      </van-cell-group>
      
      <van-cell-group title="功能测试">
        <van-cell title="API连接测试" :value="apiStatus" />
        <van-cell title="路由系统" :value="routerStatus" />
        <van-cell title="Vant组件" :value="vantStatus" />
      </van-cell-group>
      
      <div class="test-buttons">
        <van-button type="primary" block @click="testAPI">
          测试API连接
        </van-button>
        
        <van-button type="success" block @click="testNotification">
          测试通知
        </van-button>
        
        <van-button type="warning" block @click="testDialog">
          测试对话框
        </van-button>
      </div>
      
      <van-cell-group title="移动端特性">
        <van-cell title="触摸支持" :value="touchSupport ? '支持' : '不支持'" />
        <van-cell title="PWA支持" :value="pwaSupport ? '支持' : '不支持'" />
        <van-cell title="离线缓存" :value="cacheSupport ? '支持' : '不支持'" />
      </van-cell-group>
    </div>
  </div>
</template>

<script>
import { detectDevice } from '@/shared/utils/device'

export default {
  name: 'MobileTest',
  data() {
    return {
      deviceInfo: {
        type: '检测中...',
        screen: '检测中...',
        userAgent: '检测中...'
      },
      apiStatus: '未测试',
      routerStatus: '正常',
      vantStatus: '正常',
      touchSupport: false,
      pwaSupport: false,
      cacheSupport: false
    }
  },
  mounted() {
    this.detectDevice()
    this.checkFeatures()
  },
  methods: {
    detectDevice() {
      const device = detectDevice()
      this.deviceInfo = {
        type: device.isMobile ? '移动设备' : '桌面设备',
        screen: `${window.screen.width}x${window.screen.height}`,
        userAgent: navigator.userAgent.substring(0, 50) + '...'
      }
    },
    
    checkFeatures() {
      // 检查触摸支持
      this.touchSupport = 'ontouchstart' in window
      
      // 检查PWA支持
      this.pwaSupport = 'serviceWorker' in navigator
      
      // 检查缓存支持
      this.cacheSupport = 'caches' in window
    },
    
    async testAPI() {
      try {
        this.apiStatus = '测试中...'
        // 测试一个简单的API请求
        const response = await this.$http.get('/jshERP-boot/sys/randomImage/1')
        this.apiStatus = '连接成功'
        this.$toast.success('API连接测试成功')
      } catch (error) {
        this.apiStatus = '连接失败'
        this.$toast.fail('API连接测试失败')
      }
    },
    
    testNotification() {
      this.$notify({
        type: 'success',
        message: '这是一个移动端通知测试'
      })
    },
    
    testDialog() {
      this.$dialog.confirm({
        title: '移动端对话框测试',
        message: '这是一个移动端对话框测试，功能正常！'
      }).then(() => {
        this.$toast.success('对话框测试完成')
      }).catch(() => {
        this.$toast.info('对话框已取消')
      })
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-test {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.test-content {
  padding: 16px;
}

.test-buttons {
  margin: 16px 0;
  
  .van-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
