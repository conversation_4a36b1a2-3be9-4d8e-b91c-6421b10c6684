<template>
  <div class="simple-test">
    <h1>移动端路由测试页面</h1>
    <p>如果您看到这个页面，说明移动端路由配置正确！</p>
    <div class="test-info">
      <p><strong>当前路径：</strong>{{ $route.path }}</p>
      <p><strong>路由名称：</strong>{{ $route.name }}</p>
      <p><strong>测试时间：</strong>{{ currentTime }}</p>
    </div>
    <div class="actions">
      <button @click="goBack">返回</button>
      <button @click="goToDesktop">桌面端</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTest',
  data() {
    return {
      currentTime: new Date().toLocaleString()
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToDesktop() {
      window.location.href = '/'
    }
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
  text-align: center;
  font-family: Arial, sans-serif;
}

.test-info {
  background: #f5f5f5;
  padding: 15px;
  margin: 20px 0;
  border-radius: 8px;
}

.actions {
  margin-top: 20px;
}

.actions button {
  margin: 0 10px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.actions button:hover {
  background: #0056b3;
}
</style>
