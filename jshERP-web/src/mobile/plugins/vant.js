/**
 * Vant 移动端组件库配置
 * 支持按需引入和全量引入两种方式
 */

import Vue from 'vue'

// 方式一：全量引入（开发阶段推荐）
import Vant from 'vant'
import 'vant/lib/index.css'

// 方式二：按需引入（生产环境推荐）
// import {
//   Button,
//   Cell,
//   CellGroup,
//   Icon,
//   Image as VanImage,
//   Loading,
//   Toast,
//   Dialog,
//   Notify,
//   ActionSheet,
//   Popup,
//   Overlay,
//   SwipeCell,
//   Stepper,
//   Rate,
//   Slider,
//   Switch,
//   Checkbox,
//   CheckboxGroup,
//   Radio,
//   RadioGroup,
//   Field,
//   Form,
//   NumberKeyboard,
//   PasswordInput,
//   Search,
//   DatetimePicker,
//   Picker,
//   Area,
//   Uploader,
//   ActionBar,
//   ActionBarIcon,
//   ActionBarButton,
//   Grid,
//   GridItem,
//   IndexBar,
//   IndexAnchor,
//   Sidebar,
//   SidebarItem,
//   NavBar,
//   Tab,
//   Tabs,
//   Tabbar,
//   TabbarItem,
//   TreeSelect,
//   NoticeBar,
//   Swipe,
//   SwipeItem,
//   PullRefresh,
//   List,
//   ContactCard,
//   ContactList,
//   ContactEdit,
//   Card,
//   SubmitBar,
//   GoodsAction,
//   GoodsActionIcon,
//   GoodsActionButton,
//   Sku,
//   AddressEdit,
//   AddressList,
//   Area as VanArea,
//   Coupon,
//   CouponCell,
//   CouponList
// } from 'vant'

// 全量引入配置
Vue.use(Vant)

// 按需引入配置（如果使用按需引入，请注释掉上面的全量引入）
// Vue.use(Button)
// Vue.use(Cell)
// Vue.use(CellGroup)
// Vue.use(Icon)
// Vue.use(VanImage)
// Vue.use(Loading)
// Vue.use(Toast)
// Vue.use(Dialog)
// Vue.use(Notify)
// Vue.use(ActionSheet)
// Vue.use(Popup)
// Vue.use(Overlay)
// Vue.use(SwipeCell)
// Vue.use(Stepper)
// Vue.use(Rate)
// Vue.use(Slider)
// Vue.use(Switch)
// Vue.use(Checkbox)
// Vue.use(CheckboxGroup)
// Vue.use(Radio)
// Vue.use(RadioGroup)
// Vue.use(Field)
// Vue.use(Form)
// Vue.use(NumberKeyboard)
// Vue.use(PasswordInput)
// Vue.use(Search)
// Vue.use(DatetimePicker)
// Vue.use(Picker)
// Vue.use(Area)
// Vue.use(Uploader)
// Vue.use(ActionBar)
// Vue.use(ActionBarIcon)
// Vue.use(ActionBarButton)
// Vue.use(Grid)
// Vue.use(GridItem)
// Vue.use(IndexBar)
// Vue.use(IndexAnchor)
// Vue.use(Sidebar)
// Vue.use(SidebarItem)
// Vue.use(NavBar)
// Vue.use(Tab)
// Vue.use(Tabs)
// Vue.use(Tabbar)
// Vue.use(TabbarItem)
// Vue.use(TreeSelect)
// Vue.use(NoticeBar)
// Vue.use(Swipe)
// Vue.use(SwipeItem)
// Vue.use(PullRefresh)
// Vue.use(List)
// Vue.use(ContactCard)
// Vue.use(ContactList)
// Vue.use(ContactEdit)
// Vue.use(Card)
// Vue.use(SubmitBar)
// Vue.use(GoodsAction)
// Vue.use(GoodsActionIcon)
// Vue.use(GoodsActionButton)
// Vue.use(Sku)
// Vue.use(AddressEdit)
// Vue.use(AddressList)
// Vue.use(VanArea)
// Vue.use(Coupon)
// Vue.use(CouponCell)
// Vue.use(CouponList)

/**
 * Vant 主题定制
 */
// 如果需要定制主题，可以在这里配置
// import 'vant/lib/index.less'

/**
 * 移动端适配配置
 */
// 设置 rem 基准值
const setRem = () => {
  const scale = document.documentElement.clientWidth / 375
  document.documentElement.style.fontSize = scale * 37.5 + 'px'
}

// 初始化
setRem()

// 监听窗口变化
window.addEventListener('resize', setRem)
window.addEventListener('orientationchange', setRem)

/**
 * Vant 全局配置
 */
// Toast 默认配置
Vue.prototype.$toast = Vue.prototype.$toast || {}
Object.assign(Vue.prototype.$toast, {
  duration: 2000,
  forbidClick: true
})

// Dialog 默认配置
Vue.prototype.$dialog = Vue.prototype.$dialog || {}

// Notify 默认配置
Vue.prototype.$notify = Vue.prototype.$notify || {}

export default Vant
