/**
 * 移动端Vuex Store配置
 * 扩展现有store，添加移动端专用模块
 */

import Vue from 'vue'
import Vuex from 'vuex'

// 导入现有的store模块
import app from '@/store/modules/app'
import user from '@/store/modules/user'
import permission from '@/store/modules/permission'

// 导入移动端专用模块
import dashboard from './modules/dashboard'

// 导入现有的getters
import getters from '@/store/getters'

Vue.use(Vuex)

// 移动端专用的getters
const mobileGetters = {
  ...getters,
  
  // Dashboard相关getters
  dashboardStatistics: state => state.dashboard.statistics,
  dashboardChartData: state => state.dashboard.chartData,
  dashboardTenantInfo: state => state.dashboard.tenantInfo,
  dashboardBadges: state => state.dashboard.badges,
  dashboardTodayData: state => state.dashboard.todayData,
  dashboardNotifications: state => state.dashboard.recentNotifications,
  dashboardLoading: state => state.dashboard.loading,
  
  // 徽章相关getters
  pendingTasksCount: state => state.dashboard.badges.pendingTasks,
  lowStockProductsCount: state => state.dashboard.badges.lowStockProducts,
  pendingOrdersCount: state => state.dashboard.badges.pendingOrders,
  pendingStockChecksCount: state => state.dashboard.badges.pendingStockChecks,
  unreadMessagesCount: state => state.dashboard.badges.unreadMessages,
  
  // 通知相关getters
  notificationCount: state => state.dashboard.badges.unreadMessages,
  hasUnreadNotifications: state => state.dashboard.badges.unreadMessages > 0,
  
  // 权限相关getters（复用现有逻辑）
  hasPermission: (state) => (permission) => {
    if (!permission) return true
    const permissionList = state.user.permissionList || []
    return permissionList.some(item => item.action === permission)
  },
  
  // 用户信息相关getters
  userInfo: state => state.user.info,
  userName: state => state.user.username,
  userRealName: state => state.user.realname,
  userAvatar: state => state.user.avatar,
  
  // 应用状态相关getters
  device: state => state.app.device,
  theme: state => state.app.theme,
  color: state => state.app.color,
  
  // 移动端专用状态
  isMobileDevice: state => state.app.device === 'mobile',
  isTabletDevice: state => state.app.device === 'tablet'
}

// 移动端专用的mutations
const mobileMutations = {
  // 设置全局加载状态
  SET_GLOBAL_LOADING(state, { loading, text = '加载中...' }) {
    state.globalLoading = loading
    state.globalLoadingText = text
  },
  
  // 设置移动端主题
  SET_MOBILE_THEME(state, theme) {
    state.mobileTheme = theme
  },
  
  // 设置移动端布局配置
  SET_MOBILE_LAYOUT_CONFIG(state, config) {
    state.mobileLayoutConfig = { ...state.mobileLayoutConfig, ...config }
  }
}

// 移动端专用的state
const mobileState = {
  // 全局加载状态
  globalLoading: false,
  globalLoadingText: '加载中...',
  
  // 移动端主题
  mobileTheme: 'light',
  
  // 移动端布局配置
  mobileLayoutConfig: {
    showHeader: true,
    showTabBar: true,
    enablePullRefresh: false,
    showBackTop: true
  }
}

const store = new Vuex.Store({
  state: {
    ...mobileState
  },
  
  mutations: {
    ...mobileMutations
  },
  
  modules: {
    app,
    user,
    permission,
    dashboard
  },
  
  getters: mobileGetters
})

export default store
