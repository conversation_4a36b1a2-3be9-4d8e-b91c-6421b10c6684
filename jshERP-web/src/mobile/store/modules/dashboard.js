/**
 * 移动端Dashboard状态管理
 * 扩展现有Vuex store，添加移动端专用状态
 */

import {
  getBuyAndSaleStatistics,
  buyOrSalePrice,
  getUserInfoWithTenant,
  getPendingTasksCount,
  getLowStockProductsCount,
  getPendingOrdersCount,
  getPendingStockChecksCount,
  getUnreadMessagesCount,
  getTodayQuickData,
  getRecentNotifications
} from '@/shared/api/dashboard'

const dashboard = {
  namespaced: true,
  
  state: {
    // 统计数据
    statistics: {
      todaySale: '0.00',
      todayRetail: '0.00',
      todayBuy: '0.00',
      monthSale: '0.00',
      monthRetail: '0.00',
      monthBuy: '0.00',
      yearSale: '0.00',
      yearRetail: '0.00',
      yearBuy: '0.00'
    },
    
    // 图表数据
    chartData: {
      buyPriceList: [],
      salePriceList: [],
      retailPriceList: []
    },
    
    // 租户信息
    tenantInfo: {
      type: '',
      expireTime: '',
      userCurrentNum: 0,
      userNumLimit: 0,
      tenantId: ''
    },
    
    // 徽章数据
    badges: {
      pendingTasks: 0,
      lowStockProducts: 0,
      pendingOrders: 0,
      pendingStockChecks: 0,
      unreadMessages: 0
    },
    
    // 今日快捷数据
    todayData: {
      newOrders: 0,
      completedOrders: 0,
      newCustomers: 0,
      stockAlerts: 0
    },
    
    // 最近通知
    recentNotifications: [],
    
    // 加载状态
    loading: {
      statistics: false,
      chartData: false,
      tenantInfo: false,
      badges: false,
      todayData: false,
      notifications: false
    },
    
    // 刷新时间
    lastRefreshTime: null
  },
  
  getters: {
    // 获取统计数据
    getStatistics: state => state.statistics,
    
    // 获取图表数据
    getChartData: state => state.chartData,
    
    // 获取租户信息
    getTenantInfo: state => state.tenantInfo,
    
    // 获取徽章数据
    getBadges: state => state.badges,
    
    // 获取今日数据
    getTodayData: state => state.todayData,
    
    // 获取最近通知
    getRecentNotifications: state => state.recentNotifications,
    
    // 获取加载状态
    getLoadingState: state => state.loading,
    
    // 检查是否需要刷新（超过5分钟）
    needsRefresh: state => {
      if (!state.lastRefreshTime) return true
      const now = new Date().getTime()
      const lastRefresh = new Date(state.lastRefreshTime).getTime()
      return (now - lastRefresh) > 5 * 60 * 1000 // 5分钟
    },
    
    // 获取待处理任务总数
    totalPendingTasks: state => {
      return state.badges.pendingTasks + 
             state.badges.pendingOrders + 
             state.badges.pendingStockChecks
    },
    
    // 检查是否有紧急提醒
    hasUrgentAlerts: state => {
      return state.badges.lowStockProducts > 0 || 
             state.badges.unreadMessages > 0
    }
  },
  
  mutations: {
    // 设置统计数据
    SET_STATISTICS(state, data) {
      state.statistics = { ...state.statistics, ...data }
    },
    
    // 设置图表数据
    SET_CHART_DATA(state, data) {
      state.chartData = { ...state.chartData, ...data }
    },
    
    // 设置租户信息
    SET_TENANT_INFO(state, data) {
      state.tenantInfo = { ...state.tenantInfo, ...data }
    },
    
    // 设置徽章数据
    SET_BADGES(state, data) {
      state.badges = { ...state.badges, ...data }
    },
    
    // 设置今日数据
    SET_TODAY_DATA(state, data) {
      state.todayData = { ...state.todayData, ...data }
    },
    
    // 设置最近通知
    SET_RECENT_NOTIFICATIONS(state, data) {
      state.recentNotifications = data
    },
    
    // 设置加载状态
    SET_LOADING(state, { key, value }) {
      state.loading[key] = value
    },
    
    // 设置刷新时间
    SET_REFRESH_TIME(state) {
      state.lastRefreshTime = new Date().toISOString()
    },
    
    // 清空所有数据
    CLEAR_ALL_DATA(state) {
      state.statistics = {
        todaySale: '0.00',
        todayRetail: '0.00',
        todayBuy: '0.00',
        monthSale: '0.00',
        monthRetail: '0.00',
        monthBuy: '0.00',
        yearSale: '0.00',
        yearRetail: '0.00',
        yearBuy: '0.00'
      }
      state.chartData = {
        buyPriceList: [],
        salePriceList: [],
        retailPriceList: []
      }
      state.badges = {
        pendingTasks: 0,
        lowStockProducts: 0,
        pendingOrders: 0,
        pendingStockChecks: 0,
        unreadMessages: 0
      }
      state.todayData = {
        newOrders: 0,
        completedOrders: 0,
        newCustomers: 0,
        stockAlerts: 0
      }
      state.recentNotifications = []
      state.lastRefreshTime = null
    }
  },
  
  actions: {
    // 获取统计数据
    async fetchStatistics({ commit }) {
      commit('SET_LOADING', { key: 'statistics', value: true })
      try {
        const response = await getBuyAndSaleStatistics()
        if (response.code === 200) {
          commit('SET_STATISTICS', response.data)
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      } finally {
        commit('SET_LOADING', { key: 'statistics', value: false })
      }
    },
    
    // 获取图表数据
    async fetchChartData({ commit }) {
      commit('SET_LOADING', { key: 'chartData', value: true })
      try {
        const response = await buyOrSalePrice()
        if (response.code === 200) {
          commit('SET_CHART_DATA', response.data)
        }
      } catch (error) {
        console.error('获取图表数据失败:', error)
      } finally {
        commit('SET_LOADING', { key: 'chartData', value: false })
      }
    },
    
    // 获取租户信息
    async fetchTenantInfo({ commit }) {
      commit('SET_LOADING', { key: 'tenantInfo', value: true })
      try {
        const response = await getUserInfoWithTenant()
        if (response.code === 200) {
          commit('SET_TENANT_INFO', response.data)
        }
      } catch (error) {
        console.error('获取租户信息失败:', error)
      } finally {
        commit('SET_LOADING', { key: 'tenantInfo', value: false })
      }
    },
    
    // 获取徽章数据
    async fetchBadges({ commit }) {
      commit('SET_LOADING', { key: 'badges', value: true })
      try {
        const [
          pendingTasksRes,
          lowStockRes,
          pendingOrdersRes,
          pendingChecksRes,
          unreadMsgRes
        ] = await Promise.allSettled([
          getPendingTasksCount(),
          getLowStockProductsCount(),
          getPendingOrdersCount(),
          getPendingStockChecksCount(),
          getUnreadMessagesCount()
        ])
        
        const badges = {}
        if (pendingTasksRes.status === 'fulfilled' && pendingTasksRes.value.code === 200) {
          badges.pendingTasks = pendingTasksRes.value.data || 0
        }
        if (lowStockRes.status === 'fulfilled' && lowStockRes.value.code === 200) {
          badges.lowStockProducts = lowStockRes.value.data || 0
        }
        if (pendingOrdersRes.status === 'fulfilled' && pendingOrdersRes.value.code === 200) {
          badges.pendingOrders = pendingOrdersRes.value.data || 0
        }
        if (pendingChecksRes.status === 'fulfilled' && pendingChecksRes.value.code === 200) {
          badges.pendingStockChecks = pendingChecksRes.value.data || 0
        }
        if (unreadMsgRes.status === 'fulfilled' && unreadMsgRes.value.code === 200) {
          badges.unreadMessages = unreadMsgRes.value.data || 0
        }
        
        commit('SET_BADGES', badges)
      } catch (error) {
        console.error('获取徽章数据失败:', error)
      } finally {
        commit('SET_LOADING', { key: 'badges', value: false })
      }
    },
    
    // 获取今日数据
    async fetchTodayData({ commit }) {
      commit('SET_LOADING', { key: 'todayData', value: true })
      try {
        const response = await getTodayQuickData()
        if (response.code === 200) {
          commit('SET_TODAY_DATA', response.data)
        }
      } catch (error) {
        console.error('获取今日数据失败:', error)
      } finally {
        commit('SET_LOADING', { key: 'todayData', value: false })
      }
    },
    
    // 获取最近通知
    async fetchRecentNotifications({ commit }, params = { pageSize: 5 }) {
      commit('SET_LOADING', { key: 'notifications', value: true })
      try {
        const response = await getRecentNotifications(params)
        if (response.code === 200) {
          commit('SET_RECENT_NOTIFICATIONS', response.data.records || [])
        }
      } catch (error) {
        console.error('获取最近通知失败:', error)
      } finally {
        commit('SET_LOADING', { key: 'notifications', value: false })
      }
    },
    
    // 刷新所有数据
    async refreshAllData({ dispatch, commit }) {
      commit('SET_REFRESH_TIME')
      await Promise.all([
        dispatch('fetchStatistics'),
        dispatch('fetchChartData'),
        dispatch('fetchTenantInfo'),
        dispatch('fetchBadges'),
        dispatch('fetchTodayData'),
        dispatch('fetchRecentNotifications')
      ])
    },
    
    // 清空数据（用户登出时）
    clearData({ commit }) {
      commit('CLEAR_ALL_DATA')
    }
  }
}

export default dashboard
