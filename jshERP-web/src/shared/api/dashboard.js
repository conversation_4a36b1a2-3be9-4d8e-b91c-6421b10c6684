/**
 * Dashboard 仪表盘相关API
 * 复用现有的jshERP后端API接口
 */

import { getAction, postAction } from '@/api/manage'

/**
 * 获取销售和采购统计数据
 * 复用桌面端的统计API
 */
export function getBuyAndSaleStatistics() {
  return getAction('/depotHead/getBuyAndSaleStatistics')
}

/**
 * 获取销售采购价格统计（图表数据）
 * 复用桌面端的图表API
 */
export function buyOrSalePrice() {
  return getAction('/depotHead/buyOrSalePrice')
}

/**
 * 获取用户信息和租户信息
 * 复用桌面端的用户信息API
 */
export function getUserInfoWithTenant() {
  return getAction('/user/infoWithTenant')
}

/**
 * 获取平台配置信息
 * @param {Object} params - 配置参数
 */
export function getPlatformConfigByKey(params) {
  return getAction('/platformConfig/getPlatformConfigByKey', params)
}

/**
 * 获取消息数量统计
 * @param {Object} params - 查询参数
 */
export function getMsgCountByType(params) {
  return getAction('/msg/getMsgCountByType', params)
}

/**
 * 获取待办任务统计
 * 这是移动端专用的API，可能需要后端新增
 */
export function getPendingTasksCount() {
  return getAction('/dashboard/pendingTasks')
}

/**
 * 获取库存预警商品数量
 */
export function getLowStockProductsCount() {
  return getAction('/material/getLowStockCount')
}

/**
 * 获取待处理订单数量
 */
export function getPendingOrdersCount() {
  return getAction('/depotHead/getPendingOrdersCount')
}

/**
 * 获取待盘点数量
 */
export function getPendingStockChecksCount() {
  return getAction('/depotHead/getPendingStockChecksCount')
}

/**
 * 获取未读消息数量
 */
export function getUnreadMessagesCount() {
  return getAction('/msg/getUnreadCount')
}

/**
 * 获取今日快捷数据
 * 移动端专用的今日数据汇总
 */
export function getTodayQuickData() {
  return getAction('/dashboard/todayQuickData')
}

/**
 * 获取最近通知列表
 * @param {Object} params - 分页参数
 */
export function getRecentNotifications(params) {
  return getAction('/msg/getRecentList', params)
}

/**
 * 标记消息为已读
 * @param {String} msgId - 消息ID
 */
export function markMessageAsRead(msgId) {
  return getAction('/msg/markAsRead', { msgId })
}

/**
 * 获取系统公告
 */
export function getSystemAnnouncements() {
  return getAction('/systemConfig/getAnnouncements')
}

/**
 * 获取用户权限菜单
 * 复用现有的权限API
 */
export function getUserPermissions() {
  return getAction('/user/queryPermissionsByUser')
}

/**
 * 获取用户按钮权限
 * 复用现有的按钮权限API
 */
export function getUserButtonPermissions() {
  return getAction('/user/queryBtnByCurrentUser')
}
