/**
 * 设备检测工具
 * 用于识别用户设备类型并提供相应的功能支持
 */

/**
 * 检测设备类型
 * @returns {Object} 设备信息对象
 */
export function detectDevice() {
  const userAgent = navigator.userAgent.toLowerCase()
  const platform = navigator.platform.toLowerCase()
  
  // 移动设备检测
  const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  
  // 平板检测
  const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent)
  
  // 具体设备类型检测
  const isIOS = /iphone|ipad|ipod/i.test(userAgent)
  const isAndroid = /android/i.test(userAgent)
  const isWeChat = /micromessenger/i.test(userAgent)
  const isAlipay = /alipayclient/i.test(userAgent)
  
  // 屏幕尺寸检测
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height
  const devicePixelRatio = window.devicePixelRatio || 1
  
  // 触摸支持检测
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  
  // 网络状态检测
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
  const networkType = connection ? connection.effectiveType : 'unknown'
  
  return {
    // 基础设备类型
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    
    // 操作系统
    isIOS,
    isAndroid,
    
    // 应用环境
    isWeChat,
    isAlipay,
    isStandalone: window.navigator.standalone === true,
    
    // 硬件特性
    isTouchDevice,
    devicePixelRatio,
    screenWidth,
    screenHeight,
    
    // 网络状态
    networkType,
    isOnline: navigator.onLine,
    
    // 用户代理
    userAgent,
    platform
  }
}

/**
 * 获取设备方向
 * @returns {String} portrait | landscape
 */
export function getOrientation() {
  if (window.orientation !== undefined) {
    return Math.abs(window.orientation) === 90 ? 'landscape' : 'portrait'
  }
  return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
}

/**
 * 检测是否支持PWA安装
 * @returns {Boolean}
 */
export function canInstallPWA() {
  return 'serviceWorker' in navigator && 'PushManager' in window
}

/**
 * 获取安全区域信息
 * @returns {Object} 安全区域尺寸
 */
export function getSafeArea() {
  const style = getComputedStyle(document.documentElement)
  
  return {
    top: parseInt(style.getPropertyValue('--safe-area-inset-top')) || 0,
    right: parseInt(style.getPropertyValue('--safe-area-inset-right')) || 0,
    bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom')) || 0,
    left: parseInt(style.getPropertyValue('--safe-area-inset-left')) || 0
  }
}

/**
 * 设备性能检测
 * @returns {Object} 性能信息
 */
export function getDevicePerformance() {
  const memory = navigator.deviceMemory || 4 // GB
  const cores = navigator.hardwareConcurrency || 4
  
  // 简单的性能评级
  let performanceLevel = 'medium'
  if (memory >= 8 && cores >= 8) {
    performanceLevel = 'high'
  } else if (memory <= 2 || cores <= 2) {
    performanceLevel = 'low'
  }
  
  return {
    memory,
    cores,
    performanceLevel
  }
}

/**
 * 监听设备方向变化
 * @param {Function} callback 回调函数
 */
export function onOrientationChange(callback) {
  const handleOrientationChange = () => {
    setTimeout(() => {
      callback(getOrientation())
    }, 100) // 延迟确保尺寸更新完成
  }
  
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleOrientationChange)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange)
    window.removeEventListener('resize', handleOrientationChange)
  }
}

/**
 * 监听网络状态变化
 * @param {Function} callback 回调函数
 */
export function onNetworkChange(callback) {
  const handleOnline = () => callback(true)
  const handleOffline = () => callback(false)
  
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
  }
}

// 默认导出设备信息
export default detectDevice()
