<template>
  <div>
    <h1>盘点复盘页面</h1>
    <p>页面加载成功！当前时间：{{ new Date().toLocaleString() }}</p>
    <a-button type="primary">测试按钮</a-button>
  </div>
</template>

<script>
export default {
  name: 'InventoryCheckList',
  data() {
    return {
      message: '盘点复盘页面加载成功',
      // 表格列配置
      columns: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'action' }
        },
        {
          title: '申请编号',
          dataIndex: 'number',
          width: 150
        },
        {
          title: '关联单据',
          dataIndex: 'linkNumber',
          width: 150
        },
        {
          title: '商品信息',
          dataIndex: 'materialParam',
          width: 200
        },
        {
          title: '申请日期',
          dataIndex: 'operTime',
          width: 150
        },
        {
          title: '操作员',
          dataIndex: 'operPersonName',
          width: 100
        },
        {
          title: '数量',
          dataIndex: 'totalNum',
          width: 100,
          align: 'right'
        },
        {
          title: '金额合计',
          dataIndex: 'totalPrice',
          width: 120,
          align: 'right'
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'customRenderStatus' }
        }
      ],
      // API配置
      url: {
        list: '/depotHead/list',
        delete: '/depotHead/delete',
        deleteBatch: '/depotHead/deleteBatch'
      }
    }
  },
  created() {
    // 暂时使用mock数据测试界面
    this.loadMockData()
    this.initDepotList()
    this.initUserList()
  },
  methods: {
    // 新增
    myHandleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
    },

    // 编辑
    myHandleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑'
    },

    // 查看详情
    myHandleDetail(record, title, prefixNo) {
      this.$refs.modalForm.detail(record)
      this.$refs.modalForm.title = '查看详情'
    },

    // 删除
    myHandleDelete(record) {
      this.handleDelete(record.id)
    },

    // 弹窗确定
    modalFormOk() {
      this.loadData()
    },

    // 弹窗关闭
    modalFormClose() {
      // 弹窗关闭处理
    },

    // 初始化仓库列表
    initDepotList() {
      this.getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      })
    },

    // 初始化用户列表
    initUserList() {
      this.getAction('/user/list').then(res => {
        if (res.success) {
          this.userList = res.result || []
        }
      })
    },

    // 打印
    handlePrint() {
      this.$message.info('打印功能开发中...')
    },

    // 加载Mock数据用于测试界面
    loadMockData() {
      this.loading = true
      setTimeout(() => {
        this.dataSource = [
          {
            id: 1,
            number: 'PDFP202412260001',
            linkNumber: 'PDLR202412260001',
            materialParam: '苹果 iPhone 15, 华为 Mate 60',
            operTime: '2024-12-26 10:30:00',
            operPersonName: '张三',
            totalNum: 150,
            totalPrice: 146800.00,
            status: '0'
          },
          {
            id: 2,
            number: 'PDFP202412260002',
            linkNumber: 'PDLR202412260002',
            materialParam: '小米 14 Pro, OPPO Find X7',
            operTime: '2024-12-26 11:15:00',
            operPersonName: '李四',
            totalNum: 120,
            totalPrice: 95600.00,
            status: '1'
          },
          {
            id: 3,
            number: 'PDFP202412260003',
            linkNumber: '',
            materialParam: 'vivo X100, 一加 12',
            operTime: '2024-12-26 14:20:00',
            operPersonName: '王五',
            totalNum: 90,
            totalPrice: 67800.00,
            status: '0'
          }
        ]
        this.ipagination.total = 3
        this.loading = false
      }, 500)
    }
  }
}
</script>

<style lang="less" scoped>
// jshERP标准样式，无需额外样式
</style>
