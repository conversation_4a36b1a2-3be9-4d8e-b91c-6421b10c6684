/**
 * 库存管理模块类型定义
 * 
 * 包含其他入库、其他出库、调拨出库、组装单、拆卸单等库存操作的类型定义
 */

import type { BaseDocumentHeader, BaseDetailItem, BaseEntity } from './business'

// ================================
// 基础类型定义
// ================================

/**
 * 操作员信息
 */
export interface Operator extends BaseEntity {
  name: string
  code: string
  department?: string
  position?: string
}

/**
 * 库存商品信息
 */
export interface InventoryProduct extends BaseEntity {
  name: string
  code: string
  spec?: string
  unit: string
  currentStock: number
  availableStock: number
  lockedStock: number
  costPrice: number
  averagePrice: number
  lastInPrice: number
  lastOutPrice: number
  warehouseId: string | number
  warehouseName: string
  locationCode?: string
}

/**
 * 入库类型枚举
 */
export enum InType {
  PRODUCTION = 'production',      // 生产入库
  TRANSFER = 'transfer',          // 调拨入库
  INVENTORY_GAIN = 'inventory_gain', // 盘盈入库
  RETURN = 'return',              // 退货入库
  GIFT = 'gift',                  // 赠品入库
  OTHER = 'other'                 // 其他入库
}

/**
 * 出库类型枚举
 */
export enum OutType {
  LOSS = 'loss',                  // 损耗出库
  TRANSFER = 'transfer',          // 调拨出库
  INVENTORY_LOSS = 'inventory_loss', // 盘亏出库
  SAMPLE = 'sample',              // 样品出库
  GIFT = 'gift',                  // 赠品出库
  SCRAP = 'scrap',                // 报废出库
  OTHER = 'other'                 // 其他出库
}

/**
 * 组装类型枚举
 */
export enum AssemblyType {
  SIMPLE = 'simple',              // 简单组装
  COMPLEX = 'complex',            // 复杂组装
  BATCH = 'batch'                 // 批量组装
}

/**
 * 拆卸类型枚举
 */
export enum DisassemblyType {
  SIMPLE = 'simple',              // 简单拆卸
  COMPLEX = 'complex',            // 复杂拆卸
  BATCH = 'batch'                 // 批量拆卸
}

// ================================
// 其他入库相关类型
// ================================

/**
 * 其他入库表单
 */
export interface OtherInForm extends BaseDocumentHeader {
  // 基础信息
  inNo: string
  inDate: string
  operator: string
  operatorId: string | number
  
  // 仓库信息
  warehouseId: string | number
  warehouseName: string
  
  // 入库信息
  inType: InType
  sourceDocument?: string
  sourceDocumentId?: string | number
  
  // 商品信息
  products: InventoryProduct[]
  
  // 金额信息
  totalQuantity: number
  totalAmount: number
  
  // 附加信息
  remark?: string
  attachments?: string[]
}

// ================================
// 其他出库相关类型
// ================================

/**
 * 其他出库表单
 */
export interface OtherOutForm extends BaseDocumentHeader {
  // 基础信息
  outNo: string
  outDate: string
  operator: string
  operatorId: string | number
  
  // 仓库信息
  warehouseId: string | number
  warehouseName: string
  
  // 出库信息
  outType: OutType
  targetDocument?: string
  targetDocumentId?: string | number
  
  // 商品信息
  products: InventoryProduct[]
  
  // 金额信息
  totalQuantity: number
  totalAmount: number
  
  // 附加信息
  remark?: string
  attachments?: string[]
}

// ================================
// 调拨出库相关类型
// ================================

/**
 * 调拨出库表单
 */
export interface TransferOutForm extends BaseDocumentHeader {
  // 基础信息
  transferNo: string
  transferDate: string
  operator: string
  operatorId: string | number
  
  // 仓库信息
  fromWarehouseId: string | number
  fromWarehouseName: string
  toWarehouseId: string | number
  toWarehouseName: string
  
  // 关联信息
  transferInNo?: string
  transferInId?: string | number
  
  // 商品信息
  products: InventoryProduct[]
  
  // 数量信息
  totalQuantity: number
  
  // 附加信息
  remark?: string
  attachments?: string[]
}

// ================================
// 组装单相关类型
// ================================

/**
 * 组装材料
 */
export interface AssemblyMaterial {
  id: string | number
  name: string
  code: string
  spec?: string
  unit: string
  requiredQuantity: number
  actualQuantity: number
  costPrice: number
  totalCost: number
  warehouseId: string | number
  warehouseName: string
}

/**
 * 组装成品
 */
export interface AssemblyProduct {
  id: string | number
  name: string
  code: string
  spec?: string
  unit: string
  plannedQuantity: number
  actualQuantity: number
  costPrice: number
  totalCost: number
  warehouseId: string | number
  warehouseName: string
}

/**
 * 组装单表单
 */
export interface AssemblyForm extends BaseDocumentHeader {
  // 基础信息
  assemblyNo: string
  assemblyDate: string
  operator: string
  operatorId: string | number
  
  // 仓库信息
  warehouseId: string | number
  warehouseName: string
  
  // 组装信息
  assemblyType: AssemblyType
  
  // 材料和成品
  materials: AssemblyMaterial[]
  products: AssemblyProduct[]
  
  // 成本信息
  totalMaterialCost: number
  totalProductCost: number
  laborCost?: number
  overheadCost?: number
  totalCost: number
  
  // 附加信息
  remark?: string
  attachments?: string[]
}

// ================================
// 拆卸单相关类型
// ================================

/**
 * 拆卸成品
 */
export interface DisassemblyProduct {
  id: string | number
  name: string
  code: string
  spec?: string
  unit: string
  plannedQuantity: number
  actualQuantity: number
  costPrice: number
  totalCost: number
  warehouseId: string | number
  warehouseName: string
}

/**
 * 拆卸材料
 */
export interface DisassemblyMaterial {
  id: string | number
  name: string
  code: string
  spec?: string
  unit: string
  expectedQuantity: number
  actualQuantity: number
  costPrice: number
  totalCost: number
  warehouseId: string | number
  warehouseName: string
}

/**
 * 拆卸单表单
 */
export interface DisassemblyForm extends BaseDocumentHeader {
  // 基础信息
  disassemblyNo: string
  disassemblyDate: string
  operator: string
  operatorId: string | number
  
  // 仓库信息
  warehouseId: string | number
  warehouseName: string
  
  // 拆卸信息
  disassemblyType: DisassemblyType
  
  // 成品和材料
  products: DisassemblyProduct[]
  materials: DisassemblyMaterial[]
  
  // 成本信息
  totalProductCost: number
  totalMaterialCost: number
  laborCost?: number
  overheadCost?: number
  totalCost: number
  
  // 附加信息
  remark?: string
  attachments?: string[]
}

// ================================
// 库存查询相关类型
// ================================

/**
 * 库存查询条件
 */
export interface InventoryQueryParams {
  warehouseId?: string | number
  productId?: string | number
  productCode?: string
  productName?: string
  categoryId?: string | number
  lowStock?: boolean
  zeroStock?: boolean
  dateFrom?: string
  dateTo?: string
  page?: number
  size?: number
}

/**
 * 库存统计信息
 */
export interface InventoryStatistics {
  totalProducts: number
  totalValue: number
  lowStockProducts: number
  zeroStockProducts: number
  warehouseCount: number
  lastUpdateTime: string
}

// ================================
// 库存操作记录类型
// ================================

/**
 * 库存操作记录
 */
export interface InventoryRecord extends BaseEntity {
  productId: string | number
  productName: string
  productCode: string
  warehouseId: string | number
  warehouseName: string
  operationType: string
  operationDate: string
  operator: string
  operatorId: string | number
  beforeQuantity: number
  changeQuantity: number
  afterQuantity: number
  unitPrice: number
  totalAmount: number
  documentNo: string
  documentType: string
  remark?: string
}

/**
 * 库存预警配置
 */
export interface InventoryAlert extends BaseEntity {
  productId: string | number
  productName: string
  warehouseId: string | number
  warehouseName: string
  minStock: number
  maxStock: number
  safeStock: number
  alertEnabled: boolean
  alertLevel: 'low' | 'high' | 'zero'
  lastAlertTime?: string
}

// ================================
// 导出类型
// ================================

export type {
  Operator,
  InventoryProduct,
  OtherInForm,
  OtherOutForm,
  TransferOutForm,
  AssemblyForm,
  DisassemblyForm,
  AssemblyMaterial,
  AssemblyProduct,
  DisassemblyProduct,
  DisassemblyMaterial,
  InventoryQueryParams,
  InventoryStatistics,
  InventoryRecord,
  InventoryAlert
}

export {
  InType,
  OutType,
  AssemblyType,
  DisassemblyType
}
