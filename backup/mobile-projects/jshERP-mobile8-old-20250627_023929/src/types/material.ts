/**
 * 商品管理相关类型定义
 * 
 * 基于jshERP后端Material模块的API接口设计
 */

import { BaseEntity, PageQuery, PageResult, ApiResponse } from './business'

/**
 * 商品基础信息接口（对应后端Material实体）
 */
export interface Material extends BaseEntity {
  id?: number
  categoryId?: number
  name: string
  mfrs?: string              // 制造商
  model?: string             // 型号
  standard?: string          // 规格
  brand?: string             // 品牌
  mnemonic?: string          // 助记码
  color?: string             // 颜色
  unit?: string              // 单位
  unitId?: number            // 单位ID
  remark?: string            // 备注
  imgName?: string           // 图片名称
  expiryNum?: number         // 保质期天数
  weight?: number            // 重量
  enabled?: boolean          // 是否启用
  otherField1?: string       // 扩展字段1
  otherField2?: string       // 扩展字段2
  otherField3?: string       // 扩展字段3
  enableSerialNumber?: string // 是否启用序列号
  enableBatchNumber?: string  // 是否启用批次号
  position?: string          // 位置
  attribute?: string         // 属性
  tenantId?: number          // 租户ID
  deleteFlag?: string        // 删除标记
}

/**
 * 商品扩展信息接口（对应后端MaterialExtend实体）
 */
export interface MaterialExtend extends BaseEntity {
  id?: number
  materialId: number         // 商品ID
  barCode?: string           // 条码
  commodityUnit?: string     // 商品单位
  purchasePrice?: number     // 采购价
  commodityDecimal?: number  // 商品小数位数
  wholesalePrice?: number    // 批发价
  lowPrice?: number          // 最低售价
  defaultFlag?: string       // 默认标记
  tenantId?: number          // 租户ID
  deleteFlag?: string        // 删除标记
}

/**
 * 商品视图对象接口（对应后端MaterialVo4Unit）
 * 用于列表展示，包含关联信息
 */
export interface MaterialVo4Unit extends Material {
  meId?: number              // MaterialExtend的ID
  unitName?: string          // 单位名称
  categoryName?: string      // 分类名称
  mBarCode?: string          // 条码
  stock?: number             // 库存数量
  materialOther?: string     // 扩展信息
  purchasePrice?: number     // 采购价
  wholesalePrice?: number    // 批发价
  lowPrice?: number          // 最低售价
  imgUrl?: string            // 图片URL（前端计算得出）
}

/**
 * 商品分类接口
 */
export interface MaterialCategory extends BaseEntity {
  id?: number
  name: string
  parentId?: number
  categoryLevel?: number
  sort?: string
  status?: string
  remark?: string
  tenantId?: number
  deleteFlag?: string
}

/**
 * 商品查询参数接口
 */
export interface MaterialQuery extends PageQuery {
  categoryId?: string        // 分类ID
  materialParam?: string     // 关键词搜索（名称、条码、规格、型号）
  standard?: string          // 规格
  model?: string             // 型号
  color?: string             // 颜色
  brand?: string             // 品牌
  mfrs?: string              // 制造商
  otherField1?: string       // 扩展字段1
  otherField2?: string       // 扩展字段2
  otherField3?: string       // 扩展字段3
  weight?: string            // 重量
  expiryNum?: string         // 保质期
  enableSerialNumber?: string // 序列号启用状态
  enableBatchNumber?: string  // 批次号启用状态
  position?: string          // 位置
  enabled?: string           // 启用状态
  remark?: string            // 备注
  mpList?: string            // 商品属性列表
}

/**
 * 商品搜索结果接口
 */
export interface MaterialSearchResult {
  id: number
  name: string
  model?: string
  standard?: string
  barCode?: string
  unitName?: string
  stock?: number
  imgUrl?: string
}

/**
 * 商品库存信息接口
 */
export interface MaterialStock {
  materialId: number
  materialName: string
  depotId: number
  depotName: string
  currentNumber: number      // 当前库存
  safetyStock?: number       // 安全库存
  lowStock?: number          // 最低库存
}

/**
 * 商品价格信息接口
 */
export interface MaterialPrice {
  materialId: number
  barCode?: string
  purchasePrice?: number     // 采购价
  wholesalePrice?: number    // 批发价
  lowPrice?: number          // 最低售价
  retailPrice?: number       // 零售价
}

/**
 * 商品操作相关接口
 */
export interface MaterialOperation {
  add: (material: Material, extends?: MaterialExtend[]) => Promise<ApiResponse<number>>
  update: (material: Material, extends?: MaterialExtend[]) => Promise<ApiResponse<number>>
  delete: (ids: number[]) => Promise<ApiResponse<number>>
  batchDelete: (ids: number[]) => Promise<ApiResponse<number>>
  checkExist: (params: Partial<Material>) => Promise<ApiResponse<boolean>>
}

/**
 * 商品列表响应接口
 */
export interface MaterialListResponse extends PageResult<MaterialVo4Unit> {
  // 继承PageResult的所有属性
}

/**
 * 商品详情响应接口
 */
export interface MaterialDetailResponse extends ApiResponse<Material> {
  // 继承ApiResponse的所有属性
}

/**
 * 商品搜索响应接口
 */
export interface MaterialSearchResponse extends ApiResponse<MaterialSearchResult[]> {
  // 继承ApiResponse的所有属性
}

/**
 * 商品分类树节点接口
 */
export interface MaterialCategoryTreeNode extends MaterialCategory {
  children?: MaterialCategoryTreeNode[]
  level?: number
  expanded?: boolean
  selected?: boolean
}

/**
 * 商品筛选条件接口
 */
export interface MaterialFilter {
  categories: MaterialCategory[]
  brands: string[]
  manufacturers: string[]
  units: string[]
  priceRange: {
    min?: number
    max?: number
  }
  stockRange: {
    min?: number
    max?: number
  }
  enabledOnly: boolean
}

/**
 * 商品导入导出接口
 */
export interface MaterialImportExport {
  template: string           // 模板文件URL
  importFile: File          // 导入文件
  exportParams: MaterialQuery // 导出参数
}

/**
 * 商品图片上传接口
 */
export interface MaterialImageUpload {
  materialId: number
  file: File
  fileName: string
  fileSize: number
  fileType: string
}

/**
 * 商品条码生成接口
 */
export interface MaterialBarCodeGenerate {
  materialId: number
  barCodeType: 'EAN13' | 'CODE128' | 'QR'
  customCode?: string
}
