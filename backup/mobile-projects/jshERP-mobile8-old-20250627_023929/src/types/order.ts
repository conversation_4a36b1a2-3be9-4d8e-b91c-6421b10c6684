/**
 * 销售订单相关类型定义
 *
 * 基于通用业务类型，定义销售订单特定的数据结构
 */

import type {
  BaseDocumentHeader,
  BaseDetailItem,
  Customer,
  Product as BaseProduct,
  FileUpload
} from './business'

/**
 * 销售订单商品信息
 */
export interface Product extends BaseProduct {
  /** 商品规格 */
  spec?: string
  /** 商品数量 */
  quantity: number
  /** 商品库存（可选） */
  stock?: number
  /** 商品单价 */
  price: number
}

/**
 * 销售人员信息
 */
export interface Salesperson {
  id: string | number
  name: string
  code?: string
  department?: string
  phoneNumber?: string
  email?: string
  enabled?: boolean
}

/**
 * 销售订单表单数据
 */
export interface OrderForm extends BaseDocumentHeader {
  // 基础信息
  orderNo: string
  customerName: string
  customerId: string | number
  orderDate: string
  salesperson: string
  salespersonId: string | number

  // 商品信息
  products: Product[]

  // 金额信息
  subtotal: number
  discountRate: number
  discountAmount: number
  finalAmount: number

  // 结算信息
  paymentAccount?: string
  receivedAmount?: number

  // 附加信息
  remark?: string
  attachments?: FileUpload[]
}

/**
 * 销售订单明细项
 */
export interface OrderDetailItem extends BaseDetailItem {
  // 继承基础明细项，添加销售特定字段
  customerPrice?: number
  memberPrice?: number
  discountRate?: number
  originalAmount?: number
}

/**
 * 表单验证规则
 */
export interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: string
  validator?: (value: any) => boolean
}

export interface ValidationRules {
  [key: string]: ValidationRule[]
}

/**
 * 订单操作类型
 */
export type OrderAction =
  | 'create'
  | 'edit'
  | 'view'
  | 'copy'
  | 'delete'
  | 'submit'
  | 'approve'
  | 'reject'
  | 'cancel'

/**
 * 客户信息接口（向后兼容）
 */
export interface Customer {
  /** 客户ID */
  id: string
  /** 客户名称 */
  name: string
  /** 客户编码 */
  code?: string
  /** 联系电话 */
  phone?: string
  /** 客户地址 */
  address?: string
  /** 客户类型 */
  type?: string
}

/**
 * 销售人员接口
 */
export interface Salesperson {
  /** 销售人员ID */
  id: string
  /** 销售人员姓名 */
  name: string
  /** 销售人员编码 */
  code?: string
  /** 部门 */
  department?: string
  /** 联系电话 */
  phone?: string
}

/**
 * 表单验证规则接口
 */
export interface ValidationRule {
  /** 是否必填 */
  required?: boolean
  /** 错误提示信息 */
  message?: string
  /** 自定义验证函数 */
  validator?: (value: any) => boolean | string
  /** 触发验证的时机 */
  trigger?: 'blur' | 'change'
}

/**
 * 表单验证规则集合
 */
export interface ValidationRules {
  [key: string]: ValidationRule[]
}

/**
 * 订单操作类型
 */
export enum OrderAction {
  /** 保存 */
  SAVE = 'save',
  /** 保存并提交 */
  SAVE_AND_SUBMIT = 'save_and_submit',
  /** 更新 */
  UPDATE = 'update',
  /** 删除 */
  DELETE = 'delete',
  /** 审核 */
  APPROVE = 'approve',
  /** 拒绝 */
  REJECT = 'reject'
}

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
  /** 时间戳 */
  timestamp?: number
}

/**
 * 分页参数接口
 */
export interface PaginationParams {
  /** 当前页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 总记录数 */
  total?: number
}

/**
 * 订单查询参数接口
 */
export interface OrderQueryParams extends PaginationParams {
  /** 订单编号 */
  orderNo?: string
  /** 客户名称 */
  customerName?: string
  /** 订单状态 */
  status?: OrderStatus
  /** 开始日期 */
  startDate?: string
  /** 结束日期 */
  endDate?: string
  /** 销售人员 */
  salesperson?: string
}
