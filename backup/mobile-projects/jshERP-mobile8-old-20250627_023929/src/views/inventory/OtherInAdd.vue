<!--
  其他入库添加页面
  
  基于通用业务表单组件，实现其他入库的添加功能
  包含入库类型选择、操作员选择、仓库选择、商品管理等
-->

<template>
  <div class="other-in-add">
    <!-- 页面头部 -->
    <van-nav-bar
      title="新增其他入库"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <!-- 入库编号 -->
        <van-field
          v-model="otherInForm.inNo"
          label="入库编号"
          placeholder="系统自动生成"
          readonly
          :border="false"
        />
        
        <!-- 入库日期 -->
        <van-field
          v-model="otherInForm.inDate"
          label="入库日期"
          placeholder="请选择入库日期"
          readonly
          is-link
          required
          :border="false"
          @click="showDatePicker = true"
        />
        
        <!-- 操作员 -->
        <van-field
          v-model="otherInForm.operator"
          label="操作员"
          placeholder="请选择操作员"
          readonly
          is-link
          required
          :border="false"
          @click="showOperatorPicker = true"
        />
        
        <!-- 入库仓库 -->
        <van-field
          v-model="otherInForm.warehouseName"
          label="入库仓库"
          placeholder="请选择入库仓库"
          readonly
          is-link
          required
          :border="false"
          @click="showWarehousePicker = true"
        />
      </van-cell-group>

      <!-- 入库信息 -->
      <van-cell-group title="入库信息" inset>
        <!-- 入库类型 -->
        <van-field
          label="入库类型"
          :border="false"
        >
          <template #input>
            <van-radio-group
              v-model="otherInForm.inType"
              direction="horizontal"
              @change="handleInTypeSelect"
            >
              <van-radio name="production">生产入库</van-radio>
              <van-radio name="transfer">调拨入库</van-radio>
              <van-radio name="inventory_gain">盘盈入库</van-radio>
              <van-radio name="other">其他入库</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        
        <!-- 来源单据 -->
        <van-field
          v-model="otherInForm.sourceDocument"
          label="来源单据"
          placeholder="请选择来源单据（可选）"
          readonly
          is-link
          :border="false"
          @click="showSourceDocumentPicker = true"
        />
      </van-cell-group>

      <!-- 商品信息 -->
      <van-cell-group title="入库商品" inset>
        <!-- 商品列表 -->
        <div v-if="otherInForm.products.length > 0" class="product-list">
          <div
            v-for="(product, index) in otherInForm.products"
            :key="index"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-code" v-if="product.code">编码: {{ product.code }}</div>
              <div class="product-spec" v-if="product.spec">规格: {{ product.spec }}</div>
              
              <!-- 库存信息 -->
              <div class="stock-info">
                <span class="current-stock">当前库存: {{ product.currentStock }}</span>
                <span class="available-stock">可用: {{ product.availableStock }}</span>
              </div>
              
              <!-- 库位信息 -->
              <div class="location-info" v-if="product.locationCode">
                <van-tag type="primary" size="small">{{ product.locationCode }}</van-tag>
              </div>
            </div>
            
            <div class="product-controls">
              <div class="quantity-control">
                <van-stepper
                  :model-value="getProductQuantity(index)"
                  min="1"
                  @change="handleInQuantityChange(index, $event)"
                />
              </div>
              
              <div class="price-control">
                <van-field
                  :model-value="getProductPrice(index)"
                  type="number"
                  placeholder="入库单价"
                  @blur="handleInPriceChange(index, Number($event.target.value))"
                />
              </div>
              
              <div class="amount">
                ¥{{ formatCurrency(getProductPrice(index) * getProductQuantity(index)) }}
              </div>
              
              <!-- 库位按钮 -->
              <van-button
                size="mini"
                type="primary"
                @click="handleLocationEdit(index)"
              >
                库位
              </van-button>
              
              <van-icon
                name="delete-o"
                class="delete-btn"
                @click="removeInProduct(index)"
              />
            </div>
          </div>
        </div>
        
        <!-- 添加商品按钮 -->
        <van-cell
          title="添加入库商品"
          is-link
          :border="false"
          @click="handleAddProduct"
        >
          <template #icon>
            <van-icon name="plus" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 汇总信息 -->
      <van-cell-group title="汇总信息" inset>
        <!-- 入库总数量 -->
        <van-field
          :model-value="totalInQuantity.toString()"
          label="入库总数量"
          readonly
          :border="false"
        />
        
        <!-- 入库总金额 -->
        <van-field
          :model-value="formatCurrency(totalInAmount)"
          label="入库总金额"
          readonly
          class="total-amount"
          :border="false"
        />
      </van-cell-group>

      <!-- 其他信息 -->
      <van-cell-group title="其他信息" inset>
        <!-- 备注 -->
        <van-field
          v-model="otherInForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入入库备注"
          rows="3"
          autosize
          :border="false"
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="default"
        size="large"
        @click="handleSaveDraft"
        :loading="loading"
      >
        保存草稿
      </van-button>
      
      <van-button
        type="primary"
        size="large"
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!isFormValid"
      >
        确认入库
      </van-button>
    </div>

    <!-- 操作员选择器 -->
    <van-popup
      v-model:show="showOperatorPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <OperatorPicker @select="handleOperatorSelect" />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="dateValue"
        title="选择入库日期"
        @confirm="handleDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 仓库选择器 -->
    <van-popup
      v-model:show="showWarehousePicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <WarehousePicker @select="handleWarehouseSelect" />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup
      v-model:show="showProductPicker"
      position="bottom"
      :style="{ height: '80%' }"
    >
      <InventoryProductPicker @select="handleProductSelect" />
    </van-popup>

    <!-- 来源单据选择器 -->
    <van-popup
      v-model:show="showSourceDocumentPicker"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <SourceDocumentPicker @select="handleSourceDocumentSelect" />
    </van-popup>

    <!-- 库位编辑弹窗 -->
    <van-popup
      v-model:show="showLocationDialog"
      position="center"
      :style="{ width: '90%', borderRadius: '12px' }"
    >
      <div class="location-dialog">
        <div class="dialog-header">
          <h3>编辑库位</h3>
          <van-icon name="cross" @click="showLocationDialog = false" />
        </div>
        
        <div class="dialog-content">
          <div class="product-info">
            <div class="product-name">{{ currentLocationProduct?.name }}</div>
            <div class="product-code">{{ currentLocationProduct?.code }}</div>
          </div>
          
          <van-field
            v-model="currentLocation"
            label="库位编码"
            placeholder="请输入库位编码"
            clearable
          />
        </div>
        
        <div class="dialog-actions">
          <van-button
            type="default"
            @click="showLocationDialog = false"
          >
            取消
          </van-button>
          
          <van-button
            type="primary"
            @click="handleLocationConfirm"
          >
            确认
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { useOtherInForm } from '@/composables/useOtherInForm'
import type { InventoryProduct, Operator, InType } from '@/types/inventory'
import type { Depot } from '@/types/business'

// 组件引入
import OperatorPicker from '@/components/picker/OperatorPicker.vue'
import WarehousePicker from '@/components/picker/WarehousePicker.vue'
import InventoryProductPicker from '@/components/picker/InventoryProductPicker.vue'
import SourceDocumentPicker from '@/components/picker/SourceDocumentPicker.vue'

// 路由
const router = useRouter()

// 表单逻辑
const {
  loading,
  submitting,
  otherInForm,
  showOperatorPicker,
  showDatePicker,
  showWarehousePicker,
  showProductPicker,
  showSourceDocumentPicker,
  dateValue,
  isFormValid,
  totalInQuantity,
  totalInAmount,
  formatCurrency,
  validateForm,
  handleDateConfirm,
  handleOperatorSelect,
  handleWarehouseSelect,
  handleInTypeSelect,
  handleSourceDocumentSelect,
  addInProduct,
  removeInProduct,
  handleInQuantityChange,
  handleInPriceChange,
  handleLocationChange,
  resetForm,
  initializeForm,
  formData
} = useOtherInForm()

// 库位编辑相关状态
const showLocationDialog = ref(false)
const currentLocationIndex = ref(-1)
const currentLocationProduct = ref<InventoryProduct | null>(null)
const currentLocation = ref('')

/**
 * 获取商品数量
 */
const getProductQuantity = (index: number): number => {
  return formData.details[index]?.basicNumber || 1
}

/**
 * 获取商品单价
 */
const getProductPrice = (index: number): number => {
  return formData.details[index]?.unitPrice || 0
}

/**
 * 返回处理
 */
const handleBack = async () => {
  // 检查是否有未保存的更改
  if (otherInForm.value.products.length > 0) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的更改，确定要离开吗？'
      })
    } catch {
      return
    }
  }
  
  router.back()
}

/**
 * 添加商品处理
 */
const handleAddProduct = () => {
  if (!otherInForm.value.warehouseName) {
    showToast({ type: 'fail', message: '请先选择入库仓库' })
    return
  }
  
  showProductPicker.value = true
}

/**
 * 商品选择处理
 */
const handleProductSelect = (product: InventoryProduct) => {
  addInProduct(product)
  showProductPicker.value = false
  showToast({ type: 'success', message: '入库商品已添加' })
}

/**
 * 库位编辑处理
 */
const handleLocationEdit = (index: number) => {
  const product = otherInForm.value.products[index]
  currentLocationIndex.value = index
  currentLocationProduct.value = product
  currentLocation.value = product.locationCode || ''
  showLocationDialog.value = true
}

/**
 * 库位确认
 */
const handleLocationConfirm = () => {
  if (currentLocationIndex.value >= 0) {
    handleLocationChange(currentLocationIndex.value, currentLocation.value)
  }
  
  showLocationDialog.value = false
  showToast({ type: 'success', message: '库位已保存' })
}

/**
 * 保存草稿
 */
const handleSaveDraft = async () => {
  try {
    loading.value = true
    
    // 这里调用API保存草稿
    // await saveOtherInDraft(otherInForm.value)
    
    showToast({ type: 'success', message: '草稿已保存' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 确认入库
 */
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  try {
    submitting.value = true
    
    // 这里调用API确认入库
    // await submitOtherIn(otherInForm.value)
    
    showToast({ type: 'success', message: '入库成功' })
    
    // 返回列表页
    router.replace('/inventory/other-in/list')
  } catch (error) {
    showToast({ type: 'fail', message: '入库失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  initializeForm()
})
</script>

<style lang="less" scoped>
.other-in-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .product-list {
    .product-item {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid #ebedf0;

      &:last-child {
        border-bottom: none;
      }

      .product-info {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-code,
        .product-spec {
          font-size: 12px;
          color: #969799;
          margin-bottom: 4px;
        }

        .stock-info {
          display: flex;
          gap: 12px;
          margin-bottom: 6px;

          .current-stock,
          .available-stock {
            font-size: 11px;
            color: #646566;
            background-color: #f2f3f5;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }

        .location-info {
          .van-tag {
            font-size: 10px;
          }
        }
      }

      .product-controls {
        display: flex;
        align-items: center;
        gap: 6px;
        flex-wrap: wrap;

        .quantity-control {
          .van-stepper {
            --van-stepper-button-width: 22px;
            --van-stepper-button-height: 22px;
            --van-stepper-input-width: 32px;
            --van-stepper-input-height: 22px;
          }
        }

        .price-control {
          width: 65px;

          .van-field {
            padding: 0;

            :deep(.van-field__control) {
              text-align: center;
              font-size: 10px;
            }
          }
        }

        .amount {
          font-size: 11px;
          font-weight: 500;
          color: #07c160;
          min-width: 45px;
          text-align: right;
        }

        .van-button {
          --van-button-mini-height: 22px;
          --van-button-mini-padding: 0 6px;
          --van-button-mini-font-size: 9px;
        }

        .delete-btn {
          color: #ee0a24;
          font-size: 14px;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }

  .total-amount {
    :deep(.van-field__label) {
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-field__control) {
      font-weight: 600;
      color: #07c160;
      font-size: 16px;
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    gap: 12px;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-weight: 500;

      &--default {
        background-color: #f7f8fa;
        border-color: #f7f8fa;
        color: #646566;
      }

      &--primary {
        background: linear-gradient(135deg, #07c160 0%, #00a854 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(7, 193, 96, 0.4);

        &:active {
          transform: translateY(1px);
          box-shadow: 0 2px 8px rgba(7, 193, 96, 0.4);
        }

        &:disabled {
          background: #c8c9cc;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }

  .location-dialog {
    padding: 20px;

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #323233;
      }

      .van-icon {
        font-size: 18px;
        color: #969799;
        cursor: pointer;
      }
    }

    .dialog-content {
      margin-bottom: 20px;

      .product-info {
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 8px;
        margin-bottom: 16px;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-code {
          font-size: 12px;
          color: #969799;
        }
      }

      .van-field {
        margin-bottom: 12px;
      }
    }

    .dialog-actions {
      display: flex;
      gap: 12px;

      .van-button {
        flex: 1;
        height: 40px;
        border-radius: 20px;
      }
    }
  }

  // 弹窗样式优化
  :deep(.van-popup) {
    border-radius: 16px 16px 0 0;
  }

  :deep(.van-picker) {
    background-color: #fff;
  }

  :deep(.van-picker__toolbar) {
    border-bottom: 1px solid #ebedf0;
  }

  :deep(.van-radio-group) {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .other-in-add {
    .product-list {
      .product-item {
        .product-controls {
          gap: 4px;

          .price-control {
            width: 55px;
          }

          .amount {
            min-width: 40px;
            font-size: 10px;
          }

          .van-button {
            --van-button-mini-padding: 0 4px;
          }
        }
      }
    }
  }
}
</style>
