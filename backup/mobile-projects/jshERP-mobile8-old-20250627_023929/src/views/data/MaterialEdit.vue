<template>
  <div class="material-edit">
    <!-- 导航栏 -->
    <van-nav-bar
      title="编辑商品"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 开发中提示 -->
    <div class="development-notice">
      <van-empty
        image="search"
        description="编辑商品功能开发中..."
      >
        <van-button 
          type="primary" 
          size="small" 
          @click="handleBack"
        >
          返回详情
        </van-button>
      </van-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

/**
 * 返回上一页
 */
const handleBack = () => {
  router.back()
}
</script>

<style scoped lang="less">
.material-edit {
  background-color: #f7f8fa;
  min-height: 100vh;

  .development-notice {
    padding: 60px 20px;
  }
}
</style>
