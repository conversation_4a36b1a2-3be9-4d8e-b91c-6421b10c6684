<!--
  ERP统计数据面板组件
  
  用于首页显示详细的统计数据，包括昨日、本月、今年和汇总数据
-->
<template>
  <div class="erp-stat-panel">
    <!-- 昨日数据 -->
    <div class="erp-stat-panel__section">
      <h3 class="erp-stat-panel__title">昨日</h3>
      <div class="erp-stat-panel__row">
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">销售</span>
          <span class="erp-stat-panel__value">{{ data.yesterday.sales }}</span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">零售</span>
          <span class="erp-stat-panel__value">{{ data.yesterday.retail }}</span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">采购</span>
          <span class="erp-stat-panel__value">{{ data.yesterday.purchase }}</span>
        </div>
      </div>
    </div>
    
    <!-- 本月数据 -->
    <div class="erp-stat-panel__section">
      <h3 class="erp-stat-panel__title">本月</h3>
      <div class="erp-stat-panel__row">
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">销售</span>
          <span class="erp-stat-panel__value">{{ data.monthly.sales }}</span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">零售</span>
          <span class="erp-stat-panel__value">{{ data.monthly.retail }}</span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">采购</span>
          <span class="erp-stat-panel__value">{{ data.monthly.purchase }}</span>
        </div>
      </div>
    </div>
    
    <!-- 今年数据 -->
    <div class="erp-stat-panel__section">
      <h3 class="erp-stat-panel__title">今年</h3>
      <div class="erp-stat-panel__row">
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">销售</span>
          <span class="erp-stat-panel__value">{{ data.yearly.sales }}</span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">零售</span>
          <span class="erp-stat-panel__value">{{ data.yearly.retail }}</span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">采购</span>
          <span class="erp-stat-panel__value">{{ data.yearly.purchase }}</span>
        </div>
      </div>
    </div>
    
    <!-- 汇总数据 -->
    <div class="erp-stat-panel__section erp-stat-panel__section--summary">
      <h3 class="erp-stat-panel__title">汇总</h3>
      <div class="erp-stat-panel__row">
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">库存总金额</span>
          <span class="erp-stat-panel__value erp-stat-panel__value--currency">
            ¥{{ formatNumber(data.summary.inventoryTotal) }}
          </span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">账户总金额</span>
          <span class="erp-stat-panel__value erp-stat-panel__value--currency">
            ¥{{ formatNumber(data.summary.accountTotal) }}
          </span>
        </div>
        <div class="erp-stat-panel__item">
          <span class="erp-stat-panel__label">今年总利润</span>
          <span class="erp-stat-panel__value erp-stat-panel__value--currency erp-stat-panel__value--profit">
            ¥{{ formatNumber(data.summary.yearlyProfit) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 统计数据接口
 */
interface StatisticsData {
  yesterday: {
    sales: number
    retail: number
    purchase: number
  }
  monthly: {
    sales: number
    retail: number
    purchase: number
  }
  yearly: {
    sales: number
    retail: number
    purchase: number
  }
  summary: {
    inventoryTotal: number
    accountTotal: number
    yearlyProfit: number
  }
}

/**
 * 组件属性接口
 */
interface Props {
  /** 统计数据 */
  data: StatisticsData
}

const props = defineProps<Props>()

/**
 * 格式化数字显示
 */
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-stat-panel {
  background: var(--erp-bg-card);
  border-radius: var(--erp-radius-lg);
  box-shadow: var(--erp-shadow-card);
  margin: var(--erp-spacing-md);
  overflow: hidden;
  
  &__section {
    padding: var(--erp-spacing-md);
    border-bottom: 1px solid var(--erp-border-light);
    
    &:last-child {
      border-bottom: none;
    }
    
    &--summary {
      background: var(--erp-bg-tertiary);
    }
  }
  
  &__title {
    font-size: var(--erp-font-size-md);
    font-weight: var(--erp-font-weight-semibold);
    color: var(--erp-text-primary);
    margin: 0 0 var(--erp-spacing-sm) 0;
  }
  
  &__row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--erp-spacing-sm);
  }
  
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  &__label {
    font-size: var(--erp-font-size-xs);
    color: var(--erp-text-secondary);
    margin-bottom: var(--erp-spacing-xs);
    line-height: var(--erp-line-height-normal);
  }
  
  &__value {
    font-size: var(--erp-font-size-md);
    font-weight: var(--erp-font-weight-semibold);
    color: var(--erp-text-primary);
    line-height: var(--erp-line-height-tight);
    
    &--currency {
      color: var(--erp-primary);
    }
    
    &--profit {
      color: var(--erp-success);
    }
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .erp-stat-panel {
    margin: var(--erp-spacing-sm);
    
    &__section {
      padding: var(--erp-spacing-sm);
    }
    
    &__label {
      font-size: 10px;
    }
    
    &__value {
      font-size: var(--erp-font-size-sm);
    }
  }
}
</style>
