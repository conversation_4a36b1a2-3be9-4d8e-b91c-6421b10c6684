<!--
  ERP菜单列表组件
  
  用于个人中心页面显示功能菜单列表
-->
<template>
  <div class="erp-menu-list">
    <div 
      v-for="(item, index) in items" 
      :key="item.id"
      class="erp-menu-list__item"
      :class="{ 'erp-menu-list__item--danger': item.danger }"
      @click="handleItemClick(item)"
    >
      <div class="erp-menu-list__icon">
        <van-icon :name="item.icon" />
      </div>
      
      <div class="erp-menu-list__content">
        <div class="erp-menu-list__label">{{ item.label }}</div>
        <div v-if="item.description" class="erp-menu-list__description">
          {{ item.description }}
        </div>
      </div>
      
      <div class="erp-menu-list__arrow">
        <van-icon name="arrow" />
      </div>
      
      <!-- 分割线 -->
      <div v-if="index < items.length - 1" class="erp-menu-list__divider"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 菜单项接口
 */
interface MenuItem {
  id: string
  icon: string
  label: string
  description?: string
  danger?: boolean
  path?: string
}

/**
 * 组件属性接口
 */
interface Props {
  /** 菜单项列表 */
  items: MenuItem[]
}

/**
 * 组件事件接口
 */
interface Emits {
  /** 菜单项点击事件 */
  itemClick: [item: MenuItem]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

/**
 * 处理菜单项点击
 */
const handleItemClick = (item: MenuItem): void => {
  emit('itemClick', item)
}
</script>

<style scoped lang="less">
@import '@/styles/variables.less';

.erp-menu-list {
  background: var(--erp-bg-card);
  border-radius: var(--erp-radius-lg);
  box-shadow: var(--erp-shadow-card);
  margin: var(--erp-spacing-md);
  overflow: hidden;
  
  &__item {
    position: relative;
    display: flex;
    align-items: center;
    padding: var(--erp-spacing-md);
    cursor: pointer;
    transition: all var(--erp-duration-fast) var(--erp-ease-out);
    
    &:hover {
      background: var(--erp-bg-tertiary);
    }
    
    &:active {
      background: var(--erp-border-light);
    }
    
    &--danger {
      .erp-menu-list__label {
        color: var(--erp-error);
      }
      
      .erp-menu-list__icon {
        color: var(--erp-error);
      }
    }
  }
  
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: var(--erp-spacing-md);
    color: var(--erp-text-secondary);
    font-size: 18px;
  }
  
  &__content {
    flex: 1;
    min-width: 0;
  }
  
  &__label {
    font-size: var(--erp-font-size-md);
    font-weight: var(--erp-font-weight-medium);
    color: var(--erp-text-primary);
    line-height: var(--erp-line-height-normal);
    margin-bottom: var(--erp-spacing-xs);
  }
  
  &__description {
    font-size: var(--erp-font-size-sm);
    color: var(--erp-text-secondary);
    line-height: var(--erp-line-height-normal);
  }
  
  &__arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    color: var(--erp-text-tertiary);
    font-size: 12px;
    margin-left: var(--erp-spacing-sm);
  }
  
  &__divider {
    position: absolute;
    bottom: 0;
    left: var(--erp-spacing-md);
    right: var(--erp-spacing-md);
    height: 1px;
    background: var(--erp-border-light);
  }
}

// 响应式适配
@media (max-width: @erp-mobile-xs) {
  .erp-menu-list {
    margin: var(--erp-spacing-sm);
    
    &__item {
      padding: var(--erp-spacing-sm) var(--erp-spacing-md);
    }
    
    &__icon {
      margin-right: var(--erp-spacing-sm);
    }
    
    &__label {
      font-size: var(--erp-font-size-sm);
    }
    
    &__description {
      font-size: var(--erp-font-size-xs);
    }
  }
}
</style>
