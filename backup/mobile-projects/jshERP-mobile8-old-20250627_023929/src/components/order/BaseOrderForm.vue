<!--
  基础订单表单组件
  
  提供订单表单的基础UI结构和交互逻辑
  可被新增和编辑页面复用
-->
<template>
  <div class="base-order-form">
    <!-- 基础信息 -->
    <van-cell-group title="基础信息" inset>
      <van-field
        v-model="orderForm.customerName"
        label="客户"
        placeholder="请选择客户"
        readonly
        required
        :error="!orderForm.customerName && showValidation"
        error-message="请选择客户"
        @click="showCustomerPicker = true"
      >
        <template #right-icon>
          <van-icon name="arrow" />
        </template>
      </van-field>
      
      <van-field
        v-model="orderForm.orderDate"
        label="单据日期"
        readonly
        required
        :error="!orderForm.orderDate && showValidation"
        error-message="请选择订单日期"
        @click="showDatePicker = true"
      >
        <template #right-icon>
          <van-icon name="arrow" />
        </template>
      </van-field>
      
      <van-field
        v-model="orderForm.orderNo"
        label="单据编号"
        readonly
      />
      
      <van-field
        v-model="orderForm.salesperson"
        label="销售人员"
        placeholder="请选择销售人员"
        readonly
        required
        :error="!orderForm.salesperson && showValidation"
        error-message="请选择销售人员"
        @click="showSalespersonPicker = true"
      >
        <template #right-icon>
          <van-icon name="arrow" />
        </template>
      </van-field>
    </van-cell-group>

    <!-- 商品清单 -->
    <van-cell-group title="商品清单" inset>
      <div class="product-actions">
        <van-button
          type="primary"
          size="small"
          icon="scan"
          @click="handleScanProduct"
        >
          扫描条码
        </van-button>
        <van-button
          type="default"
          size="small"
          icon="plus"
          @click="handleSelectProduct"
        >
          选择商品
        </van-button>
      </div>
      
      <!-- 商品列表为空时的提示 -->
      <div v-if="orderForm.products.length === 0" class="empty-products">
        <van-empty description="暂无商品">
          <template #image>
            <van-icon name="shopping-cart-o" size="60" color="#ddd" />
          </template>
          <div class="empty-tip">
            <p v-if="showValidation" class="error-text">请至少添加一个商品</p>
            <p class="tip-text">点击上方按钮添加商品</p>
          </div>
        </van-empty>
      </div>
      
      <!-- 商品列表 -->
      <div v-else class="product-list">
        <div
          v-for="(product, index) in orderForm.products"
          :key="product.id || index"
          class="product-item"
        >
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-spec">{{ product.spec }}</div>
            <div class="product-unit">单价: ¥{{ formatCurrency(product.price) }}</div>
          </div>
          <div class="product-quantity">
            <van-stepper
              v-model="product.quantity"
              min="1"
              :max="product.stock || 9999"
              @change="handleQuantityChange"
            />
          </div>
          <div class="product-amount">
            <div class="amount-value">¥{{ formatCurrency(product.price * product.quantity) }}</div>
          </div>
          <van-icon
            name="delete-o"
            class="delete-icon"
            @click="removeProduct(index)"
          />
        </div>
        
        <!-- 商品统计 -->
        <div class="product-summary">
          <div class="summary-item">
            <span class="label">商品种类:</span>
            <span class="value">{{ orderForm.products.length }}种</span>
          </div>
          <div class="summary-item">
            <span class="label">商品总数:</span>
            <span class="value">{{ totalQuantity }}件</span>
          </div>
        </div>
      </div>
      
      <!-- 金额汇总 -->
      <div class="amount-summary">
        <van-field
          v-model="formattedSubtotal"
          label="合税合计"
          readonly
        />
        <van-field
          v-model="orderForm.discountRate"
          label="优惠率(%)"
          type="number"
          :formatter="formatDiscountRate"
          @input="handleDiscountChange"
        />
        <van-field
          v-model="formattedDiscountAmount"
          label="收款优惠"
          readonly
        />
        <van-field
          v-model="formattedFinalAmount"
          label="优惠后金额"
          readonly
          class="final-amount"
        />
      </div>
    </van-cell-group>

    <!-- 结算信息 -->
    <van-cell-group title="结算信息" inset>
      <van-field
        v-model="orderForm.paymentAccount"
        label="结算账户"
        placeholder="请选择结算账户"
        readonly
      />
      <van-field
        v-model="orderForm.receivedAmount"
        label="收取订金"
        type="number"
        placeholder="0"
        :formatter="formatAmount"
      />
    </van-cell-group>

    <!-- 备注 -->
    <van-cell-group title="备注" inset>
      <van-field
        v-model="orderForm.remark"
        type="textarea"
        placeholder="请输入备注信息（可选）"
        rows="3"
        maxlength="200"
        show-word-limit
      />
    </van-cell-group>

    <!-- 附件信息 -->
    <van-cell-group title="附件信息" inset>
      <div class="attachment-info">
        <div class="attachment-tip">
          <van-icon name="info-o" />
          图片最多4张，单张大小不超过1M
        </div>
        <van-uploader
          v-model="orderForm.attachments"
          multiple
          :max-count="4"
          :max-size="1024 * 1024"
          upload-icon="plus"
          upload-text="上传图片"
          @oversize="handleOversizeFile"
        />
        <div class="attachment-note">
          <van-icon name="warning-o" />
          提醒：上传word等非图片文件请到电脑端操作
        </div>
      </div>
    </van-cell-group>

    <!-- 日期选择器 -->
    <van-date-picker
      v-model:show="showDatePicker"
      v-model="dateValue"
      title="选择日期"
      @confirm="handleDateConfirm"
    />

    <!-- 客户选择器 -->
    <van-popup v-model:show="showCustomerPicker" position="bottom">
      <div class="picker-header">
        <van-button type="default" @click="showCustomerPicker = false">取消</van-button>
        <span class="picker-title">选择客户</span>
        <van-button type="primary" @click="showCustomerPicker = false">确定</van-button>
      </div>
      <div class="picker-content">
        <van-empty description="客户选择功能开发中" />
      </div>
    </van-popup>

    <!-- 销售人员选择器 -->
    <van-popup v-model:show="showSalespersonPicker" position="bottom">
      <div class="picker-header">
        <van-button type="default" @click="showSalespersonPicker = false">取消</van-button>
        <span class="picker-title">选择销售人员</span>
        <van-button type="primary" @click="showSalespersonPicker = false">确定</van-button>
      </div>
      <div class="picker-content">
        <van-empty description="销售人员选择功能开发中" />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { useOrderForm } from '@/composables/useOrderForm'
import { useAnimationOptimization, useDebounceThrottle, useBatchDOMUpdates } from '@/composables/usePerformance'
import type { OrderForm } from '@/types/order'

interface Props {
  /** 初始数据 */
  initialData?: Partial<OrderForm>
  /** 是否显示验证错误 */
  showValidation?: boolean
}

interface Emits {
  (e: 'scan-product'): void
  (e: 'select-product'): void
}

const props = withDefaults(defineProps<Props>(), {
  showValidation: false
})

const emit = defineEmits<Emits>()

// 使用订单表单Hook
const {
  orderForm,
  showCustomerPicker,
  showDatePicker,
  showSalespersonPicker,
  dateValue,
  totalQuantity,
  formatCurrency,
  handleDateConfirm,
  removeProduct,
  handleQuantityChange,
  handleDiscountChange,
  handleOversizeFile,
  initializeForm
} = useOrderForm(props.initialData)

// 使用性能优化
const { optimizeElement, removeOptimization } = useAnimationOptimization()
const { createDebounced, createThrottled } = useDebounceThrottle()
const { addUpdate } = useBatchDOMUpdates()

// 格式化显示的金额
const formattedSubtotal = computed(() => `¥${formatCurrency(orderForm.subtotal)}`)
const formattedDiscountAmount = computed(() => `¥${formatCurrency(orderForm.discountAmount)}`)
const formattedFinalAmount = computed(() => `¥${formatCurrency(orderForm.finalAmount)}`)

// 性能优化的防抖函数
const debouncedQuantityChange = createDebounced('quantityChange', handleQuantityChange, 300)
const debouncedDiscountChange = createDebounced('discountChange', handleDiscountChange, 500)
const throttledCalculateAmount = createThrottled('calculateAmount', calculateAmount, 100)

// 批量DOM更新的包装函数
const batchedRemoveProduct = (index: number) => {
  addUpdate(() => removeProduct(index))
}

/**
 * 格式化优惠率
 */
const formatDiscountRate = (value: string): string => {
  const num = parseFloat(value)
  if (isNaN(num)) return '0'
  if (num < 0) return '0'
  if (num > 100) return '100'
  return num.toString()
}

/**
 * 格式化金额输入
 */
const formatAmount = (value: string): string => {
  const num = parseFloat(value)
  if (isNaN(num) || num < 0) return '0'
  return num.toString()
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  emit('scan-product')
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  emit('select-product')
}

// 初始化表单
initializeForm(props.initialData)

// 组件挂载时的性能优化
onMounted(() => {
  // 优化表单容器动画
  const formContainer = document.querySelector('.base-order-form')
  if (formContainer) {
    optimizeElement(formContainer as HTMLElement)
  }

  // 优化产品列表动画
  const productList = document.querySelector('.product-list')
  if (productList) {
    optimizeElement(productList as HTMLElement)
  }
})

// 组件卸载时清理优化
onUnmounted(() => {
  const formContainer = document.querySelector('.base-order-form')
  if (formContainer) {
    removeOptimization(formContainer as HTMLElement)
  }

  const productList = document.querySelector('.product-list')
  if (productList) {
    removeOptimization(productList as HTMLElement)
  }
})

// 暴露给父组件的方法和数据
defineExpose({
  orderForm: toRefs(orderForm),
  validateForm: () => {
    // 这里可以添加表单验证逻辑
    return true
  },
  // 性能优化版本的方法
  debouncedQuantityChange,
  debouncedDiscountChange,
  throttledCalculateAmount,
  batchedRemoveProduct
})
</script>

<style lang="less" scoped>
// 动画定义
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.base-order-form {
  padding-bottom: 20px;
  animation: fadeIn 0.3s ease-out;
}

// 卡片组优化
:deep(.van-cell-group) {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  animation: slideInUp 0.4s ease-out;
  transition: all 0.3s ease;

  &:nth-child(2) {
    animation-delay: 0.1s;
  }

  &:nth-child(3) {
    animation-delay: 0.2s;
  }

  &:nth-child(4) {
    animation-delay: 0.3s;
  }

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  .van-cell-group__title {
    padding: 16px 16px 8px 16px;
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 16px;
      right: 16px;
      height: 2px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 1px;
    }
  }
}

.product-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 8px 16px 16px 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  animation: slideInUp 0.5s ease-out;

  .van-button {
    flex: 1;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
      animation: pulse 0.3s ease;
    }
  }
}

.empty-products {
  padding: 60px 20px;
  text-align: center;
  animation: fadeIn 0.6s ease-out 0.3s both;

  .van-empty {
    :deep(.van-empty__image) {
      width: 120px;
      height: 120px;
      animation: fadeIn 0.8s ease-out 0.5s both;
    }

    :deep(.van-empty__description) {
      color: #969799;
      font-size: 15px;
      line-height: 22px;
      margin-top: 16px;
      animation: fadeIn 0.8s ease-out 0.7s both;
    }
  }

  .empty-tip {
    margin-top: 20px;
    animation: fadeIn 0.8s ease-out 0.9s both;

    .error-text {
      color: #ee0a24;
      font-size: 14px;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .tip-text {
      color: #969799;
      font-size: 13px;
      line-height: 18px;
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    animation: slideInUp 0.4s ease-out;
    position: relative;

    &:nth-child(n) {
      animation-delay: calc(0.1s * var(--item-index, 0));
    }

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      transform: translateX(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .product-info {
    flex: 1;

    .product-name {
      font-weight: 600;
      font-size: 15px;
      margin-bottom: 6px;
      color: #323233;
      line-height: 20px;
    }

    .product-spec {
      font-size: 13px;
      color: #969799;
      margin-bottom: 4px;
      line-height: 18px;
    }

    .product-unit {
      font-size: 12px;
      color: #ff6b35;
      font-weight: 500;
    }
  }

  .product-quantity {
    margin: 0 16px;

    :deep(.van-stepper) {
      .van-stepper__button {
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          background: #1989fa;
          color: white;
          transform: scale(1.1);
        }
      }

      .van-stepper__input {
        border-radius: 6px;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  .product-amount {
    margin-right: 16px;

    .amount-value {
      font-weight: 700;
      color: #ff6b35;
      font-size: 16px;
      position: relative;

      &::before {
        content: '¥';
        font-size: 14px;
        margin-right: 2px;
      }
    }
  }

  .delete-icon {
    color: #ee0a24;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background: linear-gradient(135deg, #fee 0%, #fdd 100%);
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(238, 10, 36, 0.2);
    }

    &:active {
      transform: scale(0.95);
      animation: shake 0.5s ease-in-out;
    }
    
    &:hover {
      opacity: 0.7;
    }
  }
}

.product-summary {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 12px 12px;
  animation: slideInUp 0.4s ease-out 0.3s both;

  .summary-item {
    text-align: center;

    .label {
      color: #969799;
      font-size: 13px;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .value {
      color: #323233;
      font-weight: 600;
      font-size: 15px;

      &.total-amount {
        color: #ff6b35;
        font-weight: 700;
        font-size: 16px;
      }
    }
  }
}

.amount-summary {
  border-top: 2px solid #667eea;
  margin-top: 16px;
  padding-top: 16px;
  animation: slideInUp 0.5s ease-out 0.4s both;

  :deep(.van-field) {
    margin-bottom: 8px;

    .van-field__label {
      font-weight: 500;
      color: #646566;
      font-size: 14px;
    }

    .van-field__control {
      font-weight: 600;
      color: #323233;
      font-size: 15px;
    }
  }

  :deep(.final-amount) {
    background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
    border-radius: 8px;
    padding: 4px 8px;
    margin: 8px 0;

    .van-field__label {
      font-weight: 700;
      color: #323233;
      font-size: 15px;
    }

    .van-field__control {
      font-weight: 700;
      color: #ff6b35;
      font-size: 18px;
    }
  }
}

.attachment-info {
  padding: 16px 20px;
  animation: slideInUp 0.5s ease-out 0.5s both;

  .attachment-tip {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #1989fa;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    border-radius: 8px;
    font-weight: 500;

    .van-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  :deep(.van-uploader) {
    .van-uploader__upload {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1989fa;
        background: #f0f9ff;
      }
    }

    .van-uploader__preview {
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .attachment-note {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #ff976a;
    margin-top: 16px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);
    border-radius: 6px;
    font-weight: 500;

    .van-icon {
      margin-right: 6px;
      font-size: 14px;
    }
  }
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  
  .picker-title {
    font-weight: 500;
    font-size: 16px;
  }
}

.picker-content {
  padding: 20px;
  min-height: 200px;
}

// 响应式设计优化
@media (max-width: @erp-mobile-xs) {
  .base-order-form {
    // 小屏幕优化
    :deep(.van-cell-group) {
      margin: 8px;
      border-radius: 8px;

      .van-cell-group__title {
        font-size: 14px;
        padding: 12px 16px 8px;
      }
    }

    :deep(.van-field) {
      padding: 12px 16px;

      .van-field__label {
        font-size: 13px;
        min-width: 70px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }

    :deep(.van-button) {
      height: 40px;
      font-size: 14px;
      border-radius: 8px;
    }
  }

  .product-list {
    :deep(.van-swipe-cell) {
      .van-cell {
        padding: 12px 16px;
      }
    }

    .product-item {
      .product-info {
        .product-name {
          font-size: 14px;
        }

        .product-details {
          font-size: 12px;
        }
      }

      .product-actions {
        .quantity-input {
          width: 80px;

          :deep(.van-field__control) {
            font-size: 13px;
          }
        }

        .amount-display {
          font-size: 13px;
        }
      }
    }
  }

  .summary-section {
    :deep(.van-field) {
      padding: 10px 16px;

      .van-field__label {
        font-size: 13px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }

    :deep(.final-amount) {
      .van-field__control {
        font-size: 16px;
      }
    }
  }

  .attachment-info {
    padding: 12px 16px;

    .attachment-tip {
      font-size: 12px;
      padding: 6px 10px;
      margin-bottom: 12px;
    }

    .attachment-note {
      font-size: 11px;
      padding: 6px 10px;
      margin-top: 12px;
    }
  }
}

@media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-lg) {
  .base-order-form {
    // 中等屏幕优化
    :deep(.van-cell-group) {
      margin: 12px;
    }

    .product-list {
      .product-item {
        .product-actions {
          .quantity-input {
            width: 90px;
          }
        }
      }
    }
  }
}

@media (min-width: @erp-tablet) {
  .base-order-form {
    // 平板优化
    max-width: 600px;
    margin: 0 auto;

    :deep(.van-cell-group) {
      margin: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }

    .product-list {
      .product-item {
        .product-actions {
          .quantity-input {
            width: 100px;
          }

          .amount-display {
            min-width: 80px;
          }
        }
      }
    }

    .attachment-info {
      :deep(.van-uploader) {
        .van-uploader__upload {
          width: 120px;
          height: 120px;
        }
      }
    }
  }
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  .base-order-form {
    // 触摸设备优化
    :deep(.van-button) {
      min-height: 44px; // iOS推荐的最小触摸目标
    }

    :deep(.van-field) {
      min-height: 44px;
    }

    .product-item {
      .delete-button {
        min-width: 44px;
        min-height: 44px;
      }

      .product-actions {
        .quantity-input {
          min-height: 44px;
        }
      }
    }
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .base-order-form {
    // 高DPI屏幕优化
    :deep(.van-icon) {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .product-item {
      .product-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
    }
  }
}
</style>
