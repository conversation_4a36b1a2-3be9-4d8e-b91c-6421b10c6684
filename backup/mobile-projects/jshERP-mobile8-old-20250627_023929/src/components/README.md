# 组件库文档

## 概述

本目录包含jshERP移动端应用的所有组件，按照功能和用途进行分类。

## 目录结构

```
components/
├── base/           # 基础组件
│   └── BaseButton.vue
├── business/       # 业务组件
└── layout/         # 布局组件
```

## 基础组件

### BaseButton

基于Vant Button组件封装的基础按钮组件。

#### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| type | string | 'default' | 按钮类型：primary/success/warning/danger/default |
| size | string | 'normal' | 按钮尺寸：large/normal/small/mini |
| disabled | boolean | false | 是否禁用 |
| loading | boolean | false | 是否显示加载状态 |
| block | boolean | false | 是否为块级元素 |
| round | boolean | false | 是否为圆角按钮 |
| square | boolean | false | 是否为方形按钮 |

#### 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| click | 点击按钮时触发 | event: MouseEvent |

#### 使用示例

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <BaseButton @click="handleClick">默认按钮</BaseButton>
    
    <!-- 不同类型 -->
    <BaseButton type="primary">主要按钮</BaseButton>
    <BaseButton type="success">成功按钮</BaseButton>
    <BaseButton type="warning">警告按钮</BaseButton>
    <BaseButton type="danger">危险按钮</BaseButton>
    
    <!-- 不同尺寸 -->
    <BaseButton size="large">大号按钮</BaseButton>
    <BaseButton size="normal">普通按钮</BaseButton>
    <BaseButton size="small">小号按钮</BaseButton>
    
    <!-- 状态 -->
    <BaseButton disabled>禁用按钮</BaseButton>
    <BaseButton loading>加载中</BaseButton>
    
    <!-- 形状 -->
    <BaseButton round>圆角按钮</BaseButton>
    <BaseButton square>方形按钮</BaseButton>
    <BaseButton block>块级按钮</BaseButton>
  </div>
</template>

<script setup lang="ts">
const handleClick = (event: MouseEvent) => {
  console.log('按钮被点击', event)
}
</script>
```

## 开发指南

### 新增组件

1. 在对应的分类目录下创建组件文件
2. 使用TypeScript编写组件
3. 添加完整的属性和事件类型定义
4. 编写详细的文档注释
5. 在此文档中添加使用说明

### 组件规范

1. **命名规范**：使用PascalCase命名，基础组件以Base开头
2. **类型安全**：所有props和events都要有TypeScript类型定义
3. **文档注释**：使用JSDoc格式编写组件文档
4. **样式规范**：使用CSS变量，遵循设计系统
5. **可访问性**：考虑无障碍访问需求

### 最佳实践

1. **单一职责**：每个组件只负责一个功能
2. **可复用性**：设计时考虑在不同场景下的复用
3. **可扩展性**：通过props和slots提供扩展能力
4. **性能优化**：合理使用computed和watch
5. **错误处理**：提供友好的错误提示和降级方案
