import { BaseAdapter } from './base'
import { httpClient } from '@/utils/request'

// 销售订单数据接口
export interface SalesOrder {
  id?: number
  number?: string
  operTime?: string
  organId?: number
  organName?: string
  accountId?: number
  accountName?: string
  changeAmount?: number
  totalPrice?: number
  payType?: string
  remark?: string
  status?: string
  items?: SalesOrderItem[]
}

// 销售订单明细接口
export interface SalesOrderItem {
  id?: number
  materialId?: number
  materialName?: string
  materialModel?: string
  materialUnit?: string
  operNumber?: number
  unitPrice?: number
  taxRate?: number
  allPrice?: number
  remark?: string
}

// 创建销售订单请求
export interface CreateSalesOrderRequest {
  info: string // JSON字符串格式的主表信息
  rows: string // JSON字符串格式的明细信息
}

// 分页请求参数
export interface SalesPageRequest {
  currentPage: number
  pageSize: number
  type?: string
  beginTime?: string
  endTime?: string
  materialParam?: string
  organId?: number
  creator?: string
  depotId?: number
  accountId?: number
  salesMan?: string
  remark?: string
}

// 分页响应
export interface SalesPageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

export class SalesAdapter extends BaseAdapter<any, any> {
  protected endpoint = '/depotHead'
  
  transform(rawData: any): any {
    return rawData.result || rawData
  }
  
  /**
   * 获取销售订单列表
   */
  async getSalesOrderList(params: SalesPageRequest): Promise<SalesPageResponse<SalesOrder>> {
    const queryParams = {
      currentPage: params.currentPage,
      pageSize: params.pageSize,
      type: '销售',
      beginTime: params.beginTime,
      endTime: params.endTime,
      materialParam: params.materialParam,
      organId: params.organId,
      creator: params.creator,
      depotId: params.depotId,
      accountId: params.accountId,
      salesMan: params.salesMan,
      remark: params.remark
    }
    
    const response = await httpClient.get(`${this.endpoint}/list`, { params: queryParams }) as any
    return {
      records: response.result?.records || response.records || [],
      total: response.result?.total || response.total || 0,
      size: response.result?.size || response.size || params.pageSize,
      current: response.result?.current || response.current || params.currentPage,
      pages: response.result?.pages || response.pages || 0
    }
  }
  
  /**
   * 创建销售订单
   */
  async createSalesOrder(orderData: CreateSalesOrderRequest): Promise<any> {
    const response = await httpClient.post(`${this.endpoint}/addDepotHeadAndDetail`, orderData)
    return this.transform(response)
  }
  
  /**
   * 获取销售订单详情
   */
  async getSalesOrderDetail(id: number): Promise<SalesOrder> {
    const response = await httpClient.get(`${this.endpoint}/info`, { params: { id } }) as any
    return this.transform(response)
  }
  
  /**
   * 更新销售订单
   */
  async updateSalesOrder(orderData: CreateSalesOrderRequest): Promise<any> {
    const response = await httpClient.put(`${this.endpoint}/updateDepotHeadAndDetail`, orderData)
    return this.transform(response)
  }
  
  /**
   * 删除销售订单
   */
  async deleteSalesOrder(id: number): Promise<any> {
    const response = await httpClient.delete(`${this.endpoint}/delete`, { params: { id } })
    return this.transform(response)
  }
  
  /**
   * 批量删除销售订单
   */
  async batchDeleteSalesOrders(ids: string): Promise<any> {
    const response = await httpClient.delete(`${this.endpoint}/deleteBatch`, { params: { ids } })
    return this.transform(response)
  }
  
  /**
   * 获取销售统计数据
   */
  async getSalesStatistics(): Promise<any> {
    const response = await httpClient.get(`${this.endpoint}/getBuyAndSaleStatistics`)
    return this.transform(response)
  }
  
  /**
   * 审核销售订单
   */
  async approveSalesOrder(id: number): Promise<any> {
    const response = await httpClient.post(`${this.endpoint}/approve`, { id })
    return this.transform(response)
  }
  
  /**
   * 反审核销售订单
   */
  async unapproveSalesOrder(id: number): Promise<any> {
    const response = await httpClient.post(`${this.endpoint}/unapprove`, { id })
    return this.transform(response)
  }
}

export const salesAdapter = new SalesAdapter()
