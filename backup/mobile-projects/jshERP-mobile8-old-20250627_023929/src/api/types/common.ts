// 通用API类型定义

export interface ApiResponse<T> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: string
}

export interface PageRequest {
  current: number
  size: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

export interface BaseEntity {
  id: string
  createTime?: string
  updateTime?: string
  createUser?: string
  updateUser?: string
  deleteFlag?: string
  tenantId?: string
}