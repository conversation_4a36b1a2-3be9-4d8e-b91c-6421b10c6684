import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAdapter } from '@/api/adapters/auth'

export interface UserInfo {
  id: string
  username: string
  realname: string
  avatar: string
  email?: string
  phone?: string
}

export interface LoginCredentials {
  username: string
  password: string
  captcha: string
  captchaId?: string
  rememberMe?: boolean
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  const tenantId = ref<number>(1)

  const isLoggedIn = computed(() => !!token.value)

  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      // 调用真实的登录API
      const response = await authAdapter.login(credentials)

      // 存储认证信息
      token.value = response.token
      userInfo.value = response.userInfo
      permissions.value = response.permissions
      tenantId.value = response.tenantId

      // 持久化存储 - 按照jshERP桌面端标准
      localStorage.setItem('ACCESS_TOKEN', token.value)
      localStorage.setItem('USER_INFO', JSON.stringify(userInfo.value))
      localStorage.setItem('USER_ID', userInfo.value.id)
      localStorage.setItem('USER_LOGIN_NAME', userInfo.value.username)
      localStorage.setItem('permissions', JSON.stringify(permissions.value))
      localStorage.setItem('tenant_id', tenantId.value.toString())

      // 设置axios默认headers
      setAuthHeaders()

    } catch (error: any) {
      console.error('Login failed:', error)
      throw new Error(error.message || '登录失败')
    }
  }
  
  const logout = async (): Promise<void> => {
    try {
      // 调用登出API
      await authAdapter.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除状态
      clearAuthState()
    }
  }

  const clearAuthState = (): void => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    tenantId.value = 1

    // 清除存储 - 按照jshERP桌面端标准
    localStorage.removeItem('ACCESS_TOKEN')
    localStorage.removeItem('USER_INFO')
    localStorage.removeItem('USER_ID')
    localStorage.removeItem('USER_LOGIN_NAME')
    localStorage.removeItem('permissions')
    localStorage.removeItem('tenant_id')

    // 清除axios headers
    clearAuthHeaders()
  }

  const setAuthHeaders = (): void => {
    // 这里可以设置axios默认headers，但我们在request拦截器中处理
    // 保留此方法以备将来使用
  }

  const clearAuthHeaders = (): void => {
    // 清除axios headers，在request拦截器中处理
  }

  const checkPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 初始化时从localStorage恢复状态 - 使用jshERP桌面端标准键名
  const initAuth = (): void => {
    const savedToken = localStorage.getItem('ACCESS_TOKEN')
    const savedUserInfo = localStorage.getItem('USER_INFO')
    const savedPermissions = localStorage.getItem('permissions')
    const savedTenantId = localStorage.getItem('tenant_id')

    if (savedToken && savedUserInfo) {
      token.value = savedToken
      userInfo.value = JSON.parse(savedUserInfo)
      permissions.value = savedPermissions ? JSON.parse(savedPermissions) : []
      tenantId.value = savedTenantId ? parseInt(savedTenantId) : 1

      // 设置认证headers
      setAuthHeaders()
    }
  }

  return {
    token,
    user: userInfo, // 添加user别名以保持兼容性
    userInfo,
    permissions,
    tenantId,
    isLoggedIn,
    login,
    logout,
    clearAuthState,
    checkPermission,
    initAuth
  }
})