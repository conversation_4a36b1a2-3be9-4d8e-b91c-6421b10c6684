/**
 * 性能优化 Composable
 * 
 * 提供Vue组件级别的性能优化功能
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { 
  performanceMonitor, 
  memoryLeakDetector, 
  debounce, 
  throttle, 
  optimizeAnimation, 
  removeAnimationOptimization,
  batchDOMUpdates,
  type PerformanceMetrics 
} from '@/utils/performance'

/**
 * 性能监控 Hook
 */
export function usePerformanceMonitor() {
  const metrics = ref<Partial<PerformanceMetrics>>({})
  const isMonitoring = ref(false)
  
  const startMonitoring = () => {
    isMonitoring.value = true
    performanceMonitor.startMonitoring()
  }
  
  const stopMonitoring = () => {
    isMonitoring.value = false
    performanceMonitor.reset()
  }
  
  const getMetrics = () => {
    metrics.value = performanceMonitor.getMetrics()
    return metrics.value
  }
  
  const refreshMetrics = debounce(() => {
    getMetrics()
  }, 1000)
  
  onMounted(() => {
    startMonitoring()
    // 定期更新指标
    const interval = setInterval(refreshMetrics, 5000)
    
    onUnmounted(() => {
      clearInterval(interval)
      stopMonitoring()
    })
  })
  
  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    getMetrics,
    refreshMetrics
  }
}

/**
 * 内存泄漏检测 Hook
 */
export function useMemoryLeakDetection() {
  const isDetecting = ref(false)
  const memoryUsage = ref<{
    used: number
    total: number
    limit: number
  }>({
    used: 0,
    total: 0,
    limit: 0
  })
  
  const startDetection = () => {
    isDetecting.value = true
    memoryLeakDetector.start()
  }
  
  const stopDetection = () => {
    isDetecting.value = false
    memoryLeakDetector.stop()
  }
  
  const updateMemoryUsage = () => {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory
      memoryUsage.value = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      }
    }
  }
  
  onMounted(() => {
    startDetection()
    updateMemoryUsage()
    
    const interval = setInterval(updateMemoryUsage, 10000)
    
    onUnmounted(() => {
      clearInterval(interval)
      stopDetection()
    })
  })
  
  return {
    isDetecting,
    memoryUsage,
    startDetection,
    stopDetection,
    updateMemoryUsage
  }
}

/**
 * 动画性能优化 Hook
 */
export function useAnimationOptimization() {
  const optimizedElements = new Set<HTMLElement>()
  
  const optimizeElement = (element: HTMLElement) => {
    optimizeAnimation(element)
    optimizedElements.add(element)
  }
  
  const removeOptimization = (element: HTMLElement) => {
    removeAnimationOptimization(element)
    optimizedElements.delete(element)
  }
  
  const optimizeAllElements = () => {
    optimizedElements.forEach(element => {
      optimizeAnimation(element)
    })
  }
  
  const removeAllOptimizations = () => {
    optimizedElements.forEach(element => {
      removeAnimationOptimization(element)
    })
    optimizedElements.clear()
  }
  
  onUnmounted(() => {
    removeAllOptimizations()
  })
  
  return {
    optimizeElement,
    removeOptimization,
    optimizeAllElements,
    removeAllOptimizations
  }
}

/**
 * 防抖和节流 Hook
 */
export function useDebounceThrottle() {
  const debouncedFunctions = new Map<string, Function>()
  const throttledFunctions = new Map<string, Function>()
  
  const createDebounced = <T extends (...args: any[]) => any>(
    key: string,
    func: T,
    wait: number,
    immediate = false
  ) => {
    const debouncedFunc = debounce(func, wait, immediate)
    debouncedFunctions.set(key, debouncedFunc)
    return debouncedFunc
  }
  
  const createThrottled = <T extends (...args: any[]) => any>(
    key: string,
    func: T,
    limit: number
  ) => {
    const throttledFunc = throttle(func, limit)
    throttledFunctions.set(key, throttledFunc)
    return throttledFunc
  }
  
  const getDebounced = (key: string) => {
    return debouncedFunctions.get(key)
  }
  
  const getThrottled = (key: string) => {
    return throttledFunctions.get(key)
  }
  
  const clearAll = () => {
    debouncedFunctions.clear()
    throttledFunctions.clear()
  }
  
  onUnmounted(() => {
    clearAll()
  })
  
  return {
    createDebounced,
    createThrottled,
    getDebounced,
    getThrottled,
    clearAll
  }
}

/**
 * DOM 批量更新 Hook
 */
export function useBatchDOMUpdates() {
  const pendingUpdates = ref<(() => void)[]>([])
  const isUpdating = ref(false)
  
  const addUpdate = (updateFunc: () => void) => {
    pendingUpdates.value.push(updateFunc)
    scheduleUpdate()
  }
  
  const scheduleUpdate = debounce(() => {
    if (pendingUpdates.value.length > 0 && !isUpdating.value) {
      isUpdating.value = true
      
      batchDOMUpdates(() => {
        pendingUpdates.value.forEach(update => update())
        pendingUpdates.value = []
        isUpdating.value = false
      })
    }
  }, 16) // 约60fps
  
  const flushUpdates = () => {
    if (pendingUpdates.value.length > 0) {
      pendingUpdates.value.forEach(update => update())
      pendingUpdates.value = []
      isUpdating.value = false
    }
  }
  
  return {
    addUpdate,
    flushUpdates,
    isUpdating,
    pendingUpdatesCount: () => pendingUpdates.value.length
  }
}

/**
 * 图片懒加载 Hook
 */
export function useLazyImages() {
  const loadedImages = new Set<string>()
  const loadingImages = new Set<string>()
  const failedImages = new Set<string>()
  
  const loadImage = (src: string): Promise<void> => {
    if (loadedImages.has(src)) {
      return Promise.resolve()
    }
    
    if (loadingImages.has(src)) {
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (loadedImages.has(src)) {
            resolve()
          } else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
      })
    }
    
    loadingImages.add(src)
    
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        loadedImages.add(src)
        loadingImages.delete(src)
        resolve()
      }
      img.onerror = () => {
        failedImages.add(src)
        loadingImages.delete(src)
        reject(new Error(`Failed to load image: ${src}`))
      }
      img.src = src
    })
  }
  
  const preloadImages = (srcs: string[]) => {
    return Promise.allSettled(srcs.map(src => loadImage(src)))
  }
  
  const isImageLoaded = (src: string) => loadedImages.has(src)
  const isImageLoading = (src: string) => loadingImages.has(src)
  const isImageFailed = (src: string) => failedImages.has(src)
  
  const clearCache = () => {
    loadedImages.clear()
    loadingImages.clear()
    failedImages.clear()
  }
  
  onUnmounted(() => {
    clearCache()
  })
  
  return {
    loadImage,
    preloadImages,
    isImageLoaded,
    isImageLoading,
    isImageFailed,
    clearCache,
    stats: {
      loaded: () => loadedImages.size,
      loading: () => loadingImages.size,
      failed: () => failedImages.size
    }
  }
}

/**
 * 组件性能分析 Hook
 */
export function useComponentPerformance(componentName: string) {
  const renderTime = ref(0)
  const mountTime = ref(0)
  const updateCount = ref(0)
  
  let mountStartTime = 0
  let renderStartTime = 0
  
  const startRender = () => {
    renderStartTime = performance.now()
  }
  
  const endRender = () => {
    if (renderStartTime > 0) {
      renderTime.value = performance.now() - renderStartTime
      updateCount.value++
      renderStartTime = 0
    }
  }
  
  const logPerformance = () => {
    console.log(`Component ${componentName} Performance:`, {
      mountTime: mountTime.value,
      lastRenderTime: renderTime.value,
      updateCount: updateCount.value,
      averageRenderTime: renderTime.value / Math.max(updateCount.value, 1)
    })
  }
  
  onMounted(() => {
    mountStartTime = performance.now()
    
    nextTick(() => {
      mountTime.value = performance.now() - mountStartTime
    })
  })
  
  return {
    renderTime,
    mountTime,
    updateCount,
    startRender,
    endRender,
    logPerformance
  }
}
