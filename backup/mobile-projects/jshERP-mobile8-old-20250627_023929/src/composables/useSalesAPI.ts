/**
 * 销售订单API集成Hook
 * 
 * 提供销售订单的完整CRUD操作，包括：
 * - 获取销售订单列表
 * - 创建销售订单
 * - 更新销售订单
 * - 删除销售订单
 * - 获取销售统计
 */

import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { 
  salesAdapter, 
  type SalesOrder, 
  type SalesOrderItem, 
  type CreateSalesOrderRequest,
  type SalesPageRequest,
  type SalesPageResponse
} from '@/api/adapters/sales'

export function useSalesAPI() {
  // 响应式数据
  const loading = ref<boolean>(false)
  const submitting = ref<boolean>(false)
  const salesOrders = ref<SalesOrder[]>([])
  const currentOrder = ref<SalesOrder | null>(null)
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0
  })

  /**
   * 获取销售订单列表
   */
  const getSalesOrderList = async (params?: Partial<SalesPageRequest>): Promise<void> => {
    try {
      loading.value = true
      
      const requestParams: SalesPageRequest = {
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
        ...params
      }
      
      const response = await salesAdapter.getSalesOrderList(requestParams)
      
      salesOrders.value = response.records
      pagination.total = response.total
      pagination.current = response.current
      
    } catch (error) {
      console.error('获取销售订单列表失败:', error)
      showToast({ type: 'fail', message: '获取销售订单列表失败' })
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建销售订单
   */
  const createSalesOrder = async (orderData: any): Promise<boolean> => {
    try {
      submitting.value = true
      
      // 构造主表信息
      const mainInfo = {
        number: orderData.orderNo,
        operTime: orderData.orderDate,
        organId: orderData.customerId,
        accountId: 1, // 默认账户
        changeAmount: orderData.finalAmount,
        totalPrice: orderData.subtotal,
        payType: orderData.paymentAccount,
        remark: orderData.remark,
        type: '销售',
        subType: '销售订单',
        status: '0' // 未审核
      }
      
      // 构造明细信息
      const detailItems = orderData.products.map((product: any, index: number) => ({
        id: index + 1,
        materialId: product.id,
        operNumber: product.quantity,
        unitPrice: product.price,
        taxRate: 0,
        allPrice: product.price * product.quantity,
        remark: product.remark || ''
      }))
      
      const requestData: CreateSalesOrderRequest = {
        info: JSON.stringify(mainInfo),
        rows: JSON.stringify(detailItems)
      }
      
      await salesAdapter.createSalesOrder(requestData)
      
      showToast({ type: 'success', message: '销售订单创建成功' })
      
      // 刷新列表
      await getSalesOrderList()
      
      return true
      
    } catch (error) {
      console.error('创建销售订单失败:', error)
      showToast({ type: 'fail', message: '创建销售订单失败' })
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 获取销售订单详情
   */
  const getSalesOrderDetail = async (id: number): Promise<SalesOrder | null> => {
    try {
      loading.value = true
      
      const order = await salesAdapter.getSalesOrderDetail(id)
      currentOrder.value = order
      
      return order
      
    } catch (error) {
      console.error('获取销售订单详情失败:', error)
      showToast({ type: 'fail', message: '获取销售订单详情失败' })
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新销售订单
   */
  const updateSalesOrder = async (orderData: any): Promise<boolean> => {
    try {
      submitting.value = true
      
      // 构造更新数据（与创建类似）
      const mainInfo = {
        id: orderData.id,
        number: orderData.orderNo,
        operTime: orderData.orderDate,
        organId: orderData.customerId,
        accountId: 1,
        changeAmount: orderData.finalAmount,
        totalPrice: orderData.subtotal,
        payType: orderData.paymentAccount,
        remark: orderData.remark,
        type: '销售',
        subType: '销售订单'
      }
      
      const detailItems = orderData.products.map((product: any, index: number) => ({
        id: product.detailId || index + 1,
        materialId: product.id,
        operNumber: product.quantity,
        unitPrice: product.price,
        taxRate: 0,
        allPrice: product.price * product.quantity,
        remark: product.remark || ''
      }))
      
      const requestData: CreateSalesOrderRequest = {
        info: JSON.stringify(mainInfo),
        rows: JSON.stringify(detailItems)
      }
      
      await salesAdapter.updateSalesOrder(requestData)
      
      showToast({ type: 'success', message: '销售订单更新成功' })
      
      // 刷新列表
      await getSalesOrderList()
      
      return true
      
    } catch (error) {
      console.error('更新销售订单失败:', error)
      showToast({ type: 'fail', message: '更新销售订单失败' })
      return false
    } finally {
      submitting.value = false
    }
  }

  /**
   * 删除销售订单
   */
  const deleteSalesOrder = async (id: number): Promise<boolean> => {
    try {
      loading.value = true
      
      await salesAdapter.deleteSalesOrder(id)
      
      showToast({ type: 'success', message: '销售订单删除成功' })
      
      // 刷新列表
      await getSalesOrderList()
      
      return true
      
    } catch (error) {
      console.error('删除销售订单失败:', error)
      showToast({ type: 'fail', message: '删除销售订单失败' })
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除销售订单
   */
  const batchDeleteSalesOrders = async (ids: number[]): Promise<boolean> => {
    try {
      loading.value = true
      
      const idsString = ids.join(',')
      await salesAdapter.batchDeleteSalesOrders(idsString)
      
      showToast({ type: 'success', message: '批量删除成功' })
      
      // 刷新列表
      await getSalesOrderList()
      
      return true
      
    } catch (error) {
      console.error('批量删除失败:', error)
      showToast({ type: 'fail', message: '批量删除失败' })
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取销售统计
   */
  const getSalesStatistics = async (): Promise<any> => {
    try {
      const statistics = await salesAdapter.getSalesStatistics()
      return statistics
    } catch (error) {
      console.error('获取销售统计失败:', error)
      showToast({ type: 'fail', message: '获取销售统计失败' })
      return null
    }
  }

  /**
   * 审核销售订单
   */
  const approveSalesOrder = async (id: number): Promise<boolean> => {
    try {
      loading.value = true
      
      await salesAdapter.approveSalesOrder(id)
      
      showToast({ type: 'success', message: '审核成功' })
      
      // 刷新列表
      await getSalesOrderList()
      
      return true
      
    } catch (error) {
      console.error('审核失败:', error)
      showToast({ type: 'fail', message: '审核失败' })
      return false
    } finally {
      loading.value = false
    }
  }

  /**
   * 分页处理
   */
  const handlePageChange = (page: number): void => {
    pagination.current = page
    getSalesOrderList()
  }

  /**
   * 重置分页
   */
  const resetPagination = (): void => {
    pagination.current = 1
    pagination.total = 0
  }

  return {
    // 响应式数据
    loading,
    submitting,
    salesOrders,
    currentOrder,
    pagination,
    
    // 方法
    getSalesOrderList,
    createSalesOrder,
    getSalesOrderDetail,
    updateSalesOrder,
    deleteSalesOrder,
    batchDeleteSalesOrders,
    getSalesStatistics,
    approveSalesOrder,
    handlePageChange,
    resetPagination
  }
}
