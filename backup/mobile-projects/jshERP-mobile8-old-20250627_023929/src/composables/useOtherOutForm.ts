/**
 * 其他出库表单业务逻辑Hook
 * 
 * 基于通用业务表单逻辑，扩展其他出库特定功能：
 * - 出库类型管理
 * - 操作员选择
 * - 仓库选择
 * - 库存验证
 * - 商品出库管理
 * - 金额计算
 */

import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from '@/composables/business/useBusinessForm'
import { DocumentType } from '@/types/business'
import type { 
  OtherOutForm, 
  InventoryProduct, 
  Operator,
  OutType
} from '@/types/inventory'
import type { FormConfig } from '@/types/business'
import type { Depot } from '@/types/business'

/**
 * 库存警告信息
 */
interface StockWarning {
  productId: string | number
  productName: string
  requestedQuantity: number
  availableQuantity: number
  message: string
}

/**
 * 其他出库表单Hook
 */
export function useOtherOutForm(initialData?: Partial<OtherOutForm>) {
  // 其他出库配置
  const otherOutConfig: FormConfig = {
    formType: DocumentType.OTHER_OUT,
    showPartnerSelector: false,
    partnerType: '',
    showDepotSelector: true,
    showAccountSelector: false,
    showLinkDocument: true,
    editable: true,
    defaultValues: {
      number: '',
      operTime: new Date().toISOString().slice(0, 16),
      ...initialData
    }
  }

  // 使用通用业务表单逻辑
  const businessForm = useBusinessForm(otherOutConfig)
  
  // 选择器显示状态
  const showOperatorPicker = ref<boolean>(false)
  const showDatePicker = ref<boolean>(false)
  const showWarehousePicker = ref<boolean>(false)
  const showProductPicker = ref<boolean>(false)
  const showTargetDocumentPicker = ref<boolean>(false)
  const dateValue = ref<string[]>([])
  
  // 库存验证状态
  const stockWarnings = ref<StockWarning[]>([])
  
  // 其他出库特定的计算属性
  const otherOutForm = computed(() => ({
    // 映射通用字段到其他出库字段
    outNo: businessForm.formData.number,
    outDate: businessForm.formData.operTime,
    operator: businessForm.formData.otherField1 || '',
    operatorId: businessForm.formData.otherField2 || '',
    
    // 仓库信息
    warehouseId: businessForm.formData.depotId || '',
    warehouseName: businessForm.formData.depotName || '',
    
    // 出库信息
    outType: businessForm.formData.otherField3 as OutType || 'other',
    targetDocument: businessForm.formData.linkNumber || '',
    targetDocumentId: businessForm.formData.linkId || '',
    
    // 商品信息
    products: businessForm.formData.details.map(detail => ({
      id: detail.productId,
      name: detail.productName,
      code: detail.otherField1 || '',
      spec: detail.otherField2 || '',
      unit: detail.otherField3 || '个',
      currentStock: detail.otherField4 ? Number(detail.otherField4) : 0,
      availableStock: detail.otherField5 ? Number(detail.otherField5) : 0,
      lockedStock: 0,
      costPrice: detail.unitPrice || 0,
      averagePrice: detail.unitPrice || 0,
      lastInPrice: detail.unitPrice || 0,
      lastOutPrice: detail.unitPrice || 0,
      warehouseId: businessForm.formData.depotId || '',
      warehouseName: businessForm.formData.depotName || '',
      locationCode: detail.otherField6 || ''
    })) as InventoryProduct[],
    
    // 数量和金额信息
    totalQuantity: businessForm.formData.details.reduce((total, detail) => total + (detail.basicNumber || 0), 0),
    totalAmount: businessForm.formData.totalPrice || 0,
    
    // 附加信息
    remark: businessForm.formData.remark || '',
    attachments: []
  }))

  // 计算属性
  const isFormValid = computed(() => {
    const form = otherOutForm.value
    return form.outDate && 
           form.operator && 
           form.warehouseName &&
           form.products.length > 0 &&
           form.totalQuantity > 0 &&
           isStockSufficient.value
  })

  const totalOutQuantity = computed(() => {
    return otherOutForm.value.totalQuantity
  })

  const totalOutAmount = computed(() => {
    return otherOutForm.value.totalAmount
  })

  const isStockSufficient = computed(() => {
    return stockWarnings.value.length === 0
  })

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number): string => {
    return amount.toFixed(2)
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 生成其他出库编号
   */
  const generateOutNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `QTCK${year}${month}${day}${random}`
  }

  /**
   * 获取出库类型文本
   */
  const getOutTypeText = (type: OutType): string => {
    switch (type) {
      case 'loss': return '损耗出库'
      case 'transfer': return '调拨出库'
      case 'inventory_loss': return '盘亏出库'
      case 'sample': return '样品出库'
      case 'gift': return '赠品出库'
      case 'scrap': return '报废出库'
      default: return '其他出库'
    }
  }

  /**
   * 获取出库类型选项
   */
  const getOutTypeOptions = () => [
    { text: '损耗出库', value: 'loss' },
    { text: '调拨出库', value: 'transfer' },
    { text: '盘亏出库', value: 'inventory_loss' },
    { text: '样品出库', value: 'sample' },
    { text: '赠品出库', value: 'gift' },
    { text: '报废出库', value: 'scrap' },
    { text: '其他出库', value: 'other' }
  ]

  /**
   * 验证库存数量
   */
  const validateStockQuantity = (productId: string | number, requestedQuantity: number): boolean => {
    const detail = businessForm.formData.details.find(d => d.productId === productId)
    if (!detail) return false
    
    const availableStock = Number(detail.otherField5) || 0
    return requestedQuantity <= availableStock
  }

  /**
   * 检查可用库存
   */
  const checkAvailableStock = (productId: string | number, requestedQuantity: number): StockWarning | null => {
    const detail = businessForm.formData.details.find(d => d.productId === productId)
    if (!detail) return null
    
    const availableStock = Number(detail.otherField5) || 0
    const productName = detail.productName
    
    if (requestedQuantity > availableStock) {
      return {
        productId,
        productName,
        requestedQuantity,
        availableQuantity: availableStock,
        message: `商品"${productName}"库存不足，可用库存：${availableStock}，申请出库：${requestedQuantity}`
      }
    }
    
    return null
  }

  /**
   * 更新库存警告
   */
  const updateStockWarnings = (): void => {
    const warnings: StockWarning[] = []
    
    businessForm.formData.details.forEach(detail => {
      const warning = checkAvailableStock(detail.productId, detail.basicNumber || 0)
      if (warning) {
        warnings.push(warning)
      }
    })
    
    stockWarnings.value = warnings
  }

  /**
   * 计算金额
   */
  const calculateAmount = (): void => {
    const totalAmount = businessForm.formData.details.reduce((total, detail) => {
      return total + ((detail.unitPrice || 0) * (detail.basicNumber || 0))
    }, 0)
    
    businessForm.formData.totalPrice = totalAmount
    updateStockWarnings()
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const form = otherOutForm.value
    const errors: string[] = []

    // 验证日期
    if (!form.outDate) {
      errors.push('请选择出库日期')
    }

    // 验证操作员
    if (!form.operator) {
      errors.push('请选择操作员')
    }

    // 验证仓库
    if (!form.warehouseName) {
      errors.push('请选择出库仓库')
    }

    // 验证商品
    if (form.products.length === 0) {
      errors.push('请至少添加一个出库商品')
    }

    // 验证出库数量
    for (const product of form.products) {
      const detail = businessForm.formData.details.find(d => d.productId === product.id)
      if (!detail || detail.basicNumber <= 0) {
        errors.push(`商品"${product.name}"的出库数量必须大于0`)
        break
      }
    }

    // 验证总数量
    if (form.totalQuantity <= 0) {
      errors.push('出库总数量必须大于0')
    }

    // 验证库存
    if (!isStockSufficient.value) {
      errors.push('存在库存不足的商品，请检查出库数量')
    }

    if (errors.length > 0) {
      showToast({ type: 'fail', message: errors[0] })
      return false
    }

    return true
  }

  /**
   * 日期确认处理
   */
  const handleDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    businessForm.formData.operTime = formatDate(date)
    showDatePicker.value = false
  }

  /**
   * 操作员选择处理
   */
  const handleOperatorSelect = (operator: Operator): void => {
    businessForm.formData.otherField1 = operator.name
    businessForm.formData.otherField2 = operator.id.toString()
    showOperatorPicker.value = false
  }

  /**
   * 仓库选择处理
   */
  const handleWarehouseSelect = (warehouse: Depot): void => {
    businessForm.formData.depotName = warehouse.name
    businessForm.formData.depotId = warehouse.id
    
    // 更新所有商品的仓库信息
    businessForm.formData.details.forEach(detail => {
      detail.otherField7 = warehouse.id.toString()
      detail.otherField8 = warehouse.name
    })
    
    showWarehousePicker.value = false
  }

  /**
   * 出库类型选择处理
   */
  const handleOutTypeSelect = (type: OutType): void => {
    businessForm.formData.otherField3 = type
  }

  /**
   * 目标单据选择处理
   */
  const handleTargetDocumentSelect = (document: any): void => {
    businessForm.formData.linkNumber = document.documentNo
    businessForm.formData.linkId = document.id
    showTargetDocumentPicker.value = false
  }

  /**
   * 添加出库商品
   */
  const addOutProduct = (product: InventoryProduct): void => {
    businessForm.addDetailItem({
      productId: product.id,
      productName: product.name,
      unitPrice: product.costPrice,
      basicNumber: 1,
      otherField1: product.code || '',
      otherField2: product.spec || '',
      otherField3: product.unit || '个',
      otherField4: product.currentStock.toString(),
      otherField5: product.availableStock.toString(),
      otherField6: product.locationCode || '',
      otherField7: product.warehouseId.toString(),
      otherField8: product.warehouseName
    })

    calculateAmount()
  }

  /**
   * 移除出库商品
   */
  const removeOutProduct = async (index: number): Promise<void> => {
    try {
      await showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个出库商品吗？'
      })

      businessForm.removeDetailItem(index)
      calculateAmount()
      showToast({ type: 'success', message: '商品已删除' })
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 出库数量变化处理
   */
  const handleOutQuantityChange = (index: number, quantity: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].basicNumber = quantity
      calculateAmount()
    }
  }

  /**
   * 出库单价变化处理
   */
  const handleOutPriceChange = (index: number, price: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].unitPrice = price
      calculateAmount()
    }
  }

  /**
   * 库位变化处理
   */
  const handleLocationChange = (index: number, location: string): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].otherField6 = location
    }
  }

  /**
   * 重置表单
   */
  const resetForm = (): void => {
    businessForm.resetForm()
    businessForm.formData.number = generateOutNo()
    businessForm.formData.operTime = formatDate(new Date())
    businessForm.formData.otherField3 = 'other'
    stockWarnings.value = []
  }

  /**
   * 初始化表单
   */
  const initializeForm = (data?: Partial<OtherOutForm>): void => {
    if (data) {
      // 映射数据到业务表单
      businessForm.formData.number = data.outNo || generateOutNo()
      businessForm.formData.operTime = data.outDate || formatDate(new Date())
      businessForm.formData.otherField1 = data.operator || ''
      businessForm.formData.otherField2 = data.operatorId?.toString() || ''
      businessForm.formData.depotName = data.warehouseName || ''
      businessForm.formData.depotId = data.warehouseId || ''
      businessForm.formData.otherField3 = data.outType || 'other'
      businessForm.formData.linkNumber = data.targetDocument || ''
      businessForm.formData.linkId = data.targetDocumentId || ''
      businessForm.formData.remark = data.remark || ''

      // 映射商品数据
      if (data.products) {
        businessForm.formData.details = data.products.map(product => ({
          productId: product.id,
          productName: product.name,
          unitPrice: product.costPrice,
          basicNumber: 1,
          otherField1: product.code || '',
          otherField2: product.spec || '',
          otherField3: product.unit || '个',
          otherField4: product.currentStock.toString(),
          otherField5: product.availableStock.toString(),
          otherField6: product.locationCode || '',
          otherField7: product.warehouseId.toString(),
          otherField8: product.warehouseName
        }))
      }
    } else {
      businessForm.formData.number = generateOutNo()
      businessForm.formData.operTime = formatDate(new Date())
      businessForm.formData.otherField3 = 'other'
    }

    stockWarnings.value = []
  }

  return {
    // 响应式数据
    loading: businessForm.loading,
    submitting: businessForm.submitting,
    otherOutForm,
    showOperatorPicker,
    showDatePicker,
    showWarehousePicker,
    showProductPicker,
    showTargetDocumentPicker,
    dateValue,
    stockWarnings,

    // 计算属性
    isFormValid,
    totalOutQuantity,
    totalOutAmount,
    isStockSufficient,

    // 业务表单方法
    ...businessForm,

    // 其他出库特定方法
    formatCurrency,
    formatDate,
    generateOutNo,
    getOutTypeText,
    getOutTypeOptions,
    validateStockQuantity,
    checkAvailableStock,
    updateStockWarnings,
    calculateAmount,
    validateForm,
    handleDateConfirm,
    handleOperatorSelect,
    handleWarehouseSelect,
    handleOutTypeSelect,
    handleTargetDocumentSelect,
    addOutProduct,
    removeOutProduct,
    handleOutQuantityChange,
    handleOutPriceChange,
    handleLocationChange,
    resetForm,
    initializeForm
  }
}
