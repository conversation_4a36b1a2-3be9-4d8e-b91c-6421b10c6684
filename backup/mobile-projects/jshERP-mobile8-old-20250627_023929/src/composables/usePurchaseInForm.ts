/**
 * 采购入库表单业务逻辑Hook
 * 
 * 基于通用业务表单逻辑，扩展采购入库特定功能：
 * - 供应商和采购员管理
 * - 仓库选择和库存管理
 * - 质检功能和状态管理
 * - 关联采购订单功能
 */

import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from '@/composables/business/useBusinessForm'
import { DocumentType } from '@/types/business'
import type { 
  PurchaseInForm, 
  PurchaseProduct, 
  Purchaser,
  PurchaseInStatus
} from '@/types/purchase'
import type { FormConfig } from '@/types/business'
import type { Supplier, Depot } from '@/types/business'

/**
 * 采购入库表单Hook
 */
export function usePurchaseInForm(initialData?: Partial<PurchaseInForm>) {
  // 采购入库配置
  const purchaseInConfig: FormConfig = {
    formType: DocumentType.PURCHASE_IN,
    showPartnerSelector: true,
    partnerType: 'supplier',
    showDepotSelector: true,
    showAccountSelector: false,
    showLinkDocument: true,
    editable: true,
    defaultValues: {
      number: '',
      operTime: new Date().toISOString().slice(0, 16),
      ...initialData
    }
  }

  // 使用通用业务表单逻辑
  const businessForm = useBusinessForm(purchaseInConfig)
  
  // 选择器显示状态
  const showSupplierPicker = ref<boolean>(false)
  const showDatePicker = ref<boolean>(false)
  const showPurchaserPicker = ref<boolean>(false)
  const showWarehousePicker = ref<boolean>(false)
  const showOrderPicker = ref<boolean>(false)
  const dateValue = ref<string[]>([])
  
  // 采购入库特定的计算属性
  const purchaseInForm = computed(() => ({
    // 映射通用字段到采购入库字段
    inNo: businessForm.formData.number,
    supplierName: businessForm.formData.partnerName || '',
    supplierId: businessForm.formData.partnerId || '',
    inDate: businessForm.formData.operTime,
    purchaser: businessForm.formData.otherField1 || '',
    purchaserId: businessForm.formData.otherField2 || '',
    
    // 关联信息
    relatedOrderNo: businessForm.formData.linkNumber || '',
    relatedOrderId: businessForm.formData.linkId || '',
    
    // 商品信息
    products: businessForm.formData.details.map(detail => ({
      id: detail.productId,
      name: detail.productName,
      spec: detail.otherField1 || '',
      quantity: detail.basicNumber || 0,
      purchasePrice: detail.unitPrice || 0,
      supplierProductCode: detail.otherField2 || '',
      qualityCheckResult: detail.otherField3 as 'passed' | 'failed' | 'pending' || 'pending',
      qualityCheckRemark: detail.otherField4 || ''
    })) as PurchaseProduct[],
    
    // 金额信息
    subtotal: businessForm.formData.totalPrice || 0,
    discountRate: businessForm.formData.otherField5 ? Number(businessForm.formData.otherField5) : 0,
    discountAmount: 0, // 计算得出
    finalAmount: businessForm.totalAmount.value,
    
    // 仓库信息
    warehouseId: businessForm.formData.depotId || '',
    warehouseName: businessForm.formData.depotName || '',
    
    // 质检信息
    qualityCheckStatus: businessForm.formData.otherField6 as 'pending' | 'passed' | 'failed' || 'pending',
    qualityCheckRemark: businessForm.formData.otherField7 || '',
    
    // 附加信息
    remark: businessForm.formData.remark || '',
    attachments: []
  }))

  // 计算属性
  const isFormValid = computed(() => {
    const form = purchaseInForm.value
    return form.supplierName && 
           form.inDate && 
           form.purchaser && 
           form.warehouseName &&
           form.products.length > 0
  })

  const totalQuantity = computed(() => {
    return purchaseInForm.value.products.reduce((total, product) => total + product.quantity, 0)
  })

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number): string => {
    return amount.toFixed(2)
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 生成采购入库编号
   */
  const generateInNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `CGRK${year}${month}${day}${random}`
  }

  /**
   * 计算金额
   */
  const calculateAmount = (): void => {
    const form = purchaseInForm.value
    
    // 计算小计
    const subtotal = form.products.reduce((total, product) => {
      return total + (product.purchasePrice * product.quantity)
    }, 0)
    
    // 更新业务表单的总价
    businessForm.formData.totalPrice = subtotal
    
    // 计算优惠金额
    const discountAmount = subtotal * (form.discountRate / 100)
    
    // 计算最终金额
    const finalAmount = subtotal - discountAmount
    
    // 更新相关字段
    businessForm.formData.otherField5 = form.discountRate.toString()
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const form = purchaseInForm.value
    const errors: string[] = []

    // 验证供应商
    if (!form.supplierName) {
      errors.push('请选择供应商')
    }

    // 验证日期
    if (!form.inDate) {
      errors.push('请选择入库日期')
    }

    // 验证采购员
    if (!form.purchaser) {
      errors.push('请选择采购员')
    }

    // 验证仓库
    if (!form.warehouseName) {
      errors.push('请选择入库仓库')
    }

    // 验证商品
    if (form.products.length === 0) {
      errors.push('请至少添加一个商品')
    }

    // 验证商品数量
    for (const product of form.products) {
      if (product.quantity <= 0) {
        errors.push(`商品"${product.name}"的数量必须大于0`)
        break
      }
    }

    // 验证优惠率
    if (form.discountRate < 0 || form.discountRate > 100) {
      errors.push('优惠率必须在0-100之间')
    }

    if (errors.length > 0) {
      showToast({ type: 'fail', message: errors[0] })
      return false
    }

    return true
  }

  /**
   * 日期确认处理
   */
  const handleDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    businessForm.formData.operTime = formatDate(date)
    showDatePicker.value = false
  }

  /**
   * 供应商选择处理
   */
  const handleSupplierSelect = (supplier: Supplier): void => {
    businessForm.formData.partnerName = supplier.name
    businessForm.formData.partnerId = supplier.id
    showSupplierPicker.value = false
  }

  /**
   * 采购员选择处理
   */
  const handlePurchaserSelect = (purchaser: Purchaser): void => {
    businessForm.formData.otherField1 = purchaser.name
    businessForm.formData.otherField2 = purchaser.id.toString()
    showPurchaserPicker.value = false
  }

  /**
   * 仓库选择处理
   */
  const handleWarehouseSelect = (warehouse: Depot): void => {
    businessForm.formData.depotName = warehouse.name
    businessForm.formData.depotId = warehouse.id
    showWarehousePicker.value = false
  }

  /**
   * 采购订单选择处理
   */
  const handleOrderSelect = (order: any): void => {
    businessForm.formData.linkNumber = order.orderNo
    businessForm.formData.linkId = order.id
    
    // 自动填充供应商信息
    businessForm.formData.partnerName = order.supplierName
    businessForm.formData.partnerId = order.supplierId
    businessForm.formData.otherField1 = order.purchaser
    businessForm.formData.otherField2 = order.purchaserId.toString()
    
    // 自动填充商品信息
    businessForm.formData.details = order.products.map((product: any) => ({
      productId: product.id,
      productName: product.name,
      unitPrice: product.purchasePrice,
      basicNumber: product.quantity,
      otherField1: product.spec || '',
      otherField2: product.supplierProductCode || '',
      otherField3: 'pending', // 质检状态
      otherField4: '' // 质检备注
    }))
    
    calculateAmount()
    showOrderPicker.value = false
  }

  /**
   * 添加商品
   */
  const addProduct = (product: PurchaseProduct): void => {
    businessForm.addDetailItem({
      productId: product.id,
      productName: product.name,
      unitPrice: product.purchasePrice,
      basicNumber: product.quantity,
      otherField1: product.spec || '',
      otherField2: product.supplierProductCode || '',
      otherField3: 'pending', // 质检状态
      otherField4: '' // 质检备注
    })
    
    calculateAmount()
  }

  /**
   * 移除商品
   */
  const removeProduct = async (index: number): Promise<void> => {
    try {
      await showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个商品吗？'
      })
      
      businessForm.removeDetailItem(index)
      calculateAmount()
      showToast({ type: 'success', message: '商品已删除' })
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 数量变化处理
   */
  const handleQuantityChange = (index: number, quantity: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].basicNumber = quantity
      calculateAmount()
    }
  }

  /**
   * 单价变化处理
   */
  const handlePriceChange = (index: number, price: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].unitPrice = price
      calculateAmount()
    }
  }

  /**
   * 质检状态变化处理
   */
  const handleQualityCheckChange = (index: number, status: 'passed' | 'failed' | 'pending'): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].otherField3 = status
    }
  }

  /**
   * 质检备注变化处理
   */
  const handleQualityRemarkChange = (index: number, remark: string): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].otherField4 = remark
    }
  }

  /**
   * 优惠率变化处理
   */
  const handleDiscountChange = (): void => {
    const form = purchaseInForm.value
    
    // 限制优惠率范围
    if (form.discountRate < 0) {
      businessForm.formData.otherField5 = '0'
    } else if (form.discountRate > 100) {
      businessForm.formData.otherField5 = '100'
    }
    
    calculateAmount()
  }

  /**
   * 重置表单
   */
  const resetForm = (): void => {
    businessForm.resetForm()
    businessForm.formData.number = generateInNo()
    businessForm.formData.operTime = formatDate(new Date())
  }

  /**
   * 初始化表单
   */
  const initializeForm = (data?: Partial<PurchaseInForm>): void => {
    if (data) {
      // 映射数据到业务表单
      businessForm.formData.number = data.inNo || generateInNo()
      businessForm.formData.partnerName = data.supplierName || ''
      businessForm.formData.partnerId = data.supplierId || ''
      businessForm.formData.operTime = data.inDate || formatDate(new Date())
      businessForm.formData.otherField1 = data.purchaser || ''
      businessForm.formData.otherField2 = data.purchaserId?.toString() || ''
      businessForm.formData.depotName = data.warehouseName || ''
      businessForm.formData.depotId = data.warehouseId || ''
      businessForm.formData.linkNumber = data.relatedOrderNo || ''
      businessForm.formData.linkId = data.relatedOrderId || ''
      businessForm.formData.remark = data.remark || ''
      
      // 映射商品数据
      if (data.products) {
        businessForm.formData.details = data.products.map(product => ({
          productId: product.id,
          productName: product.name,
          unitPrice: product.purchasePrice,
          basicNumber: product.quantity,
          otherField1: product.spec || '',
          otherField2: product.supplierProductCode || '',
          otherField3: product.qualityCheckResult || 'pending',
          otherField4: product.qualityCheckRemark || ''
        }))
      }
    } else {
      businessForm.formData.number = generateInNo()
      businessForm.formData.operTime = formatDate(new Date())
    }
  }

  return {
    // 响应式数据
    loading: businessForm.loading,
    submitting: businessForm.submitting,
    purchaseInForm,
    showSupplierPicker,
    showDatePicker,
    showPurchaserPicker,
    showWarehousePicker,
    showOrderPicker,
    dateValue,
    
    // 计算属性
    isFormValid,
    totalQuantity,
    
    // 业务表单方法
    ...businessForm,
    
    // 采购入库特定方法
    formatCurrency,
    formatDate,
    generateInNo,
    calculateAmount,
    validateForm,
    handleDateConfirm,
    handleSupplierSelect,
    handlePurchaserSelect,
    handleWarehouseSelect,
    handleOrderSelect,
    addProduct,
    removeProduct,
    handleQuantityChange,
    handlePriceChange,
    handleQualityCheckChange,
    handleQualityRemarkChange,
    handleDiscountChange,
    resetForm,
    initializeForm
  }
}
