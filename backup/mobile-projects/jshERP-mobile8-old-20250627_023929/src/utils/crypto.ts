import md5 from 'md5'

/**
 * MD5加密工具
 */
export class CryptoUtil {
  /**
   * MD5加密
   * @param text 要加密的文本
   * @returns MD5加密后的字符串
   */
  static md5(text: string): string {
    return md5(text)
  }

  /**
   * 密码MD5加密 - 按照jshERP标准
   * @param password 原始密码
   * @returns MD5加密后的密码
   */
  static encryptPassword(password: string): string {
    return CryptoUtil.md5(password)
  }
}

// 导出便捷方法
export const md5Hash = CryptoUtil.md5
export const encryptPassword = CryptoUtil.encryptPassword
