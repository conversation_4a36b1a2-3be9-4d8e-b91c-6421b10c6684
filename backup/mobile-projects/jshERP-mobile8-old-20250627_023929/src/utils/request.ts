import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores'
import router from '@/router'

class HttpClient {
  private instance: AxiosInstance
  
  constructor(config: AxiosRequestConfig) {
    this.instance = axios.create(config)
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器 - 与桌面端保持一致
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()

        // 使用X-Access-Token请求头，与桌面端一致
        if (authStore.token) {
          config.headers['X-Access-Token'] = authStore.token
        }

        // jshERP通过token自动处理多租户，不需要额外的租户ID请求头
        // 移除X-Tenant-ID设置以保持与桌面端一致

        // 设置Content-Type
        if (!config.headers['Content-Type']) {
          config.headers['Content-Type'] = 'application/json'
        }

        return config
      },
      (error) => {
        console.error('Request error:', error)
        return Promise.reject(error)
      }
    )
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token过期，清除认证信息并跳转到登录页
          const authStore = useAuthStore()
          await authStore.logout()
          router.push('/auth/login')
        } else if (error.response?.status === 403) {
          // 权限不足
          console.error('Permission denied')
        } else if (error.response?.status >= 500) {
          // 服务器错误
          console.error('Server error:', error.response.data)
        }
        
        return Promise.reject(error)
      }
    )
  }
  
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get(url, config)
    return response.data
  }
  
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post(url, data, config)
    return response.data
  }
  
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put(url, data, config)
    return response.data
  }
  
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete(url, config)
    return response.data
  }
  
  async upload<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    })
    return response.data
  }
}

export const httpClient = new HttpClient({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})