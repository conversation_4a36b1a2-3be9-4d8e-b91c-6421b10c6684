// ERP移动端通用样式
@import './variables.less';

// 全局重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--erp-font-size-md);
  line-height: var(--erp-line-height-normal);
  color: var(--erp-text-primary);
  background-color: var(--erp-bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ERP页面容器
.erp-page {
  min-height: 100vh;
  background: var(--erp-bg-secondary);
  padding-bottom: 60px; // Tab栏高度
  
  &--no-tabbar {
    padding-bottom: 0;
  }
}

// ERP头部样式
.erp-header {
  background: var(--erp-gradient);
  color: var(--erp-text-white);
  padding: var(--erp-spacing-lg) var(--erp-spacing-md);
  text-align: center;
  position: relative;
  z-index: var(--erp-z-header);
  
  &__title {
    font-size: var(--erp-font-size-title);
    font-weight: var(--erp-font-weight-bold);
    margin: 0 0 var(--erp-spacing-xs) 0;
    line-height: var(--erp-line-height-tight);
  }
  
  &__subtitle {
    font-size: var(--erp-font-size-subtitle);
    font-weight: var(--erp-font-weight-normal);
    margin: 0;
    opacity: 0.9;
    line-height: var(--erp-line-height-normal);
  }
  
  &--simple {
    padding: var(--erp-spacing-md);
    
    .erp-header__title {
      font-size: var(--erp-font-size-xl);
      margin: 0;
    }
  }
}

// ERP卡片样式
.erp-card {
  background: var(--erp-bg-card);
  border-radius: var(--erp-radius-lg);
  box-shadow: var(--erp-shadow-card);
  margin: var(--erp-spacing-md);
  overflow: hidden;
  
  &__header {
    padding: var(--erp-spacing-md);
    border-bottom: 1px solid var(--erp-border-light);
    
    &-title {
      font-size: var(--erp-font-size-lg);
      font-weight: var(--erp-font-weight-semibold);
      color: var(--erp-text-primary);
      margin: 0;
    }
  }
  
  &__body {
    padding: var(--erp-spacing-md);
  }
  
  &__footer {
    padding: var(--erp-spacing-md);
    border-top: 1px solid var(--erp-border-light);
    background: var(--erp-bg-tertiary);
  }
  
  &--no-margin {
    margin: 0;
  }
  
  &--no-shadow {
    box-shadow: none;
    border: 1px solid var(--erp-border-light);
  }
}

// ERP网格布局
.erp-grid {
  display: grid;
  gap: var(--erp-spacing-md);
  padding: var(--erp-spacing-md);
  
  &--1 { grid-template-columns: 1fr; }
  &--2 { grid-template-columns: repeat(2, 1fr); }
  &--3 { grid-template-columns: repeat(3, 1fr); }
  &--4 { grid-template-columns: repeat(4, 1fr); }
  &--5 { grid-template-columns: repeat(5, 1fr); }
  
  // 响应式调整
  @media (max-width: @erp-mobile-xs) {
    &--4 { grid-template-columns: repeat(3, 1fr); }
    &--5 { grid-template-columns: repeat(4, 1fr); }
  }
  
  @media (min-width: @erp-tablet) {
    &--3 { grid-template-columns: repeat(4, 1fr); }
    &--4 { grid-template-columns: repeat(5, 1fr); }
  }
}

// ERP分组标题
.erp-section {
  margin-bottom: var(--erp-spacing-lg);
  
  &__title {
    display: flex;
    align-items: center;
    padding: var(--erp-spacing-md);
    font-size: var(--erp-font-size-lg);
    font-weight: var(--erp-font-weight-semibold);
    color: var(--erp-text-primary);
    margin: 0;
    
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: var(--erp-primary);
      border-radius: var(--erp-radius-xs);
      margin-right: var(--erp-spacing-sm);
      flex-shrink: 0;
    }
  }
  
  &__content {
    background: var(--erp-bg-card);
    border-radius: var(--erp-radius-lg);
    box-shadow: var(--erp-shadow-card);
    margin: 0 var(--erp-spacing-md);
    overflow: hidden;
  }
}

// ERP按钮样式
.erp-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--erp-spacing-sm) var(--erp-spacing-md);
  border: none;
  border-radius: var(--erp-radius-md);
  font-size: var(--erp-font-size-md);
  font-weight: var(--erp-font-weight-medium);
  cursor: pointer;
  transition: all var(--erp-duration-fast) var(--erp-ease-out);
  text-decoration: none;
  
  &--primary {
    background: var(--erp-primary);
    color: var(--erp-text-white);
    
    &:hover {
      background: var(--erp-primary-dark);
    }
  }
  
  &--secondary {
    background: var(--erp-bg-tertiary);
    color: var(--erp-text-primary);
    border: 1px solid var(--erp-border-medium);
    
    &:hover {
      background: var(--erp-border-light);
    }
  }
  
  &--text {
    background: transparent;
    color: var(--erp-primary);
    
    &:hover {
      background: var(--erp-bg-tertiary);
    }
  }
}

// ERP工具类
.erp-text {
  &--primary { color: var(--erp-text-primary); }
  &--secondary { color: var(--erp-text-secondary); }
  &--tertiary { color: var(--erp-text-tertiary); }
  &--white { color: var(--erp-text-white); }
  &--success { color: var(--erp-success); }
  &--warning { color: var(--erp-warning); }
  &--error { color: var(--erp-error); }
  
  &--xs { font-size: var(--erp-font-size-xs); }
  &--sm { font-size: var(--erp-font-size-sm); }
  &--md { font-size: var(--erp-font-size-md); }
  &--lg { font-size: var(--erp-font-size-lg); }
  &--xl { font-size: var(--erp-font-size-xl); }
  
  &--normal { font-weight: var(--erp-font-weight-normal); }
  &--medium { font-weight: var(--erp-font-weight-medium); }
  &--semibold { font-weight: var(--erp-font-weight-semibold); }
  &--bold { font-weight: var(--erp-font-weight-bold); }
  
  &--center { text-align: center; }
  &--left { text-align: left; }
  &--right { text-align: right; }
}

.erp-bg {
  &--primary { background-color: var(--erp-bg-primary); }
  &--secondary { background-color: var(--erp-bg-secondary); }
  &--tertiary { background-color: var(--erp-bg-tertiary); }
  &--card { background-color: var(--erp-bg-card); }
}

.erp-border {
  &--light { border: 1px solid var(--erp-border-light); }
  &--medium { border: 1px solid var(--erp-border-medium); }
  &--dark { border: 1px solid var(--erp-border-dark); }
  &--primary { border: 1px solid var(--erp-border-primary); }
}

.erp-radius {
  &--xs { border-radius: var(--erp-radius-xs); }
  &--sm { border-radius: var(--erp-radius-sm); }
  &--md { border-radius: var(--erp-radius-md); }
  &--lg { border-radius: var(--erp-radius-lg); }
  &--xl { border-radius: var(--erp-radius-xl); }
  &--round { border-radius: var(--erp-radius-round); }
}

.erp-shadow {
  &--light { box-shadow: var(--erp-shadow-light); }
  &--medium { box-shadow: var(--erp-shadow-medium); }
  &--heavy { box-shadow: var(--erp-shadow-heavy); }
  &--card { box-shadow: var(--erp-shadow-card); }
}

// 间距工具类
.erp-m {
  &-xs { margin: var(--erp-spacing-xs); }
  &-sm { margin: var(--erp-spacing-sm); }
  &-md { margin: var(--erp-spacing-md); }
  &-lg { margin: var(--erp-spacing-lg); }
  &-xl { margin: var(--erp-spacing-xl); }
}

.erp-p {
  &-xs { padding: var(--erp-spacing-xs); }
  &-sm { padding: var(--erp-spacing-sm); }
  &-md { padding: var(--erp-spacing-md); }
  &-lg { padding: var(--erp-spacing-lg); }
  &-xl { padding: var(--erp-spacing-xl); }
}

// 页面过渡动画增强
.page-transition {
  &-enter-active,
  &-leave-active {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }

  &-leave-to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

// 响应式设计增强
@media (max-width: @erp-mobile-xs) {
  .erp-page {
    padding-bottom: 50px; // 小屏幕调整
  }

  .erp-header {
    padding: var(--erp-spacing-md) var(--erp-spacing-sm);

    &__title {
      font-size: var(--erp-font-size-lg);
    }
  }

  .erp-card {
    margin: var(--erp-spacing-sm);
    border-radius: var(--erp-radius-md);

    &__header,
    &__body,
    &__footer {
      padding: var(--erp-spacing-sm);
    }
  }

  .erp-grid {
    padding: var(--erp-spacing-sm);
    gap: var(--erp-spacing-sm);
  }
}

@media (min-width: @erp-tablet) {
  .erp-page {
    max-width: 768px;
    margin: 0 auto;
    box-shadow: var(--erp-shadow-medium);
  }

  .erp-header {
    border-radius: var(--erp-radius-lg) var(--erp-radius-lg) 0 0;
  }
}

// 性能优化类
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

// 无障碍增强
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 触摸优化
.touch-optimized {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}
