// ERP移动端页面过渡动画样式
@import './variables.less';

// 全局过渡动画基础类
.page-transition-base {
  transition: all var(--erp-duration-page-transition) var(--erp-ease-page-transition);
  transform: translateZ(0); // 启用硬件加速
  will-change: transform, opacity;
}

// 滑动过渡动画 - 向左进入
.slide-left-enter-active,
.slide-left-leave-active {
  .page-transition-base();
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(var(--erp-transition-slide-distance));
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(calc(-1 * var(--erp-transition-slide-distance)));
}

// 滑动过渡动画 - 向右进入
.slide-right-enter-active,
.slide-right-leave-active {
  .page-transition-base();
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(calc(-1 * var(--erp-transition-slide-distance)));
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(var(--erp-transition-slide-distance));
}

// 淡入淡出过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--erp-duration-normal) var(--erp-ease-out);
}

.fade-enter-from,
.fade-leave-to {
  opacity: var(--erp-transition-fade-opacity);
}

// 缩放过渡动画
.scale-enter-active,
.scale-leave-active {
  transition: all var(--erp-duration-normal) var(--erp-ease-in-out);
}

.scale-enter-from {
  opacity: var(--erp-transition-fade-opacity);
  transform: scale(var(--erp-transition-scale-from));
}

.scale-leave-to {
  opacity: var(--erp-transition-fade-opacity);
  transform: scale(var(--erp-transition-scale-to));
}

// 向上滑动过渡动画（适用于模态框等）
.slide-up-enter-active,
.slide-up-leave-active {
  .page-transition-base();
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(100%);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(100%);
}

// 向下滑动过渡动画
.slide-down-enter-active,
.slide-down-leave-active {
  .page-transition-base();
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-100%);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}

// 弹性过渡动画
.bounce-enter-active {
  animation: bounce-in var(--erp-duration-slow) ease-out;
}

.bounce-leave-active {
  animation: bounce-out var(--erp-duration-normal) ease-in;
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

// 旋转过渡动画
.rotate-enter-active,
.rotate-leave-active {
  .page-transition-base();
}

.rotate-enter-from {
  opacity: 0;
  transform: rotate(-180deg) scale(0.8);
}

.rotate-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.8);
}

// 翻转过渡动画
.flip-enter-active,
.flip-leave-active {
  .page-transition-base();
}

.flip-enter-from {
  opacity: 0;
  transform: rotateY(-90deg);
}

.flip-leave-to {
  opacity: 0;
  transform: rotateY(90deg);
}

// 响应式过渡动画优化
@media (max-width: @erp-mobile-xs) {
  // 小屏幕设备减少动画复杂度
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active {
    transition-duration: calc(var(--erp-duration-page-transition) * 0.8);
  }
  
  // 禁用复杂动画以提升性能
  .bounce-enter-active,
  .bounce-leave-active,
  .rotate-enter-active,
  .rotate-leave-active,
  .flip-enter-active,
  .flip-leave-active {
    animation: none;
    transition: opacity var(--erp-duration-fast) ease-out;
  }
}

@media (prefers-reduced-motion: reduce) {
  // 尊重用户的减少动画偏好
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active,
  .fade-enter-active,
  .fade-leave-active,
  .scale-enter-active,
  .scale-leave-active,
  .slide-up-enter-active,
  .slide-up-leave-active,
  .slide-down-enter-active,
  .slide-down-leave-active,
  .bounce-enter-active,
  .bounce-leave-active,
  .rotate-enter-active,
  .rotate-leave-active,
  .flip-enter-active,
  .flip-leave-active {
    transition: none !important;
    animation: none !important;
  }
}

// 页面容器过渡优化
.router-view-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}

// 过渡期间的性能优化
.page-transition-active {
  pointer-events: none; // 过渡期间禁用交互
  user-select: none;
  -webkit-user-select: none;
}
