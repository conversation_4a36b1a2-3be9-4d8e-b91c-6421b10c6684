name: jshERP Mobile CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '16'
  REGISTRY: ghcr.io
  IMAGE_NAME: jsh-erp-mobile

jobs:
  # 代码质量检查
  lint:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint

    - name: Check code formatting
      run: npm run lint:fix

  # 单元测试
  test:
    runs-on: ubuntu-latest
    needs: lint
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run unit tests
      run: npm run test:unit

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      if: success()

  # 构建测试
  build:
    runs-on: ubuntu-latest
    needs: [lint, test]
    strategy:
      matrix:
        env: [development, test, production]
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build for ${{ matrix.env }}
      run: npm run build:${{ matrix.env == 'development' && 'dev' || matrix.env == 'test' && 'test' || 'prod' }}

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist-${{ matrix.env }}
        path: dist/
        retention-days: 7

  # Docker构建
  docker:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push'
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到测试环境
  deploy-test:
    runs-on: ubuntu-latest
    needs: [build, docker]
    if: github.ref == 'refs/heads/develop'
    environment: test
    steps:
    - name: Deploy to test environment
      run: |
        echo "部署到测试环境"
        # 这里添加实际的部署逻辑

  # 部署到生产环境
  deploy-prod:
    runs-on: ubuntu-latest
    needs: [build, docker]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - name: Deploy to production environment
      run: |
        echo "部署到生产环境"
        # 这里添加实际的部署逻辑
