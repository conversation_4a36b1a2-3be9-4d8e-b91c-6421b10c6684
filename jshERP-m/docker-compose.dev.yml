# jshERP移动端 开发环境 Docker Compose配置
version: '3.8'

services:
  # 移动端开发服务
  jsh-erp-mobile-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: jsh-erp-mobile-dev
    restart: unless-stopped
    ports:
      - "8899:8899"
    environment:
      - NODE_ENV=development
      - TZ=Asia/Shanghai
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs/dev:/app/logs
    networks:
      - jsh-erp-dev-network
    command: npm run serve

  # MySQL开发数据库
  mysql-dev:
    image: mysql:5.7.33
    container_name: jsh-erp-mysql-dev
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=jsh_erp_dev
      - MYSQL_USER=jsh_user
      - MYSQL_PASSWORD=123456
      - TZ=Asia/Shanghai
    volumes:
      - ./data/mysql-dev:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - jsh-erp-dev-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis开发缓存
  redis-dev:
    image: redis:6.2.1-alpine
    container_name: jsh-erp-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./data/redis-dev:/data
    networks:
      - jsh-erp-dev-network
    command: redis-server --appendonly yes

networks:
  jsh-erp-dev-network:
    driver: bridge
    name: jsh-erp-dev-network
