const path = require('path')
const CompressionPlugin = require('compression-webpack-plugin')
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')

function resolve (dir) {
    return path.join(__dirname, dir)
}

// 是否为生产环境
const isProduction = process.env.NODE_ENV === 'production'

// 是否启用bundle分析
const enableBundleAnalyzer = process.env.ANALYZE === 'true'

// vue.config.js
module.exports = {
    // 生产环境禁用source map以减少包体积
    productionSourceMap: !isProduction,

    // 公共路径配置
    publicPath: process.env.VUE_APP_PUBLIC_PATH || '/',

    // 输出目录
    outputDir: 'dist',

    // 静态资源目录
    assetsDir: 'static',

    // webpack配置
    configureWebpack: config => {
        // 生产环境优化
        if (isProduction) {
            // 移除console和debugger
            config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
            config.optimization.minimizer[0].options.terserOptions.compress.drop_debugger = true

            // 代码分割优化
            config.optimization.splitChunks = {
                chunks: 'all',
                cacheGroups: {
                    // 第三方库单独打包
                    vendor: {
                        name: 'chunk-vendors',
                        test: /[\\/]node_modules[\\/]/,
                        priority: 10,
                        chunks: 'initial'
                    },
                    // ant-design-vue单独打包
                    antdv: {
                        name: 'chunk-antdv',
                        test: /[\\/]node_modules[\\/]ant-design-vue[\\/]/,
                        priority: 20,
                        chunks: 'all'
                    },
                    // 公共模块
                    common: {
                        name: 'chunk-common',
                        minChunks: 2,
                        priority: 5,
                        chunks: 'initial',
                        reuseExistingChunk: true
                    }
                }
            }
        }

        // Bundle分析器
        if (enableBundleAnalyzer) {
            config.plugins.push(new BundleAnalyzerPlugin())
        }
    },
    chainWebpack: (config) => {
        // 路径别名配置
        config.resolve.alias
            .set('@$', resolve('src'))
            .set('@api', resolve('src/api'))
            .set('@assets', resolve('src/assets'))
            .set('@comp', resolve('src/components'))
            .set('@views', resolve('src/views'))
            .set('@utils', resolve('src/utils'))
            .set('@styles', resolve('src/styles'))

        // 预加载优化
        config.plugin('preload').tap(() => [
            {
                rel: 'preload',
                fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
                include: 'initial'
            }
        ])

        // 预获取优化
        config.plugin('prefetch').tap(() => [
            {
                rel: 'prefetch',
                fileBlacklist: [/\.map$/, /hot-update\.js$/]
            }
        ])

        // 生产环境优化
        if (isProduction) {
            // Gzip压缩
            config.plugin('compressionPlugin').use(new CompressionPlugin({
                test: /\.(js|css|html|svg)$/,
                threshold: 8192, // 对超过8k的数据压缩
                deleteOriginalAssets: false,
                algorithm: 'gzip'
            }))

            // 图片压缩优化 - 在Docker环境中禁用以避免pngquant兼容性问题
            // config.module
            //     .rule('images')
            //     .test(/\.(gif|png|jpe?g|svg)$/i)
            //     .use('image-webpack-loader')
            //     .loader('image-webpack-loader')
            //     .options({
            //         mozjpeg: { progressive: true, quality: 80 },
            //         optipng: { enabled: false },
            //         pngquant: { quality: [0.65, 0.8], speed: 4 },
            //         gifsicle: { interlaced: false }
            //     })
        }

        // 开发环境热更新优化
        if (!isProduction) {
            config.devtool('eval-cheap-module-source-map')
        }
    },
    css: {
        // 生产环境提取CSS
        extract: isProduction,
        // CSS source map
        sourceMap: !isProduction,
        loaderOptions: {
            less: {
                modifyVars: {
                    /* less 变量覆盖，用于自定义 ant design 主题 */
                    'primary-color': '#3B82F6', // 移动端主色调
                    'link-color': '#3B82F6',
                    'border-radius-base': '6px', // 移动端圆角
                    'font-size-base': '14px', // 移动端字体大小
                    'line-height-base': '1.5'
                },
                javascriptEnabled: true
            },
            // PostCSS配置
            postcss: {
                plugins: [
                    require('autoprefixer')({
                        overrideBrowserslist: [
                            'Android 4.1',
                            'iOS 7.1',
                            'Chrome > 31',
                            'ff > 31',
                            'ie >= 8'
                        ]
                    })
                ]
            }
        }
    },
    devServer: {
        port: 8899,
        host: '0.0.0.0',
        open: false, // 不自动打开浏览器
        overlay: {
            warnings: false,
            errors: true
        },
        proxy: {
            '/jshERP-boot': {
                target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:9999',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/jshERP-boot': '/jshERP-boot'
                }
            }
        }
    },

    // 并行处理
    parallel: require('os').cpus().length > 1,

    // PWA配置
    pwa: {
        name: 'jshERP移动端',
        themeColor: '#3B82F6',
        msTileColor: '#000000',
        appleMobileWebAppCapable: 'yes',
        appleMobileWebAppStatusBarStyle: 'black',
        workboxPluginMode: 'InjectManifest',
        workboxOptions: {
            swSrc: 'src/sw.js'
        }
    },

    // 关闭eslint检查
    lintOnSave: false
}
