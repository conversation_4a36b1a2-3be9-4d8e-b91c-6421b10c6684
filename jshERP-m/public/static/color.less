@primary-color: #1890ff;
/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
.bezierEasingMixin() {
  @functions: ~`(function() {
  var NEWTON_ITERATIONS = 4;
  var NEWTON_MIN_SLOPE = 0.001;
  var SUBDIVISION_PRECISION = 0.0000001;
  var SUBDIVISION_MAX_ITERATIONS = 10;

  var kSplineTableSize = 11;
  var kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);

  var float32ArraySupported = typeof Float32Array === 'function';

  function A (aA1, aA2) { return 1.0 - 3.0 * aA2 + 3.0 * aA1; }
  function B (aA1, aA2) { return 3.0 * aA2 - 6.0 * aA1; }
  function C (aA1)      { return 3.0 * aA1; }

  // Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.
  function calcBezier (aT, aA1, aA2) { return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT; }

  // Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.
  function getSlope (aT, aA1, aA2) { return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1); }

  function binarySubdivide (aX, aA, aB, mX1, mX2) {
    var currentX, currentT, i = 0;
    do {
      currentT = aA + (aB - aA) / 2.0;
      currentX = calcBezier(currentT, mX1, mX2) - aX;
      if (currentX > 0.0) {
        aB = currentT;
      } else {
        aA = currentT;
      }
    } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);
    return currentT;
  }

  function newtonRaphsonIterate (aX, aGuessT, mX1, mX2) {
   for (var i = 0; i < NEWTON_ITERATIONS; ++i) {
     var currentSlope = getSlope(aGuessT, mX1, mX2);
     if (currentSlope === 0.0) {
       return aGuessT;
     }
     var currentX = calcBezier(aGuessT, mX1, mX2) - aX;
     aGuessT -= currentX / currentSlope;
   }
   return aGuessT;
  }

  var BezierEasing = function (mX1, mY1, mX2, mY2) {
    if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {
      throw new Error('bezier x values must be in [0, 1] range');
    }

    // Precompute samples table
    var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);
    if (mX1 !== mY1 || mX2 !== mY2) {
      for (var i = 0; i < kSplineTableSize; ++i) {
        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);
      }
    }

    function getTForX (aX) {
      var intervalStart = 0.0;
      var currentSample = 1;
      var lastSample = kSplineTableSize - 1;

      for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {
        intervalStart += kSampleStepSize;
      }
      --currentSample;

      // Interpolate to provide an initial guess for t
      var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);
      var guessForT = intervalStart + dist * kSampleStepSize;

      var initialSlope = getSlope(guessForT, mX1, mX2);
      if (initialSlope >= NEWTON_MIN_SLOPE) {
        return newtonRaphsonIterate(aX, guessForT, mX1, mX2);
      } else if (initialSlope === 0.0) {
        return guessForT;
      } else {
        return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);
      }
    }

    return function BezierEasing (x) {
      if (mX1 === mY1 && mX2 === mY2) {
        return x; // linear
      }
      // Because JavaScript number are imprecise, we should guarantee the extremes are right.
      if (x === 0) {
        return 0;
      }
      if (x === 1) {
        return 1;
      }
      return calcBezier(getTForX(x), mY1, mY2);
    };
  };

  this.colorEasing = BezierEasing(0.26, 0.09, 0.37, 0.18);
  // less 3 requires a return
  return '';
})()`;
}
// It is hacky way to make this function will be compiled preferentially by less
// resolve error: `ReferenceError: colorPalette is not defined`
// https://github.com/ant-design/ant-motion/issues/44
.bezierEasingMixin();

/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
.tinyColorMixin() {
  @functions: ~`(function() {
// TinyColor v1.4.1
// https://github.com/bgrins/TinyColor
// 2016-07-07, Brian Grinstead, MIT License
var trimLeft = /^\s+/,
    trimRight = /\s+$/,
    tinyCounter = 0,
    mathRound = Math.round,
    mathMin = Math.min,
    mathMax = Math.max,
    mathRandom = Math.random;

function tinycolor (color, opts) {

    color = (color) ? color : '';
    opts = opts || { };

    // If input is already a tinycolor, return itself
    if (color instanceof tinycolor) {
       return color;
    }
    // If we are called as a function, call using new instead
    if (!(this instanceof tinycolor)) {
        return new tinycolor(color, opts);
    }

    var rgb = inputToRGB(color);
    this._originalInput = color,
    this._r = rgb.r,
    this._g = rgb.g,
    this._b = rgb.b,
    this._a = rgb.a,
    this._roundA = mathRound(100*this._a) / 100,
    this._format = opts.format || rgb.format;
    this._gradientType = opts.gradientType;

    // Don't let the range of [0,255] come back in [0,1].
    // Potentially lose a little bit of precision here, but will fix issues where
    // .5 gets interpreted as half of the total, instead of half of 1
    // If it was supposed to be 128, this was already taken care of by inputToRgb
    if (this._r < 1) { this._r = mathRound(this._r); }
    if (this._g < 1) { this._g = mathRound(this._g); }
    if (this._b < 1) { this._b = mathRound(this._b); }

    this._ok = rgb.ok;
    this._tc_id = tinyCounter++;
}

tinycolor.prototype = {
    isDark: function() {
        return this.getBrightness() < 128;
    },
    isLight: function() {
        return !this.isDark();
    },
    isValid: function() {
        return this._ok;
    },
    getOriginalInput: function() {
      return this._originalInput;
    },
    getFormat: function() {
        return this._format;
    },
    getAlpha: function() {
        return this._a;
    },
    getBrightness: function() {
        //http://www.w3.org/TR/AERT#color-contrast
        var rgb = this.toRgb();
        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    },
    getLuminance: function() {
        //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef
        var rgb = this.toRgb();
        var RsRGB, GsRGB, BsRGB, R, G, B;
        RsRGB = rgb.r/255;
        GsRGB = rgb.g/255;
        BsRGB = rgb.b/255;

        if (RsRGB <= 0.03928) {R = RsRGB / 12.92;} else {R = Math.pow(((RsRGB + 0.055) / 1.055), 2.4);}
        if (GsRGB <= 0.03928) {G = GsRGB / 12.92;} else {G = Math.pow(((GsRGB + 0.055) / 1.055), 2.4);}
        if (BsRGB <= 0.03928) {B = BsRGB / 12.92;} else {B = Math.pow(((BsRGB + 0.055) / 1.055), 2.4);}
        return (0.2126 * R) + (0.7152 * G) + (0.0722 * B);
    },
    setAlpha: function(value) {
        this._a = boundAlpha(value);
        this._roundA = mathRound(100*this._a) / 100;
        return this;
    },
    toHsv: function() {
        var hsv = rgbToHsv(this._r, this._g, this._b);
        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this._a };
    },
    toHsvString: function() {
        var hsv = rgbToHsv(this._r, this._g, this._b);
        var h = mathRound(hsv.h * 360), s = mathRound(hsv.s * 100), v = mathRound(hsv.v * 100);
        return (this._a == 1) ?
          "hsv("  + h + ", " + s + "%, " + v + "%)" :
          "hsva(" + h + ", " + s + "%, " + v + "%, "+ this._roundA + ")";
    },
    toHsl: function() {
        var hsl = rgbToHsl(this._r, this._g, this._b);
        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this._a };
    },
    toHslString: function() {
        var hsl = rgbToHsl(this._r, this._g, this._b);
        var h = mathRound(hsl.h * 360), s = mathRound(hsl.s * 100), l = mathRound(hsl.l * 100);
        return (this._a == 1) ?
          "hsl("  + h + ", " + s + "%, " + l + "%)" :
          "hsla(" + h + ", " + s + "%, " + l + "%, "+ this._roundA + ")";
    },
    toHex: function(allow3Char) {
        return rgbToHex(this._r, this._g, this._b, allow3Char);
    },
    toHexString: function(allow3Char) {
        return '#' + this.toHex(allow3Char);
    },
    toHex8: function(allow4Char) {
        return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);
    },
    toHex8String: function(allow4Char) {
        return '#' + this.toHex8(allow4Char);
    },
    toRgb: function() {
        return { r: mathRound(this._r), g: mathRound(this._g), b: mathRound(this._b), a: this._a };
    },
    toRgbString: function() {
        return (this._a == 1) ?
          "rgb("  + mathRound(this._r) + ", " + mathRound(this._g) + ", " + mathRound(this._b) + ")" :
          "rgba(" + mathRound(this._r) + ", " + mathRound(this._g) + ", " + mathRound(this._b) + ", " + this._roundA + ")";
    },
    toPercentageRgb: function() {
        return { r: mathRound(bound01(this._r, 255) * 100) + "%", g: mathRound(bound01(this._g, 255) * 100) + "%", b: mathRound(bound01(this._b, 255) * 100) + "%", a: this._a };
    },
    toPercentageRgbString: function() {
        return (this._a == 1) ?
          "rgb("  + mathRound(bound01(this._r, 255) * 100) + "%, " + mathRound(bound01(this._g, 255) * 100) + "%, " + mathRound(bound01(this._b, 255) * 100) + "%)" :
          "rgba(" + mathRound(bound01(this._r, 255) * 100) + "%, " + mathRound(bound01(this._g, 255) * 100) + "%, " + mathRound(bound01(this._b, 255) * 100) + "%, " + this._roundA + ")";
    },
    toName: function() {
        if (this._a === 0) {
            return "transparent";
        }

        if (this._a < 1) {
            return false;
        }

        return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;
    },
    toFilter: function(secondColor) {
        var hex8String = '#' + rgbaToArgbHex(this._r, this._g, this._b, this._a);
        var secondHex8String = hex8String;
        var gradientType = this._gradientType ? "GradientType = 1, " : "";

        if (secondColor) {
            var s = tinycolor(secondColor);
            secondHex8String = '#' + rgbaToArgbHex(s._r, s._g, s._b, s._a);
        }

        return "progid:DXImageTransform.Microsoft.gradient("+gradientType+"startColorstr="+hex8String+",endColorstr="+secondHex8String+")";
    },
    toString: function(format) {
        var formatSet = !!format;
        format = format || this._format;

        var formattedString = false;
        var hasAlpha = this._a < 1 && this._a >= 0;
        var needsAlphaFormat = !formatSet && hasAlpha && (format === "hex" || format === "hex6" || format === "hex3" || format === "hex4" || format === "hex8" || format === "name");

        if (needsAlphaFormat) {
            // Special case for "transparent", all other non-alpha formats
            // will return rgba when there is transparency.
            if (format === "name" && this._a === 0) {
                return this.toName();
            }
            return this.toRgbString();
        }
        if (format === "rgb") {
            formattedString = this.toRgbString();
        }
        if (format === "prgb") {
            formattedString = this.toPercentageRgbString();
        }
        if (format === "hex" || format === "hex6") {
            formattedString = this.toHexString();
        }
        if (format === "hex3") {
            formattedString = this.toHexString(true);
        }
        if (format === "hex4") {
            formattedString = this.toHex8String(true);
        }
        if (format === "hex8") {
            formattedString = this.toHex8String();
        }
        if (format === "name") {
            formattedString = this.toName();
        }
        if (format === "hsl") {
            formattedString = this.toHslString();
        }
        if (format === "hsv") {
            formattedString = this.toHsvString();
        }

        return formattedString || this.toHexString();
    },
    clone: function() {
        return tinycolor(this.toString());
    },

    _applyModification: function(fn, args) {
        var color = fn.apply(null, [this].concat([].slice.call(args)));
        this._r = color._r;
        this._g = color._g;
        this._b = color._b;
        this.setAlpha(color._a);
        return this;
    },
    lighten: function() {
        return this._applyModification(lighten, arguments);
    },
    brighten: function() {
        return this._applyModification(brighten, arguments);
    },
    darken: function() {
        return this._applyModification(darken, arguments);
    },
    desaturate: function() {
        return this._applyModification(desaturate, arguments);
    },
    saturate: function() {
        return this._applyModification(saturate, arguments);
    },
    greyscale: function() {
        return this._applyModification(greyscale, arguments);
    },
    spin: function() {
        return this._applyModification(spin, arguments);
    },

    _applyCombination: function(fn, args) {
        return fn.apply(null, [this].concat([].slice.call(args)));
    },
    analogous: function() {
        return this._applyCombination(analogous, arguments);
    },
    complement: function() {
        return this._applyCombination(complement, arguments);
    },
    monochromatic: function() {
        return this._applyCombination(monochromatic, arguments);
    },
    splitcomplement: function() {
        return this._applyCombination(splitcomplement, arguments);
    },
    triad: function() {
        return this._applyCombination(triad, arguments);
    },
    tetrad: function() {
        return this._applyCombination(tetrad, arguments);
    }
};

// If input is an object, force 1 into "1.0" to handle ratios properly
// String input requires "1.0" as input, so 1 will be treated as 1
tinycolor.fromRatio = function(color, opts) {
    if (typeof color == "object") {
        var newColor = {};
        for (var i in color) {
            if (color.hasOwnProperty(i)) {
                if (i === "a") {
                    newColor[i] = color[i];
                }
                else {
                    newColor[i] = convertToPercentage(color[i]);
                }
            }
        }
        color = newColor;
    }

    return tinycolor(color, opts);
};

// Given a string or object, convert that input to RGB
// Possible string inputs:
//
//     "red"
//     "#f00" or "f00"
//     "#ff0000" or "ff0000"
//     "#ff000000" or "ff000000"
//     "rgb 255 0 0" or "rgb (255, 0, 0)"
//     "rgb 1.0 0 0" or "rgb (1, 0, 0)"
//     "rgba (255, 0, 0, 1)" or "rgba 255, 0, 0, 1"
//     "rgba (1.0, 0, 0, 1)" or "rgba 1.0, 0, 0, 1"
//     "hsl(0, 100%, 50%)" or "hsl 0 100% 50%"
//     "hsla(0, 100%, 50%, 1)" or "hsla 0 100% 50%, 1"
//     "hsv(0, 100%, 100%)" or "hsv 0 100% 100%"
//
function inputToRGB(color) {

    var rgb = { r: 0, g: 0, b: 0 };
    var a = 1;
    var s = null;
    var v = null;
    var l = null;
    var ok = false;
    var format = false;

    if (typeof color == "string") {
        color = stringInputToObject(color);
    }

    if (typeof color == "object") {
        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {
            rgb = rgbToRgb(color.r, color.g, color.b);
            ok = true;
            format = String(color.r).substr(-1) === "%" ? "prgb" : "rgb";
        }
        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {
            s = convertToPercentage(color.s);
            v = convertToPercentage(color.v);
            rgb = hsvToRgb(color.h, s, v);
            ok = true;
            format = "hsv";
        }
        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {
            s = convertToPercentage(color.s);
            l = convertToPercentage(color.l);
            rgb = hslToRgb(color.h, s, l);
            ok = true;
            format = "hsl";
        }

        if (color.hasOwnProperty("a")) {
            a = color.a;
        }
    }

    a = boundAlpha(a);

    return {
        ok: ok,
        format: color.format || format,
        r: mathMin(255, mathMax(rgb.r, 0)),
        g: mathMin(255, mathMax(rgb.g, 0)),
        b: mathMin(255, mathMax(rgb.b, 0)),
        a: a
    };
}

// Conversion Functions
// --------------------

// rgbToHsl, rgbToHsv, hslToRgb, hsvToRgb modified from:
// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>

// rgbToRgb
// Handle bounds / percentage checking to conform to CSS color spec
// <http://www.w3.org/TR/css3-color/>
// *Assumes:* r, g, b in [0, 255] or [0, 1]
// *Returns:* { r, g, b } in [0, 255]
function rgbToRgb(r, g, b){
    return {
        r: bound01(r, 255) * 255,
        g: bound01(g, 255) * 255,
        b: bound01(b, 255) * 255
    };
}

// rgbToHsl
// Converts an RGB color value to HSL.
// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]
// *Returns:* { h, s, l } in [0,1]
function rgbToHsl(r, g, b) {

    r = bound01(r, 255);
    g = bound01(g, 255);
    b = bound01(b, 255);

    var max = mathMax(r, g, b), min = mathMin(r, g, b);
    var h, s, l = (max + min) / 2;

    if(max == min) {
        h = s = 0; // achromatic
    }
    else {
        var d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch(max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }

        h /= 6;
    }

    return { h: h, s: s, l: l };
}

// hslToRgb
// Converts an HSL color value to RGB.
// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]
// *Returns:* { r, g, b } in the set [0, 255]
function hslToRgb(h, s, l) {
    var r, g, b;

    h = bound01(h, 360);
    s = bound01(s, 100);
    l = bound01(l, 100);

    function hue2rgb(p, q, t) {
        if(t < 0) t += 1;
        if(t > 1) t -= 1;
        if(t < 1/6) return p + (q - p) * 6 * t;
        if(t < 1/2) return q;
        if(t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
    }

    if(s === 0) {
        r = g = b = l; // achromatic
    }
    else {
        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        var p = 2 * l - q;
        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
    }

    return { r: r * 255, g: g * 255, b: b * 255 };
}

// rgbToHsv
// Converts an RGB color value to HSV
// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]
// *Returns:* { h, s, v } in [0,1]
function rgbToHsv(r, g, b) {

    r = bound01(r, 255);
    g = bound01(g, 255);
    b = bound01(b, 255);

    var max = mathMax(r, g, b), min = mathMin(r, g, b);
    var h, s, v = max;

    var d = max - min;
    s = max === 0 ? 0 : d / max;

    if(max == min) {
        h = 0; // achromatic
    }
    else {
        switch(max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }
    return { h: h, s: s, v: v };
}

// hsvToRgb
// Converts an HSV color value to RGB.
// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]
// *Returns:* { r, g, b } in the set [0, 255]
 function hsvToRgb(h, s, v) {

    h = bound01(h, 360) * 6;
    s = bound01(s, 100);
    v = bound01(v, 100);

    var i = Math.floor(h),
        f = h - i,
        p = v * (1 - s),
        q = v * (1 - f * s),
        t = v * (1 - (1 - f) * s),
        mod = i % 6,
        r = [v, q, p, p, t, v][mod],
        g = [t, v, v, q, p, p][mod],
        b = [p, p, t, v, v, q][mod];

    return { r: r * 255, g: g * 255, b: b * 255 };
}

// rgbToHex
// Converts an RGB color to hex
// Assumes r, g, and b are contained in the set [0, 255]
// Returns a 3 or 6 character hex
function rgbToHex(r, g, b, allow3Char) {

    var hex = [
        pad2(mathRound(r).toString(16)),
        pad2(mathRound(g).toString(16)),
        pad2(mathRound(b).toString(16))
    ];

    // Return a 3 character hex if possible
    if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {
        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);
    }

    return hex.join("");
}

// rgbaToHex
// Converts an RGBA color plus alpha transparency to hex
// Assumes r, g, b are contained in the set [0, 255] and
// a in [0, 1]. Returns a 4 or 8 character rgba hex
function rgbaToHex(r, g, b, a, allow4Char) {

    var hex = [
        pad2(mathRound(r).toString(16)),
        pad2(mathRound(g).toString(16)),
        pad2(mathRound(b).toString(16)),
        pad2(convertDecimalToHex(a))
    ];

    // Return a 4 character hex if possible
    if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {
        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);
    }

    return hex.join("");
}

// rgbaToArgbHex
// Converts an RGBA color to an ARGB Hex8 string
// Rarely used, but required for "toFilter()"
function rgbaToArgbHex(r, g, b, a) {

    var hex = [
        pad2(convertDecimalToHex(a)),
        pad2(mathRound(r).toString(16)),
        pad2(mathRound(g).toString(16)),
        pad2(mathRound(b).toString(16))
    ];

    return hex.join("");
}

// equals
// Can be called with any tinycolor input
tinycolor.equals = function (color1, color2) {
    if (!color1 || !color2) { return false; }
    return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();
};

tinycolor.random = function() {
    return tinycolor.fromRatio({
        r: mathRandom(),
        g: mathRandom(),
        b: mathRandom()
    });
};

// Modification Functions
// ----------------------
// Thanks to less.js for some of the basics here
// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>

function desaturate(color, amount) {
    amount = (amount === 0) ? 0 : (amount || 10);
    var hsl = tinycolor(color).toHsl();
    hsl.s -= amount / 100;
    hsl.s = clamp01(hsl.s);
    return tinycolor(hsl);
}

function saturate(color, amount) {
    amount = (amount === 0) ? 0 : (amount || 10);
    var hsl = tinycolor(color).toHsl();
    hsl.s += amount / 100;
    hsl.s = clamp01(hsl.s);
    return tinycolor(hsl);
}

function greyscale(color) {
    return tinycolor(color).desaturate(100);
}

function lighten (color, amount) {
    amount = (amount === 0) ? 0 : (amount || 10);
    var hsl = tinycolor(color).toHsl();
    hsl.l += amount / 100;
    hsl.l = clamp01(hsl.l);
    return tinycolor(hsl);
}

function brighten(color, amount) {
    amount = (amount === 0) ? 0 : (amount || 10);
    var rgb = tinycolor(color).toRgb();
    rgb.r = mathMax(0, mathMin(255, rgb.r - mathRound(255 * - (amount / 100))));
    rgb.g = mathMax(0, mathMin(255, rgb.g - mathRound(255 * - (amount / 100))));
    rgb.b = mathMax(0, mathMin(255, rgb.b - mathRound(255 * - (amount / 100))));
    return tinycolor(rgb);
}

function darken (color, amount) {
    amount = (amount === 0) ? 0 : (amount || 10);
    var hsl = tinycolor(color).toHsl();
    hsl.l -= amount / 100;
    hsl.l = clamp01(hsl.l);
    return tinycolor(hsl);
}

// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.
// Values outside of this range will be wrapped into this range.
function spin(color, amount) {
    var hsl = tinycolor(color).toHsl();
    var hue = (hsl.h + amount) % 360;
    hsl.h = hue < 0 ? 360 + hue : hue;
    return tinycolor(hsl);
}

// Combination Functions
// ---------------------
// Thanks to jQuery xColor for some of the ideas behind these
// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>

function complement(color) {
    var hsl = tinycolor(color).toHsl();
    hsl.h = (hsl.h + 180) % 360;
    return tinycolor(hsl);
}

function triad(color) {
    var hsl = tinycolor(color).toHsl();
    var h = hsl.h;
    return [
        tinycolor(color),
        tinycolor({ h: (h + 120) % 360, s: hsl.s, l: hsl.l }),
        tinycolor({ h: (h + 240) % 360, s: hsl.s, l: hsl.l })
    ];
}

function tetrad(color) {
    var hsl = tinycolor(color).toHsl();
    var h = hsl.h;
    return [
        tinycolor(color),
        tinycolor({ h: (h + 90) % 360, s: hsl.s, l: hsl.l }),
        tinycolor({ h: (h + 180) % 360, s: hsl.s, l: hsl.l }),
        tinycolor({ h: (h + 270) % 360, s: hsl.s, l: hsl.l })
    ];
}

function splitcomplement(color) {
    var hsl = tinycolor(color).toHsl();
    var h = hsl.h;
    return [
        tinycolor(color),
        tinycolor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l}),
        tinycolor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l})
    ];
}

function analogous(color, results, slices) {
    results = results || 6;
    slices = slices || 30;

    var hsl = tinycolor(color).toHsl();
    var part = 360 / slices;
    var ret = [tinycolor(color)];

    for (hsl.h = ((hsl.h - (part * results >> 1)) + 720) % 360; --results; ) {
        hsl.h = (hsl.h + part) % 360;
        ret.push(tinycolor(hsl));
    }
    return ret;
}

function monochromatic(color, results) {
    results = results || 6;
    var hsv = tinycolor(color).toHsv();
    var h = hsv.h, s = hsv.s, v = hsv.v;
    var ret = [];
    var modification = 1 / results;

    while (results--) {
        ret.push(tinycolor({ h: h, s: s, v: v}));
        v = (v + modification) % 1;
    }

    return ret;
}

// Utility Functions
// ---------------------

tinycolor.mix = function(color1, color2, amount) {
    amount = (amount === 0) ? 0 : (amount || 50);

    var rgb1 = tinycolor(color1).toRgb();
    var rgb2 = tinycolor(color2).toRgb();

    var p = amount / 100;

    var rgba = {
        r: ((rgb2.r - rgb1.r) * p) + rgb1.r,
        g: ((rgb2.g - rgb1.g) * p) + rgb1.g,
        b: ((rgb2.b - rgb1.b) * p) + rgb1.b,
        a: ((rgb2.a - rgb1.a) * p) + rgb1.a
    };

    return tinycolor(rgba);
};

// Readability Functions
// ---------------------
// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)

// contrast
// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)
tinycolor.readability = function(color1, color2) {
    var c1 = tinycolor(color1);
    var c2 = tinycolor(color2);
    return (Math.max(c1.getLuminance(),c2.getLuminance())+0.05) / (Math.min(c1.getLuminance(),c2.getLuminance())+0.05);
};

// isReadable
// Ensure that foreground and background color combinations meet WCAG2 guidelines.
// The third argument is an optional Object.
//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';
//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.
// If the entire object is absent, isReadable defaults to {level:"AA",size:"small"}.

// *Example*
//    tinycolor.isReadable("#000", "#111") => false
//    tinycolor.isReadable("#000", "#111",{level:"AA",size:"large"}) => false
tinycolor.isReadable = function(color1, color2, wcag2) {
    var readability = tinycolor.readability(color1, color2);
    var wcag2Parms, out;

    out = false;

    wcag2Parms = validateWCAG2Parms(wcag2);
    switch (wcag2Parms.level + wcag2Parms.size) {
        case "AAsmall":
        case "AAAlarge":
            out = readability >= 4.5;
            break;
        case "AAlarge":
            out = readability >= 3;
            break;
        case "AAAsmall":
            out = readability >= 7;
            break;
    }
    return out;

};

// mostReadable
// Given a base color and a list of possible foreground or background
// colors for that base, returns the most readable color.
// Optionally returns Black or White if the most readable color is unreadable.
// *Example*
//    tinycolor.mostReadable(tinycolor.mostReadable("#123", ["#124", "#125"],{includeFallbackColors:false}).toHexString(); // "#112255"
//    tinycolor.mostReadable(tinycolor.mostReadable("#123", ["#124", "#125"],{includeFallbackColors:true}).toHexString();  // "#ffffff"
//    tinycolor.mostReadable("#a8015a", ["#faf3f3"],{includeFallbackColors:true,level:"AAA",size:"large"}).toHexString(); // "#faf3f3"
//    tinycolor.mostReadable("#a8015a", ["#faf3f3"],{includeFallbackColors:true,level:"AAA",size:"small"}).toHexString(); // "#ffffff"
tinycolor.mostReadable = function(baseColor, colorList, args) {
    var bestColor = null;
    var bestScore = 0;
    var readability;
    var includeFallbackColors, level, size ;
    args = args || {};
    includeFallbackColors = args.includeFallbackColors ;
    level = args.level;
    size = args.size;

    for (var i= 0; i < colorList.length ; i++) {
        readability = tinycolor.readability(baseColor, colorList[i]);
        if (readability > bestScore) {
            bestScore = readability;
            bestColor = tinycolor(colorList[i]);
        }
    }

    if (tinycolor.isReadable(baseColor, bestColor, {"level":level,"size":size}) || !includeFallbackColors) {
        return bestColor;
    }
    else {
        args.includeFallbackColors=false;
        return tinycolor.mostReadable(baseColor,["#fff", "#000"],args);
    }
};

// Big List of Colors
// ------------------
// <http://www.w3.org/TR/css3-color/#svg-color>
var names = tinycolor.names = {
    aliceblue: "f0f8ff",
    antiquewhite: "faebd7",
    aqua: "0ff",
    aquamarine: "7fffd4",
    azure: "f0ffff",
    beige: "f5f5dc",
    bisque: "ffe4c4",
    black: "000",
    blanchedalmond: "ffebcd",
    blue: "00f",
    blueviolet: "8a2be2",
    brown: "a52a2a",
    burlywood: "deb887",
    burntsienna: "ea7e5d",
    cadetblue: "5f9ea0",
    chartreuse: "7fff00",
    chocolate: "d2691e",
    coral: "ff7f50",
    cornflowerblue: "6495ed",
    cornsilk: "fff8dc",
    crimson: "dc143c",
    cyan: "0ff",
    darkblue: "00008b",
    darkcyan: "008b8b",
    darkgoldenrod: "b8860b",
    darkgray: "a9a9a9",
    darkgreen: "006400",
    darkgrey: "a9a9a9",
    darkkhaki: "bdb76b",
    darkmagenta: "8b008b",
    darkolivegreen: "556b2f",
    darkorange: "ff8c00",
    darkorchid: "9932cc",
    darkred: "8b0000",
    darksalmon: "e9967a",
    darkseagreen: "8fbc8f",
    darkslateblue: "483d8b",
    darkslategray: "2f4f4f",
    darkslategrey: "2f4f4f",
    darkturquoise: "00ced1",
    darkviolet: "9400d3",
    deeppink: "ff1493",
    deepskyblue: "00bfff",
    dimgray: "696969",
    dimgrey: "696969",
    dodgerblue: "1e90ff",
    firebrick: "b22222",
    floralwhite: "fffaf0",
    forestgreen: "228b22",
    fuchsia: "f0f",
    gainsboro: "dcdcdc",
    ghostwhite: "f8f8ff",
    gold: "ffd700",
    goldenrod: "daa520",
    gray: "808080",
    green: "008000",
    greenyellow: "adff2f",
    grey: "808080",
    honeydew: "f0fff0",
    hotpink: "ff69b4",
    indianred: "cd5c5c",
    indigo: "4b0082",
    ivory: "fffff0",
    khaki: "f0e68c",
    lavender: "e6e6fa",
    lavenderblush: "fff0f5",
    lawngreen: "7cfc00",
    lemonchiffon: "fffacd",
    lightblue: "add8e6",
    lightcoral: "f08080",
    lightcyan: "e0ffff",
    lightgoldenrodyellow: "fafad2",
    lightgray: "d3d3d3",
    lightgreen: "90ee90",
    lightgrey: "d3d3d3",
    lightpink: "ffb6c1",
    lightsalmon: "ffa07a",
    lightseagreen: "20b2aa",
    lightskyblue: "87cefa",
    lightslategray: "789",
    lightslategrey: "789",
    lightsteelblue: "b0c4de",
    lightyellow: "ffffe0",
    lime: "0f0",
    limegreen: "32cd32",
    linen: "faf0e6",
    magenta: "f0f",
    maroon: "800000",
    mediumaquamarine: "66cdaa",
    mediumblue: "0000cd",
    mediumorchid: "ba55d3",
    mediumpurple: "9370db",
    mediumseagreen: "3cb371",
    mediumslateblue: "7b68ee",
    mediumspringgreen: "00fa9a",
    mediumturquoise: "48d1cc",
    mediumvioletred: "c71585",
    midnightblue: "191970",
    mintcream: "f5fffa",
    mistyrose: "ffe4e1",
    moccasin: "ffe4b5",
    navajowhite: "ffdead",
    navy: "000080",
    oldlace: "fdf5e6",
    olive: "808000",
    olivedrab: "6b8e23",
    orange: "ffa500",
    orangered: "ff4500",
    orchid: "da70d6",
    palegoldenrod: "eee8aa",
    palegreen: "98fb98",
    paleturquoise: "afeeee",
    palevioletred: "db7093",
    papayawhip: "ffefd5",
    peachpuff: "ffdab9",
    peru: "cd853f",
    pink: "ffc0cb",
    plum: "dda0dd",
    powderblue: "b0e0e6",
    purple: "800080",
    rebeccapurple: "663399",
    red: "f00",
    rosybrown: "bc8f8f",
    royalblue: "4169e1",
    saddlebrown: "8b4513",
    salmon: "fa8072",
    sandybrown: "f4a460",
    seagreen: "2e8b57",
    seashell: "fff5ee",
    sienna: "a0522d",
    silver: "c0c0c0",
    skyblue: "87ceeb",
    slateblue: "6a5acd",
    slategray: "708090",
    slategrey: "708090",
    snow: "fffafa",
    springgreen: "00ff7f",
    steelblue: "4682b4",
    tan: "d2b48c",
    teal: "008080",
    thistle: "d8bfd8",
    tomato: "ff6347",
    turquoise: "40e0d0",
    violet: "ee82ee",
    wheat: "f5deb3",
    white: "fff",
    whitesmoke: "f5f5f5",
    yellow: "ff0",
    yellowgreen: "9acd32"
};

// Make it easy to access colors via hexNames[hex]
var hexNames = tinycolor.hexNames = flip(names);

// Utilities
// ---------

// { 'name1': 'val1' } becomes { 'val1': 'name1' }
function flip(o) {
    var flipped = { };
    for (var i in o) {
        if (o.hasOwnProperty(i)) {
            flipped[o[i]] = i;
        }
    }
    return flipped;
}

// Return a valid alpha value [0,1] with all invalid values being set to 1
function boundAlpha(a) {
    a = parseFloat(a);

    if (isNaN(a) || a < 0 || a > 1) {
        a = 1;
    }

    return a;
}

// Take input from [0, n] and return it as [0, 1]
function bound01(n, max) {
    if (isOnePointZero(n)) { n = "100%"; }

    var processPercent = isPercentage(n);
    n = mathMin(max, mathMax(0, parseFloat(n)));

    // Automatically convert percentage into number
    if (processPercent) {
        n = parseInt(n * max, 10) / 100;
    }

    // Handle floating point rounding errors
    if ((Math.abs(n - max) < 0.000001)) {
        return 1;
    }

    // Convert into [0, 1] range if it isn't already
    return (n % max) / parseFloat(max);
}

// Force a number between 0 and 1
function clamp01(val) {
    return mathMin(1, mathMax(0, val));
}

// Parse a base-16 hex value into a base-10 integer
function parseIntFromHex(val) {
    return parseInt(val, 16);
}

// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1
// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>
function isOnePointZero(n) {
    return typeof n == "string" && n.indexOf('.') != -1 && parseFloat(n) === 1;
}

// Check to see if string passed in is a percentage
function isPercentage(n) {
    return typeof n === "string" && n.indexOf('%') != -1;
}

// Force a hex value to have 2 characters
function pad2(c) {
    return c.length == 1 ? '0' + c : '' + c;
}

// Replace a decimal with it's percentage value
function convertToPercentage(n) {
    if (n <= 1) {
        n = (n * 100) + "%";
    }

    return n;
}

// Converts a decimal to a hex value
function convertDecimalToHex(d) {
    return Math.round(parseFloat(d) * 255).toString(16);
}
// Converts a hex value to a decimal
function convertHexToDecimal(h) {
    return (parseIntFromHex(h) / 255);
}

var matchers = (function() {

    // <http://www.w3.org/TR/css3-values/#integers>
    var CSS_INTEGER = "[-\\+]?\\d+%?";

    // <http://www.w3.org/TR/css3-values/#number-value>
    var CSS_NUMBER = "[-\\+]?\\d*\\.\\d+%?";

    // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.
    var CSS_UNIT = "(?:" + CSS_NUMBER + ")|(?:" + CSS_INTEGER + ")";

    // Actual matching.
    // Parentheses and commas are optional, but not required.
    // Whitespace can take the place of commas or opening paren
    var PERMISSIVE_MATCH3 = "[\\s|\\(]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")\\s*\\)?";
    var PERMISSIVE_MATCH4 = "[\\s|\\(]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")[,|\\s]+(" + CSS_UNIT + ")\\s*\\)?";

    return {
        CSS_UNIT: new RegExp(CSS_UNIT),
        rgb: new RegExp("rgb" + PERMISSIVE_MATCH3),
        rgba: new RegExp("rgba" + PERMISSIVE_MATCH4),
        hsl: new RegExp("hsl" + PERMISSIVE_MATCH3),
        hsla: new RegExp("hsla" + PERMISSIVE_MATCH4),
        hsv: new RegExp("hsv" + PERMISSIVE_MATCH3),
        hsva: new RegExp("hsva" + PERMISSIVE_MATCH4),
        hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
        hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
        hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
        hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
    };
})();

// isValidCSSUnit
// Take in a single string / number and check to see if it looks like a CSS unit
// (see matchers above for definition).
function isValidCSSUnit(color) {
    return !!matchers.CSS_UNIT.exec(color);
}

// stringInputToObject
// Permissive string parsing.  Take in a number of formats, and output an object
// based on detected format.  Returns { r, g, b } or { h, s, l } or { h, s, v}
function stringInputToObject(color) {

    color = color.replace(trimLeft, '').replace(trimRight, '').toLowerCase();
    var named = false;
    if (names[color]) {
        color = names[color];
        named = true;
    }
    else if (color == 'transparent') {
        return { r: 0, g: 0, b: 0, a: 0, format: "name" };
    }

    // Try to match string input using regular expressions.
    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]
    // Just return an object and let the conversion functions handle that.
    // This way the result will be the same whether the tinycolor is initialized with string or object.
    var match;
    if ((match = matchers.rgb.exec(color))) {
        return { r: match[1], g: match[2], b: match[3] };
    }
    if ((match = matchers.rgba.exec(color))) {
        return { r: match[1], g: match[2], b: match[3], a: match[4] };
    }
    if ((match = matchers.hsl.exec(color))) {
        return { h: match[1], s: match[2], l: match[3] };
    }
    if ((match = matchers.hsla.exec(color))) {
        return { h: match[1], s: match[2], l: match[3], a: match[4] };
    }
    if ((match = matchers.hsv.exec(color))) {
        return { h: match[1], s: match[2], v: match[3] };
    }
    if ((match = matchers.hsva.exec(color))) {
        return { h: match[1], s: match[2], v: match[3], a: match[4] };
    }
    if ((match = matchers.hex8.exec(color))) {
        return {
            r: parseIntFromHex(match[1]),
            g: parseIntFromHex(match[2]),
            b: parseIntFromHex(match[3]),
            a: convertHexToDecimal(match[4]),
            format: named ? "name" : "hex8"
        };
    }
    if ((match = matchers.hex6.exec(color))) {
        return {
            r: parseIntFromHex(match[1]),
            g: parseIntFromHex(match[2]),
            b: parseIntFromHex(match[3]),
            format: named ? "name" : "hex"
        };
    }
    if ((match = matchers.hex4.exec(color))) {
        return {
            r: parseIntFromHex(match[1] + '' + match[1]),
            g: parseIntFromHex(match[2] + '' + match[2]),
            b: parseIntFromHex(match[3] + '' + match[3]),
            a: convertHexToDecimal(match[4] + '' + match[4]),
            format: named ? "name" : "hex8"
        };
    }
    if ((match = matchers.hex3.exec(color))) {
        return {
            r: parseIntFromHex(match[1] + '' + match[1]),
            g: parseIntFromHex(match[2] + '' + match[2]),
            b: parseIntFromHex(match[3] + '' + match[3]),
            format: named ? "name" : "hex"
        };
    }

    return false;
}

function validateWCAG2Parms(parms) {
    // return valid WCAG2 parms for isReadable.
    // If input parms are invalid, return {"level":"AA", "size":"small"}
    var level, size;
    parms = parms || {"level":"AA", "size":"small"};
    level = (parms.level || "AA").toUpperCase();
    size = (parms.size || "small").toLowerCase();
    if (level !== "AA" && level !== "AAA") {
        level = "AA";
    }
    if (size !== "small" && size !== "large") {
        size = "small";
    }
    return {"level":level, "size":size};
}

this.tinycolor = tinycolor;

})()`;
}
// It is hacky way to make this function will be compiled preferentially by less
// resolve error: `ReferenceError: colorPalette is not defined`
// https://github.com/ant-design/ant-motion/issues/44
.tinyColorMixin();

// We create a very complex algorithm which take the place of original tint/shade color system
// to make sure no one can understand it 👻
// and create an entire color palette magicly by inputing just a single primary color.
// We are using bezier-curve easing function and some color manipulations like tint/shade/darken/spin
.colorPaletteMixin() {
  @functions: ~`(function() {
  var hueStep = 2;
  var saturationStep = 16;
  var saturationStep2 = 5;
  var brightnessStep1 = 5;
  var brightnessStep2 = 15;
  var lightColorCount = 5;
  var darkColorCount = 4;

  var getHue = function(hsv, i, isLight) {
    var hue;
    if (hsv.h >= 60 && hsv.h <= 240) {
      hue = isLight ? hsv.h - hueStep * i : hsv.h + hueStep * i;
    } else {
      hue = isLight ? hsv.h + hueStep * i : hsv.h - hueStep * i;
    }
    if (hue < 0) {
      hue += 360;
    } else if (hue >= 360) {
      hue -= 360;
    }
    return Math.round(hue);
  };
  var getSaturation = function(hsv, i, isLight) {
    var saturation;
    if (isLight) {
      saturation = Math.round(hsv.s * 100) - saturationStep * i;
    } else if (i == darkColorCount) {
      saturation = Math.round(hsv.s * 100) + saturationStep;
    } else {
      saturation = Math.round(hsv.s * 100) + saturationStep2 * i;
    }
    if (saturation > 100) {
      saturation = 100;
    }
    if (isLight && i === lightColorCount && saturation > 10) {
      saturation = 10;
    }
    if (saturation < 6) {
      saturation = 6;
    }
    return Math.round(saturation);
  };
  var getValue = function(hsv, i, isLight) {
    if (isLight) {
      return Math.round(hsv.v * 100) + brightnessStep1 * i;
    }
    return Math.round(hsv.v * 100) - brightnessStep2 * i;
  };

  this.colorPalette = function(color, index) {
    var isLight = index <= 6;
    var hsv = tinycolor(color).toHsv();
    var i = isLight ? lightColorCount + 1 - index : index - lightColorCount - 1;
    return tinycolor({
      h: getHue(hsv, i, isLight),
      s: getSaturation(hsv, i, isLight),
      v: getValue(hsv, i, isLight),
    }).toHexString();
  };
})()`;
}
// It is hacky way to make this function will be compiled preferentially by less
// resolve error: `ReferenceError: colorPalette is not defined`
// https://github.com/ant-design/ant-motion/issues/44
.colorPaletteMixin();

// color palettes
@blue-1: color(~`colorPalette("@{blue-6}", 1)`);
@blue-2: color(~`colorPalette("@{blue-6}", 2)`);
@blue-3: color(~`colorPalette("@{blue-6}", 3)`);
@blue-4: color(~`colorPalette("@{blue-6}", 4)`);
@blue-5: color(~`colorPalette("@{blue-6}", 5)`);
@blue-6: #1890ff;
@blue-7: color(~`colorPalette("@{blue-6}", 7)`);
@blue-8: color(~`colorPalette("@{blue-6}", 8)`);
@blue-9: color(~`colorPalette("@{blue-6}", 9)`);
@blue-10: color(~`colorPalette("@{blue-6}", 10)`);

@purple-1: color(~`colorPalette("@{purple-6}", 1)`);
@purple-2: color(~`colorPalette("@{purple-6}", 2)`);
@purple-3: color(~`colorPalette("@{purple-6}", 3)`);
@purple-4: color(~`colorPalette("@{purple-6}", 4)`);
@purple-5: color(~`colorPalette("@{purple-6}", 5)`);
@purple-6: #722ed1;
@purple-7: color(~`colorPalette("@{purple-6}", 7)`);
@purple-8: color(~`colorPalette("@{purple-6}", 8)`);
@purple-9: color(~`colorPalette("@{purple-6}", 9)`);
@purple-10: color(~`colorPalette("@{purple-6}", 10)`);

@cyan-1: color(~`colorPalette("@{cyan-6}", 1)`);
@cyan-2: color(~`colorPalette("@{cyan-6}", 2)`);
@cyan-3: color(~`colorPalette("@{cyan-6}", 3)`);
@cyan-4: color(~`colorPalette("@{cyan-6}", 4)`);
@cyan-5: color(~`colorPalette("@{cyan-6}", 5)`);
@cyan-6: #13c2c2;
@cyan-7: color(~`colorPalette("@{cyan-6}", 7)`);
@cyan-8: color(~`colorPalette("@{cyan-6}", 8)`);
@cyan-9: color(~`colorPalette("@{cyan-6}", 9)`);
@cyan-10: color(~`colorPalette("@{cyan-6}", 10)`);

@green-1: color(~`colorPalette("@{green-6}", 1)`);
@green-2: color(~`colorPalette("@{green-6}", 2)`);
@green-3: color(~`colorPalette("@{green-6}", 3)`);
@green-4: color(~`colorPalette("@{green-6}", 4)`);
@green-5: color(~`colorPalette("@{green-6}", 5)`);
@green-6: #52c41a;
@green-7: color(~`colorPalette("@{green-6}", 7)`);
@green-8: color(~`colorPalette("@{green-6}", 8)`);
@green-9: color(~`colorPalette("@{green-6}", 9)`);
@green-10: color(~`colorPalette("@{green-6}", 10)`);

@magenta-1: color(~`colorPalette("@{magenta-6}", 1)`);
@magenta-2: color(~`colorPalette("@{magenta-6}", 2)`);
@magenta-3: color(~`colorPalette("@{magenta-6}", 3)`);
@magenta-4: color(~`colorPalette("@{magenta-6}", 4)`);
@magenta-5: color(~`colorPalette("@{magenta-6}", 5)`);
@magenta-6: #eb2f96;
@magenta-7: color(~`colorPalette("@{magenta-6}", 7)`);
@magenta-8: color(~`colorPalette("@{magenta-6}", 8)`);
@magenta-9: color(~`colorPalette("@{magenta-6}", 9)`);
@magenta-10: color(~`colorPalette("@{magenta-6}", 10)`);

// alias of magenta
@pink-1: color(~`colorPalette("@{pink-6}", 1)`);
@pink-2: color(~`colorPalette("@{pink-6}", 2)`);
@pink-3: color(~`colorPalette("@{pink-6}", 3)`);
@pink-4: color(~`colorPalette("@{pink-6}", 4)`);
@pink-5: color(~`colorPalette("@{pink-6}", 5)`);
@pink-6: #eb2f96;
@pink-7: color(~`colorPalette("@{pink-6}", 7)`);
@pink-8: color(~`colorPalette("@{pink-6}", 8)`);
@pink-9: color(~`colorPalette("@{pink-6}", 9)`);
@pink-10: color(~`colorPalette("@{pink-6}", 10)`);

@red-1: color(~`colorPalette("@{red-6}", 1)`);
@red-2: color(~`colorPalette("@{red-6}", 2)`);
@red-3: color(~`colorPalette("@{red-6}", 3)`);
@red-4: color(~`colorPalette("@{red-6}", 4)`);
@red-5: color(~`colorPalette("@{red-6}", 5)`);
@red-6: #f5222d;
@red-7: color(~`colorPalette("@{red-6}", 7)`);
@red-8: color(~`colorPalette("@{red-6}", 8)`);
@red-9: color(~`colorPalette("@{red-6}", 9)`);
@red-10: color(~`colorPalette("@{red-6}", 10)`);

@orange-1: color(~`colorPalette("@{orange-6}", 1)`);
@orange-2: color(~`colorPalette("@{orange-6}", 2)`);
@orange-3: color(~`colorPalette("@{orange-6}", 3)`);
@orange-4: color(~`colorPalette("@{orange-6}", 4)`);
@orange-5: color(~`colorPalette("@{orange-6}", 5)`);
@orange-6: #fa8c16;
@orange-7: color(~`colorPalette("@{orange-6}", 7)`);
@orange-8: color(~`colorPalette("@{orange-6}", 8)`);
@orange-9: color(~`colorPalette("@{orange-6}", 9)`);
@orange-10: color(~`colorPalette("@{orange-6}", 10)`);

@yellow-1: color(~`colorPalette("@{yellow-6}", 1)`);
@yellow-2: color(~`colorPalette("@{yellow-6}", 2)`);
@yellow-3: color(~`colorPalette("@{yellow-6}", 3)`);
@yellow-4: color(~`colorPalette("@{yellow-6}", 4)`);
@yellow-5: color(~`colorPalette("@{yellow-6}", 5)`);
@yellow-6: #fadb14;
@yellow-7: color(~`colorPalette("@{yellow-6}", 7)`);
@yellow-8: color(~`colorPalette("@{yellow-6}", 8)`);
@yellow-9: color(~`colorPalette("@{yellow-6}", 9)`);
@yellow-10: color(~`colorPalette("@{yellow-6}", 10)`);

@volcano-1: color(~`colorPalette("@{volcano-6}", 1)`);
@volcano-2: color(~`colorPalette("@{volcano-6}", 2)`);
@volcano-3: color(~`colorPalette("@{volcano-6}", 3)`);
@volcano-4: color(~`colorPalette("@{volcano-6}", 4)`);
@volcano-5: color(~`colorPalette("@{volcano-6}", 5)`);
@volcano-6: #fa541c;
@volcano-7: color(~`colorPalette("@{volcano-6}", 7)`);
@volcano-8: color(~`colorPalette("@{volcano-6}", 8)`);
@volcano-9: color(~`colorPalette("@{volcano-6}", 9)`);
@volcano-10: color(~`colorPalette("@{volcano-6}", 10)`);

@geekblue-1: color(~`colorPalette("@{geekblue-6}", 1)`);
@geekblue-2: color(~`colorPalette("@{geekblue-6}", 2)`);
@geekblue-3: color(~`colorPalette("@{geekblue-6}", 3)`);
@geekblue-4: color(~`colorPalette("@{geekblue-6}", 4)`);
@geekblue-5: color(~`colorPalette("@{geekblue-6}", 5)`);
@geekblue-6: #2f54eb;
@geekblue-7: color(~`colorPalette("@{geekblue-6}", 7)`);
@geekblue-8: color(~`colorPalette("@{geekblue-6}", 8)`);
@geekblue-9: color(~`colorPalette("@{geekblue-6}", 9)`);
@geekblue-10: color(~`colorPalette("@{geekblue-6}", 10)`);

@lime-1: color(~`colorPalette("@{lime-6}", 1)`);
@lime-2: color(~`colorPalette("@{lime-6}", 2)`);
@lime-3: color(~`colorPalette("@{lime-6}", 3)`);
@lime-4: color(~`colorPalette("@{lime-6}", 4)`);
@lime-5: color(~`colorPalette("@{lime-6}", 5)`);
@lime-6: #a0d911;
@lime-7: color(~`colorPalette("@{lime-6}", 7)`);
@lime-8: color(~`colorPalette("@{lime-6}", 8)`);
@lime-9: color(~`colorPalette("@{lime-6}", 9)`);
@lime-10: color(~`colorPalette("@{lime-6}", 10)`);

@gold-1: color(~`colorPalette("@{gold-6}", 1)`);
@gold-2: color(~`colorPalette("@{gold-6}", 2)`);
@gold-3: color(~`colorPalette("@{gold-6}", 3)`);
@gold-4: color(~`colorPalette("@{gold-6}", 4)`);
@gold-5: color(~`colorPalette("@{gold-6}", 5)`);
@gold-6: #faad14;
@gold-7: color(~`colorPalette("@{gold-6}", 7)`);
@gold-8: color(~`colorPalette("@{gold-6}", 8)`);
@gold-9: color(~`colorPalette("@{gold-6}", 9)`);
@gold-10: color(~`colorPalette("@{gold-6}", 10)`);

// The prefix to use on all css classes from ant.
@ant-prefix             : ant;

// -------- Colors -----------

@info-color             : @blue-6;
@success-color          : @green-6;
@processing-color       : @blue-6;
@error-color            : @red-6;
@highlight-color        : @red-6;
@warning-color          : @gold-6;
@normal-color           : #d9d9d9;

// Color used by default to control hover and active backgrounds and for
// alert info backgrounds.
@primary-1: color(~`colorPalette("@{primary-color}", 1)`);  // replace tint(@primary-color, 90%)
@primary-2: color(~`colorPalette("@{primary-color}", 2)`);  // replace tint(@primary-color, 80%)
@primary-3: color(~`colorPalette("@{primary-color}", 3)`);  // unused
@primary-4: color(~`colorPalette("@{primary-color}", 4)`);  // unused
@primary-5: color(~`colorPalette("@{primary-color}", 5)`);  // color used to control the text color in many active and hover states, replace tint(@primary-color, 20%)
@primary-6: @primary-color;                                 // color used to control the text color of active buttons, don't use, use @primary-color
@primary-7: color(~`colorPalette("@{primary-color}", 7)`);  // replace shade(@primary-color, 5%)
@primary-8: color(~`colorPalette("@{primary-color}", 8)`);  // unused
@primary-9: color(~`colorPalette("@{primary-color}", 9)`);  // unused
@primary-10: color(~`colorPalette("@{primary-color}", 10)`);  // unused

// Base Scaffolding Variables
// ---

// Background color for `<body>`
@body-background        : #fff;
// Base background color for most components
@component-background   : #fff;
@font-family  : "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif,
"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
@code-family            : "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
@heading-color          : fade(#000, 85%);
@text-color             : fade(#000, 65%);
@text-color-secondary   : fade(#000, 45%);
@heading-color-dark     : fade(#fff, 100%);
@text-color-dark        : fade(#fff, 85%);
@text-color-secondary-dark: fade(#fff, 65%);
@font-size-base         : 14px;
@font-size-lg           : @font-size-base + 2px;
@font-size-sm           : 12px;
@line-height-base       : 1.5;
@border-radius-base     : 4px;
@border-radius-sm       : 2px;

// vertical paddings
@padding-lg    : 24px; // containers
@padding-md    : 16px; // small containers and buttons
@padding-sm    : 12px; // Form controls and items
@padding-xs    : 8px;  // small items

// vertical padding for all form controls
@control-padding-horizontal: @padding-sm;
@control-padding-horizontal-sm: @padding-xs;

// The background colors for active and hover states for things like
// list items or table cells.
@item-active-bg         : @primary-1;
@item-hover-bg          : @primary-1;

// ICONFONT
@iconfont-css-prefix    : anticon;

// LINK
@link-color             : @primary-color;
@link-hover-color       : color(~`colorPalette("@{link-color}", 5)`);
@link-active-color      : color(~`colorPalette("@{link-color}", 7)`);
@link-decoration        : none;
@link-hover-decoration  : none;

// Animation
@ease-base-out       : cubic-bezier(0.7, 0.3, 0.1, 1);
@ease-base-in        : cubic-bezier(0.9, 0, 0.3, 0.7);
@ease-out            : cubic-bezier(0.215, 0.61, 0.355, 1);
@ease-in             : cubic-bezier(0.55, 0.055, 0.675, 0.19);
@ease-in-out         : cubic-bezier(0.645, 0.045, 0.355, 1);
@ease-out-back       : cubic-bezier(0.12, 0.4, 0.29, 1.46);
@ease-in-back        : cubic-bezier(0.71, -0.46, 0.88, 0.6);
@ease-in-out-back    : cubic-bezier(0.71, -0.46, 0.29, 1.46);
@ease-out-circ       : cubic-bezier(0.08, 0.82, 0.17, 1);
@ease-in-circ        : cubic-bezier(0.6, 0.04, 0.98, 0.34);
@ease-in-out-circ    : cubic-bezier(0.78, 0.14, 0.15, 0.86);
@ease-out-quint      : cubic-bezier(0.23, 1, 0.32, 1);
@ease-in-quint       : cubic-bezier(0.755, 0.05, 0.855, 0.06);
@ease-in-out-quint   : cubic-bezier(0.86, 0, 0.07, 1);

// Border color
@border-color-base      : hsv(0, 0, 85%);  // base border outline a component
@border-color-split     : hsv(0, 0, 91%);  // split border inside a component
@border-width-base      : 1px;            // width of the border for a component
@border-style-base      : solid;          // style of a components border

// Outline
@outline-blur-size      : 0;
@outline-width          : 2px;
@outline-color          : @primary-color;

@background-color-light : hsv(0, 0, 98%);  // background of header and selected item
@background-color-base  : hsv(0, 0, 96%);  // Default grey background color

// Disabled states
@disabled-color         : fade(#000, 25%);
@disabled-bg            : @background-color-base;
@disabled-color-dark    : fade(#fff, 35%);

// Shadow
@shadow-color           : rgba(0, 0, 0, .15);
@box-shadow-base        : @shadow-1-down;
@shadow-1-up            : 0 -2px 8px @shadow-color;
@shadow-1-down          : 0 2px 8px @shadow-color;
@shadow-1-left          : -2px 0 8px @shadow-color;
@shadow-1-right         : 2px 0 8px @shadow-color;
@shadow-2               : 0 4px 12px @shadow-color;

// Buttons
@btn-font-weight        : 400;
@btn-border-radius-base : @border-radius-base;
@btn-border-radius-sm   : @border-radius-base;

@btn-primary-color      : #fff;
@btn-primary-bg         : @primary-color;

@btn-default-color      : @text-color;
@btn-default-bg         : #fff;
@btn-default-border     : @border-color-base;

@btn-danger-color       : @error-color;
@btn-danger-bg          : @background-color-base;
@btn-danger-border      : @border-color-base;

@btn-disable-color      : @disabled-color;
@btn-disable-bg         : @disabled-bg;
@btn-disable-border     : @border-color-base;

@btn-padding-base       : 0 @padding-md - 1px;
@btn-font-size-lg       : @font-size-lg;
@btn-font-size-sm       : @font-size-base;
@btn-padding-lg         : @btn-padding-base;
@btn-padding-sm         : 0 @padding-xs - 1px;

@btn-height-base        : 32px;
@btn-height-lg          : 40px;
@btn-height-sm          : 24px;

@btn-circle-size        : @btn-height-base;
@btn-circle-size-lg     : @btn-height-lg;
@btn-circle-size-sm     : @btn-height-sm;

@btn-group-border       : @primary-5;

// Checkbox
@checkbox-size          : 16px;
@checkbox-color         : @primary-color;
@checkbox-check-color   : #fff;

// Radio
@radio-size             : 16px;
@radio-dot-color        : @primary-color;

// Radio buttons
@radio-button-bg           : @btn-default-bg;
@radio-button-color        : @btn-default-color;
@radio-button-hover-color  : @primary-5;
@radio-button-active-color : @primary-7;

// Media queries breakpoints
// Extra small screen / phone
@screen-xs              : 480px;
@screen-xs-min          : @screen-xs;

// Small screen / tablet
@screen-sm              : 576px;
@screen-sm-min          : @screen-sm;

// Medium screen / desktop
@screen-md              : 768px;
@screen-md-min          : @screen-md;

// Large screen / wide desktop
@screen-lg              : 992px;
@screen-lg-min          : @screen-lg;

// Extra large screen / full hd
@screen-xl              : 1200px;
@screen-xl-min          : @screen-xl;

// Extra extra large screen / large descktop
@screen-xxl              : 1600px;
@screen-xxl-min          : @screen-xxl;

// provide a maximum
@screen-xs-max          : (@screen-sm-min - 1px);
@screen-sm-max          : (@screen-md-min - 1px);
@screen-md-max          : (@screen-lg-min - 1px);
@screen-lg-max          : (@screen-xl-min - 1px);
@screen-xl-max          : (@screen-xxl-min - 1px);

// Grid system
@grid-columns           : 24;
@grid-gutter-width      : 0;

// Layout
@layout-body-background         : #f0f2f5;
@layout-header-background       : #001529;
@layout-footer-background       : @layout-body-background;
@layout-header-height           : 64px;
@layout-header-padding          : 0 50px;
@layout-footer-padding          : 24px 50px;
@layout-sider-background        : @layout-header-background;
@layout-trigger-height          : 48px;
@layout-trigger-background      : #002140;
@layout-trigger-color           : #fff;
@layout-zero-trigger-width      : 36px;
@layout-zero-trigger-height     : 42px;
// Layout light theme
@layout-sider-background-light  : #fff;
@layout-trigger-background-light: #fff;
@layout-trigger-color-light     : @text-color;

// z-index list
@zindex-affix           : 10;
@zindex-back-top        : 10;
@zindex-modal-mask      : 1000;
@zindex-modal           : 1000;
@zindex-notification    : 1010;
@zindex-message         : 1010;
@zindex-popover         : 1030;
@zindex-picker          : 1050;
@zindex-dropdown        : 1050;
@zindex-tooltip         : 1060;

// Animation
@animation-duration-slow: .3s; // Modal
@animation-duration-base: .2s;
@animation-duration-fast: .1s; // Tooltip

// Form
// ---
@label-required-color        : @highlight-color;
@label-color                 : @heading-color;
@form-item-margin-bottom     : 24px;
@form-item-trailing-colon    : true;
@form-vertical-label-padding : 0 0 8px;
@form-vertical-label-margin  : 0;

// Input
// ---
@input-height-base           : 32px;
@input-height-lg             : 40px;
@input-height-sm             : 24px;
@input-padding-horizontal    : @control-padding-horizontal - 1px;
@input-padding-horizontal-base: @input-padding-horizontal;
@input-padding-horizontal-sm : @control-padding-horizontal-sm - 1px;
@input-padding-horizontal-lg : @input-padding-horizontal;
@input-padding-vertical-base : 4px;
@input-padding-vertical-sm   : 1px;
@input-padding-vertical-lg   : 6px;
@input-placeholder-color     : hsv(0, 0, 75%);
@input-color                 : @text-color;
@input-border-color          : @border-color-base;
@input-bg                    : #fff;
@input-addon-bg              : @background-color-light;
@input-hover-border-color    : @primary-color;
@input-disabled-bg           : @disabled-bg;
@input-outline-offset        : 0 0;

// Tooltip
// ---
//* Tooltip max width
@tooltip-max-width: 250px;
//** Tooltip text color
@tooltip-color: #fff;
//** Tooltip background color
@tooltip-bg: rgba(0, 0, 0, .75);
//** Tooltip arrow width
@tooltip-arrow-width: 5px;
//** Tooltip distance with trigger
@tooltip-distance: @tooltip-arrow-width - 1px + 4px;
//** Tooltip arrow color
@tooltip-arrow-color: @tooltip-bg;

// Popover
// ---
//** Popover body background color
@popover-bg: #fff;
//** Popover text color
@popover-color: @text-color;
//** Popover maximum width
@popover-min-width: 177px;
//** Popover arrow width
@popover-arrow-width: 6px;
//** Popover arrow color
@popover-arrow-color: @popover-bg;
//** Popover outer arrow width
//** Popover outer arrow color
@popover-arrow-outer-color: @popover-bg;
//** Popover distance with trigger
@popover-distance: @popover-arrow-width + 4px;

// Modal
// --
@modal-mask-bg: rgba(0, 0, 0, 0.65);

// Progress
// --
@progress-default-color: @processing-color;
@progress-remaining-color: @background-color-base;
@progress-text-color: @text-color;

// Menu
// ---
@menu-inline-toplevel-item-height: 40px;
@menu-item-height: 40px;
@menu-collapsed-width: 80px;
@menu-bg: @component-background;
@menu-item-color: @text-color;
@menu-highlight-color: @primary-color;
@menu-item-active-bg: @item-active-bg;
@menu-item-active-border-width: 3px;
@menu-item-group-title-color: @text-color-secondary;
// dark theme
@menu-dark-color: @text-color-secondary-dark;
@menu-dark-bg: @layout-header-background;
@menu-dark-arrow-color: #fff;
@menu-dark-submenu-bg: #000c17;
@menu-dark-highlight-color: #fff;
@menu-dark-item-active-bg: @primary-color;

// Spin
// ---
@spin-dot-size-sm: 14px;
@spin-dot-size: 20px;
@spin-dot-size-lg: 32px;

// Table
// --
@table-header-bg: @background-color-light;
@table-header-color: @heading-color;
@table-header-sort-bg: @background-color-base;
@table-body-sort-bg: rgba(0, 0, 0, .01);
@table-row-hover-bg: @primary-1;
@table-selected-row-bg: #fafafa;
@table-expanded-row-bg: #fbfbfb;
@table-padding-vertical: 16px;
@table-padding-horizontal: 16px;

// Tag
// --
@tag-default-bg: @background-color-light;
@tag-default-color: @text-color;
@tag-font-size: @font-size-sm;

// TimePicker
// ---
@time-picker-panel-column-width: 56px;
@time-picker-panel-width: @time-picker-panel-column-width * 3;
@time-picker-selected-bg: @background-color-base;

// Carousel
// ---
@carousel-dot-width: 16px;
@carousel-dot-height: 3px;
@carousel-dot-active-width: 24px;

// Badge
// ---
@badge-height: 20px;
@badge-dot-size: 6px;
@badge-font-size: @font-size-sm;
@badge-font-weight: normal;
@badge-status-size: 6px;

// Rate
// ---
@rate-star-color: @yellow-6;
@rate-star-bg: @border-color-split;

// Card
// ---
@card-head-color: @heading-color;
@card-head-background: transparent;
@card-head-padding: 16px;
@card-inner-head-padding: 12px;
@card-padding-base: 24px;
@card-padding-wider: 32px;
@card-actions-background: @background-color-light;
@card-shadow: 0 2px 8px rgba(0, 0, 0, .09);

// Tabs
// ---
@tabs-card-head-background: @background-color-light;
@tabs-card-height: 40px;
@tabs-card-active-color: @primary-color;
@tabs-title-font-size: @font-size-base;
@tabs-title-font-size-lg: @font-size-lg;
@tabs-title-font-size-sm: @font-size-base;
@tabs-ink-bar-color: @primary-color;
@tabs-bar-margin: 0 0 16px 0;
@tabs-horizontal-margin: 0 32px 0 0;
@tabs-horizontal-padding: 12px 16px;
@tabs-vertical-padding: 8px 24px;
@tabs-vertical-margin: 0 0 16px 0;
@tabs-scrolling-size: 32px;
@tabs-highlight-color: @primary-color;
@tabs-hover-color: @primary-5;
@tabs-active-color: @primary-7;

// BackTop
// ---
@back-top-color: #fff;
@back-top-bg: @text-color-secondary;
@back-top-hover-bg: @text-color;

// Avatar
// ---
@avatar-size-base: 32px;
@avatar-size-lg: 40px;
@avatar-size-sm: 24px;
@avatar-font-size-base: 18px;
@avatar-font-size-lg: 24px;
@avatar-font-size-sm: 14px;
@avatar-bg: #ccc;
@avatar-color: #fff;
@avatar-border-radius: @border-radius-base;

// Switch
// ---
@switch-height: 22px;
@switch-sm-height: 16px;
@switch-sm-checked-margin-left: -(@switch-sm-height - 3px);
@switch-disabled-opacity: 0.4;
@switch-color: @primary-color;

// Pagination
// ---
@pagination-item-size: 32px;
@pagination-item-size-sm: 24px;
@pagination-font-family: Arial;
@pagination-font-weight-active: 500;

// Breadcrumb
// ---
@breadcrumb-base-color:        @text-color-secondary;
@breadcrumb-last-item-color:   @text-color;
@breadcrumb-font-size:         @font-size-base;
@breadcrumb-icon-font-size:    @font-size-base;
@breadcrumb-link-color:        @text-color-secondary;
@breadcrumb-link-color-hover:  @primary-5;
@breadcrumb-separator-color:   @text-color-secondary;
@breadcrumb-separator-margin:  0 @padding-xs;

// Slider
// ---
@slider-margin:                       14px 6px 10px;
@slider-rail-background-color:        @background-color-base;
@slider-rail-background-color-hover:  #e1e1e1;
@slider-track-background-color:       @primary-3;
@slider-track-background-color-hover: @primary-4;
@slider-handle-color:                 @primary-3;
@slider-handle-color-hover:           @primary-4;
@slider-handle-color-focus:           tint(@primary-color, 20%);
@slider-handle-color-focus-shadow:    tint(@primary-color, 50%);
@slider-handle-color-tooltip-open:    @primary-color;
@slider-dot-border-color:             @border-color-split;
@slider-dot-border-color-active:      tint(@primary-color, 50%);
@slider-disabled-color:               @disabled-color;
@slider-disabled-background-color:    @component-background;

// Tree
// ---
@tree-title-height:                   24px;
@tree-child-padding:                  18px;
@tree-directory-selected-color:       #fff;
@tree-directory-selected-bg:          @primary-color;

// Collapse
// ---
@collapse-header-padding:             12px 0 12px 40px;
@collapse-header-bg:                  @background-color-light;
@collapse-content-padding:            @padding-md;
@collapse-content-bg:                 @component-background;

// Skeleton
// ---
@skeleton-color:                      #f2f2f2;

// Transfer
// ---
@transfer-disabled-bg:                @disabled-bg;

// Message
// ---
@message-notice-content-padding:      10px 16px;

// Motion
// ---
@wave-animation-width: 6px;

// Alert
// ---
@alert-success-border-color: ~`colorPalette("@{success-color}", 3)`;
@alert-success-bg-color: ~`colorPalette("@{success-color}", 1)`;
@alert-success-icon-color: @success-color;
@alert-info-border-color: ~`colorPalette("@{info-color}", 3)`;
@alert-info-bg-color: ~`colorPalette("@{info-color}", 1)`;
@alert-info-icon-color: @info-color;
@alert-warning-border-color: ~`colorPalette("@{warning-color}", 3)`;
@alert-warning-bg-color: ~`colorPalette("@{warning-color}", 1)`;
@alert-warning-icon-color: @warning-color;
@alert-error-border-color: ~`colorPalette("@{error-color}", 3)`;
@alert-error-bg-color: ~`colorPalette("@{error-color}", 1)`;
@alert-error-icon-color: @error-color;

// List
// ---
@list-empty-text-padding:                    @padding-md;
@list-item-padding:                          @padding-sm 0;
@list-item-content-margin:                   0 0 @padding-md 0;
@list-item-meta-margin-bottom:               @padding-md;
@list-item-meta-avatar-margin-right:         @padding-md;
@list-item-meta-title-margin-bottom:         @padding-sm;

// Menu
@menu-dark-item-selected-bg: @menu-dark-item-active-bg;

// Tabs
@tab-bar-margin: @tabs-bar-margin;
@tab-horizontal-margin: @tabs-horizontal-margin;
@tab-vertical-margin: @tabs-vertical-margin;
@tab-horizontal-padding: @tabs-horizontal-padding;
@tab-vertical-padding: @tabs-vertical-padding;
@tab-scrolling-size: @tabs-scrolling-size;
@tab-highlight-color: @tabs-highlight-color;
@tab-hover-color: @tabs-hover-color;
@tab-active-color: @tabs-active-color;
@tabs-ink-bar-bg-color: @tabs-ink-bar-color;

.listContent .extra {
  color: rgba(0, 0, 0, 0.45);
}
.listContent .extra > em {
  color: rgba(0, 0, 0, 0.25);
}
.avatarItem :global .ant-avatar {
  border: 1px solid #fff;
}
.chartCard .avatar img {
  border-radius: 100%;
}
.chartCard .meta {
  color: rgba(0, 0, 0, 0.45);
}
.chartCard .total {
  color: rgba(0, 0, 0, 0.85);
}
.chartCard .footer {
  border-top: 1px solid #e8e8e8;
}
.field span:last-child {
  color: rgba(0, 0, 0, 0.85);
}
.miniProgress .progressWrap {
  background-color: #f5f5f5;
}
.miniProgress .progress {
  border-radius: 1px 0 0 1px;
  background-color: @primary-color;
}
.miniProgress .target span {
  border-radius: 100px;
}
.pie .dot {
  border-radius: 8px;
}
.pie .line {
  background-color: #e8e8e8;
}
.pie .legendTitle {
  color: rgba(0, 0, 0, 0.65);
}
.pie .percent {
  color: rgba(0, 0, 0, 0.45);
}
.pie .total > h4 {
  color: rgba(0, 0, 0, 0.45);
}
.pie .total > p {
  color: rgba(0, 0, 0, 0.85);
}
.radar .legend .legendItem {
  color: rgba(0, 0, 0, 0.45);
}
.radar .legend .legendItem h6 {
  color: rgba(0, 0, 0, 0.85);
}
.radar .legend .legendItem:after {
  background-color: #e8e8e8;
}
.radar .legend .dot {
  border-radius: 6px;
}

.timelineChart {
  background: #fff;
}
.waterWave .text span {
  color: rgba(0, 0, 0, 0.45);
}
.waterWave .text h4 {
  color: rgba(0, 0, 0, 0.85);
}
.descriptionList .title {
  color: rgba(0, 0, 0, 0.85);
}
.descriptionList .term {
  color: rgba(0, 0, 0, 0.85);
}
.descriptionList .detail {
  color: rgba(0, 0, 0, 0.65);
}
.descriptionList.small .title {
  color: rgba(0, 0, 0, 0.65);
}
.linkGroup > a {
  color: rgba(0, 0, 0, 0.65);
}
.linkGroup > a:hover {
  color: @primary-color;
}
.lines .shadow {
  color: transparent;
}
.exception .imgEle {
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: contain;
}
.exception .content h1 {
  color: #434e59;
}
.exception .content .desc {
  color: rgba(0, 0, 0, 0.45);
}
.toolbar {
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);
  background: #fff;
  border-top: 1px solid #e8e8e8;
}
.globalFooter .links a {
  color: rgba(0, 0, 0, 0.45);
}
.globalFooter .links a:hover {
  color: rgba(0, 0, 0, 0.65);
}
.globalFooter .copyright {
  color: rgba(0, 0, 0, 0.45);
}
.layout .header {
  background-color: @primary-color !important;
}
i.trigger:hover {
  background: rgba(0, 0, 0, 0.025);
}
.right .action > i {
  color: rgba(0, 0, 0, 0.65);
}
.right .action:hover {
  background: rgba(0, 0, 0, 0.025);
}
:global(.right .action.ant-popover-open) {
  background: rgba(0, 0, 0, 0.025);
}
.right .search:hover {
  background: transparent;
}
.right .account .avatar {
  color: @primary-color;
  background: rgba(255, 255, 255, 0.85);
}
.dark .action {
  color: rgba(255, 255, 255, 0.85);
}
.dark .action > i {
  color: rgba(255, 255, 255, 0.85);
}
.dark .action:hover,
.dark .action:global(.ant-popover-open) {
  background: @primary-color;
}
.dark .action :global(.ant-badge) {
  color: rgba(255, 255, 255, 0.85);
}
.headerSearch .input {
  background: transparent;
  border-radius: 0;
}
.headerSearch .input :global(.ant-select-selection) {
  background: transparent;
}
.headerSearch .input input {
  border: 0;
  box-shadow: none !important;
}
.headerSearch .input,
.headerSearch .input:hover,
.headerSearch .input:focus {
  border-bottom: 1px solid #d9d9d9;
}
.login :global .ant-tabs .ant-tabs-bar {
  border-bottom: 0;
}
.login .icon {
  color: rgba(0, 0, 0, 0.2);
}
.login .icon:hover {
  color: @primary-color;
}
.login .prefixIcon {
  color: rgba(0, 0, 0, 0.25);
}
.list .item .avatar {
  background: #fff;
}
.list .item:last-child {
  border-bottom: 0;
}
.list .item:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.list .item .extra {
  color: rgba(0, 0, 0, 0.45);
}
.notFound {
  color: rgba(0, 0, 0, 0.45);
}
.clear {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 0 0 4px 4px;
  border-top: 1px solid #e8e8e8;
}
.clear:hover {
  color: rgba(0, 0, 0, 0.85);
}
.numberInfo .suffix {
  color: rgba(0, 0, 0, 0.65);
}
.numberInfo .numberInfoTitle {
  color: rgba(0, 0, 0, 0.65);
}
.numberInfo .numberInfoSubTitle {
  color: rgba(0, 0, 0, 0.45);
}
.numberInfo .numberInfoValue > span {
  color: rgba(0, 0, 0, 0.85);
}
.numberInfo .numberInfoValue .subTotal {
  color: rgba(0, 0, 0, 0.45);
}
.numberInfo .numberInfoValue .subTotal :global .anticon-caret-up {
  color: #f5222d;
}
.numberInfo .numberInfoValue .subTotal :global .anticon-caret-down {
  color: #52c41a;
}
.numberInfolight .numberInfoValue > span {
  color: rgba(0, 0, 0, 0.65);
}
.pageHeader {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}
.pageHeader .tabs :global .ant-tabs-bar {
  border-bottom: 1px solid #e8e8e8;
}
.pageHeader .logo > img {
  border-radius: 4px;
}
.pageHeader .title {
  color: rgba(0, 0, 0, 0.85);
}
.result .icon > .success {
  color: #52c41a;
}
.result .icon > .error {
  color: #f5222d;
}
.result .title {
  color: rgba(0, 0, 0, 0.85);
}
.result .description {
  color: rgba(0, 0, 0, 0.45);
}
.result .extra {
  background: #fafafa;
  border-radius: 2px;
}
.blockChecbox .item {
  border-radius: 4px;
}
.blockChecbox .selectIcon {
  color: @primary-color;
}
.color_block {
  border-radius: 4px;
}
.title {
  color: rgba(0, 0, 0, 0.85);
}
.handle {
  background: @primary-color;
  border-radius: 4px 0 0 4px;
}
.setting-drawer-index-handle {
  /* 暂时不知道放哪解决 */
  background: @primary-color !important;
}
.themeColor .title {
  color: rgba(0, 0, 0, 0.65);
}
.themeColor .colorBlock {
  border-radius: 2px;
  color: #fff;
}
.logo h1 {
  color: white;
}
.sider {
  box-shadow: 2px 116px 6px rgba(0, 21, 41, 0.35);
}
.sider.light {
  box-shadow: 2px 116px 8px 0 rgba(29, 35, 41, 0.05);
  background-color: white;
}
.sider.light .logo {
  background-color: @primary-color !important;
  box-shadow: 1px 1px 0 0 #e8e8e8;
}
.sider.light .logo h1 {
  color: white;
}
.sider.light :global(.ant-menu-light) {
  border-right-color: transparent;
}
:global .drawer .drawer-content {
  background: #001529;
}
.standardFormRow {
  border-bottom: 1px dashed #e8e8e8;
}
.standardFormRow :global .ant-form-item-label label {
  color: rgba(0, 0, 0, 0.65);
}
.standardFormRow .label {
  color: rgba(0, 0, 0, 0.85);
}
.standardFormRowLast {
  border: none;
}

.head {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}
.head.light {
  background-color: #fff;
}
.logo h1 {
  color: #fff;
}
.light h1 {
  color: #002140;
}
.trendItem .up {
  color: #f5222d;
}
.trendItem .down {
  color: #52c41a;
}
.trendItem.trendItemGrey .up,
.trendItem.trendItemGrey .down {
  color: rgba(0, 0, 0, 0.65);
}
.trendItem.reverseColor .up {
  color: #52c41a;
}
.trendItem.reverseColor .down {
  color: #f5222d;
}
.container {
  background: #f0f2f5;
}
.title {
  color: rgba(0, 0, 0, 0.85);
}
.desc {
  color: rgba(0, 0, 0, 0.45);
}
a.listItemMetaTitle {
  color: rgba(0, 0, 0, 0.85);
}
.baseView .right .avatar_title {
  color: rgba(0, 0, 0, 0.85);
}
.main {
  background-color: #fff;
}
.main .leftmenu {
  border-right: 1px solid #e8e8e8;
}
.main .leftmenu :global .ant-menu-inline {
  border: none;
}
.main .right .title {
  color: rgba(0, 0, 0, 0.85);
}
.main :global .ant-list-split .ant-list-item:last-child {
  border-bottom: 1px solid #e8e8e8;
}
:global .ant-list-item-meta .taobao {
  color: #ff4000;
  border-radius: 4px;
}
:global .ant-list-item-meta .dingding {
  background-color: #2eabff;
  color: #fff;
  border-radius: 4px;
}
:global .ant-list-item-meta .alipay {
  color: #2eabff;
  border-radius: 4px;
}
:global font.strong {
  color: #52c41a;
}
:global font.medium {
  color: #faad14;
}
:global font.weak {
  color: #f5222d;
}




.trigger {
  background: 'red';
}
.desc {
  color: rgba(0, 0, 0, 0.45);
}
.desc h3 {
  color: rgba(0, 0, 0, 0.45);
}
.desc h4 {
  color: rgba(0, 0, 0, 0.45);
}
.information .label {
  color: rgba(0, 0, 0, 0.85);
}
.errorIcon {
  color: #f5222d;
}
.errorListItem {
  border-bottom: 1px solid #e8e8e8;
}
.errorListItem:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.errorListItem:last-child {
  border: 0;
}
.errorListItem .errorIcon {
  color: #f5222d;
}
.errorListItem .errorField {
  color: rgba(0, 0, 0, 0.45);
}
.optional {
  color: rgba(0, 0, 0, 0.45);
}
a.listItemMetaTitle {
  color: rgba(0, 0, 0, 0.85);
}
.noData {
  color: rgba(0, 0, 0, 0.25);
}
.heading {
  color: rgba(0, 0, 0, 0.85);
}
.textSecondary {
  color: rgba(0, 0, 0, 0.45);
}
.title {
  color: rgba(0, 0, 0, 0.85);
}
.main .icon {
  color: rgba(0, 0, 0, 0.2);
}
.main .icon:hover {
  color: @primary-color;
}
.success {
  color: #52c41a;
}
.warning {
  color: #faad14;
}
.error {
  color: #f5222d;
}
.progress-pass > .progress :global .ant-progress-bg {
  background-color: #faad14;
}
html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  color: rgba(0, 0, 0, 0.85);
}
abbr[title],
abbr[data-original-title] {
  border-bottom: 0;
}
a {
  color: @primary-color;
  background-color: transparent;
}
a:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
a:active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
}
a[disabled] {
  color: rgba(0, 0, 0, 0.25);
}
img {
  border-style: none;
}
table {
  border-collapse: collapse;
}
caption {
  color: rgba(0, 0, 0, 0.45);
}
input,
button,
select,
optgroup,
textarea {
  color: inherit;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
}
fieldset {
  border: 0;
}
legend {
  color: inherit;
}
mark {
  background-color: #feffe6;
}
::selection {
  background: @primary-color;
  color: #fff;
}
[ant-click-animating-without-extra-node]:after,
.ant-click-animating-node {
  border-radius: inherit;
  border: 0 solid @primary-color;
}
.ant-alert {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
}
.ant-alert-success {
  border: 1px solid #b7eb8f;
  background-color: #f6ffed;
}
.ant-alert-success .ant-alert-icon {
  color: #52c41a;
}
.ant-alert-info {
  border: 1px solid color(~`colorPalette("@{primary-color}", 3)`);
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-alert-info .ant-alert-icon {
  color: @primary-color;
}
.ant-alert-warning {
  border: 1px solid #ffe58f;
  background-color: #fffbe6;
}
.ant-alert-warning .ant-alert-icon {
  color: #faad14;
}
.ant-alert-error {
  border: 1px solid #ffa39e;
  background-color: #fff1f0;
}
.ant-alert-error .ant-alert-icon {
  color: #f5222d;
}
.ant-alert-close-icon .anticon-close {
  color: rgba(0, 0, 0, 0.45);
}
.ant-alert-close-icon .anticon-close:hover {
  color: #404040;
}
.ant-alert-with-description {
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.65);
}
.ant-alert-with-description .ant-alert-message {
  color: rgba(0, 0, 0, 0.85);
}
.ant-alert-banner {
  border-radius: 0;
  border: 0;
}
.ant-anchor {
  color: rgba(0, 0, 0, 0.65);
}
.ant-anchor-wrapper {
  background-color: #fff;
}
.ant-anchor-ink:before {
  background-color: #e8e8e8;
}
.ant-anchor-ink-ball {
  border-radius: 8px;
  border: 2px solid @primary-color;
  background-color: #fff;
}
.ant-anchor-link-title {
  color: rgba(0, 0, 0, 0.65);
}
.ant-anchor-link-active > .ant-anchor-link-title {
  color: @primary-color;
}
.ant-select-auto-complete {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-auto-complete.ant-select .ant-select-selection {
  border: 0;
  box-shadow: none;
}
.ant-select-auto-complete.ant-select .ant-input {
  background: transparent;
  border-width: 1px;
}
.ant-select-auto-complete.ant-select .ant-input:focus,
.ant-select-auto-complete.ant-select .ant-input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-avatar {
  color: rgba(0, 0, 0, 0.65);
  background: #ccc;
  color: #fff;
  border-radius: 50%;
}
.ant-avatar-image {
  background: transparent;
}
.ant-avatar-lg {
  border-radius: 50%;
}
.ant-avatar-sm {
  border-radius: 50%;
}
.ant-avatar-square {
  border-radius: 4px;
}
.ant-back-top {
  color: rgba(0, 0, 0, 0.65);
}
.ant-back-top-content {
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.45);
  color: #fff;
}
.ant-back-top-content:hover {
  background-color: rgba(0, 0, 0, 0.65);
}
.ant-back-top-icon {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoCAYAAACWwljjAAAABGdBTUEAALGPC/xhBQAAAbtJREFUWAntmMtKw0AUhhMvS5cuxILgQlRUpIggIoKIIoigG1eC+AA+jo+i6FIXBfeuXIgoeKVeitVWJX5HWhhDksnUpp3FDPyZk3Nm5nycmZKkXhAEOXSA3lG7muTeRzmfy6HneUvIhnYkQK+Q9NhAA0Opg0vBEhjBKHiyb8iGMyQMOYuK41BcBSypAL+MYXSKjtFAW7EAGEO3qN4uMQbbAkXiSfRQJ1H6a+yhlkKRcAoVFYiweYNjtCVQJJpBz2GCiPt7fBOZQpFgDpUikse5HgnkM4Fi4QX0Fpc5wf9EbLqpUCy4jMoJSXWhFwbMNgWKhVbRhy5jirhs9fy/oFhgHVVTJEs7RLZ8sSEoJm6iz7SZDMbJ+/OKERQTttCXQRLToRUmrKWCYuA2+jbN0MB4OQobYShfdTCgn/sL1K36M7TLrN3n+758aPy2rrpR6+/od5E8tf/A1uLS9aId5T7J3CNYihkQ4D9PiMdMC7mp4rjB9kjFjZp8BlnVHJBuO1yFXIV0FdDF3RlyFdJVQBdv5AxVdIsq8apiZ2PyYO1EVykesGfZEESsCkweyR8MUW+V8uJ1gkYipmpdP1pm2aJVPEGzAAAAAElFTkSuQmCC) 100%/100% no-repeat;
}
.ant-badge {
  color: rgba(0, 0, 0, 0.65);
  color: unset;
}
.ant-badge-count {
  border-radius: 10px;
  background: #f5222d;
  color: #fff;
  box-shadow: 0 0 0 1px #fff;
}
.ant-badge-count a,
.ant-badge-count a:hover {
  color: #fff;
}
.ant-badge-dot {
  border-radius: 100%;
  background: #f5222d;
  box-shadow: 0 0 0 1px #fff;
}
.ant-badge-status-dot {
  border-radius: 50%;
}
.ant-badge-status-success {
  background-color: #52c41a;
}
.ant-badge-status-processing {
  background-color: @primary-color;
}
.ant-badge-status-processing:after {
  border-radius: 50%;
  border: 1px solid @primary-color;
}
.ant-badge-status-default {
  background-color: #d9d9d9;
}
.ant-badge-status-error {
  background-color: #f5222d;
}
.ant-badge-status-warning {
  background-color: #faad14;
}
.ant-badge-status-text {
  color: rgba(0, 0, 0, 0.65);
}
.ant-breadcrumb {
  color: rgba(0, 0, 0, 0.65);
  color: rgba(0, 0, 0, 0.45);
}
.ant-breadcrumb a {
  color: rgba(0, 0, 0, 0.45);
}
.ant-breadcrumb a:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-breadcrumb > span:last-child {
  color: rgba(0, 0, 0, 0.65);
}
.ant-breadcrumb-separator {
  color: rgba(0, 0, 0, 0.45);
}
.ant-btn {
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  border-color: #d9d9d9;
}
.ant-btn:not([disabled]):active {
  box-shadow: none;
}
.ant-btn-lg {
  border-radius: 4px;
}
.ant-btn-sm {
  border-radius: 4px;
}
.ant-btn > a:only-child {
  color: currentColor;
}
.ant-btn > a:only-child:after {
  background: transparent;
}
.ant-btn:hover,
.ant-btn:focus {
  color: color(~`colorPalette("@{primary-color}", 5)`);
  background-color: #fff;
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn:hover > a:only-child,
.ant-btn:focus > a:only-child {
  color: currentColor;
}
.ant-btn:hover > a:only-child:after,
.ant-btn:focus > a:only-child:after {
  background: transparent;
}
.ant-btn:active,
.ant-btn.active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
  background-color: #fff;
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-btn:active > a:only-child,
.ant-btn.active > a:only-child {
  color: currentColor;
}
.ant-btn:active > a:only-child:after,
.ant-btn.active > a:only-child:after {
  background: transparent;
}
.ant-btn.disabled,
.ant-btn[disabled],
.ant-btn.disabled:hover,
.ant-btn[disabled]:hover,
.ant-btn.disabled:focus,
.ant-btn[disabled]:focus,
.ant-btn.disabled:active,
.ant-btn[disabled]:active,
.ant-btn.disabled.active,
.ant-btn[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn.disabled > a:only-child,
.ant-btn[disabled] > a:only-child,
.ant-btn.disabled:hover > a:only-child,
.ant-btn[disabled]:hover > a:only-child,
.ant-btn.disabled:focus > a:only-child,
.ant-btn[disabled]:focus > a:only-child,
.ant-btn.disabled:active > a:only-child,
.ant-btn[disabled]:active > a:only-child,
.ant-btn.disabled.active > a:only-child,
.ant-btn[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn.disabled > a:only-child:after,
.ant-btn[disabled] > a:only-child:after,
.ant-btn.disabled:hover > a:only-child:after,
.ant-btn[disabled]:hover > a:only-child:after,
.ant-btn.disabled:focus > a:only-child:after,
.ant-btn[disabled]:focus > a:only-child:after,
.ant-btn.disabled:active > a:only-child:after,
.ant-btn[disabled]:active > a:only-child:after,
.ant-btn.disabled.active > a:only-child:after,
.ant-btn[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-btn:hover,
.ant-btn:focus,
.ant-btn:active,
.ant-btn.active {
  background: #fff;
}
.ant-btn-primary {
  color: #fff;
  background-color: @primary-color;
  border-color: @primary-color;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.035);
}
.ant-btn-primary > a:only-child {
  color: currentColor;
}
.ant-btn-primary > a:only-child:after {
  background: transparent;
}
.ant-btn-primary:hover,
.ant-btn-primary:focus {
  color: #fff;
  background-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-primary:hover > a:only-child,
.ant-btn-primary:focus > a:only-child {
  color: currentColor;
}
.ant-btn-primary:hover > a:only-child:after,
.ant-btn-primary:focus > a:only-child:after {
  background: transparent;
}
.ant-btn-primary:active,
.ant-btn-primary.active {
  color: #fff;
  background-color: color(~`colorPalette("@{primary-color}", 7)`);
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-btn-primary:active > a:only-child,
.ant-btn-primary.active > a:only-child {
  color: currentColor;
}
.ant-btn-primary:active > a:only-child:after,
.ant-btn-primary.active > a:only-child:after {
  background: transparent;
}
.ant-btn-primary.disabled,
.ant-btn-primary[disabled],
.ant-btn-primary.disabled:hover,
.ant-btn-primary[disabled]:hover,
.ant-btn-primary.disabled:focus,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary.disabled:active,
.ant-btn-primary[disabled]:active,
.ant-btn-primary.disabled.active,
.ant-btn-primary[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn-primary.disabled > a:only-child,
.ant-btn-primary[disabled] > a:only-child,
.ant-btn-primary.disabled:hover > a:only-child,
.ant-btn-primary[disabled]:hover > a:only-child,
.ant-btn-primary.disabled:focus > a:only-child,
.ant-btn-primary[disabled]:focus > a:only-child,
.ant-btn-primary.disabled:active > a:only-child,
.ant-btn-primary[disabled]:active > a:only-child,
.ant-btn-primary.disabled.active > a:only-child,
.ant-btn-primary[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn-primary.disabled > a:only-child:after,
.ant-btn-primary[disabled] > a:only-child:after,
.ant-btn-primary.disabled:hover > a:only-child:after,
.ant-btn-primary[disabled]:hover > a:only-child:after,
.ant-btn-primary.disabled:focus > a:only-child:after,
.ant-btn-primary[disabled]:focus > a:only-child:after,
.ant-btn-primary.disabled:active > a:only-child:after,
.ant-btn-primary[disabled]:active > a:only-child:after,
.ant-btn-primary.disabled.active > a:only-child:after,
.ant-btn-primary[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child) {
  border-right-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-left-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child):disabled {
  border-color: #d9d9d9;
}
.ant-btn-group .ant-btn-primary:first-child:not(:last-child) {
  border-right-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-group .ant-btn-primary:first-child:not(:last-child)[disabled] {
  border-right-color: #d9d9d9;
}
.ant-btn-group .ant-btn-primary:last-child:not(:first-child),
.ant-btn-group .ant-btn-primary + .ant-btn-primary {
  border-left-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled],
.ant-btn-group .ant-btn-primary + .ant-btn-primary[disabled] {
  border-left-color: #d9d9d9;
}
.ant-btn-ghost {
  color: rgba(0, 0, 0, 0.65);
  background-color: transparent;
  border-color: #d9d9d9;
}
.ant-btn-ghost > a:only-child {
  color: currentColor;
}
.ant-btn-ghost > a:only-child:after {
  background: transparent;
}
.ant-btn-ghost:hover,
.ant-btn-ghost:focus {
  color: color(~`colorPalette("@{primary-color}", 5)`);
  background-color: transparent;
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-ghost:hover > a:only-child,
.ant-btn-ghost:focus > a:only-child {
  color: currentColor;
}
.ant-btn-ghost:hover > a:only-child:after,
.ant-btn-ghost:focus > a:only-child:after {
  background: transparent;
}
.ant-btn-ghost:active,
.ant-btn-ghost.active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
  background-color: transparent;
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-btn-ghost:active > a:only-child,
.ant-btn-ghost.active > a:only-child {
  color: currentColor;
}
.ant-btn-ghost:active > a:only-child:after,
.ant-btn-ghost.active > a:only-child:after {
  background: transparent;
}
.ant-btn-ghost.disabled,
.ant-btn-ghost[disabled],
.ant-btn-ghost.disabled:hover,
.ant-btn-ghost[disabled]:hover,
.ant-btn-ghost.disabled:focus,
.ant-btn-ghost[disabled]:focus,
.ant-btn-ghost.disabled:active,
.ant-btn-ghost[disabled]:active,
.ant-btn-ghost.disabled.active,
.ant-btn-ghost[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn-ghost.disabled > a:only-child,
.ant-btn-ghost[disabled] > a:only-child,
.ant-btn-ghost.disabled:hover > a:only-child,
.ant-btn-ghost[disabled]:hover > a:only-child,
.ant-btn-ghost.disabled:focus > a:only-child,
.ant-btn-ghost[disabled]:focus > a:only-child,
.ant-btn-ghost.disabled:active > a:only-child,
.ant-btn-ghost[disabled]:active > a:only-child,
.ant-btn-ghost.disabled.active > a:only-child,
.ant-btn-ghost[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn-ghost.disabled > a:only-child:after,
.ant-btn-ghost[disabled] > a:only-child:after,
.ant-btn-ghost.disabled:hover > a:only-child:after,
.ant-btn-ghost[disabled]:hover > a:only-child:after,
.ant-btn-ghost.disabled:focus > a:only-child:after,
.ant-btn-ghost[disabled]:focus > a:only-child:after,
.ant-btn-ghost.disabled:active > a:only-child:after,
.ant-btn-ghost[disabled]:active > a:only-child:after,
.ant-btn-ghost.disabled.active > a:only-child:after,
.ant-btn-ghost[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-btn-dashed {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  border-color: #d9d9d9;
  border-style: dashed;
}
.ant-btn-dashed > a:only-child {
  color: currentColor;
}
.ant-btn-dashed > a:only-child:after {
  background: transparent;
}
.ant-btn-dashed:hover,
.ant-btn-dashed:focus {
  color: color(~`colorPalette("@{primary-color}", 5)`);
  background-color: #fff;
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-dashed:hover > a:only-child,
.ant-btn-dashed:focus > a:only-child {
  color: currentColor;
}
.ant-btn-dashed:hover > a:only-child:after,
.ant-btn-dashed:focus > a:only-child:after {
  background: transparent;
}
.ant-btn-dashed:active,
.ant-btn-dashed.active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
  background-color: #fff;
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-btn-dashed:active > a:only-child,
.ant-btn-dashed.active > a:only-child {
  color: currentColor;
}
.ant-btn-dashed:active > a:only-child:after,
.ant-btn-dashed.active > a:only-child:after {
  background: transparent;
}
.ant-btn-dashed.disabled,
.ant-btn-dashed[disabled],
.ant-btn-dashed.disabled:hover,
.ant-btn-dashed[disabled]:hover,
.ant-btn-dashed.disabled:focus,
.ant-btn-dashed[disabled]:focus,
.ant-btn-dashed.disabled:active,
.ant-btn-dashed[disabled]:active,
.ant-btn-dashed.disabled.active,
.ant-btn-dashed[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn-dashed.disabled > a:only-child,
.ant-btn-dashed[disabled] > a:only-child,
.ant-btn-dashed.disabled:hover > a:only-child,
.ant-btn-dashed[disabled]:hover > a:only-child,
.ant-btn-dashed.disabled:focus > a:only-child,
.ant-btn-dashed[disabled]:focus > a:only-child,
.ant-btn-dashed.disabled:active > a:only-child,
.ant-btn-dashed[disabled]:active > a:only-child,
.ant-btn-dashed.disabled.active > a:only-child,
.ant-btn-dashed[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn-dashed.disabled > a:only-child:after,
.ant-btn-dashed[disabled] > a:only-child:after,
.ant-btn-dashed.disabled:hover > a:only-child:after,
.ant-btn-dashed[disabled]:hover > a:only-child:after,
.ant-btn-dashed.disabled:focus > a:only-child:after,
.ant-btn-dashed[disabled]:focus > a:only-child:after,
.ant-btn-dashed.disabled:active > a:only-child:after,
.ant-btn-dashed[disabled]:active > a:only-child:after,
.ant-btn-dashed.disabled.active > a:only-child:after,
.ant-btn-dashed[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-btn-danger {
  color: #f5222d;
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}
.ant-btn-danger > a:only-child {
  color: currentColor;
}
.ant-btn-danger > a:only-child:after {
  background: transparent;
}
.ant-btn-danger:hover {
  color: #fff;
  background-color: #ff4d4f;
  border-color: #ff4d4f;
}
.ant-btn-danger:hover > a:only-child {
  color: currentColor;
}
.ant-btn-danger:hover > a:only-child:after {
  background: transparent;
}
.ant-btn-danger:focus {
  color: #ff4d4f;
  background-color: #fff;
  border-color: #ff4d4f;
}
.ant-btn-danger:focus > a:only-child {
  color: currentColor;
}
.ant-btn-danger:focus > a:only-child:after {
  background: transparent;
}
.ant-btn-danger:active,
.ant-btn-danger.active {
  color: #fff;
  background-color: #cf1322;
  border-color: #cf1322;
}
.ant-btn-danger:active > a:only-child,
.ant-btn-danger.active > a:only-child {
  color: currentColor;
}
.ant-btn-danger:active > a:only-child:after,
.ant-btn-danger.active > a:only-child:after {
  background: transparent;
}
.ant-btn-danger.disabled,
.ant-btn-danger[disabled],
.ant-btn-danger.disabled:hover,
.ant-btn-danger[disabled]:hover,
.ant-btn-danger.disabled:focus,
.ant-btn-danger[disabled]:focus,
.ant-btn-danger.disabled:active,
.ant-btn-danger[disabled]:active,
.ant-btn-danger.disabled.active,
.ant-btn-danger[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn-danger.disabled > a:only-child,
.ant-btn-danger[disabled] > a:only-child,
.ant-btn-danger.disabled:hover > a:only-child,
.ant-btn-danger[disabled]:hover > a:only-child,
.ant-btn-danger.disabled:focus > a:only-child,
.ant-btn-danger[disabled]:focus > a:only-child,
.ant-btn-danger.disabled:active > a:only-child,
.ant-btn-danger[disabled]:active > a:only-child,
.ant-btn-danger.disabled.active > a:only-child,
.ant-btn-danger[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn-danger.disabled > a:only-child:after,
.ant-btn-danger[disabled] > a:only-child:after,
.ant-btn-danger.disabled:hover > a:only-child:after,
.ant-btn-danger[disabled]:hover > a:only-child:after,
.ant-btn-danger.disabled:focus > a:only-child:after,
.ant-btn-danger[disabled]:focus > a:only-child:after,
.ant-btn-danger.disabled:active > a:only-child:after,
.ant-btn-danger[disabled]:active > a:only-child:after,
.ant-btn-danger.disabled.active > a:only-child:after,
.ant-btn-danger[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-btn-circle,
.ant-btn-circle-outline {
  border-radius: 50%;
}
.ant-btn-circle.ant-btn-lg,
.ant-btn-circle-outline.ant-btn-lg {
  border-radius: 50%;
}
.ant-btn-circle.ant-btn-sm,
.ant-btn-circle-outline.ant-btn-sm {
  border-radius: 50%;
}
.ant-btn:before {
  background: #fff;
  border-radius: inherit;
}
.ant-btn-group-lg > .ant-btn,
.ant-btn-group-lg > span > .ant-btn {
  border-radius: 0;
}
.ant-btn-group-sm > .ant-btn,
.ant-btn-group-sm > span > .ant-btn {
  border-radius: 0;
}
.ant-btn-group .ant-btn-primary + .ant-btn:not(.ant-btn-primary):not([disabled]) {
  border-left-color: transparent;
}
.ant-btn-group .ant-btn {
  border-radius: 0;
}
.ant-btn-group > .ant-btn:only-child {
  border-radius: 4px;
}
.ant-btn-group > span:only-child > .ant-btn {
  border-radius: 4px;
}
.ant-btn-group > .ant-btn:first-child:not(:last-child),
.ant-btn-group > span:first-child:not(:last-child) > .ant-btn {
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.ant-btn-group > .ant-btn:last-child:not(:first-child),
.ant-btn-group > span:last-child:not(:first-child) > .ant-btn {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
.ant-btn-group-sm > .ant-btn:only-child {
  border-radius: 4px;
}
.ant-btn-group-sm > span:only-child > .ant-btn {
  border-radius: 4px;
}
.ant-btn-group-sm > .ant-btn:first-child:not(:last-child),
.ant-btn-group-sm > span:first-child:not(:last-child) > .ant-btn {
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.ant-btn-group-sm > .ant-btn:last-child:not(:first-child),
.ant-btn-group-sm > span:last-child:not(:first-child) > .ant-btn {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
.ant-btn-group > .ant-btn-group:not(:first-child):not(:last-child) > .ant-btn {
  border-radius: 0;
}
.ant-btn-group > .ant-btn-group:first-child:not(:last-child) > .ant-btn:last-child {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.ant-btn-group > .ant-btn-group:last-child:not(:first-child) > .ant-btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.ant-btn-background-ghost {
  background: transparent !important;
  border-color: #fff;
  color: #fff;
}
.ant-btn-background-ghost.ant-btn-primary {
  color: @primary-color;
  background-color: transparent;
  border-color: @primary-color;
}
.ant-btn-background-ghost.ant-btn-primary > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-primary > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-primary:hover,
.ant-btn-background-ghost.ant-btn-primary:focus {
  color: color(~`colorPalette("@{primary-color}", 5)`);
  background-color: transparent;
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-btn-background-ghost.ant-btn-primary:hover > a:only-child,
.ant-btn-background-ghost.ant-btn-primary:focus > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-primary:hover > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary:focus > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-primary:active,
.ant-btn-background-ghost.ant-btn-primary.active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
  background-color: transparent;
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-btn-background-ghost.ant-btn-primary:active > a:only-child,
.ant-btn-background-ghost.ant-btn-primary.active > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-primary:active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary.active > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-primary.disabled,
.ant-btn-background-ghost.ant-btn-primary[disabled],
.ant-btn-background-ghost.ant-btn-primary.disabled:hover,
.ant-btn-background-ghost.ant-btn-primary[disabled]:hover,
.ant-btn-background-ghost.ant-btn-primary.disabled:focus,
.ant-btn-background-ghost.ant-btn-primary[disabled]:focus,
.ant-btn-background-ghost.ant-btn-primary.disabled:active,
.ant-btn-background-ghost.ant-btn-primary[disabled]:active,
.ant-btn-background-ghost.ant-btn-primary.disabled.active,
.ant-btn-background-ghost.ant-btn-primary[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn-background-ghost.ant-btn-primary.disabled > a:only-child,
.ant-btn-background-ghost.ant-btn-primary[disabled] > a:only-child,
.ant-btn-background-ghost.ant-btn-primary.disabled:hover > a:only-child,
.ant-btn-background-ghost.ant-btn-primary[disabled]:hover > a:only-child,
.ant-btn-background-ghost.ant-btn-primary.disabled:focus > a:only-child,
.ant-btn-background-ghost.ant-btn-primary[disabled]:focus > a:only-child,
.ant-btn-background-ghost.ant-btn-primary.disabled:active > a:only-child,
.ant-btn-background-ghost.ant-btn-primary[disabled]:active > a:only-child,
.ant-btn-background-ghost.ant-btn-primary.disabled.active > a:only-child,
.ant-btn-background-ghost.ant-btn-primary[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-primary.disabled > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary[disabled] > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary.disabled:hover > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary[disabled]:hover > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary.disabled:focus > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary[disabled]:focus > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary.disabled:active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary[disabled]:active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary.disabled.active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-primary[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-danger {
  color: #f5222d;
  background-color: transparent;
  border-color: #f5222d;
}
.ant-btn-background-ghost.ant-btn-danger > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-danger > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-danger:hover,
.ant-btn-background-ghost.ant-btn-danger:focus {
  color: #ff4d4f;
  background-color: transparent;
  border-color: #ff4d4f;
}
.ant-btn-background-ghost.ant-btn-danger:hover > a:only-child,
.ant-btn-background-ghost.ant-btn-danger:focus > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-danger:hover > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger:focus > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-danger:active,
.ant-btn-background-ghost.ant-btn-danger.active {
  color: #cf1322;
  background-color: transparent;
  border-color: #cf1322;
}
.ant-btn-background-ghost.ant-btn-danger:active > a:only-child,
.ant-btn-background-ghost.ant-btn-danger.active > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-danger:active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger.active > a:only-child:after {
  background: transparent;
}
.ant-btn-background-ghost.ant-btn-danger.disabled,
.ant-btn-background-ghost.ant-btn-danger[disabled],
.ant-btn-background-ghost.ant-btn-danger.disabled:hover,
.ant-btn-background-ghost.ant-btn-danger[disabled]:hover,
.ant-btn-background-ghost.ant-btn-danger.disabled:focus,
.ant-btn-background-ghost.ant-btn-danger[disabled]:focus,
.ant-btn-background-ghost.ant-btn-danger.disabled:active,
.ant-btn-background-ghost.ant-btn-danger[disabled]:active,
.ant-btn-background-ghost.ant-btn-danger.disabled.active,
.ant-btn-background-ghost.ant-btn-danger[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-btn-background-ghost.ant-btn-danger.disabled > a:only-child,
.ant-btn-background-ghost.ant-btn-danger[disabled] > a:only-child,
.ant-btn-background-ghost.ant-btn-danger.disabled:hover > a:only-child,
.ant-btn-background-ghost.ant-btn-danger[disabled]:hover > a:only-child,
.ant-btn-background-ghost.ant-btn-danger.disabled:focus > a:only-child,
.ant-btn-background-ghost.ant-btn-danger[disabled]:focus > a:only-child,
.ant-btn-background-ghost.ant-btn-danger.disabled:active > a:only-child,
.ant-btn-background-ghost.ant-btn-danger[disabled]:active > a:only-child,
.ant-btn-background-ghost.ant-btn-danger.disabled.active > a:only-child,
.ant-btn-background-ghost.ant-btn-danger[disabled].active > a:only-child {
  color: currentColor;
}
.ant-btn-background-ghost.ant-btn-danger.disabled > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger[disabled] > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger.disabled:hover > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger[disabled]:hover > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger.disabled:focus > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger[disabled]:focus > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger.disabled:active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger[disabled]:active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger.disabled.active > a:only-child:after,
.ant-btn-background-ghost.ant-btn-danger[disabled].active > a:only-child:after {
  background: transparent;
}
.christmas.ant-btn-primary:before {
  background: url(data:image/png;base64,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) no-repeat 50% 0;
  background-size: 64px;
}
.christmas.ant-btn-primary.ant-btn-lg:before {
  background-size: 72px;
}
.christmas.ant-btn-primary.ant-btn-sm:before {
  background-size: 56px;
}
.ant-fullcalendar {
  color: rgba(0, 0, 0, 0.65);
  border-top: 1px solid #d9d9d9;
}
.ant-fullcalendar table {
  border-collapse: collapse;
  background-color: transparent;
}
.ant-fullcalendar table,
.ant-fullcalendar th,
.ant-fullcalendar td {
  border: 0;
}
.ant-fullcalendar-calendar-table {
  border-spacing: 0;
}
.ant-fullcalendar-value {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 2px;
  background: transparent;
}
.ant-fullcalendar-value:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-fullcalendar-value:active {
  background: @primary-color;
  color: #fff;
}
.ant-fullcalendar-today .ant-fullcalendar-value,
.ant-fullcalendar-month-panel-current-cell .ant-fullcalendar-value {
  box-shadow: 0 0 0 1px @primary-color inset;
}
.ant-fullcalendar-selected-day .ant-fullcalendar-value,
.ant-fullcalendar-month-panel-selected-cell .ant-fullcalendar-value {
  background: @primary-color;
  color: #fff;
}
.ant-fullcalendar-disabled-cell-first-of-row .ant-fullcalendar-value {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.ant-fullcalendar-disabled-cell-last-of-row .ant-fullcalendar-value {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.ant-fullcalendar-last-month-cell .ant-fullcalendar-value,
.ant-fullcalendar-next-month-btn-day .ant-fullcalendar-value {
  color: rgba(0, 0, 0, 0.25);
}
.ant-fullcalendar-month-panel-table {
  border-collapse: separate;
}
.ant-fullcalendar-fullscreen {
  border-top: 0;
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen .ant-fullcalendar-date {
  color: rgba(0, 0, 0, 0.65);
  border-top: 2px solid #e8e8e8;
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month:hover,
.ant-fullcalendar-fullscreen .ant-fullcalendar-date:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month:active,
.ant-fullcalendar-fullscreen .ant-fullcalendar-date:active {
  background: color(~`colorPalette("@{primary-color}", 2)`);
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-value {
  background: transparent;
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-today .ant-fullcalendar-value {
  color: rgba(0, 0, 0, 0.65);
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month-panel-current-cell .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen .ant-fullcalendar-today .ant-fullcalendar-date {
  border-top-color: @primary-color;
  background: transparent;
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month-panel-current-cell .ant-fullcalendar-value,
.ant-fullcalendar-fullscreen .ant-fullcalendar-today .ant-fullcalendar-value {
  box-shadow: none;
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month-panel-selected-cell .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen .ant-fullcalendar-selected-day .ant-fullcalendar-date {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-month-panel-selected-cell .ant-fullcalendar-value,
.ant-fullcalendar-fullscreen .ant-fullcalendar-selected-day .ant-fullcalendar-value {
  color: @primary-color;
}
.ant-fullcalendar-fullscreen .ant-fullcalendar-last-month-cell .ant-fullcalendar-date,
.ant-fullcalendar-fullscreen .ant-fullcalendar-next-month-btn-day .ant-fullcalendar-date {
  color: rgba(0, 0, 0, 0.25);
}
.ant-fullcalendar-disabled-cell:not(.ant-fullcalendar-today) .ant-fullcalendar-date,
.ant-fullcalendar-disabled-cell:not(.ant-fullcalendar-today) .ant-fullcalendar-date:hover {
  background: transparent;
}
.ant-fullcalendar-disabled-cell .ant-fullcalendar-value {
  color: rgba(0, 0, 0, 0.25);
  border-radius: 0;
}
.ant-card {
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
  border-radius: 2px;
}
.ant-card-hoverable:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  border-color: rgba(0, 0, 0, 0.09);
}
.ant-card-bordered {
  border: 1px solid #e8e8e8;
}
.ant-card-head {
  background: transparent;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 2px 2px 0 0;
  color: rgba(0, 0, 0, 0.85);
}
.ant-card-head .ant-tabs {
  color: rgba(0, 0, 0, 0.65);
}
.ant-card-head .ant-tabs-bar {
  border-bottom: 1px solid #e8e8e8;
}
.ant-card-extra {
  color: rgba(0, 0, 0, 0.65);
}
.ant-card-grid {
  border-radius: 0;
  border: 0;
  box-shadow: 1px 0 0 0 #e8e8e8, 0 1px 0 0 #e8e8e8, 1px 1px 0 0 #e8e8e8, 1px 0 0 0 #e8e8e8 inset, 0 1px 0 0 #e8e8e8 inset;
}
.ant-card-grid:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-card-cover img {
  border-radius: 2px 2px 0 0;
}
.ant-card-actions {
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}
.ant-card-actions > li {
  color: rgba(0, 0, 0, 0.45);
}
.ant-card-actions > li > span:hover {
  color: @primary-color;
}
.ant-card-actions > li > span a {
  color: rgba(0, 0, 0, 0.45);
}
.ant-card-actions > li > span a:hover {
  color: @primary-color;
}
.ant-card-actions > li:not(:last-child) {
  border-right: 1px solid #e8e8e8;
}
.ant-card-type-inner .ant-card-head {
  background: #fafafa;
}
.ant-card-meta-title {
  color: rgba(0, 0, 0, 0.85);
}
.ant-card-meta-description {
  color: rgba(0, 0, 0, 0.45);
}
.ant-card-loading-block {
  border-radius: 2px;
  background: linear-gradient(90deg, rgba(207, 216, 220, 0.2), rgba(207, 216, 220, 0.4), rgba(207, 216, 220, 0.2));
  background-size: 600% 600%;
}
.ant-carousel {
  color: rgba(0, 0, 0, 0.65);
}
.ant-carousel .slick-slider {
  -webkit-tap-highlight-color: transparent;
}
.ant-carousel .slick-vertical .slick-slide {
  border: 1px solid transparent;
}
.ant-carousel .slick-prev,
.ant-carousel .slick-next {
  background: transparent;
  color: transparent;
  border: 0;
}
.ant-carousel .slick-prev:hover,
.ant-carousel .slick-next:hover,
.ant-carousel .slick-prev:focus,
.ant-carousel .slick-next:focus {
  background: transparent;
  color: transparent;
}
.ant-carousel .slick-dots li button {
  border: 0;
  background: #fff;
  border-radius: 1px;
  color: transparent;
}
.ant-carousel .slick-dots li.slick-active button {
  background: #fff;
}
.ant-cascader {
  color: rgba(0, 0, 0, 0.65);
}
.ant-cascader-input.ant-input {
  background-color: transparent !important;
}
.ant-cascader-picker {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  border-radius: 4px;
}
.ant-cascader-picker-with-value .ant-cascader-picker-label {
  color: transparent;
}
.ant-cascader-picker-disabled {
  background: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-cascader-picker:focus .ant-cascader-input {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-cascader-picker-show-search.ant-cascader-picker-focused {
  color: rgba(0, 0, 0, 0.25);
}
.ant-cascader-picker-clear {
  background: #fff;
  color: rgba(0, 0, 0, 0.25);
}
.ant-cascader-picker-clear:hover {
  color: rgba(0, 0, 0, 0.45);
}
.ant-cascader-picker-arrow {
  color: rgba(0, 0, 0, 0.25);
}
.ant-cascader-menus {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-cascader-menu {
  border-right: 1px solid #e8e8e8;
}
.ant-cascader-menu:first-child {
  border-radius: 4px 0 0 4px;
}
.ant-cascader-menu:last-child {
  border-right-color: transparent;
  border-radius: 0 4px 4px 0;
}
.ant-cascader-menu:only-child {
  border-radius: 4px;
}
.ant-cascader-menu-item:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-cascader-menu-item-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-cascader-menu-item-disabled:hover {
  background: transparent;
}
.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
  background: #f5f5f5;
}
.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,
.ant-cascader-menu-item-expand .ant-cascader-menu-item-loading-icon {
  color: rgba(0, 0, 0, 0.45);
}
.ant-cascader-menu-item .ant-cascader-menu-item-keyword {
  color: #f5222d;
}
.ant-checkbox {
  color: rgba(0, 0, 0, 0.65);
}
.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: @primary-color;
}
.ant-checkbox-checked:after {
  border-radius: 2px;
  border: 1px solid @primary-color;
}
.ant-checkbox-inner {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fff;
}
.ant-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
.ant-checkbox-indeterminate .ant-checkbox-inner:after {
  border: 0;
  background-color: @primary-color;
}
.ant-checkbox-indeterminate.ant-checkbox-disabled .ant-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ant-checkbox-checked .ant-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: @primary-color;
  border-color: @primary-color;
}
.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ant-checkbox-disabled .ant-checkbox-inner {
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5;
}
.ant-checkbox-disabled .ant-checkbox-inner:after {
  border-color: #f5f5f5;
}
.ant-checkbox-disabled + span {
  color: rgba(0, 0, 0, 0.25);
}
.ant-checkbox-wrapper {
  color: rgba(0, 0, 0, 0.65);
}
.ant-checkbox-group {
  color: rgba(0, 0, 0, 0.65);
}
.ant-collapse {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  border-bottom: 0;
}
.ant-collapse > .ant-collapse-item {
  border-bottom: 1px solid #d9d9d9;
}
.ant-collapse > .ant-collapse-item:last-child,
.ant-collapse > .ant-collapse-item:last-child > .ant-collapse-header {
  border-radius: 0 0 4px 4px;
}
.ant-collapse > .ant-collapse-item > .ant-collapse-header {
  color: rgba(0, 0, 0, 0.85);
}
.ant-collapse-content {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  border-top: 1px solid #d9d9d9;
}
.ant-collapse-item:last-child > .ant-collapse-content {
  border-radius: 0 0 4px 4px;
}
.ant-collapse-borderless {
  background-color: #fff;
  border: 0;
}
.ant-collapse-borderless > .ant-collapse-item {
  border-bottom: 1px solid #d9d9d9;
}
.ant-collapse-borderless > .ant-collapse-item:last-child,
.ant-collapse-borderless > .ant-collapse-item:last-child .ant-collapse-header {
  border-radius: 0;
}
.ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
  background-color: transparent;
  border-top: 0;
}
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header,
.ant-collapse .ant-collapse-item-disabled > .ant-collapse-header > .arrow {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar-picker-container {
  color: rgba(0, 0, 0, 0.65);
}
.ant-calendar-picker {
  color: rgba(0, 0, 0, 0.65);
}
.ant-calendar-picker:hover .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: @primary-color;
}
.ant-calendar-picker:focus .ant-calendar-picker-input:not(.ant-input-disabled) {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-calendar-picker-clear {
  color: rgba(0, 0, 0, 0.25);
  background: #fff;
}
.ant-calendar-picker-clear:hover {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar-picker-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar {
  border: 1px solid #fff;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-clip: padding-box;
}
.ant-calendar-input-wrap {
  border-bottom: 1px solid #e8e8e8;
}
.ant-calendar-input {
  border: 0;
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
}
.ant-calendar-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-calendar-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-calendar-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-calendar-header {
  border-bottom: 1px solid #e8e8e8;
}
.ant-calendar-header a:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-calendar-header .ant-calendar-century-select,
.ant-calendar-header .ant-calendar-decade-select,
.ant-calendar-header .ant-calendar-year-select,
.ant-calendar-header .ant-calendar-month-select {
  color: rgba(0, 0, 0, 0.85);
}
.ant-calendar-header .ant-calendar-prev-century-btn,
.ant-calendar-header .ant-calendar-next-century-btn,
.ant-calendar-header .ant-calendar-prev-decade-btn,
.ant-calendar-header .ant-calendar-next-decade-btn,
.ant-calendar-header .ant-calendar-prev-month-btn,
.ant-calendar-header .ant-calendar-next-month-btn,
.ant-calendar-header .ant-calendar-prev-year-btn,
.ant-calendar-header .ant-calendar-next-year-btn {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar table {
  border-collapse: collapse;
  background-color: transparent;
}
.ant-calendar table,
.ant-calendar th,
.ant-calendar td {
  border: 0;
}
.ant-calendar-calendar-table {
  border-spacing: 0;
}
.ant-calendar-date {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 2px;
  border: 1px solid transparent;
  background: transparent;
}
.ant-calendar-date:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-calendar-date:active {
  color: #fff;
  background: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-calendar-today .ant-calendar-date {
  border-color: @primary-color;
  color: @primary-color;
}
.ant-calendar-last-month-cell .ant-calendar-date,
.ant-calendar-next-month-btn-day .ant-calendar-date {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar-selected-day .ant-calendar-date {
  background: #d1e9ff;
}
.ant-calendar-selected-date .ant-calendar-date,
.ant-calendar-selected-start-date .ant-calendar-date,
.ant-calendar-selected-end-date .ant-calendar-date {
  background: @primary-color;
  color: #fff;
  border: 1px solid transparent;
}
.ant-calendar-selected-date .ant-calendar-date:hover,
.ant-calendar-selected-start-date .ant-calendar-date:hover,
.ant-calendar-selected-end-date .ant-calendar-date:hover {
  background: @primary-color;
}
.ant-calendar-disabled-cell .ant-calendar-date {
  color: #bcbcbc;
  background: #f5f5f5;
  border-radius: 0;
  border: 1px solid transparent;
}
.ant-calendar-disabled-cell .ant-calendar-date:hover {
  background: #f5f5f5;
}
.ant-calendar-disabled-cell.ant-calendar-today .ant-calendar-date:before {
  border: 1px solid #bcbcbc;
  border-radius: 2px;
}
.ant-calendar-disabled-cell-first-of-row .ant-calendar-date {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.ant-calendar-disabled-cell-last-of-row .ant-calendar-date {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.ant-calendar-footer {
  border-top: 1px solid #e8e8e8;
}
.ant-calendar-footer:empty {
  border-top: 0;
}
.ant-calendar .ant-calendar-today-btn-disabled,
.ant-calendar .ant-calendar-clear-btn-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar .ant-calendar-clear-btn:after {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar .ant-calendar-clear-btn:hover:after {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar .ant-calendar-ok-btn {
  background-image: none;
  border: 1px solid transparent;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
  color: #fff;
  background-color: @primary-color;
  border-color: @primary-color;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.035);
  border-radius: 4px;
}
.ant-calendar .ant-calendar-ok-btn:not([disabled]):active {
  box-shadow: none;
}
.ant-calendar .ant-calendar-ok-btn-lg {
  border-radius: 4px;
}
.ant-calendar .ant-calendar-ok-btn-sm {
  border-radius: 4px;
}
.ant-calendar .ant-calendar-ok-btn > a:only-child {
  color: currentColor;
}
.ant-calendar .ant-calendar-ok-btn > a:only-child:after {
  background: transparent;
}
.ant-calendar .ant-calendar-ok-btn:hover,
.ant-calendar .ant-calendar-ok-btn:focus {
  color: #fff;
  background-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-calendar .ant-calendar-ok-btn:hover > a:only-child,
.ant-calendar .ant-calendar-ok-btn:focus > a:only-child {
  color: currentColor;
}
.ant-calendar .ant-calendar-ok-btn:hover > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn:focus > a:only-child:after {
  background: transparent;
}
.ant-calendar .ant-calendar-ok-btn:active,
.ant-calendar .ant-calendar-ok-btn.active {
  color: #fff;
  background-color: color(~`colorPalette("@{primary-color}", 7)`);
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-calendar .ant-calendar-ok-btn:active > a:only-child,
.ant-calendar .ant-calendar-ok-btn.active > a:only-child {
  color: currentColor;
}
.ant-calendar .ant-calendar-ok-btn:active > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn.active > a:only-child:after {
  background: transparent;
}
.ant-calendar .ant-calendar-ok-btn.disabled,
.ant-calendar .ant-calendar-ok-btn[disabled],
.ant-calendar .ant-calendar-ok-btn.disabled:hover,
.ant-calendar .ant-calendar-ok-btn[disabled]:hover,
.ant-calendar .ant-calendar-ok-btn.disabled:focus,
.ant-calendar .ant-calendar-ok-btn[disabled]:focus,
.ant-calendar .ant-calendar-ok-btn.disabled:active,
.ant-calendar .ant-calendar-ok-btn[disabled]:active,
.ant-calendar .ant-calendar-ok-btn.disabled.active,
.ant-calendar .ant-calendar-ok-btn[disabled].active {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-calendar .ant-calendar-ok-btn.disabled > a:only-child,
.ant-calendar .ant-calendar-ok-btn[disabled] > a:only-child,
.ant-calendar .ant-calendar-ok-btn.disabled:hover > a:only-child,
.ant-calendar .ant-calendar-ok-btn[disabled]:hover > a:only-child,
.ant-calendar .ant-calendar-ok-btn.disabled:focus > a:only-child,
.ant-calendar .ant-calendar-ok-btn[disabled]:focus > a:only-child,
.ant-calendar .ant-calendar-ok-btn.disabled:active > a:only-child,
.ant-calendar .ant-calendar-ok-btn[disabled]:active > a:only-child,
.ant-calendar .ant-calendar-ok-btn.disabled.active > a:only-child,
.ant-calendar .ant-calendar-ok-btn[disabled].active > a:only-child {
  color: currentColor;
}
.ant-calendar .ant-calendar-ok-btn.disabled > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn[disabled] > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn.disabled:hover > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn[disabled]:hover > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn.disabled:focus > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn[disabled]:focus > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn.disabled:active > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn[disabled]:active > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn.disabled.active > a:only-child:after,
.ant-calendar .ant-calendar-ok-btn[disabled].active > a:only-child:after {
  background: transparent;
}
.ant-calendar .ant-calendar-ok-btn-disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}
.ant-calendar .ant-calendar-ok-btn-disabled > a:only-child {
  color: currentColor;
}
.ant-calendar .ant-calendar-ok-btn-disabled > a:only-child:after {
  background: transparent;
}
.ant-calendar .ant-calendar-ok-btn-disabled:hover {
  color: rgba(0, 0, 0, 0.25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
}
.ant-calendar .ant-calendar-ok-btn-disabled:hover > a:only-child {
  color: currentColor;
}
.ant-calendar .ant-calendar-ok-btn-disabled:hover > a:only-child:after {
  background: transparent;
}
.ant-calendar-range-picker-input {
  background-color: transparent;
  border: 0;
}
.ant-calendar-range-picker-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-calendar-range-picker-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-calendar-range-picker-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-calendar-range-picker-separator {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar-range-left .ant-calendar-time-picker-inner {
  border-right: 1px solid #e8e8e8;
}
.ant-calendar-range-right .ant-calendar-time-picker-inner {
  border-left: 1px solid #e8e8e8;
}
.ant-calendar-range-middle {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar-range .ant-calendar-input,
.ant-calendar-range .ant-calendar-time-picker-input {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  border: 0;
  box-shadow: none;
}
.ant-calendar-range .ant-calendar-input::-moz-placeholder,
.ant-calendar-range .ant-calendar-time-picker-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-calendar-range .ant-calendar-input:-ms-input-placeholder,
.ant-calendar-range .ant-calendar-time-picker-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-calendar-range .ant-calendar-input::-webkit-input-placeholder,
.ant-calendar-range .ant-calendar-time-picker-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-calendar-range .ant-calendar-input:hover,
.ant-calendar-range .ant-calendar-time-picker-input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-calendar-range .ant-calendar-input:focus,
.ant-calendar-range .ant-calendar-time-picker-input:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-calendar-range .ant-calendar-input-disabled,
.ant-calendar-range .ant-calendar-time-picker-input-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar-range .ant-calendar-input-disabled:hover,
.ant-calendar-range .ant-calendar-time-picker-input-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-calendar-range .ant-calendar-input:focus,
.ant-calendar-range .ant-calendar-time-picker-input:focus {
  box-shadow: none;
}
.ant-calendar-range .ant-calendar-in-range-cell {
  border-radius: 0;
}
.ant-calendar-range .ant-calendar-in-range-cell:before {
  background: color(~`colorPalette("@{primary-color}", 1)`);
  border-radius: 0;
  border: 0;
}
.ant-calendar-range .ant-calendar-header,
.ant-calendar-range .ant-calendar-month-panel-header,
.ant-calendar-range .ant-calendar-year-panel-header {
  border-bottom: 0;
}
.ant-calendar-range .ant-calendar-body,
.ant-calendar-range .ant-calendar-month-panel-body,
.ant-calendar-range .ant-calendar-year-panel-body {
  border-top: 1px solid #e8e8e8;
}
.ant-calendar-range.ant-calendar-time .ant-calendar-time-picker-inner {
  background: none;
}
.ant-calendar-range.ant-calendar-time .ant-calendar-time-picker-combobox {
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
}
.ant-calendar-range.ant-calendar-show-time-picker .ant-calendar-body {
  border-top-color: transparent;
}
.ant-calendar-time-picker {
  background-color: #fff;
}
.ant-calendar-time-picker-inner {
  background-color: #fff;
  background-clip: padding-box;
}
.ant-calendar-time-picker-select {
  border-right: 1px solid #e8e8e8;
}
.ant-calendar-time-picker-select:first-child {
  border-left: 0;
}
.ant-calendar-time-picker-select:last-child {
  border-right: 0;
}
.ant-calendar-time-picker-select li:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
li.ant-calendar-time-picker-select-option-selected {
  background: #f5f5f5;
}
li.ant-calendar-time-picker-select-option-disabled {
  color: rgba(0, 0, 0, 0.25);
}
li.ant-calendar-time-picker-select-option-disabled:hover {
  background: transparent;
}
.ant-calendar-time .ant-calendar-day-select {
  color: rgba(0, 0, 0, 0.85);
}
.ant-calendar-time .ant-calendar-footer .ant-calendar-time-picker-btn-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar-month-panel {
  border-radius: 4px;
  background: #fff;
}
.ant-calendar-month-panel-header {
  border-bottom: 1px solid #e8e8e8;
}
.ant-calendar-month-panel-header a:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-calendar-month-panel-header .ant-calendar-month-panel-century-select,
.ant-calendar-month-panel-header .ant-calendar-month-panel-decade-select,
.ant-calendar-month-panel-header .ant-calendar-month-panel-year-select,
.ant-calendar-month-panel-header .ant-calendar-month-panel-month-select {
  color: rgba(0, 0, 0, 0.85);
}
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-century-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-century-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-decade-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-decade-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-month-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-month-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-year-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-year-btn {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar-month-panel-table {
  border-collapse: separate;
}
.ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month {
  background: @primary-color;
  color: #fff;
}
.ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month:hover {
  background: @primary-color;
  color: #fff;
}
.ant-calendar-month-panel-cell-disabled .ant-calendar-month-panel-month,
.ant-calendar-month-panel-cell-disabled .ant-calendar-month-panel-month:hover {
  color: #bcbcbc;
  background: #f5f5f5;
}
.ant-calendar-month-panel-month {
  color: rgba(0, 0, 0, 0.65);
  background: transparent;
  border-radius: 2px;
}
.ant-calendar-month-panel-month:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-calendar-year-panel {
  border-radius: 4px;
  background: #fff;
}
.ant-calendar-year-panel-header {
  border-bottom: 1px solid #e8e8e8;
}
.ant-calendar-year-panel-header a:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-calendar-year-panel-header .ant-calendar-year-panel-century-select,
.ant-calendar-year-panel-header .ant-calendar-year-panel-decade-select,
.ant-calendar-year-panel-header .ant-calendar-year-panel-year-select,
.ant-calendar-year-panel-header .ant-calendar-year-panel-month-select {
  color: rgba(0, 0, 0, 0.85);
}
.ant-calendar-year-panel-header .ant-calendar-year-panel-prev-century-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-next-century-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-prev-decade-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-next-decade-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-prev-month-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-next-month-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-prev-year-btn,
.ant-calendar-year-panel-header .ant-calendar-year-panel-next-year-btn {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar-year-panel-table {
  border-collapse: separate;
}
.ant-calendar-year-panel-year {
  color: rgba(0, 0, 0, 0.65);
  background: transparent;
  border-radius: 2px;
}
.ant-calendar-year-panel-year:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year {
  background: @primary-color;
  color: #fff;
}
.ant-calendar-year-panel-selected-cell .ant-calendar-year-panel-year:hover {
  background: @primary-color;
  color: #fff;
}
.ant-calendar-year-panel-last-decade-cell .ant-calendar-year-panel-year,
.ant-calendar-year-panel-next-decade-cell .ant-calendar-year-panel-year {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar-decade-panel {
  background: #fff;
  border-radius: 4px;
}
.ant-calendar-decade-panel-header {
  border-bottom: 1px solid #e8e8e8;
}
.ant-calendar-decade-panel-header a:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-century-select,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-decade-select,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-year-select,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-month-select {
  color: rgba(0, 0, 0, 0.85);
}
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-prev-century-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-next-century-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-prev-decade-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-next-decade-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-prev-month-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-next-month-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-prev-year-btn,
.ant-calendar-decade-panel-header .ant-calendar-decade-panel-next-year-btn {
  color: rgba(0, 0, 0, 0.45);
}
.ant-calendar-decade-panel-table {
  border-collapse: separate;
}
.ant-calendar-decade-panel-decade {
  color: rgba(0, 0, 0, 0.65);
  background: transparent;
  border-radius: 2px;
}
.ant-calendar-decade-panel-decade:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-calendar-decade-panel-selected-cell .ant-calendar-decade-panel-decade {
  background: @primary-color;
  color: #fff;
}
.ant-calendar-decade-panel-selected-cell .ant-calendar-decade-panel-decade:hover {
  background: @primary-color;
  color: #fff;
}
.ant-calendar-decade-panel-last-century-cell .ant-calendar-decade-panel-decade,
.ant-calendar-decade-panel-next-century-cell .ant-calendar-decade-panel-decade {
  color: rgba(0, 0, 0, 0.25);
}
.ant-calendar-week-number .ant-calendar-body tr:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-calendar-week-number .ant-calendar-body tr.ant-calendar-active-week {
  background: color(~`colorPalette("@{primary-color}", 2)`);
}
.ant-calendar-week-number .ant-calendar-body tr .ant-calendar-selected-day .ant-calendar-date,
.ant-calendar-week-number .ant-calendar-body tr .ant-calendar-selected-day:hover .ant-calendar-date {
  background: transparent;
  color: rgba(0, 0, 0, 0.65);
}
.ant-divider {
  color: rgba(0, 0, 0, 0.65);
  background: #e8e8e8;
}
.ant-divider-horizontal.ant-divider-with-text,
.ant-divider-horizontal.ant-divider-with-text-left,
.ant-divider-horizontal.ant-divider-with-text-right {
  background: transparent;
  color: rgba(0, 0, 0, 0.85);
}
.ant-divider-horizontal.ant-divider-with-text:before,
.ant-divider-horizontal.ant-divider-with-text-left:before,
.ant-divider-horizontal.ant-divider-with-text-right:before,
.ant-divider-horizontal.ant-divider-with-text:after,
.ant-divider-horizontal.ant-divider-with-text-left:after,
.ant-divider-horizontal.ant-divider-with-text-right:after {
  border-top: 1px solid #e8e8e8;
}
.ant-divider-dashed {
  background: none;
  border-top: 1px dashed #e8e8e8;
}
.ant-divider-horizontal.ant-divider-with-text.ant-divider-dashed,
.ant-divider-horizontal.ant-divider-with-text-left.ant-divider-dashed,
.ant-divider-horizontal.ant-divider-with-text-right.ant-divider-dashed {
  border-top: 0;
}
.ant-divider-horizontal.ant-divider-with-text.ant-divider-dashed:before,
.ant-divider-horizontal.ant-divider-with-text-left.ant-divider-dashed:before,
.ant-divider-horizontal.ant-divider-with-text-right.ant-divider-dashed:before,
.ant-divider-horizontal.ant-divider-with-text.ant-divider-dashed:after,
.ant-divider-horizontal.ant-divider-with-text-left.ant-divider-dashed:after,
.ant-divider-horizontal.ant-divider-with-text-right.ant-divider-dashed:after {
  border-style: dashed none none;
}
.ant-drawer-left.ant-drawer-open .ant-drawer-content-wrapper {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}
.ant-drawer-right.ant-drawer-open .ant-drawer-content-wrapper {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}
.ant-drawer-top.ant-drawer-open .ant-drawer-content-wrapper {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-drawer-bottom.ant-drawer-open .ant-drawer-content-wrapper {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
}
.ant-drawer-title {
  color: rgba(0, 0, 0, 0.85);
}
.ant-drawer-content {
  background-color: #fff;
  border: 0;
  background-clip: padding-box;
}
.ant-drawer-close {
  border: 0;
  background: transparent;
  color: rgba(0, 0, 0, 0.45);
}
.ant-drawer-close:focus,
.ant-drawer-close:hover {
  color: #444;
}
.ant-drawer-header {
  border-radius: 4px 4px 0 0;
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border-bottom: 1px solid #e8e8e8;
}
.ant-drawer-mask {
  background-color: rgba(0, 0, 0, 0.65);
}
.ant-drawer-open-content {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.ant-dropdown {
  color: rgba(0, 0, 0, 0.65);
}
.ant-dropdown-menu {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-clip: padding-box;
}
.ant-dropdown-menu-item-group-title {
  color: rgba(0, 0, 0, 0.45);
}
.ant-dropdown-menu-item,
.ant-dropdown-menu-submenu-title {
  color: rgba(0, 0, 0, 0.65);
}
.ant-dropdown-menu-item > a,
.ant-dropdown-menu-submenu-title > a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-dropdown-menu-item-selected,
.ant-dropdown-menu-submenu-title-selected,
.ant-dropdown-menu-item-selected > a,
.ant-dropdown-menu-submenu-title-selected > a {
  color: @primary-color;
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-dropdown-menu-item-disabled,
.ant-dropdown-menu-submenu-title-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-dropdown-menu-item-disabled:hover,
.ant-dropdown-menu-submenu-title-disabled:hover {
  color: rgba(0, 0, 0, 0.25);
  background-color: #fff;
}
.ant-dropdown-menu-item-divider,
.ant-dropdown-menu-submenu-title-divider {
  background-color: #e8e8e8;
}
.ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow-icon,
.ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
  color: rgba(0, 0, 0, 0.45);
}
.ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title,
.ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-dropdown-menu-dark,
.ant-dropdown-menu-dark .ant-dropdown-menu {
  background: #001529;
}
.ant-dropdown-menu-dark .ant-dropdown-menu-item,
.ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title,
.ant-dropdown-menu-dark .ant-dropdown-menu-item > a {
  color: rgba(255, 255, 255, 0.65);
}
.ant-dropdown-menu-dark .ant-dropdown-menu-item .ant-dropdown-menu-submenu-arrow:after,
.ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow:after,
.ant-dropdown-menu-dark .ant-dropdown-menu-item > a .ant-dropdown-menu-submenu-arrow:after {
  color: rgba(255, 255, 255, 0.65);
}
.ant-dropdown-menu-dark .ant-dropdown-menu-item:hover,
.ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title:hover,
.ant-dropdown-menu-dark .ant-dropdown-menu-item > a:hover {
  color: #fff;
  background: transparent;
}
.ant-dropdown-menu-dark .ant-dropdown-menu-item-selected,
.ant-dropdown-menu-dark .ant-dropdown-menu-item-selected:hover,
.ant-dropdown-menu-dark .ant-dropdown-menu-item-selected > a {
  background: @primary-color;
  color: #fff;
}
.ant-form {
  color: rgba(0, 0, 0, 0.65);
}
.ant-form legend {
  color: rgba(0, 0, 0, 0.45);
  border: 0;
  border-bottom: 1px solid #d9d9d9;
}
.ant-form output {
  color: rgba(0, 0, 0, 0.65);
}
.ant-form-item-required:before {
  color: #f5222d;
}
.ant-form-item {
  color: rgba(0, 0, 0, 0.65);
}
.ant-form-item-label label {
  color: rgba(0, 0, 0, 0.85);
}
.ant-form-explain,
.ant-form-extra {
  color: rgba(0, 0, 0, 0.45);
}
form .ant-upload {
  background: transparent;
}
.ant-input-group-wrap .ant-select-selection {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.ant-input-group-wrap .ant-select-selection:hover {
  border-color: #d9d9d9;
}
.ant-input-group-wrap .ant-select-selection--single {
  background-color: #eee;
}
.ant-input-group-wrap .ant-select-open .ant-select-selection {
  border-color: #d9d9d9;
  box-shadow: none;
}
.has-success.has-feedback .ant-form-item-children-icon {
  color: #52c41a;
}
.has-warning .ant-form-explain,
.has-warning .ant-form-split {
  color: #faad14;
}
.has-warning .ant-input,
.has-warning .ant-input:hover {
  border-color: #faad14;
}
.has-warning .ant-input:focus {
  border-color: #ffc53d;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
  border-right-width: 1px !important;
}
.has-warning .ant-input:not([disabled]):hover {
  border-color: #faad14;
}
.has-warning .ant-calendar-picker-open .ant-calendar-picker-input {
  border-color: #ffc53d;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
  border-right-width: 1px !important;
}
.has-warning .ant-input-prefix {
  color: #faad14;
}
.has-warning .ant-input-group-addon {
  color: #faad14;
  border-color: #faad14;
  background-color: #fff;
}
.has-warning .has-feedback {
  color: #faad14;
}
.has-warning.has-feedback .ant-form-item-children-icon {
  color: #faad14;
}
.has-warning .ant-select-selection {
  border-color: #faad14;
}
.has-warning .ant-select-open .ant-select-selection,
.has-warning .ant-select-focused .ant-select-selection {
  border-color: #ffc53d;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
  border-right-width: 1px !important;
}
.has-warning .ant-calendar-picker-icon:after,
.has-warning .ant-time-picker-icon:after,
.has-warning .ant-picker-icon:after,
.has-warning .ant-select-arrow,
.has-warning .ant-cascader-picker-arrow {
  color: #faad14;
}
.has-warning .ant-input-number,
.has-warning .ant-time-picker-input {
  border-color: #faad14;
}
.has-warning .ant-input-number-focused,
.has-warning .ant-time-picker-input-focused,
.has-warning .ant-input-number:focus,
.has-warning .ant-time-picker-input:focus {
  border-color: #ffc53d;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
  border-right-width: 1px !important;
}
.has-warning .ant-input-number:not([disabled]):hover,
.has-warning .ant-time-picker-input:not([disabled]):hover {
  border-color: #faad14;
}
.has-warning .ant-cascader-picker:focus .ant-cascader-input {
  border-color: #ffc53d;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
  border-right-width: 1px !important;
}
.has-error .ant-form-explain,
.has-error .ant-form-split {
  color: #f5222d;
}
.has-error .ant-input,
.has-error .ant-input:hover {
  border-color: #f5222d;
}
.has-error .ant-input:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  border-right-width: 1px !important;
}
.has-error .ant-input:not([disabled]):hover {
  border-color: #f5222d;
}
.has-error .ant-calendar-picker-open .ant-calendar-picker-input {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  border-right-width: 1px !important;
}
.has-error .ant-input-prefix {
  color: #f5222d;
}
.has-error .ant-input-group-addon {
  color: #f5222d;
  border-color: #f5222d;
  background-color: #fff;
}
.has-error .has-feedback {
  color: #f5222d;
}
.has-error.has-feedback .ant-form-item-children-icon {
  color: #f5222d;
}
.has-error .ant-select-selection {
  border-color: #f5222d;
}
.has-error .ant-select-open .ant-select-selection,
.has-error .ant-select-focused .ant-select-selection {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  border-right-width: 1px !important;
}
.has-error .ant-select.ant-select-auto-complete .ant-input:focus {
  border-color: #f5222d;
}
.has-error .ant-input-group-addon .ant-select-selection {
  border-color: transparent;
  box-shadow: none;
}
.has-error .ant-calendar-picker-icon:after,
.has-error .ant-time-picker-icon:after,
.has-error .ant-picker-icon:after,
.has-error .ant-select-arrow,
.has-error .ant-cascader-picker-arrow {
  color: #f5222d;
}
.has-error .ant-input-number,
.has-error .ant-time-picker-input {
  border-color: #f5222d;
}
.has-error .ant-input-number-focused,
.has-error .ant-time-picker-input-focused,
.has-error .ant-input-number:focus,
.has-error .ant-time-picker-input:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  border-right-width: 1px !important;
}
.has-error .ant-input-number:not([disabled]):hover,
.has-error .ant-time-picker-input:not([disabled]):hover {
  border-color: #f5222d;
}
.has-error .ant-mention-wrapper .ant-mention-editor,
.has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):hover {
  border-color: #f5222d;
}
.has-error .ant-mention-wrapper.ant-mention-active:not([disabled]) .ant-mention-editor,
.has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  border-right-width: 1px !important;
}
.has-error .ant-cascader-picker:focus .ant-cascader-input {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  border-right-width: 1px !important;
}
.is-validating.has-feedback .ant-form-item-children-icon {
  color: @primary-color;
}
.ant-input-number {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-input-number::-moz-placeholder {
  color: #bfbfbf;
}
.ant-input-number:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-input-number::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-input-number:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input-number:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-number-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-input-number-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-input-number-handler {
  color: rgba(0, 0, 0, 0.45);
}
.ant-input-number-handler:active {
  background: #f4f4f4;
}
.ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-input-number-handler-up-inner,
.ant-input-number-handler-down-inner {
  color: rgba(0, 0, 0, 0.45);
}
.ant-input-number:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input-number-focused {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-number-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-input-number-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-input-number-input {
  background-color: transparent;
  border: 0;
  border-radius: 4px;
}
.ant-input-number-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-input-number-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-input-number-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-input-number-handler-wrap {
  border-left: 1px solid #d9d9d9;
  background: #fff;
  border-radius: 0 4px 4px 0;
}
.ant-input-number-handler-down {
  border-top: 1px solid #d9d9d9;
}
.ant-input-number-handler-up-disabled:hover .ant-input-number-handler-up-inner,
.ant-input-number-handler-down-disabled:hover .ant-input-number-handler-down-inner {
  color: rgba(0, 0, 0, 0.25);
}
.ant-input {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-input-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-input-group {
  color: rgba(0, 0, 0, 0.65);
  border-collapse: separate;
  border-spacing: 0;
}
.ant-input-group-addon:not(:first-child):not(:last-child),
.ant-input-group-wrap:not(:first-child):not(:last-child),
.ant-input-group > .ant-input:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.ant-input-group .ant-input:focus {
  border-right-width: 1px;
}
.ant-input-group .ant-input:hover {
  border-right-width: 1px;
}
.ant-input-group-addon {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-input-group-addon .ant-select .ant-select-selection {
  background-color: inherit;
  border: 1px solid transparent;
  box-shadow: none;
}
.ant-input-group-addon .ant-select-open .ant-select-selection,
.ant-input-group-addon .ant-select-focused .ant-select-selection {
  color: @primary-color;
}
.ant-input-group > .ant-input:first-child,
.ant-input-group-addon:first-child {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.ant-input-group > .ant-input:first-child .ant-select .ant-select-selection,
.ant-input-group-addon:first-child .ant-select .ant-select-selection {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.ant-input-group > .ant-input-affix-wrapper:not(:first-child) .ant-input {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.ant-input-group > .ant-input-affix-wrapper:not(:last-child) .ant-input {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.ant-input-group-addon:first-child {
  border-right: 0;
}
.ant-input-group-addon:last-child {
  border-left: 0;
}
.ant-input-group > .ant-input:last-child,
.ant-input-group-addon:last-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.ant-input-group > .ant-input:last-child .ant-select .ant-select-selection,
.ant-input-group-addon:last-child .ant-select .ant-select-selection {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child),
.ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child),
.ant-input-group.ant-input-group-compact > .ant-input:not(:first-child):not(:last-child) {
  border-right-width: 1px;
  border-right-color: transparent;
}
.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child):hover,
.ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child):hover,
.ant-input-group.ant-input-group-compact > .ant-input:not(:first-child):not(:last-child):hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child):focus,
.ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child):focus,
.ant-input-group.ant-input-group-compact > .ant-input:not(:first-child):not(:last-child):focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-group.ant-input-group-compact > * {
  border-radius: 0;
  border-right-width: 0;
}
.ant-input-group.ant-input-group-compact > span:not(:last-child) > .ant-input {
  border-right-width: 0;
}
.ant-input-group.ant-input-group-compact > .ant-select > .ant-select-selection,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker .ant-input,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete .ant-input,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker .ant-input,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper .ant-mention-editor,
.ant-input-group.ant-input-group-compact > .ant-time-picker .ant-time-picker-input {
  border-radius: 0;
  border-right-width: 1px;
  border-right-color: transparent;
}
.ant-input-group.ant-input-group-compact > .ant-select > .ant-select-selection:hover,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper .ant-mention-editor:hover,
.ant-input-group.ant-input-group-compact > .ant-time-picker .ant-time-picker-input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input-group.ant-input-group-compact > .ant-select > .ant-select-selection:focus,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper .ant-mention-editor:focus,
.ant-input-group.ant-input-group-compact > .ant-time-picker .ant-time-picker-input:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-group.ant-input-group-compact > *:first-child,
.ant-input-group.ant-input-group-compact > .ant-select:first-child > .ant-select-selection,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker:first-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:first-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker:first-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper:first-child .ant-mention-editor,
.ant-input-group.ant-input-group-compact > .ant-time-picker:first-child .ant-time-picker-input {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.ant-input-group.ant-input-group-compact > *:last-child,
.ant-input-group.ant-input-group-compact > .ant-select:last-child > .ant-select-selection,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker:last-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:last-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker:last-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker-focused:last-child .ant-input,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper:last-child .ant-mention-editor,
.ant-input-group.ant-input-group-compact > .ant-time-picker:last-child .ant-time-picker-input {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right-width: 1px;
  border-right-color: #d9d9d9;
}
.ant-input-group.ant-input-group-compact > *:last-child:hover,
.ant-input-group.ant-input-group-compact > .ant-select:last-child > .ant-select-selection:hover,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker:last-child .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:last-child .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker:last-child .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker-focused:last-child .ant-input:hover,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper:last-child .ant-mention-editor:hover,
.ant-input-group.ant-input-group-compact > .ant-time-picker:last-child .ant-time-picker-input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input-group.ant-input-group-compact > *:last-child:focus,
.ant-input-group.ant-input-group-compact > .ant-select:last-child > .ant-select-selection:focus,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker:last-child .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:last-child .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker:last-child .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker-focused:last-child .ant-input:focus,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper:last-child .ant-mention-editor:focus,
.ant-input-group.ant-input-group-compact > .ant-time-picker:last-child .ant-time-picker-input:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-group.ant-input-group-compact > *:last-child:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-select:last-child > .ant-select-selection:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-calendar-picker:last-child .ant-input:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-select-auto-complete:last-child .ant-input:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker:last-child .ant-input:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-cascader-picker-focused:last-child .ant-input:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-mention-wrapper:last-child .ant-mention-editor:focus .ant-cascader-input,
.ant-input-group.ant-input-group-compact > .ant-time-picker:last-child .ant-time-picker-input:focus .ant-cascader-input {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-input-affix-wrapper {
  color: rgba(0, 0, 0, 0.65);
}
.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-input-affix-wrapper .ant-input-prefix,
.ant-input-affix-wrapper .ant-input-suffix {
  color: rgba(0, 0, 0, 0.65);
}
.ant-input-search-icon {
  color: rgba(0, 0, 0, 0.45);
}
.ant-input-search-icon:hover {
  color: #333;
}
.ant-input-search > .ant-input-suffix > .ant-input-search-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.ant-layout {
  background: #f0f2f5;
}
.ant-layout-header {
  background: #001529;
}
.ant-layout-footer {
  background: #f0f2f5;
  color: rgba(0, 0, 0, 0.65);
}
.ant-layout-sider {
  background: #001529;
}
.ant-layout-sider-trigger {
  color: #fff;
  background: #002140;
}
.ant-layout-sider-zero-width-trigger {
  background: #001529;
  color: #fff;
  border-radius: 0 4px 4px 0;
}
.ant-layout-sider-zero-width-trigger:hover {
  background: #192c3e;
}
.ant-layout-sider-light {
  background: #fff;
}
.ant-layout-sider-light .ant-layout-sider-trigger {
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
}
.ant-layout-sider-light .ant-layout-sider-zero-width-trigger {
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
}
.ant-list {
  color: rgba(0, 0, 0, 0.65);
}
.ant-list-empty-text {
  color: rgba(0, 0, 0, 0.45);
}
.ant-list-item-meta-title {
  color: rgba(0, 0, 0, 0.65);
}
.ant-list-item-meta-title > a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-list-item-meta-title > a:hover {
  color: @primary-color;
}
.ant-list-item-meta-description {
  color: rgba(0, 0, 0, 0.45);
}
.ant-list-item-action > li {
  color: rgba(0, 0, 0, 0.45);
}
.ant-list-item-action-split {
  background-color: #e8e8e8;
}
.ant-list-empty {
  color: rgba(0, 0, 0, 0.45);
}
.ant-list-split .ant-list-item {
  border-bottom: 1px solid #e8e8e8;
}
.ant-list-split .ant-list-item:last-child {
  border-bottom: none;
}
.ant-list-split .ant-list-header {
  border-bottom: 1px solid #e8e8e8;
}
.ant-list-something-after-last-item .ant-spin-container > .ant-list-item:last-child {
  border-bottom: 1px solid #e8e8e8;
}
.ant-list-vertical .ant-list-item-meta-title {
  color: rgba(0, 0, 0, 0.85);
}
.ant-list-vertical .ant-list-item-content {
  color: rgba(0, 0, 0, 0.65);
}
.ant-list-grid .ant-list-item {
  border-bottom: none;
}
.ant-list-bordered {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
.ant-list-bordered .ant-list-item {
  border-bottom: 1px solid #e8e8e8;
}
.ant-mention-wrapper {
  color: rgba(0, 0, 0, 0.65);
}
.ant-mention-wrapper .ant-mention-editor {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-mention-wrapper .ant-mention-editor::-moz-placeholder {
  color: #bfbfbf;
}
.ant-mention-wrapper .ant-mention-editor:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-mention-wrapper .ant-mention-editor::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-mention-wrapper .ant-mention-editor:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-mention-wrapper .ant-mention-editor:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-mention-wrapper .ant-mention-editor-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-mention-wrapper .ant-mention-editor-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-mention-wrapper.ant-mention-active:not(.disabled) .ant-mention-editor {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-mention-wrapper.disabled .ant-mention-editor {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-mention-wrapper.disabled .ant-mention-editor:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-mention-wrapper .public-DraftEditorPlaceholder-root .public-DraftEditorPlaceholder-inner {
  color: #bfbfbf;
}
.ant-mention-dropdown {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}
.ant-mention-dropdown-notfound.ant-mention-dropdown-item {
  color: rgba(0, 0, 0, 0.25);
}
.ant-mention-dropdown-notfound.ant-mention-dropdown-item .anticon-loading {
  color: @primary-color;
}
.ant-mention-dropdown-item {
  color: rgba(0, 0, 0, 0.65);
}
.ant-mention-dropdown-item:hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-mention-dropdown-item.focus,
.ant-mention-dropdown-item-active {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-mention-dropdown-item-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-mention-dropdown-item-disabled:hover {
  color: rgba(0, 0, 0, 0.25);
  background-color: #fff;
}
.ant-mention-dropdown-item-selected,
.ant-mention-dropdown-item-selected:hover {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.65);
}
.ant-mention-dropdown-item-divider {
  background-color: #e8e8e8;
}
.ant-menu {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
}
.ant-menu-item-group-title {
  color: rgba(0, 0, 0, 0.45);
}
.ant-menu-item:active,
.ant-menu-submenu-title:active {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-menu-item > a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-menu-item > a:hover {
  color: @primary-color;
}
.ant-menu-item > a:before {
  background-color: transparent;
}
.ant-menu-item-divider {
  background-color: #e8e8e8;
}
.ant-menu-item:hover,
.ant-menu-item-active,
.ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
.ant-menu-submenu-active,
.ant-menu-submenu-title:hover {
  color: @primary-color;
}
.ant-menu-horizontal > .ant-menu-item:hover,
.ant-menu-horizontal > .ant-menu-item-active,
.ant-menu-horizontal > .ant-menu-submenu .ant-menu-submenu-title:hover {
  background-color: transparent;
}
.ant-menu-item-selected {
  color: @primary-color;
}
.ant-menu-item-selected > a,
.ant-menu-item-selected > a:hover {
  color: @primary-color;
}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border-right: 1px solid #e8e8e8;
}
.ant-menu-vertical-right {
  border-left: 1px solid #e8e8e8;
}
.ant-menu-vertical.ant-menu-sub,
.ant-menu-vertical-left.ant-menu-sub,
.ant-menu-vertical-right.ant-menu-sub {
  border-right: 0;
}
.ant-menu-vertical.ant-menu-sub .ant-menu-item,
.ant-menu-vertical-left.ant-menu-sub .ant-menu-item,
.ant-menu-vertical-right.ant-menu-sub .ant-menu-item {
  border-right: 0;
}
.ant-menu-vertical.ant-menu-sub .ant-menu-item:after,
.ant-menu-vertical-left.ant-menu-sub .ant-menu-item:after,
.ant-menu-vertical-right.ant-menu-sub .ant-menu-item:after {
  border-right: 0;
}
.ant-menu > .ant-menu-item-divider {
  background-color: #e8e8e8;
}
.ant-menu-submenu-popup {
  border-radius: 4px;
}
.ant-menu-submenu > .ant-menu {
  background-color: #fff;
  border-radius: 4px;
}
.ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical > .ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical-left > .ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical-right > .ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-submenu-inline > .ant-menu-submenu-title .ant-menu-submenu-arrow:after {
  background: #fff;
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65));
  border-radius: 2px;
}
.ant-menu-submenu-vertical > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical-left > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical-right > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after,
.ant-menu-submenu-inline > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:after,
.ant-menu-submenu-vertical > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical-left > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before,
.ant-menu-submenu-vertical-right > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before,
.ant-menu-submenu-inline > .ant-menu-submenu-title:hover .ant-menu-submenu-arrow:before {
  background: linear-gradient(to right, @primary-color, @primary-color);
}
.ant-menu-vertical .ant-menu-submenu-selected,
.ant-menu-vertical-left .ant-menu-submenu-selected,
.ant-menu-vertical-right .ant-menu-submenu-selected {
  color: @primary-color;
}
.ant-menu-vertical .ant-menu-submenu-selected > a,
.ant-menu-vertical-left .ant-menu-submenu-selected > a,
.ant-menu-vertical-right .ant-menu-submenu-selected > a {
  color: @primary-color;
}
.ant-menu-horizontal {
  border: 0;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: none;
}
.ant-menu-horizontal > .ant-menu-item,
.ant-menu-horizontal > .ant-menu-submenu {
  border-bottom: 2px solid transparent;
}
.ant-menu-horizontal > .ant-menu-item:hover,
.ant-menu-horizontal > .ant-menu-submenu:hover,
.ant-menu-horizontal > .ant-menu-item-active,
.ant-menu-horizontal > .ant-menu-submenu-active,
.ant-menu-horizontal > .ant-menu-item-open,
.ant-menu-horizontal > .ant-menu-submenu-open,
.ant-menu-horizontal > .ant-menu-item-selected,
.ant-menu-horizontal > .ant-menu-submenu-selected {
  border-bottom: 2px solid @primary-color;
  color: @primary-color;
}
.ant-menu-horizontal > .ant-menu-item > a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-menu-horizontal > .ant-menu-item > a:hover {
  color: @primary-color;
}
.ant-menu-horizontal > .ant-menu-item-selected > a {
  color: @primary-color;
}
.ant-menu-vertical .ant-menu-item:after,
.ant-menu-vertical-left .ant-menu-item:after,
.ant-menu-vertical-right .ant-menu-item:after,
.ant-menu-inline .ant-menu-item:after {
  border-right: 3px solid @primary-color;
}
.ant-menu-inline-collapsed-tooltip a {
  color: rgba(255, 255, 255, 0.85);
}
.ant-menu-root.ant-menu-vertical,
.ant-menu-root.ant-menu-vertical-left,
.ant-menu-root.ant-menu-vertical-right,
.ant-menu-root.ant-menu-inline {
  box-shadow: none;
}
.ant-menu-sub.ant-menu-inline {
  border: 0;
  box-shadow: none;
  border-radius: 0;
}
.ant-menu-item-disabled,
.ant-menu-submenu-disabled {
  color: rgba(0, 0, 0, 0.25) !important;
  background: none;
  border-color: transparent !important;
}
.ant-menu-item-disabled > a,
.ant-menu-submenu-disabled > a {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-menu-item-disabled > .ant-menu-submenu-title,
.ant-menu-submenu-disabled > .ant-menu-submenu-title {
  color: rgba(0, 0, 0, 0.25) !important;
}
.ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after {
  background: rgba(0, 0, 0, 0.25) !important;
}
.ant-menu-dark,
.ant-menu-dark .ant-menu-sub {
  color: rgba(255, 255, 255, 0.65);
  background: #001529;
}
.ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-sub .ant-menu-submenu-title .ant-menu-submenu-arrow:before {
  background: #fff;
}
.ant-menu-dark.ant-menu-submenu-popup {
  background: transparent;
}
.ant-menu-dark .ant-menu-inline.ant-menu-sub {
  background: #000c17;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45) inset;
}
.ant-menu-dark.ant-menu-horizontal {
  border-bottom: 0;
}
.ant-menu-dark.ant-menu-horizontal > .ant-menu-item,
.ant-menu-dark.ant-menu-horizontal > .ant-menu-submenu {
  border-color: #001529;
  border-bottom: 0;
}
.ant-menu-dark .ant-menu-item,
.ant-menu-dark .ant-menu-item-group-title,
.ant-menu-dark .ant-menu-item > a {
  color: rgba(255, 255, 255, 0.65);
}
.ant-menu-dark.ant-menu-inline,
.ant-menu-dark.ant-menu-vertical,
.ant-menu-dark.ant-menu-vertical-left,
.ant-menu-dark.ant-menu-vertical-right {
  border-right: 0;
}
.ant-menu-dark.ant-menu-inline .ant-menu-item,
.ant-menu-dark.ant-menu-vertical .ant-menu-item,
.ant-menu-dark.ant-menu-vertical-left .ant-menu-item,
.ant-menu-dark.ant-menu-vertical-right .ant-menu-item {
  border-right: 0;
}
.ant-menu-dark.ant-menu-inline .ant-menu-item:after,
.ant-menu-dark.ant-menu-vertical .ant-menu-item:after,
.ant-menu-dark.ant-menu-vertical-left .ant-menu-item:after,
.ant-menu-dark.ant-menu-vertical-right .ant-menu-item:after {
  border-right: 0;
}
.ant-menu-dark .ant-menu-item:hover,
.ant-menu-dark .ant-menu-item-active,
.ant-menu-dark .ant-menu-submenu-active,
.ant-menu-dark .ant-menu-submenu-open,
.ant-menu-dark .ant-menu-submenu-selected,
.ant-menu-dark .ant-menu-submenu-title:hover {
  background-color: transparent;
  color: #fff;
}
.ant-menu-dark .ant-menu-item:hover > a,
.ant-menu-dark .ant-menu-item-active > a,
.ant-menu-dark .ant-menu-submenu-active > a,
.ant-menu-dark .ant-menu-submenu-open > a,
.ant-menu-dark .ant-menu-submenu-selected > a,
.ant-menu-dark .ant-menu-submenu-title:hover > a {
  color: #fff;
}
.ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-item:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-item-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-active > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-selected > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-title:hover > .ant-menu-submenu-title:hover > .ant-menu-submenu-arrow:before {
  background: #fff;
}
.ant-menu-dark .ant-menu-item-selected {
  border-right: 0;
  color: #fff;
}
.ant-menu-dark .ant-menu-item-selected:after {
  border-right: 0;
}
.ant-menu-dark .ant-menu-item-selected > a,
.ant-menu-dark .ant-menu-item-selected > a:hover {
  color: #fff;
}
.ant-menu.ant-menu-dark .ant-menu-item-selected,
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
  background-color: @primary-color;
}
.ant-menu-dark .ant-menu-item-disabled,
.ant-menu-dark .ant-menu-submenu-disabled,
.ant-menu-dark .ant-menu-item-disabled > a,
.ant-menu-dark .ant-menu-submenu-disabled > a {
  color: rgba(255, 255, 255, 0.35) !important;
}
.ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title,
.ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title {
  color: rgba(255, 255, 255, 0.35) !important;
}
.ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:before,
.ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after,
.ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title > .ant-menu-submenu-arrow:after {
  background: rgba(255, 255, 255, 0.35) !important;
}
.ant-message {
  color: rgba(0, 0, 0, 0.65);
}
.ant-message-notice-content {
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #fff;
}
.ant-message-success .anticon {
  color: #52c41a;
}
.ant-message-error .anticon {
  color: #f5222d;
}
.ant-message-warning .anticon {
  color: #faad14;
}
.ant-message-info .anticon,
.ant-message-loading .anticon {
  color: @primary-color;
}
.ant-modal {
  color: rgba(0, 0, 0, 0.65);
}
.ant-modal-title {
  color: rgba(0, 0, 0, 0.85);
}
.ant-modal-content {
  background-color: #fff;
  border: 0;
  border-radius: 4px;
  background-clip: padding-box;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.ant-modal-close {
  border: 0;
  background: transparent;
  color: rgba(0, 0, 0, 0.45);
}
.ant-modal-close:focus,
.ant-modal-close:hover {
  color: #444;
}
.ant-modal-header {
  border-radius: 4px 4px 0 0;
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border-bottom: 1px solid #e8e8e8;
}
.ant-modal-footer {
  border-top: 1px solid #e8e8e8;
  border-radius: 0 0 4px 4px;
}
.ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.1) !important;
}
.ant-confirm-body .ant-confirm-title {
  color: rgba(0, 0, 0, 0.85);
}
.ant-confirm-body .ant-confirm-content {
  color: rgba(0, 0, 0, 0.65);
}
.ant-confirm-error .ant-confirm-body > .anticon {
  color: #f5222d;
}
.ant-confirm-warning .ant-confirm-body > .anticon,
.ant-confirm-confirm .ant-confirm-body > .anticon {
  color: #faad14;
}
.ant-confirm-info .ant-confirm-body > .anticon {
  color: @primary-color;
}
.ant-confirm-success .ant-confirm-body > .anticon {
  color: #52c41a;
}
.ant-notification {
  color: rgba(0, 0, 0, 0.65);
}
.ant-notification-notice {
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #fff;
}
.ant-notification-notice-message {
  color: rgba(0, 0, 0, 0.85);
}
.ant-notification-notice-message-single-line-auto-margin {
  background-color: transparent;
}
.ant-notification-notice-icon-success {
  color: #52c41a;
}
.ant-notification-notice-icon-info {
  color: @primary-color;
}
.ant-notification-notice-icon-warning {
  color: #faad14;
}
.ant-notification-notice-icon-error {
  color: #f5222d;
}
.ant-notification-notice-close {
  color: rgba(0, 0, 0, 0.45);
}
.ant-notification-notice-close:hover {
  color: rgba(0, 0, 0, 0.67);
}
.ant-pagination {
  color: rgba(0, 0, 0, 0.65);
}
.ant-pagination-item {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
}
.ant-pagination-item a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-pagination-item:focus,
.ant-pagination-item:hover {
  border-color: @primary-color;
}
.ant-pagination-item:focus a,
.ant-pagination-item:hover a {
  color: @primary-color;
}
.ant-pagination-item-active {
  border-color: @primary-color;
}
.ant-pagination-item-active a {
  color: @primary-color;
}
.ant-pagination-item-active:focus,
.ant-pagination-item-active:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-pagination-item-active:focus a,
.ant-pagination-item-active:hover a {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: @primary-color;
}
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
  color: rgba(0, 0, 0, 0.25);
}
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
}
.ant-pagination-prev a,
.ant-pagination-next a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-pagination-prev:hover a,
.ant-pagination-next:hover a {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  border: 1px solid #d9d9d9;
  background-color: #fff;
  border-radius: 4px;
}
.ant-pagination-prev:focus .ant-pagination-item-link,
.ant-pagination-next:focus .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  border-color: @primary-color;
  color: @primary-color;
}
.ant-pagination-disabled a,
.ant-pagination-disabled:hover a,
.ant-pagination-disabled:focus a,
.ant-pagination-disabled .ant-pagination-item-link,
.ant-pagination-disabled:hover .ant-pagination-item-link,
.ant-pagination-disabled:focus .ant-pagination-item-link {
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.25);
}
.ant-pagination-options-quick-jumper input {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-pagination-options-quick-jumper input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-pagination-options-quick-jumper input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-pagination-options-quick-jumper input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-pagination-options-quick-jumper input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-pagination-options-quick-jumper input:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-pagination-options-quick-jumper input-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-pagination-options-quick-jumper input-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-pagination-simple .ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-simple .ant-pagination-next .ant-pagination-item-link {
  border: 0;
}
.ant-pagination-simple .ant-pagination-simple-pager input {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
.ant-pagination-simple .ant-pagination-simple-pager input:hover {
  border-color: @primary-color;
}
.ant-pagination.mini .ant-pagination-item:not(.ant-pagination-item-active) {
  background: transparent;
  border-color: transparent;
}
.ant-pagination.mini .ant-pagination-prev .ant-pagination-item-link,
.ant-pagination.mini .ant-pagination-next .ant-pagination-item-link {
  border-color: transparent;
  background: transparent;
}
.ant-popover {
  color: rgba(0, 0, 0, 0.65);
}
.ant-popover:after {
  background: rgba(255, 255, 255, 0.01);
}
.ant-popover-inner {
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-popover-title {
  border-bottom: 1px solid #e8e8e8;
  color: rgba(0, 0, 0, 0.85);
}
.ant-popover-inner-content {
  color: rgba(0, 0, 0, 0.65);
}
.ant-popover-message {
  color: rgba(0, 0, 0, 0.65);
}
.ant-popover-message > .anticon {
  color: #faad14;
}
.ant-popover-arrow {
  background: #fff;
  border-color: transparent;
  border-style: solid;
}
.ant-popover-placement-top > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-topLeft > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-topRight > .ant-popover-content > .ant-popover-arrow {
  box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
}
.ant-popover-placement-right > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-rightTop > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-rightBottom > .ant-popover-content > .ant-popover-arrow {
  box-shadow: -3px 3px 7px rgba(0, 0, 0, 0.07);
}
.ant-popover-placement-bottom > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-bottomLeft > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-bottomRight > .ant-popover-content > .ant-popover-arrow {
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}
.ant-popover-placement-left > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-leftTop > .ant-popover-content > .ant-popover-arrow,
.ant-popover-placement-leftBottom > .ant-popover-content > .ant-popover-arrow {
  box-shadow: 3px -3px 7px rgba(0, 0, 0, 0.07);
}
.ant-progress {
  color: rgba(0, 0, 0, 0.65);
}
.ant-progress-inner {
  background-color: #f5f5f5;
  border-radius: 100px;
}
.ant-progress-success-bg,
.ant-progress-bg {
  background-color: @primary-color;
}
.ant-progress-success-bg {
  background-color: #52c41a;
}
.ant-progress-text {
  color: rgba(0, 0, 0, 0.45);
}
.ant-progress-status-active .ant-progress-bg:before {
  background: #fff;
  border-radius: 10px;
}
.ant-progress-status-exception .ant-progress-bg {
  background-color: #f5222d;
}
.ant-progress-status-exception .ant-progress-text {
  color: #f5222d;
}
.ant-progress-status-success .ant-progress-bg {
  background-color: #52c41a;
}
.ant-progress-status-success .ant-progress-text {
  color: #52c41a;
}
.ant-progress-circle .ant-progress-inner {
  background-color: transparent;
}
.ant-progress-circle .ant-progress-text {
  color: rgba(0, 0, 0, 0.65);
}
.ant-progress-circle.ant-progress-status-exception .ant-progress-text {
  color: #f5222d;
}
.ant-progress-circle.ant-progress-status-success .ant-progress-text {
  color: #52c41a;
}
.ant-radio-group {
  color: rgba(0, 0, 0, 0.65);
}
.ant-radio-wrapper {
  color: rgba(0, 0, 0, 0.65);
}
.ant-radio {
  color: rgba(0, 0, 0, 0.65);
}
.ant-radio-wrapper:hover .ant-radio .ant-radio-inner,
.ant-radio:hover .ant-radio-inner,
.ant-radio-focused .ant-radio-inner {
  border-color: @primary-color;
}
.ant-radio-checked:after {
  border-radius: 50%;
  border: 1px solid @primary-color;
}
.ant-radio-inner {
  border-width: 1px;
  border-style: solid;
  border-radius: 100px;
  border-color: #d9d9d9;
  background-color: #fff;
}
.ant-radio-inner:after {
  border-radius: 8px;
  border-top: 0;
  border-left: 0;
  background-color: @primary-color;
}
.ant-radio-checked .ant-radio-inner {
  border-color: @primary-color;
}
.ant-radio-disabled .ant-radio-inner {
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5;
}
.ant-radio-disabled .ant-radio-inner:after {
  background-color: #ccc;
}
.ant-radio-disabled + span {
  color: rgba(0, 0, 0, 0.25);
}
.ant-radio-button-wrapper {
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #d9d9d9;
  border-left: 0;
  border-top-width: 1.02px;
  background: #fff;
}
.ant-radio-button-wrapper a {
  color: rgba(0, 0, 0, 0.65);
}
.ant-radio-button-wrapper:not(:first-child)::before {
  background-color: #d9d9d9;
}
.ant-radio-button-wrapper:first-child {
  border-radius: 4px 0 0 4px;
  border-left: 1px solid #d9d9d9;
}
.ant-radio-button-wrapper:last-child {
  border-radius: 0 4px 4px 0;
}
.ant-radio-button-wrapper:first-child:last-child {
  border-radius: 4px;
}
.ant-radio-button-wrapper:hover,
.ant-radio-button-wrapper-focused {
  color: @primary-color;
}
.ant-radio-button-wrapper-checked {
  background: #fff;
  border-color: @primary-color;
  color: @primary-color;
  box-shadow: -1px 0 0 0 @primary-color;
}
.ant-radio-button-wrapper-checked::before {
  background-color: @primary-color !important;
}
.ant-radio-button-wrapper-checked:first-child {
  border-color: @primary-color;
  box-shadow: none !important;
}
.ant-radio-button-wrapper-checked:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: -1px 0 0 0 color(~`colorPalette("@{primary-color}", 5)`);
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-radio-button-wrapper-checked:active {
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
  box-shadow: -1px 0 0 0 color(~`colorPalette("@{primary-color}", 7)`);
  color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: @primary-color;
  border-color: @primary-color;
  color: #fff;
}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  background: color(~`colorPalette("@{primary-color}", 5)`);
  color: #fff;
}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
  border-color: color(~`colorPalette("@{primary-color}", 7)`);
  background: color(~`colorPalette("@{primary-color}", 7)`);
  color: #fff;
}
.ant-radio-button-wrapper-disabled {
  border-color: #d9d9d9;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-radio-button-wrapper-disabled:first-child,
.ant-radio-button-wrapper-disabled:hover {
  border-color: #d9d9d9;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-radio-button-wrapper-disabled:first-child {
  border-left-color: #d9d9d9;
}
.ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
  color: #fff;
  background-color: #e6e6e6;
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-rate {
  color: rgba(0, 0, 0, 0.65);
  color: #fadb14;
}
.ant-rate-star {
  color: inherit;
}
.ant-rate-star-first,
.ant-rate-star-second {
  color: #e8e8e8;
}
.ant-rate-star-half .ant-rate-star-first,
.ant-rate-star-full .ant-rate-star-second {
  color: inherit;
}
.ant-select {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select > ul > li > a {
  background-color: #fff;
}
.ant-select-arrow {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-selection {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  border-top-width: 1.02px;
}
.ant-select-selection:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-select-focused .ant-select-selection,
.ant-select-selection:focus,
.ant-select-selection:active {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-select-selection__clear {
  background: #fff;
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-selection__clear:hover {
  color: rgba(0, 0, 0, 0.45);
}
.ant-select-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-disabled .ant-select-selection {
  background: #f5f5f5;
}
.ant-select-disabled .ant-select-selection:hover,
.ant-select-disabled .ant-select-selection:focus,
.ant-select-disabled .ant-select-selection:active {
  border-color: #d9d9d9;
  box-shadow: none;
}
.ant-select-disabled .ant-select-selection--multiple .ant-select-selection__choice {
  background: #f5f5f5;
  color: #aaa;
}
.ant-select-disabled .ant-select-selection__choice__remove {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-disabled .ant-select-selection__choice__remove:hover {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-selection__placeholder,
.ant-select-search__field__placeholder {
  color: #bfbfbf;
}
.ant-select-search--inline .ant-select-search__field {
  border-width: 0;
  background: transparent;
  border-radius: 4px;
}
.ant-select-selection--multiple .ant-select-selection__choice {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
}
.ant-select-selection--multiple .ant-select-selection__choice__remove {
  color: rgba(0, 0, 0, 0.45);
}
.ant-select-selection--multiple .ant-select-selection__choice__remove:hover {
  color: #404040;
}
.ant-select-open .ant-select-selection {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-select-combobox .ant-select-search__field {
  box-shadow: none;
}
.ant-select-dropdown {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}
.ant-select-dropdown-menu-item-group-title {
  color: rgba(0, 0, 0, 0.45);
}
.ant-select-dropdown-menu-item-group-list .ant-select-dropdown-menu-item:first-child:not(:last-child),
.ant-select-dropdown-menu-item-group:not(:last-child) .ant-select-dropdown-menu-item-group-list .ant-select-dropdown-menu-item:last-child {
  border-radius: 0;
}
.ant-select-dropdown-menu-item {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-dropdown-menu-item:hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-select-dropdown-menu-item:first-child {
  border-radius: 4px 4px 0 0;
}
.ant-select-dropdown-menu-item:last-child {
  border-radius: 0 0 4px 4px;
}
.ant-select-dropdown-menu-item-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-dropdown-menu-item-disabled:hover {
  color: rgba(0, 0, 0, 0.25);
  background-color: #fff;
}
.ant-select-dropdown-menu-item-selected,
.ant-select-dropdown-menu-item-selected:hover {
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-dropdown-menu-item-active {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-select-dropdown-menu-item-divider {
  background-color: #e8e8e8;
}
.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item .ant-select-selected-icon {
  color: transparent;
}
.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item:hover .ant-select-selected-icon {
  color: #ddd;
}
.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected .ant-select-selected-icon,
.ant-select-dropdown.ant-select-dropdown--multiple .ant-select-dropdown-menu-item-selected:hover .ant-select-selected-icon {
  color: @primary-color;
}
.ant-skeleton-header .ant-skeleton-avatar {
  background: #f2f2f2;
}
.ant-skeleton-header .ant-skeleton-avatar.ant-skeleton-avatar-circle {
  border-radius: 50%;
}
.ant-skeleton-header .ant-skeleton-avatar-lg.ant-skeleton-avatar-circle {
  border-radius: 50%;
}
.ant-skeleton-header .ant-skeleton-avatar-sm.ant-skeleton-avatar-circle {
  border-radius: 50%;
}
.ant-skeleton-content .ant-skeleton-title {
  background: #f2f2f2;
}
.ant-skeleton-content .ant-skeleton-paragraph > li {
  background: #f2f2f2;
}
.ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-title,
.ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-paragraph > li {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
}
.ant-skeleton.ant-skeleton-active .ant-skeleton-avatar {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
}
.ant-slider {
  color: rgba(0, 0, 0, 0.65);
}
.ant-slider-rail {
  border-radius: 2px;
  background-color: #f5f5f5;
}
.ant-slider-track {
  border-radius: 4px;
  background-color: color(~`colorPalette("@{primary-color}", 3)`);
}
.ant-slider-handle {
  border-radius: 50%;
  border: solid 2px color(~`colorPalette("@{primary-color}", 3)`);
  background-color: #fff;
}
.ant-slider-handle:focus {
  border-color: #46a6ff;
  box-shadow: 0 0 0 5px #8cc8ff;
}
.ant-slider-handle.ant-tooltip-open {
  border-color: @primary-color;
}
.ant-slider:hover .ant-slider-rail {
  background-color: #e1e1e1;
}
.ant-slider:hover .ant-slider-track {
  background-color: color(~`colorPalette("@{primary-color}", 4)`);
}
.ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
  border-color: color(~`colorPalette("@{primary-color}", 4)`);
}
.ant-slider-mark-text {
  color: rgba(0, 0, 0, 0.45);
}
.ant-slider-mark-text-active {
  color: rgba(0, 0, 0, 0.65);
}
.ant-slider-step {
  background: transparent;
}
.ant-slider-dot {
  border: 2px solid #e8e8e8;
  background-color: #fff;
  border-radius: 50%;
}
.ant-slider-dot-active {
  border-color: #8cc8ff;
}
.ant-slider-disabled .ant-slider-track {
  background-color: rgba(0, 0, 0, 0.25) !important;
}
.ant-slider-disabled .ant-slider-handle,
.ant-slider-disabled .ant-slider-dot {
  border-color: rgba(0, 0, 0, 0.25) !important;
  background-color: #fff;
  box-shadow: none;
}
.ant-spin {
  color: rgba(0, 0, 0, 0.65);
  color: @primary-color;
}
.ant-spin-blur:after {
  background: #fff;
}
.ant-spin-tip {
  color: rgba(0, 0, 0, 0.45);
}
.ant-spin-dot i {
  border-radius: 100%;
  background-color: @primary-color;
}
.ant-steps {
  color: rgba(0, 0, 0, 0.65);
}
.ant-steps-item-icon {
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 32px;
}
.ant-steps-item-icon > .ant-steps-icon {
  color: @primary-color;
}
.ant-steps-item-tail:after {
  background: #e8e8e8;
  border-radius: 1px;
}
.ant-steps-item-title {
  color: rgba(0, 0, 0, 0.65);
}
.ant-steps-item-title:after {
  background: #e8e8e8;
}
.ant-steps-item-description {
  color: rgba(0, 0, 0, 0.45);
}
.ant-steps-item-wait .ant-steps-item-icon {
  border-color: rgba(0, 0, 0, 0.25);
  background-color: #fff;
}
.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: rgba(0, 0, 0, 0.25);
}
.ant-steps-item-wait > .ant-steps-item-content > .ant-steps-item-title {
  color: rgba(0, 0, 0, 0.45);
}
.ant-steps-item-wait > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: #e8e8e8;
}
.ant-steps-item-wait > .ant-steps-item-content > .ant-steps-item-description {
  color: rgba(0, 0, 0, 0.45);
}
.ant-steps-item-wait > .ant-steps-item-tail:after {
  background-color: #e8e8e8;
}
.ant-steps-item-process .ant-steps-item-icon {
  border-color: @primary-color;
  background-color: #fff;
}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
  color: @primary-color;
}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: @primary-color;
}
.ant-steps-item-process > .ant-steps-item-content > .ant-steps-item-title {
  color: rgba(0, 0, 0, 0.85);
}
.ant-steps-item-process > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: #e8e8e8;
}
.ant-steps-item-process > .ant-steps-item-content > .ant-steps-item-description {
  color: rgba(0, 0, 0, 0.65);
}
.ant-steps-item-process > .ant-steps-item-tail:after {
  background-color: #e8e8e8;
}
.ant-steps-item-process .ant-steps-item-icon {
  background: @primary-color;
}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
  color: #fff;
}
.ant-steps-item-finish .ant-steps-item-icon {
  border-color: @primary-color;
  background-color: #fff;
}
.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  color: @primary-color;
}
.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: @primary-color;
}
.ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-title {
  color: rgba(0, 0, 0, 0.65);
}
.ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: @primary-color;
}
.ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-description {
  color: rgba(0, 0, 0, 0.45);
}
.ant-steps-item-finish > .ant-steps-item-tail:after {
  background-color: @primary-color;
}
.ant-steps-item-error .ant-steps-item-icon {
  border-color: #f5222d;
  background-color: #fff;
}
.ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon {
  color: #f5222d;
}
.ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot {
  background: #f5222d;
}
.ant-steps-item-error > .ant-steps-item-content > .ant-steps-item-title {
  color: #f5222d;
}
.ant-steps-item-error > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: #e8e8e8;
}
.ant-steps-item-error > .ant-steps-item-content > .ant-steps-item-description {
  color: #f5222d;
}
.ant-steps-item-error > .ant-steps-item-tail:after {
  background-color: #e8e8e8;
}
.ant-steps-item.ant-steps-next-error .ant-steps-item-title:after {
  background: #f5222d;
}
.ant-steps-item-custom .ant-steps-item-icon {
  background: none;
  border: 0;
}
.ant-steps-item-custom.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
  color: @primary-color;
}
.ant-steps-small .ant-steps-item-icon {
  border-radius: 24px;
}
.ant-steps-small .ant-steps-item-description {
  color: rgba(0, 0, 0, 0.45);
}
.ant-steps-small .ant-steps-item-custom .ant-steps-item-icon {
  border-radius: 0;
  border: 0;
  background: none;
}
.ant-steps-dot .ant-steps-item-icon {
  border: 0;
  background: transparent;
}
.ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot {
  border-radius: 100px;
}
.ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot:after {
  background: rgba(0, 0, 0, 0.001);
}
.ant-switch {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 100px;
  border: 1px solid transparent;
  background-color: rgba(0, 0, 0, 0.25);
}
.ant-switch-inner {
  color: #fff;
}
.ant-switch-loading-icon,
.ant-switch:after {
  border-radius: 18px;
  background-color: #fff;
}
.ant-switch:after {
  box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);
}
.ant-switch-loading-icon {
  background: transparent;
}
.ant-switch-loading .ant-switch-loading-icon {
  color: rgba(0, 0, 0, 0.65);
}
.ant-switch-checked.ant-switch-loading .ant-switch-loading-icon {
  color: @primary-color;
}
.ant-switch:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.ant-switch:focus:hover {
  box-shadow: none;
}
.ant-switch-checked {
  background-color: @primary-color;
}
.ant-table {
  color: rgba(0, 0, 0, 0.65);
}
.ant-table table {
  border-collapse: collapse;
  border-radius: 4px 4px 0 0;
}
.ant-table-thead > tr > th {
  background: #fafafa;
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #e8e8e8;
}
.ant-table-thead > tr > th .anticon-filter,
.ant-table-thead > tr > th .ant-table-filter-icon {
  color: #bfbfbf;
}
.ant-table-thead > tr > th .ant-table-filter-selected.anticon-filter {
  color: @primary-color;
}
.ant-table-thead > tr > th .ant-table-column-sorter {
  color: #bfbfbf;
}
.ant-table-thead > tr > th .ant-table-column-sorter-up.on,
.ant-table-thead > tr > th .ant-table-column-sorter-down.on {
  color: @primary-color;
}
.ant-table-thead > tr > th.ant-table-column-has-actions:hover {
  background: #f5f5f5;
}
.ant-table-thead > tr > th.ant-table-column-has-actions:hover .anticon-filter,
.ant-table-thead > tr > th.ant-table-column-has-actions:hover .ant-table-filter-icon {
  background: #f5f5f5;
}
.ant-table-thead > tr > th.ant-table-column-has-actions:hover .anticon-filter:hover,
.ant-table-thead > tr > th.ant-table-column-has-actions:hover .ant-table-filter-icon:hover {
  color: rgba(0, 0, 0, 0.45);
  background: #ebebeb;
}
.ant-table-thead > tr > th.ant-table-column-has-actions:hover .anticon-filter:active,
.ant-table-thead > tr > th.ant-table-column-has-actions:hover .ant-table-filter-icon:active {
  color: rgba(0, 0, 0, 0.65);
}
.ant-table-thead > tr > th.ant-table-column-has-actions .anticon-filter.ant-table-filter-open,
.ant-table-thead > tr > th.ant-table-column-has-actions .ant-table-filter-icon.ant-table-filter-open {
  color: rgba(0, 0, 0, 0.45);
  background: #ebebeb;
}
.ant-table-thead > tr > th.ant-table-column-has-actions:active .ant-table-column-sorter-up:not(.on),
.ant-table-thead > tr > th.ant-table-column-has-actions:active .ant-table-column-sorter-down:not(.on) {
  color: rgba(0, 0, 0, 0.45);
}
.ant-table-thead > tr > th .ant-table-column-sorters:before {
  background: transparent;
}
.ant-table-thead > tr > th .ant-table-column-sorters:hover:before {
  background: rgba(0, 0, 0, 0.04);
}
.ant-table-thead > tr:first-child > th:first-child {
  border-top-left-radius: 4px;
}
.ant-table-thead > tr:first-child > th:last-child {
  border-top-right-radius: 4px;
}
.ant-table-thead > tr:not(:last-child) > th[colspan] {
  border-bottom: 0;
}
.ant-table-tbody > tr > td {
  border-bottom: 1px solid #e8e8e8;
}
.ant-table-thead > tr.ant-table-row-hover > td,
.ant-table-tbody > tr.ant-table-row-hover > td,
.ant-table-thead > tr:hover > td,
.ant-table-tbody > tr:hover > td {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-table-thead > tr:hover {
  background: none;
}
.ant-table-footer {
  background: #fafafa;
  border-radius: 0 0 4px 4px;
  border-top: 1px solid #e8e8e8;
}
.ant-table-footer:before {
  background: #fafafa;
}
.ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid #e8e8e8;
}
.ant-table-title {
  border-radius: 4px 4px 0 0;
}
.ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #e8e8e8;
}
.ant-table-title + .ant-table-content {
  border-radius: 4px 4px 0 0;
}
.ant-table-bordered .ant-table-title + .ant-table-content,
.ant-table-bordered .ant-table-title + .ant-table-content table,
.ant-table-bordered .ant-table-title + .ant-table-content .ant-table-thead > tr:first-child > th {
  border-radius: 0;
}
.ant-table-without-column-header .ant-table-title + .ant-table-content,
.ant-table-without-column-header table {
  border-radius: 0;
}
.ant-table-tbody > tr.ant-table-row-selected td {
  background: #fafafa;
}
.ant-table-thead > tr > th.ant-table-column-sort {
  background: #f5f5f5;
}
.ant-table-tbody > tr > td.ant-table-column-sort {
  background: rgba(0, 0, 0, 0.01);
}
.ant-table-header {
  background: #fafafa;
}
.ant-table-header table {
  border-radius: 4px 4px 0 0;
}
.ant-table-loading .ant-table-body {
  background: #fff;
}
.ant-table-bordered .ant-table-header > table,
.ant-table-bordered .ant-table-body > table,
.ant-table-bordered .ant-table-fixed-left table,
.ant-table-bordered .ant-table-fixed-right table {
  border: 1px solid #e8e8e8;
  border-right: 0;
  border-bottom: 0;
}
.ant-table-bordered.ant-table-empty .ant-table-placeholder {
  border-left: 1px solid #e8e8e8;
  border-right: 1px solid #e8e8e8;
}
.ant-table-bordered.ant-table-fixed-header .ant-table-header > table {
  border-bottom: 0;
}
.ant-table-bordered.ant-table-fixed-header .ant-table-body > table {
  border-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.ant-table-bordered.ant-table-fixed-header .ant-table-body-inner > table {
  border-top: 0;
}
.ant-table-bordered.ant-table-fixed-header .ant-table-placeholder {
  border: 0;
}
.ant-table-bordered .ant-table-thead > tr:not(:last-child) > th {
  border-bottom: 1px solid #e8e8e8;
}
.ant-table-bordered .ant-table-thead > tr > th,
.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #e8e8e8;
}
.ant-table-placeholder {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  color: rgba(0, 0, 0, 0.45);
}
.ant-table-filter-dropdown {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-table-filter-dropdown .ant-dropdown-menu {
  border: 0;
  box-shadow: none;
  border-radius: 4px 4px 0 0;
}
.ant-table-filter-dropdown .ant-dropdown-menu-sub {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-table-filter-dropdown .ant-dropdown-menu .ant-dropdown-submenu-contain-selected .ant-dropdown-menu-submenu-title:after {
  color: @primary-color;
}
.ant-table-filter-dropdown > .ant-dropdown-menu > .ant-dropdown-menu-item:last-child,
.ant-table-filter-dropdown > .ant-dropdown-menu > .ant-dropdown-menu-submenu:last-child .ant-dropdown-menu-submenu-title {
  border-radius: 0;
}
.ant-table-filter-dropdown-btns {
  border-top: 1px solid #e8e8e8;
}
.ant-table-filter-dropdown-link {
  color: @primary-color;
}
.ant-table-filter-dropdown-link:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-table-filter-dropdown-link:active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-table-selection .anticon-down {
  color: #bfbfbf;
}
.ant-table-selection-menu {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-table-selection-menu .ant-action-down {
  color: #bfbfbf;
}
.ant-table-selection-down:hover .anticon-down {
  color: #666;
}
.ant-table-row-expand-icon {
  border: 1px solid #e8e8e8;
  background: #fff;
}
tr.ant-table-expanded-row,
tr.ant-table-expanded-row:hover {
  background: #fbfbfb;
}
.ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
  background: #fff;
}
.ant-table-fixed-left,
.ant-table-fixed-right {
  border-radius: 0;
}
.ant-table-fixed-left table,
.ant-table-fixed-right table {
  background: #fff;
}
.ant-table-fixed-header .ant-table-fixed-left .ant-table-body-outer .ant-table-fixed,
.ant-table-fixed-header .ant-table-fixed-right .ant-table-body-outer .ant-table-fixed {
  border-radius: 0;
}
.ant-table-fixed-left {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
}
.ant-table-fixed-left,
.ant-table-fixed-left table {
  border-radius: 4px 0 0 0;
}
.ant-table-fixed-left .ant-table-thead > tr > th:last-child {
  border-top-right-radius: 0;
}
.ant-table-fixed-right {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.15);
}
.ant-table-fixed-right,
.ant-table-fixed-right table {
  border-radius: 0 4px 0 0;
}
.ant-table-fixed-right .ant-table-expanded-row {
  color: transparent;
}
.ant-table-fixed-right .ant-table-thead > tr > th:first-child {
  border-top-left-radius: 0;
}
.ant-table.ant-table-scroll-position-left .ant-table-fixed-left {
  box-shadow: none;
}
.ant-table.ant-table-scroll-position-right .ant-table-fixed-right {
  box-shadow: none;
}
.ant-table-small {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}
.ant-table-small > .ant-table-title {
  border-bottom: 1px solid #e8e8e8;
}
.ant-table-small > .ant-table-content > .ant-table-header > table,
.ant-table-small > .ant-table-content > .ant-table-body > table,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table {
  border: 0;
}
.ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}
.ant-table-small > .ant-table-content .ant-table-header {
  background: #fff;
}
.ant-table-small > .ant-table-content .ant-table-placeholder,
.ant-table-small > .ant-table-content .ant-table-row:last-child td {
  border-bottom: 0;
}
.ant-table-small.ant-table-bordered {
  border-right: 0;
}
.ant-table-small.ant-table-bordered .ant-table-title {
  border: 0;
  border-bottom: 1px solid #e8e8e8;
  border-right: 1px solid #e8e8e8;
}
.ant-table-small.ant-table-bordered .ant-table-content {
  border-right: 1px solid #e8e8e8;
}
.ant-table-small.ant-table-bordered .ant-table-footer {
  border: 0;
  border-top: 1px solid #e8e8e8;
  border-right: 1px solid #e8e8e8;
}
.ant-table-small.ant-table-bordered .ant-table-placeholder {
  border-left: 0;
  border-bottom: 0;
}
.ant-table-small.ant-table-bordered .ant-table-thead > tr > th:last-child,
.ant-table-small.ant-table-bordered .ant-table-tbody > tr > td:last-child {
  border-right: none;
}
.ant-table-small.ant-table-bordered .ant-table-fixed-left .ant-table-thead > tr > th:last-child,
.ant-table-small.ant-table-bordered .ant-table-fixed-left .ant-table-tbody > tr > td:last-child {
  border-right: 1px solid #e8e8e8;
}
.ant-table-small.ant-table-bordered .ant-table-fixed-right {
  border-right: 1px solid #e8e8e8;
}
.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
  border: 1px solid #e8e8e8;
  border-bottom: 0;
  border-radius: 4px 4px 0 0;
  background: #fafafa;
}
.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active {
  background: #fff;
  border-color: @primary-color !important;
  color: @primary-color;
}
.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab .ant-tabs-close-x {
  color: rgba(0, 0, 0, 0.45);
}
.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab .ant-tabs-close-x:hover {
  color: rgba(0, 0, 0, 0.85);
}
.ant-tabs-extra-content .ant-tabs-new-tab {
  border-radius: 2px;
  border: 1px solid #e8e8e8;
  color: rgba(0, 0, 0, 0.65);
}
.ant-tabs-extra-content .ant-tabs-new-tab:hover {
  color: @primary-color;
  border-color: @primary-color;
}
.ant-tabs-vertical.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
  border-bottom: 1px solid #e8e8e8;
}
.ant-tabs-vertical.ant-tabs-card.ant-tabs-left > .ant-tabs-bar .ant-tabs-tab {
  border-right: 0;
  border-radius: 4px 0 0 4px;
}
.ant-tabs-vertical.ant-tabs-card.ant-tabs-right > .ant-tabs-bar .ant-tabs-tab {
  border-left: 0;
  border-radius: 0 4px 4px 0;
}
.ant-tabs.ant-tabs-card.ant-tabs-bottom > .ant-tabs-bar .ant-tabs-tab {
  border-bottom: 1px solid #e8e8e8;
  border-top: 0;
  border-radius: 0 0 4px 4px;
}
.ant-tabs.ant-tabs-card.ant-tabs-bottom > .ant-tabs-bar .ant-tabs-tab-active {
  color: @primary-color;
}
.ant-tabs {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tabs-ink-bar {
  background-color: @primary-color;
}
.ant-tabs-bar {
  border-bottom: 1px solid #e8e8e8;
}
.ant-tabs-bottom .ant-tabs-bar {
  border-bottom: none;
  border-top: 1px solid #e8e8e8;
}
.ant-tabs-tab-prev,
.ant-tabs-tab-next {
  border: 0;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.45);
}
.ant-tabs-tab-prev:hover,
.ant-tabs-tab-next:hover {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tabs-tab-btn-disabled,
.ant-tabs-tab-btn-disabled:hover {
  color: rgba(0, 0, 0, 0.25);
}
.ant-tabs-nav .ant-tabs-tab-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-tabs-nav .ant-tabs-tab:hover {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-tabs-nav .ant-tabs-tab:active {
  color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-tabs-nav .ant-tabs-tab-active {
  color: @primary-color;
}
.ant-tabs-vertical > .ant-tabs-bar {
  border-bottom: 0;
}
.ant-tabs-vertical.ant-tabs-left > .ant-tabs-bar {
  border-right: 1px solid #e8e8e8;
}
.ant-tabs-vertical.ant-tabs-left > .ant-tabs-content {
  border-left: 1px solid #e8e8e8;
}
.ant-tabs-vertical.ant-tabs-right > .ant-tabs-bar {
  border-left: 1px solid #e8e8e8;
}
.ant-tabs-vertical.ant-tabs-right > .ant-tabs-content {
  border-right: 1px solid #e8e8e8;
}
.ant-tag {
  color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background: #fafafa;
}
.ant-tag,
.ant-tag a,
.ant-tag a:hover {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tag .anticon-close {
  color: rgba(0, 0, 0, 0.45);
}
.ant-tag .anticon-close:hover {
  color: rgba(0, 0, 0, 0.85);
}
.ant-tag-has-color {
  border-color: transparent;
}
.ant-tag-has-color,
.ant-tag-has-color a,
.ant-tag-has-color a:hover,
.ant-tag-has-color .anticon-close,
.ant-tag-has-color .anticon-close:hover {
  color: #fff;
}
.ant-tag-checkable {
  background-color: transparent;
  border-color: transparent;
}
.ant-tag-checkable:not(.ant-tag-checkable-checked):hover {
  color: @primary-color;
}
.ant-tag-checkable:active,
.ant-tag-checkable-checked {
  color: #fff;
}
.ant-tag-checkable-checked {
  background-color: @primary-color;
}
.ant-tag-checkable:active {
  background-color: color(~`colorPalette("@{primary-color}", 7)`);
}
.ant-tag-pink {
  color: #eb2f96;
  background: #fff0f6;
  border-color: #ffadd2;
}
.ant-tag-pink-inverse {
  background: #eb2f96;
  border-color: #eb2f96;
  color: #fff;
}
.ant-tag-magenta {
  color: #eb2f96;
  background: #fff0f6;
  border-color: #ffadd2;
}
.ant-tag-magenta-inverse {
  background: #eb2f96;
  border-color: #eb2f96;
  color: #fff;
}
.ant-tag-red {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}
.ant-tag-red-inverse {
  background: #f5222d;
  border-color: #f5222d;
  color: #fff;
}
.ant-tag-volcano {
  color: #fa541c;
  background: #fff2e8;
  border-color: #ffbb96;
}
.ant-tag-volcano-inverse {
  background: #fa541c;
  border-color: #fa541c;
  color: #fff;
}
.ant-tag-orange {
  color: #fa8c16;
  background: #fff7e6;
  border-color: #ffd591;
}
.ant-tag-orange-inverse {
  background: #fa8c16;
  border-color: #fa8c16;
  color: #fff;
}
.ant-tag-yellow {
  color: #fadb14;
  background: #feffe6;
  border-color: #fffb8f;
}
.ant-tag-yellow-inverse {
  background: #fadb14;
  border-color: #fadb14;
  color: #fff;
}
.ant-tag-gold {
  color: #faad14;
  background: #fffbe6;
  border-color: #ffe58f;
}
.ant-tag-gold-inverse {
  background: #faad14;
  border-color: #faad14;
  color: #fff;
}
.ant-tag-cyan {
  color: #13c2c2;
  background: #e6fffb;
  border-color: #87e8de;
}
.ant-tag-cyan-inverse {
  background: #13c2c2;
  border-color: #13c2c2;
  color: #fff;
}
.ant-tag-lime {
  color: #a0d911;
  background: #fcffe6;
  border-color: #eaff8f;
}
.ant-tag-lime-inverse {
  background: #a0d911;
  border-color: #a0d911;
  color: #fff;
}
.ant-tag-green {
  color: #52c41a;
  background: #f6ffed;
  border-color: #b7eb8f;
}
.ant-tag-green-inverse {
  background: #52c41a;
  border-color: #52c41a;
  color: #fff;
}
.ant-tag-blue {
  color: @primary-color;
  background: color(~`colorPalette("@{primary-color}", 1)`);
  border-color: color(~`colorPalette("@{primary-color}", 3)`);
}
.ant-tag-blue-inverse {
  background: @primary-color;
  border-color: @primary-color;
  color: #fff;
}
.ant-tag-geekblue {
  color: #2f54eb;
  background: #f0f5ff;
  border-color: #adc6ff;
}
.ant-tag-geekblue-inverse {
  background: #2f54eb;
  border-color: #2f54eb;
  color: #fff;
}
.ant-tag-purple {
  color: #722ed1;
  background: #f9f0ff;
  border-color: #d3adf7;
}
.ant-tag-purple-inverse {
  background: #722ed1;
  border-color: #722ed1;
  color: #fff;
}
.ant-time-picker-panel {
  color: rgba(0, 0, 0, 0.65);
}
.ant-time-picker-panel-inner {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-clip: padding-box;
}
.ant-time-picker-panel-input {
  border: 0;
}
.ant-time-picker-panel-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-time-picker-panel-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-time-picker-panel-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-time-picker-panel-input-wrap {
  border-bottom: 1px solid #e8e8e8;
}
.ant-time-picker-panel-input-invalid {
  border-color: red;
}
.ant-time-picker-panel-clear-btn-icon svg {
  color: rgba(0, 0, 0, 0.25);
}
.ant-time-picker-panel-clear-btn-icon svg:hover {
  color: rgba(0, 0, 0, 0.45);
}
.ant-time-picker-panel-select {
  border-left: 1px solid #e8e8e8;
}
.ant-time-picker-panel-select:first-child {
  border-left: 0;
}
.ant-time-picker-panel-select:last-child {
  border-right: 0;
}
.ant-time-picker-panel-select li:hover {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
li.ant-time-picker-panel-select-option-selected {
  background: #f5f5f5;
}
li.ant-time-picker-panel-select-option-selected:hover {
  background: #f5f5f5;
}
li.ant-time-picker-panel-select-option-disabled {
  color: rgba(0, 0, 0, 0.25);
}
li.ant-time-picker-panel-select-option-disabled:hover {
  background: transparent;
}
.ant-time-picker-panel-addon {
  border-top: 1px solid #e8e8e8;
}
.ant-time-picker {
  color: rgba(0, 0, 0, 0.65);
}
.ant-time-picker-input {
  color: rgba(0, 0, 0, 0.65);
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-time-picker-input::-moz-placeholder {
  color: #bfbfbf;
}
.ant-time-picker-input:-ms-input-placeholder {
  color: #bfbfbf;
}
.ant-time-picker-input::-webkit-input-placeholder {
  color: #bfbfbf;
}
.ant-time-picker-input:hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  border-right-width: 1px !important;
}
.ant-time-picker-input:focus {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-right-width: 1px !important;
}
.ant-time-picker-input-disabled {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-time-picker-input-disabled:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-time-picker-input[disabled] {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.25);
}
.ant-time-picker-input[disabled]:hover {
  border-color: #e6d8d8;
  border-right-width: 1px !important;
}
.ant-time-picker-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-time-picker-icon .ant-time-picker-clock-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-timeline {
  color: rgba(0, 0, 0, 0.65);
}
.ant-timeline-item-tail {
  border-left: 2px solid #e8e8e8;
}
.ant-timeline-item-head {
  background-color: #fff;
  border-radius: 100px;
  border: 2px solid transparent;
}
.ant-timeline-item-head-blue {
  border-color: @primary-color;
  color: @primary-color;
}
.ant-timeline-item-head-red {
  border-color: #f5222d;
  color: #f5222d;
}
.ant-timeline-item-head-green {
  border-color: #52c41a;
  color: #52c41a;
}
.ant-timeline-item-head-custom {
  border: 0;
  border-radius: 0;
}
.ant-timeline.ant-timeline-pending .ant-timeline-item-last .ant-timeline-item-tail {
  border-left: 2px dotted #e8e8e8;
}
.ant-timeline.ant-timeline-reverse .ant-timeline-item-pending .ant-timeline-item-tail {
  border-left: 2px dotted #e8e8e8;
}
.ant-tooltip {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tooltip-inner {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
.ant-tooltip-arrow {
  border-color: transparent;
  border-style: solid;
}
.ant-tooltip-placement-top .ant-tooltip-arrow,
.ant-tooltip-placement-topLeft .ant-tooltip-arrow,
.ant-tooltip-placement-topRight .ant-tooltip-arrow {
  border-width: 5px 5px 0;
  border-top-color: rgba(0, 0, 0, 0.75);
}
.ant-tooltip-placement-right .ant-tooltip-arrow,
.ant-tooltip-placement-rightTop .ant-tooltip-arrow,
.ant-tooltip-placement-rightBottom .ant-tooltip-arrow {
  border-width: 5px 5px 5px 0;
  border-right-color: rgba(0, 0, 0, 0.75);
}
.ant-tooltip-placement-left .ant-tooltip-arrow,
.ant-tooltip-placement-leftTop .ant-tooltip-arrow,
.ant-tooltip-placement-leftBottom .ant-tooltip-arrow {
  border-width: 5px 0 5px 5px;
  border-left-color: rgba(0, 0, 0, 0.75);
}
.ant-tooltip-placement-bottom .ant-tooltip-arrow,
.ant-tooltip-placement-bottomLeft .ant-tooltip-arrow,
.ant-tooltip-placement-bottomRight .ant-tooltip-arrow {
  border-width: 0 5px 5px;
  border-bottom-color: rgba(0, 0, 0, 0.75);
}
.ant-transfer {
  color: rgba(0, 0, 0, 0.65);
}
.ant-transfer-disabled .ant-transfer-list {
  background: #f5f5f5;
}
.ant-transfer-list {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-transfer-list-search-action {
  color: rgba(0, 0, 0, 0.25);
}
.ant-transfer-list-search-action .anticon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-transfer-list-search-action .anticon:hover {
  color: rgba(0, 0, 0, 0.45);
}
.ant-transfer-list-header {
  border-radius: 4px 4px 0 0;
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  border-bottom: 1px solid #e8e8e8;
}
.ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-transfer-list-content-item-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-transfer-list-body-not-found {
  color: rgba(0, 0, 0, 0.25);
}
.ant-transfer-list-footer {
  border-top: 1px solid #e8e8e8;
  border-radius: 0 0 4px 4px;
}
.ant-select-tree-checkbox {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-inner,
.ant-select-tree-checkbox:hover .ant-select-tree-checkbox-inner,
.ant-select-tree-checkbox-input:focus + .ant-select-tree-checkbox-inner {
  border-color: @primary-color;
}
.ant-select-tree-checkbox-checked:after {
  border-radius: 2px;
  border: 1px solid @primary-color;
}
.ant-select-tree-checkbox-inner {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fff;
}
.ant-select-tree-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
.ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner:after {
  border: 0;
  background-color: @primary-color;
}
.ant-select-tree-checkbox-indeterminate.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
  background-color: @primary-color;
  border-color: @primary-color;
}
.ant-select-tree-checkbox-disabled.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner {
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5;
}
.ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner:after {
  border-color: #f5f5f5;
}
.ant-select-tree-checkbox-disabled + span {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-tree-checkbox-wrapper {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-tree-checkbox-group {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-tree {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-tree li .ant-select-tree-node-content-wrapper {
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-tree li .ant-select-tree-node-content-wrapper:hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
  background-color: color(~`colorPalette("@{primary-color}", 2)`);
}
.ant-select-tree li span.ant-select-tree-switcher,
.ant-select-tree li span.ant-select-tree-iconEle {
  border: 0 none;
}
.ant-select-tree li span.ant-select-icon_loading .ant-select-switcher-loading-icon {
  color: @primary-color;
}
.ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_open .ant-select-switcher-loading-icon,
.ant-select-tree li span.ant-select-tree-switcher.ant-select-tree-switcher_close .ant-select-switcher-loading-icon {
  color: @primary-color;
}
li.ant-select-tree-treenode-disabled > span:not(.ant-select-tree-switcher),
li.ant-select-tree-treenode-disabled > .ant-select-tree-node-content-wrapper,
li.ant-select-tree-treenode-disabled > .ant-select-tree-node-content-wrapper span {
  color: rgba(0, 0, 0, 0.25);
}
li.ant-select-tree-treenode-disabled > .ant-select-tree-node-content-wrapper:hover {
  background: transparent;
}
.ant-select-tree-dropdown {
  color: rgba(0, 0, 0, 0.65);
}
.ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
.ant-select-tree-dropdown .ant-select-not-found {
  color: rgba(0, 0, 0, 0.25);
}
.ant-tree.ant-tree-directory > li span.ant-tree-node-content-wrapper,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li span.ant-tree-node-content-wrapper {
  border-radius: 0;
}
.ant-tree.ant-tree-directory > li span.ant-tree-node-content-wrapper:hover,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li span.ant-tree-node-content-wrapper:hover {
  background: transparent;
}
.ant-tree.ant-tree-directory > li span.ant-tree-node-content-wrapper:hover:before,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li span.ant-tree-node-content-wrapper:hover:before {
  background: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-tree.ant-tree-directory > li span.ant-tree-node-content-wrapper.ant-tree-node-selected,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li span.ant-tree-node-content-wrapper.ant-tree-node-selected {
  color: #fff;
  background: transparent;
}
.ant-tree.ant-tree-directory > li.ant-tree-treenode-selected > span.ant-tree-switcher,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li.ant-tree-treenode-selected > span.ant-tree-switcher {
  color: #fff;
}
.ant-tree.ant-tree-directory > li.ant-tree-treenode-selected > span.ant-tree-checkbox .ant-tree-checkbox-inner,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li.ant-tree-treenode-selected > span.ant-tree-checkbox .ant-tree-checkbox-inner {
  border-color: @primary-color;
}
.ant-tree.ant-tree-directory > li.ant-tree-treenode-selected > span.ant-tree-checkbox.ant-tree-checkbox-checked:after,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li.ant-tree-treenode-selected > span.ant-tree-checkbox.ant-tree-checkbox-checked:after {
  border-color: #fff;
}
.ant-tree.ant-tree-directory > li.ant-tree-treenode-selected > span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li.ant-tree-treenode-selected > span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background: #fff;
}
.ant-tree.ant-tree-directory > li.ant-tree-treenode-selected > span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner:after,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li.ant-tree-treenode-selected > span.ant-tree-checkbox.ant-tree-checkbox-checked .ant-tree-checkbox-inner:after {
  border-color: @primary-color;
}
.ant-tree.ant-tree-directory > li.ant-tree-treenode-selected > span.ant-tree-node-content-wrapper:before,
.ant-tree.ant-tree-directory .ant-tree-child-tree > li.ant-tree-treenode-selected > span.ant-tree-node-content-wrapper:before {
  background: @primary-color;
}
.ant-tree-checkbox {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
.ant-tree-checkbox:hover .ant-tree-checkbox-inner,
.ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {
  border-color: @primary-color;
}
.ant-tree-checkbox-checked:after {
  border-radius: 2px;
  border: 1px solid @primary-color;
}
.ant-tree-checkbox-inner {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #fff;
}
.ant-tree-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner:after {
  border: 0;
  background-color: @primary-color;
}
.ant-tree-checkbox-indeterminate.ant-tree-checkbox-disabled .ant-tree-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ant-tree-checkbox-checked .ant-tree-checkbox-inner:after {
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}
.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background-color: @primary-color;
  border-color: @primary-color;
}
.ant-tree-checkbox-disabled.ant-tree-checkbox-checked .ant-tree-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
  border-color: #d9d9d9 !important;
  background-color: #f5f5f5;
}
.ant-tree-checkbox-disabled .ant-tree-checkbox-inner:after {
  border-color: #f5f5f5;
}
.ant-tree-checkbox-disabled + span {
  color: rgba(0, 0, 0, 0.25);
}
.ant-tree-checkbox-wrapper {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tree-checkbox-group {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tree {
  color: rgba(0, 0, 0, 0.65);
}
.ant-tree li span[draggable],
.ant-tree li span[draggable="true"] {
  border-top: 2px transparent solid;
  border-bottom: 2px transparent solid;
}
.ant-tree li.drag-over > span[draggable] {
  background-color: @primary-color;
  color: white;
}
.ant-tree li.drag-over-gap-top > span[draggable] {
  border-top-color: @primary-color;
}
.ant-tree li.drag-over-gap-bottom > span[draggable] {
  border-bottom-color: @primary-color;
}
.ant-tree li.filter-node > span {
  color: #f5222d !important;
}
.ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-loading-icon,
.ant-tree li.ant-tree-treenode-loading span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-loading-icon {
  color: @primary-color;
}
.ant-tree li .ant-tree-node-content-wrapper {
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.65);
}
.ant-tree li .ant-tree-node-content-wrapper:hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: color(~`colorPalette("@{primary-color}", 2)`);
}
.ant-tree li span.ant-tree-switcher,
.ant-tree li span.ant-tree-iconEle {
  border: 0 none;
}
li.ant-tree-treenode-disabled > span:not(.ant-tree-switcher),
li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper,
li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
  color: rgba(0, 0, 0, 0.25);
}
li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper:hover {
  background: transparent;
}
.ant-tree.ant-tree-show-line li span.ant-tree-switcher {
  background: #fff;
  color: rgba(0, 0, 0, 0.45);
}
.ant-tree.ant-tree-show-line li:not(:last-child):before {
  border-left: 1px solid #d9d9d9;
}
.ant-upload {
  color: rgba(0, 0, 0, 0.65);
}
.ant-upload.ant-upload-select-picture-card {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}
.ant-upload.ant-upload-select-picture-card:hover {
  border-color: @primary-color;
}
.ant-upload.ant-upload-drag {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
}
.ant-upload.ant-upload-drag.ant-upload-drag-hover:not(.ant-upload-disabled) {
  border: 2px dashed color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-upload.ant-upload-drag:not(.ant-upload-disabled):hover {
  border-color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {
  color: color(~`colorPalette("@{primary-color}", 5)`);
}
.ant-upload.ant-upload-drag p.ant-upload-text {
  color: rgba(0, 0, 0, 0.85);
}
.ant-upload.ant-upload-drag p.ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload.ant-upload-drag .anticon-plus {
  color: rgba(0, 0, 0, 0.25);
}
.ant-upload.ant-upload-drag .anticon-plus:hover {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload.ant-upload-drag:hover .anticon-plus {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload-list {
  color: rgba(0, 0, 0, 0.65);
}
.ant-upload-list-item-info .anticon-loading,
.ant-upload-list-item-info .anticon-paper-clip {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload-list-item .anticon-close {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload-list-item .anticon-close:hover {
  color: rgba(0, 0, 0, 0.65);
}
.ant-upload-list-item:hover .ant-upload-list-item-info {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}
.ant-upload-list-item-error,
.ant-upload-list-item-error .anticon-paper-clip,
.ant-upload-list-item-error .ant-upload-list-item-name {
  color: #f5222d;
}
.ant-upload-list-item-error .anticon-close {
  color: #f5222d !important;
}
.ant-upload-list-picture .ant-upload-list-item,
.ant-upload-list-picture-card .ant-upload-list-item {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
.ant-upload-list-picture .ant-upload-list-item:hover,
.ant-upload-list-picture-card .ant-upload-list-item:hover {
  background: transparent;
}
.ant-upload-list-picture .ant-upload-list-item-error,
.ant-upload-list-picture-card .ant-upload-list-item-error {
  border-color: #f5222d;
}
.ant-upload-list-picture .ant-upload-list-item:hover .ant-upload-list-item-info,
.ant-upload-list-picture-card .ant-upload-list-item:hover .ant-upload-list-item-info {
  background: transparent;
}
.ant-upload-list-picture .ant-upload-list-item-uploading,
.ant-upload-list-picture-card .ant-upload-list-item-uploading {
  border-style: dashed;
}
.ant-upload-list-picture .ant-upload-list-item-icon,
.ant-upload-list-picture-card .ant-upload-list-item-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-upload-list-picture .ant-upload-list-item-thumbnail.anticon:before,
.ant-upload-list-picture-card .ant-upload-list-item-thumbnail.anticon:before {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload-list-picture-card .ant-upload-list-item-info:before {
  background-color: rgba(0, 0, 0, 0.5);
}
.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye-o,
.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete {
  color: rgba(255, 255, 255, 0.85);
}
.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye-o:hover,
.ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete:hover {
  color: #fff;
}
.ant-upload-list-picture-card .ant-upload-list-item-uploading.ant-upload-list-item {
  background-color: #fafafa;
}
.ant-upload-list-picture-card .ant-upload-list-item-uploading-text {
  color: rgba(0, 0, 0, 0.45);
}
.ant-upload-list .ant-upload-success-icon {
  color: #52c41a;
}

.drawer .drawer-content {
  background: #001529;
}

.ant-list-item-meta .taobao {
  color: #ff4000;
  border-radius: 4px;
}
.ant-list-item-meta .dingding {
  background-color: #2eabff;
  color: #fff;
  border-radius: 4px;
}
.ant-list-item-meta .alipay {
  color: #2eabff;
  border-radius: 4px;
}
font.strong {
  color: #52c41a;
}
font.medium {
  color: #faad14;
}
font.weak {
  color: #f5222d;
}


// begin -------- JAreaLinkage 三级联动样式 --------------
.cascader-menu-list .cascader-menu-option.hover,
.cascader-menu-list .cascader-menu-option:hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}

.area-selectable-list .area-select-option.hover {
  background-color: color(~`colorPalette("@{primary-color}", 1)`);
}

.area-select:hover {
  border-color: @primary-color;
}

.area-select:active {
  box-shadow: 0 0 0 2px color(~`colorPalette("@{primary-color}", 1)`);
}
// end -------- JAreaLinkage 三级联动样式 --------------