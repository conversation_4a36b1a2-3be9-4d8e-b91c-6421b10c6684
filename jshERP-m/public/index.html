<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<head>
  <title></title>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta name="description" content="基于SpringBoot框架，立志为中小企业提供开源好用的ERP软件，目前专注进销存+财务功能。主要模块有零售管理、采购管理、销售管理、仓库管理、财务管理、报表查询、基础数据、系统管理等。" />
  <meta name="keywords" content="erp,erp系统,进销存,进销存系统" />
  <link rel="icon" href="<%= BASE_URL %>static/favicon.ico">
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #999;
      padding: 0.2em 0;
    }
    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }
    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 120px;
      height: 120px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      /* COLOR 1 */
      border-top-color: #999;
      -webkit-animation: spin 2s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -ms-animation: spin 2s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -moz-animation: spin 2s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -o-animation: spin 2s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      animation: spin 2s linear infinite;
      /* Chrome, Firefox 16+, IE 10+, Opera */
      z-index: 1001;
    }
    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      /* COLOR 2 */
      border-top-color: #999;
      -webkit-animation: spin 3s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -moz-animation: spin 3s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -o-animation: spin 3s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -ms-animation: spin 3s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      animation: spin 3s linear infinite;
      /* Chrome, Firefox 16+, IE 10+, Opera */
    }
    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #999;
      /* COLOR 3 */
      -moz-animation: spin 1.5s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -o-animation: spin 1.5s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -ms-animation: spin 1.5s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      -webkit-animation: spin 1.5s linear infinite;
      /* Chrome, Opera 15+, Safari 5+ */
      animation: spin 1.5s linear infinite;
      /* Chrome, Firefox 16+, IE 10+, Opera */
    }
    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg);
        /* IE 9 */
        transform: rotate(0deg);
        /* Firefox 16+, IE 10+, Opera */
      }
      100% {
        -webkit-transform: rotate(360deg);
        /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg);
        /* IE 9 */
        transform: rotate(360deg);
        /* Firefox 16+, IE 10+, Opera */
      }
    }
    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg);
        /* IE 9 */
        transform: rotate(0deg);
        /* Firefox 16+, IE 10+, Opera */
      }
      100% {
        -webkit-transform: rotate(360deg);
        /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg);
        /* IE 9 */
        transform: rotate(360deg);
        /* Firefox 16+, IE 10+, Opera */
      }
    }
    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #fff;
      /* Old browsers */
      z-index: 1000;
      -webkit-transform: translateX(0);
      /* Chrome, Opera 15+, Safari 3.1+ */
      -ms-transform: translateX(0);
      /* IE 9 */
      transform: translateX(0);
      /* Firefox 16+, IE 10+, Opera */
    }
    #loader-wrapper .loader-section.section-left {
      left: 0;
    }
    #loader-wrapper .loader-section.section-right {
      right: 0;
    }
    /* Loaded */
    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      /* Chrome, Opera 15+, Safari 3.1+ */
      -ms-transform: translateX(-100%);
      /* IE 9 */
      transform: translateX(-100%);
      /* Firefox 16+, IE 10+, Opera */
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }
    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      /* Chrome, Opera 15+, Safari 3.1+ */
      -ms-transform: translateX(100%);
      /* IE 9 */
      transform: translateX(100%);
      /* Firefox 16+, IE 10+, Opera */
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }
    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }
    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      /* Chrome, Opera 15+, Safari 3.1+ */
      -ms-transform: translateY(-100%);
      /* IE 9 */
      transform: translateY(-100%);
      /* Firefox 16+, IE 10+, Opera */
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }
    /* JavaScript Turned Off */
    .no-js #loader-wrapper {
      display: none;
    }
    .no-js h1 {
      color: #222222;
    }
    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #999;
      font-size: 14px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }
    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 14px;
      color: #999;
      opacity: 0.5;
    }
    /* 滚动条优化 start */
    ::-webkit-scrollbar{
      width:8px;
      height:8px;
    }
    ::-webkit-scrollbar-track{
      background: #f6f6f6;
      border-radius:2px;
    }
    ::-webkit-scrollbar-thumb{
      background: #cdcdcd;
      border-radius:2px;
    }
    ::-webkit-scrollbar-thumb:hover{
      background: #747474;
    }
    ::-webkit-scrollbar-corner {
      background: #f6f6f6;
    }
    /* 滚动条优化 end */
  </style>
  <script>
    function getPlatform(type) {
      let res = '';
      let ajax = new XMLHttpRequest();
      let url = window._CONFIG['domianURL'] + '/platformConfig/getPlatform/' + type
      ajax.onreadystatechange = function () {
        if (ajax.readyState===4 &&ajax.status===200) {
          res = ajax.responseText;
        } else {
          res = 'ERP系统';
        }
      }
      ajax.open('get', url, false);
      ajax.send(null);
      return res;
    }
    window._CONFIG = {};
    window._CONFIG['domianURL'] = '/jshERP-boot';
    let statisticsCode = '1cd9bcbaae133f03a6eb19da6579aaba'
    window.SYS_TITLE = getPlatform("name");
    window.SYS_URL = getPlatform("url");
    window._statistics = 'https://hm.baidu.com/hm.js?' + statisticsCode
    document.title = window.SYS_TITLE;
    //statistics
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = window._statistics;
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
</head>

<body>
<!-- built files will be auto injected -->
<div id="app">
  <div id="loader-wrapper">
    <div id="loader"></div>
    <div class="loader-section section-left"></div>
    <div class="loader-section section-right"></div>
    <div class="load_title">
      正在加载系统,请耐心等待
    </div>
  </div>
</div>
</body>

<!-- 全局配置-多语言切换-开始 -->
<script src="<%= BASE_URL %>static/translate.js"></script>
<script>
//设置本地语种（当前网页的语种）。如果不设置，默认就是 'chinese_simplified' 简体中文
translate.language.setLocal('chinese_simplified');
translate.service.use('client.edge');
//翻译自定义
translate.nomenclature.append('chinese_simplified','english',`
管伊佳ERP=GuanYiJia
`)
//开启html页面变化的监控，对变化部分会进行自动翻译
translate.listener.start();
//不显示语言选择标签
translate.selectLanguageTag.show = false;
//执行翻译初始化操作，显示出select语言选择
//translate.execute();

//VUE的渲染需要时间，所以留出一点点时间来进行翻译切换
document.addEventListener('DOMContentLoaded', function () {
    //页面 DOM 已渲染完毕，当然最好是能监控到整个vue渲染完毕后触发最好
    translate.execute();
    //2秒后再一次，避免有遗漏
    setTimeout(function(){
    	translate.execute();
    },2000);
});
</script>
<!-- 全局配置-多语言切换-结束 -->

</html>