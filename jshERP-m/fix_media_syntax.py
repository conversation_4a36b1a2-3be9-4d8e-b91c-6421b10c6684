#!/usr/bin/env python3
"""
修复 responsive.less 文件中的 .media-* 语法错误
"""

import re

def fix_media_syntax():
    file_path = 'src/styles/mobile/responsive.less'
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原始文件长度: {len(content)} 字符")
        
        # 修复 .media-*({ ... } 缺少 }); 的问题
        # 查找所有 .media-xxx({ ... } 后面没有 }); 的情况
        
        # 模式1: .media-xxx({ content } 后面直接是换行或其他内容，需要添加 );
        pattern1 = r'(\.media-[a-z]+\(\{[^}]*\})\s*(?!\);)'
        matches1 = re.findall(pattern1, content)
        print(f"找到需要修复的 .media-* 调用: {len(matches1)} 个")
        
        # 执行修复
        content = re.sub(pattern1, r'\1);', content)
        
        # 模式2: 修复连续的 .media-* 调用之间缺少 }); 的问题
        # 例如: } .media-xl({ 应该是 }); .media-xl({
        pattern2 = r'(\})\s*\n(\s*)(\.media-[a-z]+\(\{)'
        content = re.sub(pattern2, r'\1);\n\2\3', content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"修复完成！新文件长度: {len(content)} 字符")
        print("已修复所有 .media-* 语法错误")
        
    except Exception as e:
        print(f"修复失败: {e}")

if __name__ == "__main__":
    fix_media_syntax()
