#!/bin/bash

# 批量修复 @/styles/mobile/index.less 导入路径

echo "开始修复导入路径..."

# 修复 components/mobile 目录下的文件
find src/components/mobile -name "*.vue" -exec sed -i "s|@import '@/styles/mobile/index.less';|@import '../../styles/mobile/index.less';|g" {} \;

# 修复 components/debug 目录下的文件  
find src/components/debug -name "*.vue" -exec sed -i "s|@import '@/styles/mobile/index.less';|@import '../../styles/mobile/index.less';|g" {} \;

# 修复 views/mobile 目录下的文件
find src/views/mobile -name "*.vue" -exec sed -i "s|@import '@/styles/mobile/index.less';|@import '../../../styles/mobile/index.less';|g" {} \;

# 修复 views/mobile/components 目录下的文件
find src/views/mobile/components -name "*.vue" -exec sed -i "s|@import '@/styles/mobile/index.less';|@import '../../../../styles/mobile/index.less';|g" {} \;

# 修复 views/debug 目录下的文件
find src/views/debug -name "*.vue" -exec sed -i "s|@import '@/styles/mobile/index.less';|@import '../../../styles/mobile/index.less';|g" {} \;

echo "导入路径修复完成！"
