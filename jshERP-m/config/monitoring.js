/**
 * jshERP移动端监控配置
 * 统一管理所有监控相关的配置
 */

export default {
  // 性能监控配置
  performance: {
    enabled: process.env.VUE_APP_PERFORMANCE_ENABLE === 'true',
    sampleRate: parseFloat(process.env.VUE_APP_PERFORMANCE_SAMPLE_RATE) || 0.1,
    reportUrl: process.env.VUE_APP_PERFORMANCE_REPORT_URL || '',
    batchSize: 10,
    reportInterval: 30000, // 30秒
    
    // 性能阈值
    thresholds: {
      fcp: 2000, // 首次内容绘制时间 < 2s
      lcp: 4000, // 最大内容绘制时间 < 4s
      fid: 100,  // 首次输入延迟 < 100ms
      cls: 0.1,  // 累积布局偏移 < 0.1
      ttfb: 800, // 首字节时间 < 800ms
      pageLoad: 5000, // 页面加载时间 < 5s
      apiResponse: 3000, // API响应时间 < 3s
      memoryUsage: 0.8 // 内存使用率 < 80%
    },
    
    // 监控项目
    metrics: [
      'page_load',
      'resource_load',
      'user_interaction',
      'memory_usage',
      'network_status',
      'api_performance',
      'route_change'
    ]
  },
  
  // 错误监控配置
  error: {
    enabled: process.env.VUE_APP_ERROR_MONITORING_ENABLE === 'true',
    reportUrl: process.env.VUE_APP_ERROR_REPORT_URL || '',
    sampleRate: 1.0, // 错误全量上报
    batchSize: 5,
    reportInterval: 10000, // 10秒
    maxErrors: 100,
    
    // 错误级别配置
    levels: {
      LOW: 'low',
      MEDIUM: 'medium',
      HIGH: 'high',
      CRITICAL: 'critical'
    },
    
    // 错误类型配置
    types: {
      JS_ERROR: 'js_error',
      PROMISE_REJECTION: 'promise_rejection',
      NETWORK_ERROR: 'network_error',
      API_ERROR: 'api_error',
      COMPONENT_ERROR: 'component_error',
      RESOURCE_ERROR: 'resource_error',
      CUSTOM_ERROR: 'custom_error'
    },
    
    // 忽略的错误
    ignoreErrors: [
      'Script error.',
      'Non-Error promise rejection captured',
      'ResizeObserver loop limit exceeded',
      'Network request failed'
    ],
    
    // 忽略的URL
    ignoreUrls: [
      /extensions\//i,
      /^chrome:\/\//i,
      /^moz-extension:\/\//i
    ]
  },
  
  // 告警配置
  alert: {
    enabled: process.env.VUE_APP_ALERT_ENABLE === 'true',
    webhookUrl: process.env.VUE_APP_ALERT_WEBHOOK_URL || '',
    emailUrl: process.env.VUE_APP_ALERT_EMAIL_URL || '',
    
    // 告警阈值
    thresholds: {
      errorRate: 0.1, // 错误率 > 10%
      errorCount: 10, // 10分钟内错误数 > 10个
      responseTime: 3000, // 响应时间 > 3秒
      memoryUsage: 0.8, // 内存使用率 > 80%
      networkFailure: 5, // 网络失败次数 > 5次
      criticalErrors: 1, // 严重错误 >= 1个
      apiErrorRate: 0.05, // API错误率 > 5%
      pageLoadTime: 5000 // 页面加载时间 > 5秒
    },
    
    // 告警窗口和冷却期
    alertWindow: 10 * 60 * 1000, // 10分钟窗口
    cooldownPeriod: 5 * 60 * 1000, // 5分钟冷却期
    
    // 告警级别配置
    levels: {
      CRITICAL: {
        color: '#ff4d4f',
        priority: 1,
        immediate: true
      },
      HIGH: {
        color: '#ff7a45',
        priority: 2,
        immediate: false
      },
      MEDIUM: {
        color: '#ffa940',
        priority: 3,
        immediate: false
      },
      LOW: {
        color: '#52c41a',
        priority: 4,
        immediate: false
      }
    },
    
    // 通知渠道配置
    channels: {
      webhook: {
        enabled: true,
        levels: ['critical', 'high', 'medium']
      },
      email: {
        enabled: true,
        levels: ['critical', 'high']
      },
      browser: {
        enabled: true,
        levels: ['critical']
      },
      console: {
        enabled: true,
        levels: ['critical', 'high', 'medium', 'low']
      }
    }
  },
  
  // 业务监控配置
  business: {
    enabled: true,
    
    // 关键业务指标
    metrics: {
      // 用户行为
      userActions: {
        login: { threshold: 5000 }, // 登录时间
        search: { threshold: 2000 }, // 搜索响应时间
        submit: { threshold: 3000 }, // 表单提交时间
        navigation: { threshold: 1000 } // 页面导航时间
      },
      
      // 业务流程
      businessFlows: {
        orderCreate: { threshold: 5000 }, // 订单创建
        inventoryCheck: { threshold: 2000 }, // 库存查询
        reportGenerate: { threshold: 10000 }, // 报表生成
        dataSync: { threshold: 30000 } // 数据同步
      },
      
      // 系统资源
      systemResources: {
        cacheHitRate: { threshold: 0.8 }, // 缓存命中率
        apiSuccessRate: { threshold: 0.95 }, // API成功率
        pageLoadSuccess: { threshold: 0.98 }, // 页面加载成功率
        networkStability: { threshold: 0.9 } // 网络稳定性
      }
    }
  },
  
  // 日志配置
  logging: {
    enabled: true,
    level: process.env.NODE_ENV === 'production' ? 'warn' : 'debug',
    
    // 日志类别
    categories: {
      performance: true,
      error: true,
      business: true,
      security: true,
      debug: process.env.NODE_ENV !== 'production'
    },
    
    // 日志格式
    format: {
      timestamp: true,
      level: true,
      category: true,
      userAgent: true,
      url: true,
      userId: true,
      sessionId: true
    },
    
    // 日志存储
    storage: {
      local: true, // 本地存储
      remote: true, // 远程上报
      maxSize: 1000, // 最大条数
      retention: 7 * 24 * 60 * 60 * 1000 // 保留7天
    }
  },
  
  // 开发环境配置
  development: {
    // 开发环境下的特殊配置
    mockData: true,
    debugMode: true,
    verboseLogging: true,
    
    // 测试工具
    testingTools: {
      performanceTest: true,
      errorSimulation: true,
      networkSimulation: true,
      cacheTest: true
    }
  },
  
  // 生产环境配置
  production: {
    // 生产环境下的优化配置
    optimized: true,
    minimalLogging: true,
    
    // 安全配置
    security: {
      sensitiveDataMask: true,
      errorDetailHide: true,
      debugInfoHide: true
    }
  }
}
