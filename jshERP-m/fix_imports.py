#!/usr/bin/env python3
import os
import re

def fix_import_path(file_path):
    """修复单个文件的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 计算相对路径
        relative_path = os.path.relpath('src/styles/mobile/index.less', os.path.dirname(file_path))
        
        # 替换导入路径
        old_pattern = r"@import '@/styles/mobile/index\.less';"
        new_import = f"@import '{relative_path}';"
        
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_import, content)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复: {file_path} -> {new_import}")
            return True
    except Exception as e:
        print(f"错误处理 {file_path}: {e}")
    return False

def main():
    """主函数"""
    print("开始批量修复导入路径...")
    
    # 需要修复的文件列表
    files_to_fix = [
        'src/components/mobile/MobileList.vue',
        'src/components/mobile/VirtualMobileList.vue', 
        'src/components/mobile/MobileModal.vue',
        'src/components/mobile/MobileFormItem.vue',
        'src/components/debug/DebugPanel.vue',
        'src/components/debug/PerformanceTest.vue',
        'src/views/mobile/Inventory.vue',
        'src/views/mobile/Material.vue',
        'src/views/mobile/Dashboard.vue',
        'src/views/mobile/components/OrderForm.vue',
        'src/views/mobile/components/MaterialForm.vue',
        'src/views/mobile/components/BatchOperationForm.vue',
        'src/views/mobile/components/InventoryForm.vue',
        'src/views/mobile/components/MaterialSelector.vue',
        'src/views/mobile/Orders.vue',
        'src/views/debug/CacheTest.vue',
        'src/views/debug/ErrorTest.vue',
        'src/views/debug/ComponentTest.vue',
        'src/views/debug/ApiTest.vue',
        'src/views/debug/TestCenter.vue'
    ]
    
    fixed_count = 0
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_import_path(file_path):
                fixed_count += 1
        else:
            print(f"文件不存在: {file_path}")
    
    print(f"修复完成！共修复 {fixed_count} 个文件")

if __name__ == '__main__':
    main()
