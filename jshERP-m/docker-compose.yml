# jshERP移动端 Docker Compose配置
version: '3.8'

services:
  # 移动端前端服务
  jsh-erp-mobile:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jsh-erp-mobile
    restart: unless-stopped
    ports:
      - "8899:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./logs/nginx:/var/log/nginx
    networks:
      - jsh-erp-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端服务(可选，如果需要独立部署)
  backend:
    image: jsh-erp-backend:latest
    container_name: jsh-erp-backend
    restart: unless-stopped
    ports:
      - "9999:9999"
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=docker
    volumes:
      - ./logs/backend:/app/logs
      - ./data/uploads:/app/uploads
    networks:
      - jsh-erp-network
    depends_on:
      - mysql
      - redis

  # MySQL数据库
  mysql:
    image: mysql:5.7.33
    container_name: jsh-erp-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=jsh_erp
      - MYSQL_USER=jsh_user
      - MYSQL_PASSWORD=123456
      - TZ=Asia/Shanghai
    volumes:
      - ./data/mysql:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - jsh-erp-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis缓存
  redis:
    image: redis:6.2.1-alpine
    container_name: jsh-erp-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./data/redis:/data
    networks:
      - jsh-erp-network
    command: redis-server --appendonly yes

networks:
  jsh-erp-network:
    driver: bridge
    name: jsh-erp-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
