/**
 * 响应式混入 (Responsive Mixin)
 * 为Vue组件提供响应式能力和移动端适配功能
 */

import mobileAdapter from '@/utils/mobile-adapter'

export const ResponsiveMixin = {
  data() {
    return {
      // 响应式状态
      responsive: {
        isMobile: false,
        isTablet: false,
        isDesktop: false,
        breakpoint: 'md',
        orientation: 'portrait',
        viewport: {
          width: 0,
          height: 0
        }
      }
    }
  },

  computed: {
    // 设备类型计算属性
    $isMobile() {
      return this.responsive.isMobile
    },

    $isTablet() {
      return this.responsive.isTablet
    },

    $isDesktop() {
      return this.responsive.isDesktop
    },

    // 断点计算属性
    $breakpoint() {
      return this.responsive.breakpoint
    },

    $isXs() {
      return this.responsive.breakpoint === 'xs'
    },

    $isSm() {
      return this.responsive.breakpoint === 'sm'
    },

    $isMd() {
      return this.responsive.breakpoint === 'md'
    },

    $isLg() {
      return this.responsive.breakpoint === 'lg'
    },

    $isXl() {
      return this.responsive.breakpoint === 'xl'
    },

    $isXxl() {
      return this.responsive.breakpoint === 'xxl'
    },

    // 屏幕方向计算属性
    $isPortrait() {
      return this.responsive.orientation === 'portrait'
    },

    $isLandscape() {
      return this.responsive.orientation === 'landscape'
    },

    // 视口尺寸计算属性
    $viewportWidth() {
      return this.responsive.viewport.width
    },

    $viewportHeight() {
      return this.responsive.viewport.height
    },

    // 组件配置计算属性
    $tableConfig() {
      return mobileAdapter.getComponentConfig('table')
    },

    $formConfig() {
      return mobileAdapter.getComponentConfig('form')
    },

    $buttonConfig() {
      return mobileAdapter.getComponentConfig('button')
    },

    $modalConfig() {
      return mobileAdapter.getComponentConfig('modal')
    },

    $drawerConfig() {
      return mobileAdapter.getComponentConfig('drawer')
    },

    // 样式配置计算属性
    $styleConfig() {
      return mobileAdapter.getStyleConfig()
    },

    $spacing() {
      return this.$styleConfig.spacing
    },

    $fontSize() {
      return this.$styleConfig.fontSize
    },

    $borderRadius() {
      return this.$styleConfig.borderRadius
    },

    // 响应式类名
    $responsiveClass() {
      return {
        'is-mobile': this.$isMobile,
        'is-tablet': this.$isTablet,
        'is-desktop': this.$isDesktop,
        [`is-${this.$breakpoint}`]: true,
        [`is-${this.responsive.orientation}`]: true
      }
    }
  },

  methods: {
    // 更新响应式状态
    updateResponsiveState() {
      this.responsive = {
        isMobile: mobileAdapter.isMobile(),
        isTablet: mobileAdapter.isTablet(),
        isDesktop: mobileAdapter.isDesktop(),
        breakpoint: mobileAdapter.getCurrentBreakpoint(),
        orientation: mobileAdapter.viewportConfig.orientation,
        viewport: {
          width: mobileAdapter.viewportConfig.width,
          height: mobileAdapter.viewportConfig.height
        }
      }
    },

    // 检查断点匹配
    $matchBreakpoint(breakpoint) {
      return mobileAdapter.matchBreakpoint(breakpoint)
    },

    // 获取适配后的尺寸
    $adaptiveSize(baseSize, mobileSize = null) {
      return mobileAdapter.getAdaptiveSize(baseSize, mobileSize)
    },

    // 获取适配后的间距
    $adaptiveSpacing(spacing) {
      return mobileAdapter.getAdaptiveSpacing(spacing)
    },

    // 获取适配后的字体大小
    $adaptiveFontSize(size) {
      return mobileAdapter.getAdaptiveFontSize(size)
    },

    // 响应式样式生成器
    $responsiveStyle(config) {
      const result = {}
      
      Object.keys(config).forEach(property => {
        const value = config[property]
        
        if (typeof value === 'object' && value !== null) {
          // 响应式值对象 { xs: 10, sm: 12, md: 14, lg: 16, xl: 18 }
          const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']
          const currentIndex = breakpoints.indexOf(this.$breakpoint)
          
          // 找到当前断点或更小断点的值
          for (let i = currentIndex; i >= 0; i--) {
            const bp = breakpoints[i]
            if (value[bp] !== undefined) {
              result[property] = value[bp]
              break
            }
          }
        } else {
          // 普通值
          result[property] = value
        }
      })
      
      return result
    },

    // 响应式类名生成器
    $responsiveClass(config) {
      const result = []
      
      Object.keys(config).forEach(className => {
        const condition = config[className]
        
        if (typeof condition === 'boolean') {
          if (condition) result.push(className)
        } else if (typeof condition === 'string') {
          // 断点条件 'md+', 'lg-', 'sm-md'
          if (this.$matchBreakpointCondition(condition)) {
            result.push(className)
          }
        } else if (typeof condition === 'function') {
          // 函数条件
          if (condition(this.responsive)) {
            result.push(className)
          }
        }
      })
      
      return result
    },

    // 断点条件匹配
    $matchBreakpointCondition(condition) {
      const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']
      const currentIndex = breakpoints.indexOf(this.$breakpoint)
      
      if (condition.includes('+')) {
        // 'md+' 表示 md 及以上
        const bp = condition.replace('+', '')
        const targetIndex = breakpoints.indexOf(bp)
        return currentIndex >= targetIndex
      } else if (condition.includes('-') && !condition.includes('+')) {
        if (condition.endsWith('-')) {
          // 'lg-' 表示 lg 及以下
          const bp = condition.replace('-', '')
          const targetIndex = breakpoints.indexOf(bp)
          return currentIndex <= targetIndex
        } else {
          // 'sm-md' 表示 sm 到 md 之间
          const [start, end] = condition.split('-')
          const startIndex = breakpoints.indexOf(start)
          const endIndex = breakpoints.indexOf(end)
          return currentIndex >= startIndex && currentIndex <= endIndex
        }
      } else {
        // 精确匹配
        return this.$breakpoint === condition
      }
    },

    // 响应式事件处理
    handleResponsiveChange() {
      this.updateResponsiveState()
      
      // 触发组件的响应式变化事件
      if (typeof this.onResponsiveChange === 'function') {
        this.onResponsiveChange(this.responsive)
      }
      
      // 触发全局事件
      this.$emit('responsive-change', this.responsive)
    }
  },

  created() {
    // 初始化响应式状态
    this.updateResponsiveState()
  },

  mounted() {
    // 监听适配器事件
    window.addEventListener('mobileadapter:orientationchange', this.handleResponsiveChange)
    window.addEventListener('mobileadapter:refresh', this.handleResponsiveChange)
    window.addEventListener('resize', this.handleResponsiveChange)
  },

  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('mobileadapter:orientationchange', this.handleResponsiveChange)
    window.removeEventListener('mobileadapter:refresh', this.handleResponsiveChange)
    window.removeEventListener('resize', this.handleResponsiveChange)
  }
}

// 轻量级响应式混入（只包含基础功能）
export const LiteResponsiveMixin = {
  data() {
    return {
      isMobile: false,
      breakpoint: 'md'
    }
  },

  computed: {
    $isMobile() {
      return this.isMobile
    },

    $breakpoint() {
      return this.breakpoint
    }
  },

  methods: {
    updateLiteResponsiveState() {
      this.isMobile = mobileAdapter.isMobile()
      this.breakpoint = mobileAdapter.getCurrentBreakpoint()
    }
  },

  created() {
    this.updateLiteResponsiveState()
  },

  mounted() {
    window.addEventListener('resize', this.updateLiteResponsiveState)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.updateLiteResponsiveState)
  }
}

export default ResponsiveMixin
