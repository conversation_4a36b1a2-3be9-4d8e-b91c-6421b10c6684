import Vue from 'vue'
import Vuex from 'vuex'

import app from './modules/app'
import user from './modules/user'
import permission from './modules/permission'
import enhance from './modules/enhance'
import getters from './getters'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    user,
    permission,
    enhance
  },
  state: {
    // 全局加载状态
    globalLoading: false,
    globalLoadingText: '加载中...'
  },
  mutations: {
    // 设置全局加载状态
    SET_GLOBAL_LOADING(state, { loading, text = '加载中...' }) {
      state.globalLoading = loading
      state.globalLoadingText = text
    }
  },
  actions: {

  },
  getters
})
