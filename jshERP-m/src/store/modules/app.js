import Vue from 'vue'
import {
  SIDEBAR_TYPE,
  DEFAULT_THEME,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_COLOR,
  DEFAULT_COLOR_WEAK,
  DEFAULT_FIXED_HEADER,
  DEFAULT_FIXED_SIDEMENU,
  DEFAULT_FIXED_HEADER_HIDDEN,
  DEFAULT_CONTENT_WIDTH_TYPE,
  DEFAULT_MULTI_PAGE
} from "@/store/mutation-types"

const app = {
  state: {
    sidebar: {
      opened: true,
      withoutAnimation: false
    },
    device: 'desktop',
    theme: '',
    layout: '',
    contentWidth: '',
    fixedHeader: false,
    fixSiderbar: true,
    autoHideHeader: false,
    color: null,
    weak: false,
    multipage: true, //默认多页签模式

    // 移动端专用状态
    mobile: {
      // 设备信息
      deviceInfo: {
        isMobile: false,
        isTablet: false,
        isIOS: false,
        isAndroid: false,
        screenWidth: 0,
        screenHeight: 0,
        orientation: 'portrait'
      },

      // 移动端UI状态
      ui: {
        tabbarVisible: true,
        headerVisible: true,
        sidebarVisible: false,
        keyboardVisible: false,
        statusBarHeight: 0,
        safeAreaInsets: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0
        }
      },

      // 移动端设置
      settings: {
        enablePullRefresh: true,
        enableInfiniteScroll: true,
        enableSwipeBack: true,
        enableHapticFeedback: true,
        theme: 'light' // light | dark | auto
      }
    }
  },
  mutations: {
    SET_SIDEBAR_TYPE: (state, type) => {
      state.sidebar.opened = type
      Vue.ls.set(SIDEBAR_TYPE, type)
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      Vue.ls.set(SIDEBAR_TYPE, true)
      state.sidebar.opened = false
      state.sidebar.withoutAnimation = withoutAnimation
    },
    TOGGLE_DEVICE: (state, device) => {
      state.device = device
    },
    TOGGLE_THEME: (state, theme) => {
      // setStore('_DEFAULT_THEME', theme)
      Vue.ls.set(DEFAULT_THEME, theme)
      state.theme = theme
    },
    TOGGLE_LAYOUT_MODE: (state, layout) => {
      Vue.ls.set(DEFAULT_LAYOUT_MODE, layout)
      state.layout = layout
    },
    TOGGLE_FIXED_HEADER: (state, fixed) => {
      Vue.ls.set(DEFAULT_FIXED_HEADER, fixed)
      state.fixedHeader = fixed
    },
    TOGGLE_FIXED_SIDERBAR: (state, fixed) => {
      Vue.ls.set(DEFAULT_FIXED_SIDEMENU, fixed)
      state.fixSiderbar = fixed
    },
    TOGGLE_FIXED_HEADER_HIDDEN: (state, show) => {
      Vue.ls.set(DEFAULT_FIXED_HEADER_HIDDEN, show)
      state.autoHideHeader = show
    },
    TOGGLE_CONTENT_WIDTH: (state, type) => {
      Vue.ls.set(DEFAULT_CONTENT_WIDTH_TYPE, type)
      state.contentWidth = type
    },
    TOGGLE_COLOR: (state, color) => {
      Vue.ls.set(DEFAULT_COLOR, color)
      state.color = color
    },
    TOGGLE_WEAK: (state, flag) => {
      Vue.ls.set(DEFAULT_COLOR_WEAK, flag)
      state.weak = flag
    },
    SET_MULTI_PAGE (state, multipageFlag) {
      Vue.ls.set(DEFAULT_MULTI_PAGE, multipageFlag)
      state.multipage = multipageFlag
    },

    // 移动端专用 mutations
    SET_MOBILE_DEVICE_INFO: (state, deviceInfo) => {
      state.mobile.deviceInfo = { ...state.mobile.deviceInfo, ...deviceInfo }
    },

    SET_MOBILE_UI_STATE: (state, uiState) => {
      state.mobile.ui = { ...state.mobile.ui, ...uiState }
    },

    SET_MOBILE_SETTINGS: (state, settings) => {
      state.mobile.settings = { ...state.mobile.settings, ...settings }
      Vue.ls.set('MOBILE_SETTINGS', state.mobile.settings)
    },

    TOGGLE_MOBILE_TABBAR: (state, visible) => {
      state.mobile.ui.tabbarVisible = visible
    },

    TOGGLE_MOBILE_SIDEBAR: (state, visible) => {
      state.mobile.ui.sidebarVisible = visible
    },

    SET_SAFE_AREA_INSETS: (state, insets) => {
      state.mobile.ui.safeAreaInsets = { ...state.mobile.ui.safeAreaInsets, ...insets }
    }
  },
  actions: {
    setSidebar: ({ commit }, type) => {
      commit('SET_SIDEBAR_TYPE', type)
    },
    CloseSidebar({ commit }, { withoutAnimation }) {
      commit('CLOSE_SIDEBAR', withoutAnimation)
    },
    ToggleDevice({ commit }, device) {
      commit('TOGGLE_DEVICE', device)
    },
    ToggleTheme({ commit }, theme) {
      commit('TOGGLE_THEME', theme)
    },
    ToggleLayoutMode({ commit }, mode) {
      commit('TOGGLE_LAYOUT_MODE', mode)
    },
    ToggleFixedHeader({ commit }, fixedHeader) {
      if (!fixedHeader) {
        commit('TOGGLE_FIXED_HEADER_HIDDEN', false)
      }
      commit('TOGGLE_FIXED_HEADER', fixedHeader)
    },
    ToggleFixSiderbar({ commit }, fixSiderbar) {
      commit( 'TOGGLE_FIXED_SIDERBAR', fixSiderbar)
    },
    ToggleFixedHeaderHidden({ commit }, show) {
      commit('TOGGLE_FIXED_HEADER_HIDDEN', show)
    },
    ToggleContentWidth({ commit }, type) {
      commit('TOGGLE_CONTENT_WIDTH', type)
    },
    ToggleColor({ commit }, color) {
      commit('TOGGLE_COLOR', color)
    },
    ToggleWeak({ commit }, weakFlag) {
      commit('TOGGLE_WEAK', weakFlag)
    },
    ToggleMultipage({ commit }, multipageFlag) {
      commit('SET_MULTI_PAGE', multipageFlag)
    },

    // 移动端专用 actions
    setMobileDeviceInfo({ commit }, deviceInfo) {
      commit('SET_MOBILE_DEVICE_INFO', deviceInfo)
    },

    setMobileUIState({ commit }, uiState) {
      commit('SET_MOBILE_UI_STATE', uiState)
    },

    setMobileSettings({ commit }, settings) {
      commit('SET_MOBILE_SETTINGS', settings)
    },

    toggleMobileTabbar({ commit }, visible) {
      commit('TOGGLE_MOBILE_TABBAR', visible)
    },

    toggleMobileSidebar({ commit }, visible) {
      commit('TOGGLE_MOBILE_SIDEBAR', visible)
    },

    setSafeAreaInsets({ commit }, insets) {
      commit('SET_SAFE_AREA_INSETS', insets)
    },

    // 初始化移动端设备信息
    initMobileDevice({ commit, dispatch }) {
      const deviceInfo = {
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        isTablet: /iPad|Android/i.test(navigator.userAgent) && !/Mobile/i.test(navigator.userAgent),
        isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
        isAndroid: /Android/.test(navigator.userAgent),
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
      }

      commit('SET_MOBILE_DEVICE_INFO', deviceInfo)
      commit('TOGGLE_DEVICE', deviceInfo.isMobile ? 'mobile' : 'desktop')

      // 加载移动端设置
      const savedSettings = Vue.ls.get('MOBILE_SETTINGS')
      if (savedSettings) {
        commit('SET_MOBILE_SETTINGS', savedSettings)
      }

      return deviceInfo
    }
  }
}

export default app