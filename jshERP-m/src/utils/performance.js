/**
 * 性能监控工具
 * 用于监控和分析移动端应用性能
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.isEnabled = process.env.NODE_ENV === 'development' || process.env.VUE_APP_PERFORMANCE_MONITOR === 'true'
    
    if (this.isEnabled) {
      this.init()
    }
  }
  
  /**
   * 初始化性能监控
   */
  init() {
    // 监控页面加载性能
    this.observePageLoad()
    
    // 监控长任务
    this.observeLongTasks()
    
    // 监控内存使用
    this.observeMemory()
    
    // 监控网络请求
    this.observeNetwork()
    
    // 监控用户交互
    this.observeUserInteraction()
  }
  
  /**
   * 监控页面加载性能
   */
  observePageLoad() {
    if (typeof window !== 'undefined' && 'performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0]
          if (navigation) {
            this.recordMetric('page_load', {
              dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
              tcp_connect: navigation.connectEnd - navigation.connectStart,
              request: navigation.responseStart - navigation.requestStart,
              response: navigation.responseEnd - navigation.responseStart,
              dom_parse: navigation.domContentLoadedEventEnd - navigation.responseEnd,
              resource_load: navigation.loadEventEnd - navigation.domContentLoadedEventEnd,
              total_time: navigation.loadEventEnd - navigation.navigationStart
            })
          }
        }, 0)
      })
    }
  }
  
  /**
   * 监控长任务
   */
  observeLongTasks() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordMetric('long_task', {
              duration: entry.duration,
              start_time: entry.startTime,
              name: entry.name
            })
          })
        })
        
        observer.observe({ entryTypes: ['longtask'] })
        this.observers.push(observer)
      } catch (e) {
        console.warn('Long task monitoring not supported:', e)
      }
    }
  }
  
  /**
   * 监控内存使用
   */
  observeMemory() {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      setInterval(() => {
        const memory = performance.memory
        this.recordMetric('memory_usage', {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          usage_ratio: memory.usedJSHeapSize / memory.jsHeapSizeLimit
        })
      }, 30000) // 每30秒记录一次
    }
  }
  
  /**
   * 监控网络请求
   */
  observeNetwork() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.initiatorType === 'xmlhttprequest' || entry.initiatorType === 'fetch') {
              this.recordMetric('network_request', {
                url: entry.name,
                duration: entry.duration,
                size: entry.transferSize || 0,
                type: entry.initiatorType
              })
            }
          })
        })
        
        observer.observe({ entryTypes: ['resource'] })
        this.observers.push(observer)
      } catch (e) {
        console.warn('Network monitoring not supported:', e)
      }
    }
  }
  
  /**
   * 监控用户交互
   */
  observeUserInteraction() {
    if (typeof window !== 'undefined') {
      const events = ['click', 'touchstart', 'scroll']
      
      events.forEach(eventType => {
        let startTime = 0
        
        window.addEventListener(eventType, () => {
          startTime = performance.now()
        }, { passive: true })
        
        window.addEventListener(eventType + 'end', () => {
          if (startTime) {
            const duration = performance.now() - startTime
            this.recordMetric('user_interaction', {
              type: eventType,
              duration: duration
            })
          }
        }, { passive: true })
      })
    }
  }
  
  /**
   * 记录性能指标
   */
  recordMetric(name, data) {
    if (!this.isEnabled) return
    
    const timestamp = Date.now()
    const metric = {
      name,
      data,
      timestamp,
      url: typeof window !== 'undefined' ? window.location.href : ''
    }
    
    // 存储到内存中
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    const metrics = this.metrics.get(name)
    metrics.push(metric)
    
    // 限制存储数量，避免内存泄漏
    if (metrics.length > 100) {
      metrics.shift()
    }
    
    // 输出到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}:`, data)
    }
    
    // 发送到服务器（生产环境）
    if (process.env.NODE_ENV === 'production') {
      this.sendToServer(metric)
    }
  }
  
  /**
   * 发送性能数据到服务器
   */
  sendToServer(metric) {
    // 使用 sendBeacon API 发送数据，不阻塞页面
    if (typeof navigator !== 'undefined' && 'sendBeacon' in navigator) {
      const data = JSON.stringify(metric)
      navigator.sendBeacon('/api/performance', data)
    }
  }
  
  /**
   * 获取性能指标
   */
  getMetrics(name) {
    return this.metrics.get(name) || []
  }
  
  /**
   * 获取所有性能指标
   */
  getAllMetrics() {
    const result = {}
    this.metrics.forEach((value, key) => {
      result[key] = value
    })
    return result
  }
  
  /**
   * 清除性能指标
   */
  clearMetrics(name) {
    if (name) {
      this.metrics.delete(name)
    } else {
      this.metrics.clear()
    }
  }
  
  /**
   * 生成性能报告
   */
  generateReport() {
    const report = {
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : '',
      metrics: {}
    }
    
    this.metrics.forEach((metrics, name) => {
      if (metrics.length > 0) {
        const values = metrics.map(m => m.data)
        
        report.metrics[name] = {
          count: metrics.length,
          latest: values[values.length - 1],
          summary: this.calculateSummary(values, name)
        }
      }
    })
    
    return report
  }
  
  /**
   * 计算指标摘要
   */
  calculateSummary(values, metricName) {
    if (values.length === 0) return null
    
    switch (metricName) {
      case 'page_load':
        return {
          avg_total_time: this.average(values.map(v => v.total_time)),
          avg_dom_parse: this.average(values.map(v => v.dom_parse)),
          avg_resource_load: this.average(values.map(v => v.resource_load))
        }
        
      case 'long_task':
        return {
          count: values.length,
          avg_duration: this.average(values.map(v => v.duration)),
          max_duration: Math.max(...values.map(v => v.duration))
        }
        
      case 'memory_usage':
        const latest = values[values.length - 1]
        return {
          current_usage: latest.usage_ratio,
          used_mb: Math.round(latest.used / 1024 / 1024),
          total_mb: Math.round(latest.total / 1024 / 1024)
        }
        
      case 'network_request':
        return {
          count: values.length,
          avg_duration: this.average(values.map(v => v.duration)),
          total_size: values.reduce((sum, v) => sum + (v.size || 0), 0)
        }
        
      default:
        return {
          count: values.length
        }
    }
  }
  
  /**
   * 计算平均值
   */
  average(numbers) {
    if (numbers.length === 0) return 0
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length
  }
  
  /**
   * 销毁监控器
   */
  destroy() {
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers = []
    this.metrics.clear()
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 导出工具函数
export const recordPerformance = (name, data) => {
  performanceMonitor.recordMetric(name, data)
}

export const getPerformanceMetrics = (name) => {
  return performanceMonitor.getMetrics(name)
}

export const generatePerformanceReport = () => {
  return performanceMonitor.generateReport()
}

export const clearPerformanceMetrics = (name) => {
  performanceMonitor.clearMetrics(name)
}

export default performanceMonitor
