/**
 * jshERP移动端告警管理器
 * 负责监控系统状态并发送告警通知
 */

class AlertManager {
  constructor(options = {}) {
    this.options = {
      // 告警配置
      enabled: process.env.VUE_APP_ALERT_ENABLE === 'true',
      webhookUrl: process.env.VUE_APP_ALERT_WEBHOOK_URL || '',
      emailUrl: process.env.VUE_APP_ALERT_EMAIL_URL || '',
      
      // 告警阈值
      thresholds: {
        errorRate: 0.1, // 错误率超过10%
        errorCount: 10, // 10分钟内错误数超过10个
        responseTime: 3000, // 响应时间超过3秒
        memoryUsage: 0.8, // 内存使用率超过80%
        networkFailure: 5, // 网络失败次数超过5次
        criticalErrors: 1 // 严重错误立即告警
      },
      
      // 告警窗口和冷却期
      alertWindow: 10 * 60 * 1000, // 10分钟窗口
      cooldownPeriod: 5 * 60 * 1000, // 5分钟冷却期
      
      ...options
    }
    
    this.alerts = []
    this.metrics = {
      errors: [],
      performance: [],
      network: []
    }
    
    this.lastAlertTimes = new Map()
    
    if (this.options.enabled) {
      this.init()
    }
  }
  
  /**
   * 初始化告警管理器
   */
  init() {
    // 监听错误事件
    this.listenToErrors()
    
    // 监听性能事件
    this.listenToPerformance()
    
    // 监听网络事件
    this.listenToNetwork()
    
    // 定期检查告警条件
    this.startMonitoring()
    
    console.log('[AlertManager] 告警管理器已启动')
  }
  
  /**
   * 监听错误事件
   */
  listenToErrors() {
    // 监听全局错误
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: Date.now(),
        level: 'high'
      })
    })
    
    // 监听Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'promise_rejection',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        timestamp: Date.now(),
        level: 'medium'
      })
    })
  }
  
  /**
   * 监听性能事件
   */
  listenToPerformance() {
    // 监听页面性能
    if (window.PerformanceObserver) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          if (entry.entryType === 'navigation') {
            this.recordPerformance({
              type: 'page_load',
              duration: entry.duration,
              timestamp: Date.now()
            })
          }
        })
      })
      
      observer.observe({ entryTypes: ['navigation'] })
    }
    
    // 监听内存使用
    if (performance.memory) {
      setInterval(() => {
        const memory = performance.memory
        const usagePercent = memory.usedJSHeapSize / memory.jsHeapSizeLimit
        
        this.recordPerformance({
          type: 'memory_usage',
          usage: usagePercent,
          timestamp: Date.now()
        })
        
        // 检查内存使用告警
        if (usagePercent > this.options.thresholds.memoryUsage) {
          this.triggerAlert('memory_high', {
            usage: (usagePercent * 100).toFixed(2) + '%',
            threshold: (this.options.thresholds.memoryUsage * 100) + '%'
          })
        }
      }, 30000) // 每30秒检查一次
    }
  }
  
  /**
   * 监听网络事件
   */
  listenToNetwork() {
    // 监听网络状态变化
    if (navigator.connection) {
      navigator.connection.addEventListener('change', () => {
        const connection = navigator.connection
        this.recordNetwork({
          type: 'network_change',
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          timestamp: Date.now()
        })
      })
    }
    
    // 监听在线/离线状态
    window.addEventListener('online', () => {
      this.recordNetwork({
        type: 'network_online',
        timestamp: Date.now()
      })
    })
    
    window.addEventListener('offline', () => {
      this.recordNetwork({
        type: 'network_offline',
        timestamp: Date.now()
      })
      
      this.triggerAlert('network_offline', {
        message: '网络连接已断开'
      })
    })
  }
  
  /**
   * 开始监控
   */
  startMonitoring() {
    setInterval(() => {
      this.checkErrorRate()
      this.checkErrorCount()
      this.checkPerformance()
      this.cleanOldMetrics()
    }, 60000) // 每分钟检查一次
  }
  
  /**
   * 记录错误
   */
  recordError(error) {
    this.metrics.errors.push(error)
    
    // 严重错误立即告警
    if (error.level === 'critical') {
      this.triggerAlert('critical_error', error)
    }
  }
  
  /**
   * 记录性能数据
   */
  recordPerformance(perf) {
    this.metrics.performance.push(perf)
    
    // 检查响应时间告警
    if (perf.type === 'page_load' && perf.duration > this.options.thresholds.responseTime) {
      this.triggerAlert('slow_response', {
        duration: perf.duration,
        threshold: this.options.thresholds.responseTime
      })
    }
  }
  
  /**
   * 记录网络数据
   */
  recordNetwork(network) {
    this.metrics.network.push(network)
  }
  
  /**
   * 检查错误率
   */
  checkErrorRate() {
    const now = Date.now()
    const windowStart = now - this.options.alertWindow
    
    const recentErrors = this.metrics.errors.filter(error => 
      error.timestamp > windowStart
    )
    
    const totalRequests = this.getTotalRequests(windowStart, now)
    
    if (totalRequests > 0) {
      const errorRate = recentErrors.length / totalRequests
      
      if (errorRate > this.options.thresholds.errorRate) {
        this.triggerAlert('high_error_rate', {
          errorRate: (errorRate * 100).toFixed(2) + '%',
          errorCount: recentErrors.length,
          totalRequests: totalRequests,
          threshold: (this.options.thresholds.errorRate * 100) + '%'
        })
      }
    }
  }
  
  /**
   * 检查错误数量
   */
  checkErrorCount() {
    const now = Date.now()
    const windowStart = now - this.options.alertWindow
    
    const recentErrors = this.metrics.errors.filter(error => 
      error.timestamp > windowStart
    )
    
    if (recentErrors.length > this.options.thresholds.errorCount) {
      this.triggerAlert('high_error_count', {
        errorCount: recentErrors.length,
        threshold: this.options.thresholds.errorCount,
        timeWindow: '10分钟'
      })
    }
  }
  
  /**
   * 检查性能指标
   */
  checkPerformance() {
    const now = Date.now()
    const windowStart = now - this.options.alertWindow
    
    const recentPerf = this.metrics.performance.filter(perf => 
      perf.timestamp > windowStart && perf.type === 'page_load'
    )
    
    if (recentPerf.length > 0) {
      const avgDuration = recentPerf.reduce((sum, perf) => sum + perf.duration, 0) / recentPerf.length
      
      if (avgDuration > this.options.thresholds.responseTime) {
        this.triggerAlert('slow_performance', {
          avgDuration: avgDuration.toFixed(2) + 'ms',
          threshold: this.options.thresholds.responseTime + 'ms',
          sampleCount: recentPerf.length
        })
      }
    }
  }
  
  /**
   * 触发告警
   */
  async triggerAlert(alertType, data) {
    const now = Date.now()
    const lastAlertTime = this.lastAlertTimes.get(alertType) || 0
    
    // 检查冷却期
    if (now - lastAlertTime < this.options.cooldownPeriod) {
      return
    }
    
    const alert = {
      type: alertType,
      timestamp: now,
      data: data,
      level: this.getAlertLevel(alertType),
      message: this.getAlertMessage(alertType, data)
    }
    
    this.alerts.push(alert)
    this.lastAlertTimes.set(alertType, now)
    
    // 发送告警通知
    await this.sendAlert(alert)
    
    console.warn('[AlertManager] 触发告警:', alert)
  }
  
  /**
   * 发送告警通知
   */
  async sendAlert(alert) {
    const promises = []
    
    // Webhook通知
    if (this.options.webhookUrl) {
      promises.push(this.sendWebhookAlert(alert))
    }
    
    // 邮件通知
    if (this.options.emailUrl) {
      promises.push(this.sendEmailAlert(alert))
    }
    
    // 浏览器通知
    promises.push(this.sendBrowserNotification(alert))
    
    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.error('[AlertManager] 发送告警失败:', error)
    }
  }
  
  /**
   * 发送Webhook告警
   */
  async sendWebhookAlert(alert) {
    try {
      await fetch(this.options.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: `jshERP移动端告警 - ${alert.type}`,
          message: alert.message,
          level: alert.level,
          timestamp: new Date(alert.timestamp).toLocaleString(),
          data: alert.data,
          url: window.location.href
        })
      })
    } catch (error) {
      console.error('[AlertManager] Webhook告警发送失败:', error)
    }
  }
  
  /**
   * 发送邮件告警
   */
  async sendEmailAlert(alert) {
    try {
      await fetch(this.options.emailUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subject: `jshERP移动端告警 - ${alert.type}`,
          body: `
            告警类型: ${alert.type}
            告警级别: ${alert.level}
            告警时间: ${new Date(alert.timestamp).toLocaleString()}
            告警消息: ${alert.message}
            页面地址: ${window.location.href}
            详细数据: ${JSON.stringify(alert.data, null, 2)}
          `
        })
      })
    } catch (error) {
      console.error('[AlertManager] 邮件告警发送失败:', error)
    }
  }
  
  /**
   * 发送浏览器通知
   */
  async sendBrowserNotification(alert) {
    if (!('Notification' in window)) {
      return
    }
    
    if (Notification.permission === 'granted') {
      new Notification(`jshERP移动端告警`, {
        body: alert.message,
        icon: '/favicon.ico',
        tag: alert.type
      })
    } else if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission()
      if (permission === 'granted') {
        new Notification(`jshERP移动端告警`, {
          body: alert.message,
          icon: '/favicon.ico',
          tag: alert.type
        })
      }
    }
  }
  
  /**
   * 获取告警级别
   */
  getAlertLevel(alertType) {
    const levelMap = {
      critical_error: 'critical',
      network_offline: 'high',
      high_error_rate: 'high',
      high_error_count: 'medium',
      memory_high: 'medium',
      slow_response: 'low',
      slow_performance: 'low'
    }
    
    return levelMap[alertType] || 'medium'
  }
  
  /**
   * 获取告警消息
   */
  getAlertMessage(alertType, data) {
    const messageMap = {
      critical_error: `严重错误: ${data.message}`,
      network_offline: '网络连接已断开',
      high_error_rate: `错误率过高: ${data.errorRate} (阈值: ${data.threshold})`,
      high_error_count: `错误数量过多: ${data.errorCount}个错误 (阈值: ${data.threshold})`,
      memory_high: `内存使用率过高: ${data.usage} (阈值: ${data.threshold})`,
      slow_response: `响应时间过慢: ${data.duration}ms (阈值: ${data.threshold}ms)`,
      slow_performance: `平均性能过慢: ${data.avgDuration} (阈值: ${data.threshold})`
    }
    
    return messageMap[alertType] || `未知告警类型: ${alertType}`
  }
  
  /**
   * 获取总请求数(模拟)
   */
  getTotalRequests(startTime, endTime) {
    // 这里应该从实际的请求统计中获取
    // 暂时返回一个估算值
    return Math.max(this.metrics.performance.filter(p => 
      p.timestamp > startTime && p.timestamp < endTime
    ).length, 1)
  }
  
  /**
   * 清理旧的指标数据
   */
  cleanOldMetrics() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000) // 保留24小时
    
    this.metrics.errors = this.metrics.errors.filter(error => error.timestamp > cutoff)
    this.metrics.performance = this.metrics.performance.filter(perf => perf.timestamp > cutoff)
    this.metrics.network = this.metrics.network.filter(network => network.timestamp > cutoff)
    
    // 清理旧的告警记录
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoff)
  }
  
  /**
   * 获取告警统计
   */
  getAlertStats() {
    const now = Date.now()
    const last24h = now - (24 * 60 * 60 * 1000)
    
    const recentAlerts = this.alerts.filter(alert => alert.timestamp > last24h)
    
    return {
      total: recentAlerts.length,
      critical: recentAlerts.filter(a => a.level === 'critical').length,
      high: recentAlerts.filter(a => a.level === 'high').length,
      medium: recentAlerts.filter(a => a.level === 'medium').length,
      low: recentAlerts.filter(a => a.level === 'low').length,
      byType: recentAlerts.reduce((acc, alert) => {
        acc[alert.type] = (acc[alert.type] || 0) + 1
        return acc
      }, {})
    }
  }
  
  /**
   * 销毁告警管理器
   */
  destroy() {
    this.alerts = []
    this.metrics = { errors: [], performance: [], network: [] }
    this.lastAlertTimes.clear()
    
    console.log('[AlertManager] 告警管理器已停止')
  }
}

// 创建全局实例
const alertManager = new AlertManager()

export default alertManager
