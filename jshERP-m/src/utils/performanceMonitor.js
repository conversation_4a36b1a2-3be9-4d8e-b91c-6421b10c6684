/**
 * jshERP移动端性能监控工具
 * 用于收集和上报前端性能数据
 */

class PerformanceMonitor {
  constructor(options = {}) {
    this.options = {
      // 是否启用监控
      enabled: process.env.VUE_APP_PERFORMANCE_ENABLE === 'true',
      // 采样率
      sampleRate: parseFloat(process.env.VUE_APP_PERFORMANCE_SAMPLE_RATE) || 0.1,
      // 上报地址
      reportUrl: process.env.VUE_APP_PERFORMANCE_REPORT_URL || '',
      // 批量上报大小
      batchSize: 10,
      // 上报间隔(ms)
      reportInterval: 30000,
      ...options
    }
    
    this.metrics = []
    this.timers = new Map()
    this.observers = []
    
    if (this.options.enabled) {
      this.init()
    }
  }
  
  /**
   * 初始化监控
   */
  init() {
    // 页面加载性能
    this.observePageLoad()
    
    // 资源加载性能
    this.observeResourceLoad()
    
    // 用户交互性能
    this.observeUserInteraction()
    
    // 内存使用监控
    this.observeMemoryUsage()
    
    // 网络状态监控
    this.observeNetworkStatus()
    
    // 定时上报
    this.startReporting()
    
    console.log('[PerformanceMonitor] 性能监控已启动')
  }
  
  /**
   * 页面加载性能监控
   */
  observePageLoad() {
    if (!window.performance) return
    
    window.addEventListener('load', () => {
      setTimeout(() => {
        const timing = performance.timing
        const navigation = performance.navigation
        
        const metrics = {
          type: 'page_load',
          timestamp: Date.now(),
          data: {
            // DNS查询时间
            dnsTime: timing.domainLookupEnd - timing.domainLookupStart,
            // TCP连接时间
            tcpTime: timing.connectEnd - timing.connectStart,
            // 请求响应时间
            requestTime: timing.responseEnd - timing.requestStart,
            // DOM解析时间
            domParseTime: timing.domContentLoadedEventEnd - timing.domLoading,
            // 页面完全加载时间
            loadTime: timing.loadEventEnd - timing.navigationStart,
            // 首次内容绘制时间
            fcp: this.getFCP(),
            // 最大内容绘制时间
            lcp: this.getLCP(),
            // 导航类型
            navigationType: navigation.type,
            // 重定向次数
            redirectCount: navigation.redirectCount
          }
        }
        
        this.addMetric(metrics)
      }, 0)
    })
  }
  
  /**
   * 资源加载性能监控
   */
  observeResourceLoad() {
    if (!window.PerformanceObserver) return
    
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach(entry => {
        if (entry.entryType === 'resource') {
          const metrics = {
            type: 'resource_load',
            timestamp: Date.now(),
            data: {
              name: entry.name,
              duration: entry.duration,
              size: entry.transferSize || 0,
              type: this.getResourceType(entry.name),
              startTime: entry.startTime
            }
          }
          
          this.addMetric(metrics)
        }
      })
    })
    
    observer.observe({ entryTypes: ['resource'] })
    this.observers.push(observer)
  }
  
  /**
   * 用户交互性能监控
   */
  observeUserInteraction() {
    // 点击响应时间
    document.addEventListener('click', (event) => {
      const startTime = performance.now()
      
      requestAnimationFrame(() => {
        const endTime = performance.now()
        const metrics = {
          type: 'user_interaction',
          timestamp: Date.now(),
          data: {
            action: 'click',
            target: event.target.tagName,
            duration: endTime - startTime,
            x: event.clientX,
            y: event.clientY
          }
        }
        
        this.addMetric(metrics)
      })
    })
    
    // 路由切换时间
    if (window.Vue && window.Vue.$router) {
      window.Vue.$router.beforeEach((to, from, next) => {
        this.startTimer('route_change')
        next()
      })
      
      window.Vue.$router.afterEach(() => {
        const duration = this.endTimer('route_change')
        if (duration) {
          const metrics = {
            type: 'route_change',
            timestamp: Date.now(),
            data: {
              duration: duration,
              from: window.Vue.$router.currentRoute.path,
              to: window.Vue.$router.currentRoute.path
            }
          }
          
          this.addMetric(metrics)
        }
      })
    }
  }
  
  /**
   * 内存使用监控
   */
  observeMemoryUsage() {
    if (!performance.memory) return
    
    setInterval(() => {
      const memory = performance.memory
      const metrics = {
        type: 'memory_usage',
        timestamp: Date.now(),
        data: {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          usagePercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)
        }
      }
      
      this.addMetric(metrics)
    }, 60000) // 每分钟检查一次
  }
  
  /**
   * 网络状态监控
   */
  observeNetworkStatus() {
    if (!navigator.connection) return
    
    const connection = navigator.connection
    
    const reportNetworkStatus = () => {
      const metrics = {
        type: 'network_status',
        timestamp: Date.now(),
        data: {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData
        }
      }
      
      this.addMetric(metrics)
    }
    
    // 初始状态
    reportNetworkStatus()
    
    // 网络状态变化
    connection.addEventListener('change', reportNetworkStatus)
  }
  
  /**
   * 开始计时
   */
  startTimer(name) {
    this.timers.set(name, performance.now())
  }
  
  /**
   * 结束计时
   */
  endTimer(name) {
    const startTime = this.timers.get(name)
    if (startTime) {
      this.timers.delete(name)
      return performance.now() - startTime
    }
    return null
  }
  
  /**
   * 添加性能指标
   */
  addMetric(metric) {
    // 采样控制
    if (Math.random() > this.options.sampleRate) {
      return
    }
    
    this.metrics.push(metric)
    
    // 达到批量大小时立即上报
    if (this.metrics.length >= this.options.batchSize) {
      this.report()
    }
  }
  
  /**
   * 开始定时上报
   */
  startReporting() {
    setInterval(() => {
      if (this.metrics.length > 0) {
        this.report()
      }
    }, this.options.reportInterval)
  }
  
  /**
   * 上报性能数据
   */
  async report() {
    if (!this.options.reportUrl || this.metrics.length === 0) {
      return
    }
    
    const data = {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      metrics: [...this.metrics]
    }
    
    // 清空待上报数据
    this.metrics = []
    
    try {
      await fetch(this.options.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
      
      console.log('[PerformanceMonitor] 性能数据上报成功')
    } catch (error) {
      console.error('[PerformanceMonitor] 性能数据上报失败:', error)
    }
  }
  
  /**
   * 获取首次内容绘制时间
   */
  getFCP() {
    const entries = performance.getEntriesByType('paint')
    const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : 0
  }
  
  /**
   * 获取最大内容绘制时间
   */
  getLCP() {
    return new Promise((resolve) => {
      if (!window.PerformanceObserver) {
        resolve(0)
        return
      }
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        resolve(lastEntry.startTime)
        observer.disconnect()
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
      
      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(0)
      }, 5000)
    })
  }
  
  /**
   * 获取资源类型
   */
  getResourceType(url) {
    if (url.includes('.js')) return 'script'
    if (url.includes('.css')) return 'stylesheet'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image'
    if (url.includes('/api/')) return 'api'
    return 'other'
  }
  
  /**
   * 销毁监控
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.timers.clear()
    this.metrics = []
    
    console.log('[PerformanceMonitor] 性能监控已停止')
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

export default performanceMonitor
