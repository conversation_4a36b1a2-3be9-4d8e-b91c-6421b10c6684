/**
 * 缓存管理工具
 * 提供多层级缓存机制，优化移动端性能
 */

/**
 * 内存缓存类
 */
class MemoryCache {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.accessTimes = new Map()
  }
  
  set(key, value, ttl = 0) {
    // 如果缓存已满，删除最久未访问的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }
    
    const item = {
      value,
      timestamp: Date.now(),
      ttl: ttl > 0 ? Date.now() + ttl : 0
    }
    
    this.cache.set(key, item)
    this.accessTimes.set(key, Date.now())
  }
  
  get(key) {
    const item = this.cache.get(key)
    if (!item) return null
    
    // 检查是否过期
    if (item.ttl > 0 && Date.now() > item.ttl) {
      this.delete(key)
      return null
    }
    
    // 更新访问时间
    this.accessTimes.set(key, Date.now())
    return item.value
  }
  
  has(key) {
    return this.cache.has(key) && this.get(key) !== null
  }
  
  delete(key) {
    this.cache.delete(key)
    this.accessTimes.delete(key)
  }
  
  clear() {
    this.cache.clear()
    this.accessTimes.clear()
  }
  
  // LRU淘汰策略
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()
    
    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.delete(oldestKey)
    }
  }
  
  size() {
    return this.cache.size
  }
}

/**
 * 本地存储缓存类
 */
class LocalStorageCache {
  constructor(prefix = 'jsherp_mobile_') {
    this.prefix = prefix
    this.isSupported = this.checkSupport()
  }
  
  checkSupport() {
    try {
      const testKey = '__test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch (e) {
      return false
    }
  }
  
  getKey(key) {
    return this.prefix + key
  }
  
  set(key, value, ttl = 0) {
    if (!this.isSupported) return false
    
    try {
      const item = {
        value,
        timestamp: Date.now(),
        ttl: ttl > 0 ? Date.now() + ttl : 0
      }
      
      localStorage.setItem(this.getKey(key), JSON.stringify(item))
      return true
    } catch (e) {
      console.warn('LocalStorage set failed:', e)
      return false
    }
  }
  
  get(key) {
    if (!this.isSupported) return null
    
    try {
      const itemStr = localStorage.getItem(this.getKey(key))
      if (!itemStr) return null
      
      const item = JSON.parse(itemStr)
      
      // 检查是否过期
      if (item.ttl > 0 && Date.now() > item.ttl) {
        this.delete(key)
        return null
      }
      
      return item.value
    } catch (e) {
      console.warn('LocalStorage get failed:', e)
      return null
    }
  }
  
  has(key) {
    return this.get(key) !== null
  }
  
  delete(key) {
    if (!this.isSupported) return false
    
    try {
      localStorage.removeItem(this.getKey(key))
      return true
    } catch (e) {
      console.warn('LocalStorage delete failed:', e)
      return false
    }
  }
  
  clear() {
    if (!this.isSupported) return false
    
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key)
        }
      })
      return true
    } catch (e) {
      console.warn('LocalStorage clear failed:', e)
      return false
    }
  }
  
  size() {
    if (!this.isSupported) return 0
    
    let count = 0
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        count++
      }
    })
    return count
  }
}

/**
 * 多层级缓存管理器
 */
class CacheManager {
  constructor(options = {}) {
    this.memoryCache = new MemoryCache(options.memoryMaxSize || 100)
    this.localStorageCache = new LocalStorageCache(options.prefix || 'jsherp_mobile_')
    
    // 缓存策略配置
    this.strategies = {
      // 内存优先策略
      memory: {
        get: (key) => this.memoryCache.get(key),
        set: (key, value, ttl) => this.memoryCache.set(key, value, ttl)
      },
      
      // 本地存储优先策略
      localStorage: {
        get: (key) => this.localStorageCache.get(key),
        set: (key, value, ttl) => this.localStorageCache.set(key, value, ttl)
      },
      
      // 多层级策略
      multi: {
        get: (key) => {
          // 先从内存缓存获取
          let value = this.memoryCache.get(key)
          if (value !== null) return value
          
          // 再从本地存储获取
          value = this.localStorageCache.get(key)
          if (value !== null) {
            // 将数据提升到内存缓存
            this.memoryCache.set(key, value)
            return value
          }
          
          return null
        },
        set: (key, value, ttl) => {
          // 同时存储到内存和本地存储
          this.memoryCache.set(key, value, ttl)
          this.localStorageCache.set(key, value, ttl)
        }
      }
    }
    
    this.defaultStrategy = options.strategy || 'multi'
  }
  
  /**
   * 设置缓存
   */
  set(key, value, options = {}) {
    const strategy = options.strategy || this.defaultStrategy
    const ttl = options.ttl || 0
    
    if (this.strategies[strategy]) {
      return this.strategies[strategy].set(key, value, ttl)
    }
    
    throw new Error(`Unknown cache strategy: ${strategy}`)
  }
  
  /**
   * 获取缓存
   */
  get(key, options = {}) {
    const strategy = options.strategy || this.defaultStrategy
    
    if (this.strategies[strategy]) {
      return this.strategies[strategy].get(key)
    }
    
    throw new Error(`Unknown cache strategy: ${strategy}`)
  }
  
  /**
   * 检查缓存是否存在
   */
  has(key, options = {}) {
    return this.get(key, options) !== null
  }
  
  /**
   * 删除缓存
   */
  delete(key) {
    this.memoryCache.delete(key)
    this.localStorageCache.delete(key)
  }
  
  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear()
    this.localStorageCache.clear()
  }
  
  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      memory: {
        size: this.memoryCache.size(),
        maxSize: this.memoryCache.maxSize
      },
      localStorage: {
        size: this.localStorageCache.size(),
        supported: this.localStorageCache.isSupported
      }
    }
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager()

/**
 * API响应缓存装饰器
 */
export function cacheResponse(key, ttl = 300000) { // 默认5分钟
  return function(target, propertyName, descriptor) {
    const method = descriptor.value
    
    descriptor.value = async function(...args) {
      const cacheKey = `api_${key}_${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cached = cacheManager.get(cacheKey)
      if (cached !== null) {
        return cached
      }
      
      // 执行原方法
      try {
        const result = await method.apply(this, args)
        
        // 缓存结果
        cacheManager.set(cacheKey, result, { ttl })
        
        return result
      } catch (error) {
        throw error
      }
    }
    
    return descriptor
  }
}

/**
 * 页面数据缓存工具
 */
export class PageCache {
  constructor(pageName) {
    this.pageName = pageName
    this.keyPrefix = `page_${pageName}_`
  }
  
  // 缓存页面状态
  saveState(state) {
    const key = this.keyPrefix + 'state'
    cacheManager.set(key, state, { ttl: 1800000 }) // 30分钟
  }
  
  // 恢复页面状态
  restoreState() {
    const key = this.keyPrefix + 'state'
    return cacheManager.get(key)
  }
  
  // 缓存列表数据
  saveListData(data, page = 1) {
    const key = this.keyPrefix + `list_${page}`
    cacheManager.set(key, data, { ttl: 300000 }) // 5分钟
  }
  
  // 获取列表数据
  getListData(page = 1) {
    const key = this.keyPrefix + `list_${page}`
    return cacheManager.get(key)
  }
  
  // 缓存详情数据
  saveDetailData(id, data) {
    const key = this.keyPrefix + `detail_${id}`
    cacheManager.set(key, data, { ttl: 600000 }) // 10分钟
  }
  
  // 获取详情数据
  getDetailData(id) {
    const key = this.keyPrefix + `detail_${id}`
    return cacheManager.get(key)
  }
  
  // 清除页面缓存
  clear() {
    // 这里需要遍历所有缓存键，删除匹配的项
    // 由于当前实现的限制，我们只能清除已知的键
    const keys = ['state', 'list_1', 'list_2', 'list_3'] // 可以扩展
    keys.forEach(key => {
      cacheManager.delete(this.keyPrefix + key)
    })
  }
}

// 导出工具函数
export const setCache = (key, value, options) => cacheManager.set(key, value, options)
export const getCache = (key, options) => cacheManager.get(key, options)
export const hasCache = (key, options) => cacheManager.has(key, options)
export const deleteCache = (key) => cacheManager.delete(key)
export const clearCache = () => cacheManager.clear()
export const getCacheStats = () => cacheManager.getStats()

export default cacheManager
