/**
 * 全局错误处理系统
 * 提供统一的错误捕获、处理和报告机制
 */

import { message, notification } from 'ant-design-vue'
import performanceMonitor from './performance'

class ErrorHandler {
  constructor() {
    this.errors = []
    this.maxErrors = 100
    this.errorCallbacks = []
    this.isInitialized = false
    
    // 错误类型配置
    this.errorTypes = {
      JS_ERROR: 'js_error',
      PROMISE_REJECTION: 'promise_rejection',
      NETWORK_ERROR: 'network_error',
      API_ERROR: 'api_error',
      COMPONENT_ERROR: 'component_error',
      RESOURCE_ERROR: 'resource_error',
      CUSTOM_ERROR: 'custom_error'
    }
    
    // 错误级别
    this.errorLevels = {
      LOW: 'low',
      MEDIUM: 'medium',
      HIGH: 'high',
      CRITICAL: 'critical'
    }
    
    // 用户友好的错误消息映射
    this.friendlyMessages = {
      'Network Error': '网络连接失败，请检查网络设置',
      'timeout': '请求超时，请稍后重试',
      'Unauthorized': '登录已过期，请重新登录',
      'Forbidden': '权限不足，无法访问该资源',
      'Not Found': '请求的资源不存在',
      'Internal Server Error': '服务器内部错误，请联系管理员',
      'Bad Gateway': '网关错误，请稍后重试',
      'Service Unavailable': '服务暂时不可用，请稍后重试'
    }
  }
  
  /**
   * 初始化错误处理系统
   */
  init() {
    if (this.isInitialized) return
    
    this.setupGlobalErrorHandlers()
    this.setupVueErrorHandler()
    this.setupNetworkErrorHandler()
    this.setupResourceErrorHandler()
    
    this.isInitialized = true
    console.log('错误处理系统已初始化')
  }
  
  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // JavaScript运行时错误
    window.addEventListener('error', (event) => {
      this.handleError({
        type: this.errorTypes.JS_ERROR,
        level: this.errorLevels.HIGH,
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error ? event.error.stack : null,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    })
    
    // Promise未捕获的拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: this.errorTypes.PROMISE_REJECTION,
        level: this.errorLevels.MEDIUM,
        message: event.reason?.message || event.reason || 'Promise rejection',
        stack: event.reason?.stack,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
      
      // 阻止默认的控制台错误输出
      event.preventDefault()
    })
  }
  
  /**
   * 设置Vue错误处理器（内部方法）
   */
  setupVueErrorHandler() {
    // 这个方法在init中调用，实际的Vue配置在导出的函数中
    console.log('Vue错误处理器准备就绪')
  }
  
  /**
   * 设置网络错误处理器
   */
  setupNetworkErrorHandler() {
    // 拦截fetch请求
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args)
        
        // 检查HTTP状态码
        if (!response.ok) {
          this.handleError({
            type: this.errorTypes.NETWORK_ERROR,
            level: this.getNetworkErrorLevel(response.status),
            message: `HTTP ${response.status}: ${response.statusText}`,
            url: args[0],
            status: response.status,
            statusText: response.statusText,
            timestamp: Date.now()
          })
        }
        
        return response
      } catch (error) {
        this.handleError({
          type: this.errorTypes.NETWORK_ERROR,
          level: this.errorLevels.HIGH,
          message: error.message,
          url: args[0],
          timestamp: Date.now(),
          stack: error.stack
        })
        throw error
      }
    }
    
    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open
    const originalXHRSend = XMLHttpRequest.prototype.send
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      this._errorHandler_method = method
      this._errorHandler_url = url
      return originalXHROpen.call(this, method, url, ...args)
    }
    
    XMLHttpRequest.prototype.send = function(...args) {
      this.addEventListener('error', () => {
        ErrorHandler.getInstance().handleError({
          type: ErrorHandler.getInstance().errorTypes.NETWORK_ERROR,
          level: ErrorHandler.getInstance().errorLevels.HIGH,
          message: 'XMLHttpRequest failed',
          method: this._errorHandler_method,
          url: this._errorHandler_url,
          timestamp: Date.now()
        })
      })
      
      this.addEventListener('timeout', () => {
        ErrorHandler.getInstance().handleError({
          type: ErrorHandler.getInstance().errorTypes.NETWORK_ERROR,
          level: ErrorHandler.getInstance().errorLevels.MEDIUM,
          message: 'XMLHttpRequest timeout',
          method: this._errorHandler_method,
          url: this._errorHandler_url,
          timestamp: Date.now()
        })
      })
      
      return originalXHRSend.call(this, ...args)
    }
  }
  
  /**
   * 设置资源加载错误处理器
   */
  setupResourceErrorHandler() {
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        // 资源加载错误
        this.handleError({
          type: this.errorTypes.RESOURCE_ERROR,
          level: this.errorLevels.MEDIUM,
          message: `Failed to load resource: ${event.target.src || event.target.href}`,
          element: event.target.tagName,
          src: event.target.src || event.target.href,
          timestamp: Date.now()
        })
      }
    }, true)
  }
  
  /**
   * 处理错误
   */
  handleError(errorInfo) {
    // 添加唯一ID
    errorInfo.id = this.generateErrorId()
    
    // 记录性能影响
    performanceMonitor.recordMetric('error_occurred', {
      type: errorInfo.type,
      level: errorInfo.level,
      timestamp: errorInfo.timestamp
    })
    
    // 存储错误
    this.storeError(errorInfo)
    
    // 显示用户友好的错误提示
    this.showUserFriendlyError(errorInfo)
    
    // 触发错误回调
    this.triggerErrorCallbacks(errorInfo)
    
    // 上报错误（如果配置了）
    this.reportError(errorInfo)
    
    // 开发环境下输出详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 ${errorInfo.type.toUpperCase()} - ${errorInfo.level.toUpperCase()}`)
      console.error('Message:', errorInfo.message)
      console.error('Details:', errorInfo)
      if (errorInfo.stack) {
        console.error('Stack:', errorInfo.stack)
      }
      console.groupEnd()
    }
  }
  
  /**
   * 存储错误
   */
  storeError(errorInfo) {
    this.errors.unshift(errorInfo)
    
    // 限制错误数量
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors)
    }
    
    // 存储到本地存储（可选）
    try {
      const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]')
      storedErrors.unshift(errorInfo)
      localStorage.setItem('app_errors', JSON.stringify(storedErrors.slice(0, 50)))
    } catch (e) {
      // 忽略存储错误
    }
  }
  
  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyError(errorInfo) {
    const friendlyMessage = this.getFriendlyMessage(errorInfo)
    
    switch (errorInfo.level) {
      case this.errorLevels.CRITICAL:
        notification.error({
          message: '严重错误',
          description: friendlyMessage,
          duration: 0, // 不自动关闭
          key: errorInfo.id
        })
        break
        
      case this.errorLevels.HIGH:
        notification.error({
          message: '错误',
          description: friendlyMessage,
          duration: 8,
          key: errorInfo.id
        })
        break
        
      case this.errorLevels.MEDIUM:
        message.error(friendlyMessage, 5)
        break
        
      case this.errorLevels.LOW:
        // 低级别错误不显示给用户
        break
    }
  }
  
  /**
   * 获取用户友好的错误消息
   */
  getFriendlyMessage(errorInfo) {
    // 检查是否有预定义的友好消息
    for (const [key, friendlyMsg] of Object.entries(this.friendlyMessages)) {
      if (errorInfo.message.includes(key)) {
        return friendlyMsg
      }
    }
    
    // 根据错误类型返回通用消息
    switch (errorInfo.type) {
      case this.errorTypes.NETWORK_ERROR:
        return '网络请求失败，请检查网络连接'
      case this.errorTypes.API_ERROR:
        return '服务器响应异常，请稍后重试'
      case this.errorTypes.COMPONENT_ERROR:
        return '页面组件出现异常，请刷新页面'
      case this.errorTypes.RESOURCE_ERROR:
        return '资源加载失败，请刷新页面'
      default:
        return '应用出现异常，请稍后重试'
    }
  }
  
  /**
   * 获取网络错误级别
   */
  getNetworkErrorLevel(status) {
    if (status >= 500) return this.errorLevels.HIGH
    if (status >= 400) return this.errorLevels.MEDIUM
    return this.errorLevels.LOW
  }
  
  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 触发错误回调
   */
  triggerErrorCallbacks(errorInfo) {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorInfo)
      } catch (e) {
        console.error('Error callback failed:', e)
      }
    })
  }
  
  /**
   * 上报错误
   */
  reportError(errorInfo) {
    // 这里可以实现错误上报逻辑
    // 例如发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 实际的错误上报逻辑
      console.log('报告错误到监控服务:', errorInfo)
    }
  }
  
  /**
   * 手动报告错误
   */
  reportCustomError(message, details = {}) {
    this.handleError({
      type: this.errorTypes.CUSTOM_ERROR,
      level: details.level || this.errorLevels.MEDIUM,
      message,
      ...details,
      timestamp: Date.now()
    })
  }
  
  /**
   * 报告API错误
   */
  reportApiError(error, requestInfo = {}) {
    this.handleError({
      type: this.errorTypes.API_ERROR,
      level: this.errorLevels.HIGH,
      message: error.message || 'API request failed',
      status: error.status,
      statusText: error.statusText,
      url: requestInfo.url,
      method: requestInfo.method,
      data: requestInfo.data,
      timestamp: Date.now(),
      stack: error.stack
    })
  }
  
  /**
   * 添加错误回调
   */
  onError(callback) {
    this.errorCallbacks.push(callback)
  }
  
  /**
   * 移除错误回调
   */
  offError(callback) {
    const index = this.errorCallbacks.indexOf(callback)
    if (index > -1) {
      this.errorCallbacks.splice(index, 1)
    }
  }
  
  /**
   * 获取错误列表
   */
  getErrors(filter = {}) {
    let filteredErrors = this.errors
    
    if (filter.type) {
      filteredErrors = filteredErrors.filter(error => error.type === filter.type)
    }
    
    if (filter.level) {
      filteredErrors = filteredErrors.filter(error => error.level === filter.level)
    }
    
    if (filter.since) {
      filteredErrors = filteredErrors.filter(error => error.timestamp >= filter.since)
    }
    
    return filteredErrors
  }
  
  /**
   * 清除错误
   */
  clearErrors() {
    this.errors = []
    localStorage.removeItem('app_errors')
  }
  
  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      byLevel: {},
      recent: this.errors.filter(error => 
        Date.now() - error.timestamp < 24 * 60 * 60 * 1000
      ).length
    }
    
    // 按类型统计
    Object.values(this.errorTypes).forEach(type => {
      stats.byType[type] = this.errors.filter(error => error.type === type).length
    })
    
    // 按级别统计
    Object.values(this.errorLevels).forEach(level => {
      stats.byLevel[level] = this.errors.filter(error => error.level === level).length
    })
    
    return stats
  }
  
  /**
   * 导出错误报告
   */
  exportErrorReport() {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      stats: this.getErrorStats(),
      errors: this.errors.slice(0, 50), // 只导出最近50个错误
      systemInfo: {
        memory: performance.memory ? {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        } : null,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        online: navigator.onLine
      }
    }
    
    return report
  }
  
  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }
}

// 创建全局实例
const errorHandler = ErrorHandler.getInstance()

// Vue错误处理器配置函数
export function setupVueErrorHandler(Vue) {
  Vue.config.errorHandler = (err, vm, info) => {
    errorHandler.handleError({
      type: errorHandler.errorTypes.COMPONENT_ERROR,
      level: errorHandler.errorLevels.HIGH,
      message: err.message,
      componentInfo: info,
      componentName: vm?.$options?.name || 'Unknown',
      stack: err.stack,
      timestamp: Date.now()
    })
  }
}

// 导出错误处理器实例和相关工具
export default errorHandler
export { ErrorHandler }
