/**
 * 响应式工具函数库
 * 提供响应式设计相关的工具函数和帮助方法
 */

import mobileAdapter from './mobile-adapter'

/**
 * 断点工具类
 */
export class BreakpointUtils {
  static breakpoints = {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
  }

  static breakpointNames = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']

  /**
   * 获取当前断点
   */
  static getCurrentBreakpoint() {
    return mobileAdapter.getCurrentBreakpoint()
  }

  /**
   * 检查是否匹配断点
   */
  static matches(condition) {
    const current = this.getCurrentBreakpoint()
    const currentIndex = this.breakpointNames.indexOf(current)

    if (Array.isArray(condition)) {
      return condition.includes(current)
    }

    if (typeof condition === 'string') {
      if (condition.includes('+')) {
        const bp = condition.replace('+', '')
        const targetIndex = this.breakpointNames.indexOf(bp)
        return currentIndex >= targetIndex
      } else if (condition.includes('-') && !condition.includes('+')) {
        if (condition.endsWith('-')) {
          const bp = condition.replace('-', '')
          const targetIndex = this.breakpointNames.indexOf(bp)
          return currentIndex <= targetIndex
        } else {
          const [start, end] = condition.split('-')
          const startIndex = this.breakpointNames.indexOf(start)
          const endIndex = this.breakpointNames.indexOf(end)
          return currentIndex >= startIndex && currentIndex <= endIndex
        }
      } else {
        return current === condition
      }
    }

    return false
  }

  /**
   * 获取断点对应的像素值
   */
  static getBreakpointValue(breakpoint) {
    return this.breakpoints[breakpoint] || 0
  }

  /**
   * 根据宽度获取断点
   */
  static getBreakpointFromWidth(width) {
    if (width < this.breakpoints.sm) return 'xs'
    if (width < this.breakpoints.md) return 'sm'
    if (width < this.breakpoints.lg) return 'md'
    if (width < this.breakpoints.xl) return 'lg'
    if (width < this.breakpoints.xxl) return 'xl'
    return 'xxl'
  }
}

/**
 * 响应式值处理器
 */
export class ResponsiveValue {
  constructor(config) {
    this.config = config
  }

  /**
   * 获取当前断点对应的值
   */
  getValue(fallback = null) {
    const currentBreakpoint = BreakpointUtils.getCurrentBreakpoint()
    const breakpoints = BreakpointUtils.breakpointNames
    const currentIndex = breakpoints.indexOf(currentBreakpoint)

    // 从当前断点向下查找可用值
    for (let i = currentIndex; i >= 0; i--) {
      const bp = breakpoints[i]
      if (this.config[bp] !== undefined) {
        return this.config[bp]
      }
    }

    return fallback
  }

  /**
   * 获取指定断点的值
   */
  getValueAt(breakpoint, fallback = null) {
    return this.config[breakpoint] !== undefined ? this.config[breakpoint] : fallback
  }

  /**
   * 设置断点值
   */
  setValue(breakpoint, value) {
    this.config[breakpoint] = value
    return this
  }

  /**
   * 批量设置值
   */
  setValues(config) {
    Object.assign(this.config, config)
    return this
  }

  /**
   * 创建CSS媒体查询
   */
  toCSSMediaQuery() {
    const rules = []
    const breakpoints = BreakpointUtils.breakpoints

    Object.keys(this.config).forEach(bp => {
      const value = this.config[bp]
      const minWidth = breakpoints[bp]
      
      if (minWidth > 0) {
        rules.push(`@media (min-width: ${minWidth}px) { /* ${bp}: ${value} */ }`)
      } else {
        rules.push(`/* ${bp}: ${value} */`)
      }
    })

    return rules.join('\n')
  }
}

/**
 * 响应式样式生成器
 */
export class ResponsiveStyleGenerator {
  /**
   * 生成响应式样式对象
   */
  static generate(config) {
    const result = {}
    
    Object.keys(config).forEach(property => {
      const value = config[property]
      
      if (value instanceof ResponsiveValue) {
        result[property] = value.getValue()
      } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        const responsiveValue = new ResponsiveValue(value)
        result[property] = responsiveValue.getValue()
      } else {
        result[property] = value
      }
    })
    
    return result
  }

  /**
   * 生成响应式类名
   */
  static generateClasses(config) {
    const result = []
    
    Object.keys(config).forEach(className => {
      const condition = config[className]
      
      if (typeof condition === 'boolean') {
        if (condition) result.push(className)
      } else if (typeof condition === 'string') {
        if (BreakpointUtils.matches(condition)) {
          result.push(className)
        }
      } else if (Array.isArray(condition)) {
        if (BreakpointUtils.matches(condition)) {
          result.push(className)
        }
      } else if (typeof condition === 'function') {
        if (condition(mobileAdapter)) {
          result.push(className)
        }
      }
    })
    
    return result
  }
}

/**
 * 设备检测工具
 */
export class DeviceUtils {
  /**
   * 检查是否为移动设备
   */
  static isMobile() {
    return mobileAdapter.isMobile()
  }

  /**
   * 检查是否为平板设备
   */
  static isTablet() {
    return mobileAdapter.isTablet()
  }

  /**
   * 检查是否为桌面设备
   */
  static isDesktop() {
    return mobileAdapter.isDesktop()
  }

  /**
   * 检查是否为iOS设备
   */
  static isIOS() {
    return mobileAdapter.isIOS()
  }

  /**
   * 检查是否为Android设备
   */
  static isAndroid() {
    return mobileAdapter.isAndroid()
  }

  /**
   * 检查是否支持触摸
   */
  static isTouch() {
    return mobileAdapter.isTouch()
  }

  /**
   * 检查是否为Retina屏幕
   */
  static isRetina() {
    return mobileAdapter.isRetina()
  }

  /**
   * 获取设备信息
   */
  static getDeviceInfo() {
    return mobileAdapter.deviceInfo
  }

  /**
   * 获取设备能力
   */
  static getCapabilities() {
    return mobileAdapter.capabilities
  }
}

/**
 * 视口工具
 */
export class ViewportUtils {
  /**
   * 获取视口尺寸
   */
  static getSize() {
    return {
      width: mobileAdapter.viewportConfig.width,
      height: mobileAdapter.viewportConfig.height
    }
  }

  /**
   * 获取可用区域尺寸
   */
  static getAvailableSize() {
    return {
      width: mobileAdapter.viewportConfig.availableWidth,
      height: mobileAdapter.viewportConfig.availableHeight
    }
  }

  /**
   * 获取安全区域
   */
  static getSafeArea() {
    return mobileAdapter.viewportConfig.safeArea
  }

  /**
   * 检查屏幕方向
   */
  static isPortrait() {
    return mobileAdapter.viewportConfig.orientation === 'portrait'
  }

  static isLandscape() {
    return mobileAdapter.viewportConfig.orientation === 'landscape'
  }

  /**
   * 获取屏幕方向
   */
  static getOrientation() {
    return mobileAdapter.viewportConfig.orientation
  }

  /**
   * 检查是否有刘海屏
   */
  static hasNotch() {
    return mobileAdapter.viewportConfig.hasNotch
  }
}

/**
 * 响应式组件配置生成器
 */
export class ComponentConfigGenerator {
  /**
   * 生成表格配置
   */
  static generateTableConfig(customConfig = {}) {
    const baseConfig = mobileAdapter.getComponentConfig('table')
    return { ...baseConfig, ...customConfig }
  }

  /**
   * 生成表单配置
   */
  static generateFormConfig(customConfig = {}) {
    const baseConfig = mobileAdapter.getComponentConfig('form')
    return { ...baseConfig, ...customConfig }
  }

  /**
   * 生成按钮配置
   */
  static generateButtonConfig(customConfig = {}) {
    const baseConfig = mobileAdapter.getComponentConfig('button')
    return { ...baseConfig, ...customConfig }
  }

  /**
   * 生成模态框配置
   */
  static generateModalConfig(customConfig = {}) {
    const baseConfig = mobileAdapter.getComponentConfig('modal')
    return { ...baseConfig, ...customConfig }
  }

  /**
   * 生成抽屉配置
   */
  static generateDrawerConfig(customConfig = {}) {
    const baseConfig = mobileAdapter.getComponentConfig('drawer')
    return { ...baseConfig, ...customConfig }
  }
}

/**
 * 响应式事件管理器
 */
export class ResponsiveEventManager {
  constructor() {
    this.listeners = new Map()
  }

  /**
   * 添加断点变化监听器
   */
  onBreakpointChange(callback) {
    this.addEventListener('breakpointchange', callback)
  }

  /**
   * 添加方向变化监听器
   */
  onOrientationChange(callback) {
    this.addEventListener('orientationchange', callback)
  }

  /**
   * 添加设备变化监听器
   */
  onDeviceChange(callback) {
    this.addEventListener('devicechange', callback)
  }

  /**
   * 添加事件监听器
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event).add(callback)

    // 监听适配器事件
    window.addEventListener(`mobileadapter:${event}`, callback)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback)
    }
    window.removeEventListener(`mobileadapter:${event}`, callback)
  }

  /**
   * 销毁事件管理器
   */
  destroy() {
    this.listeners.forEach((callbacks, event) => {
      callbacks.forEach(callback => {
        window.removeEventListener(`mobileadapter:${event}`, callback)
      })
    })
    this.listeners.clear()
  }
}

// 便捷函数
export const responsive = {
  value: (config) => new ResponsiveValue(config),
  style: (config) => ResponsiveStyleGenerator.generate(config),
  classes: (config) => ResponsiveStyleGenerator.generateClasses(config),
  matches: (condition) => BreakpointUtils.matches(condition),
  breakpoint: () => BreakpointUtils.getCurrentBreakpoint(),
  isMobile: () => DeviceUtils.isMobile(),
  isTablet: () => DeviceUtils.isTablet(),
  isDesktop: () => DeviceUtils.isDesktop(),
  viewport: () => ViewportUtils.getSize(),
  safeArea: () => ViewportUtils.getSafeArea()
}

// 默认导出
export default {
  BreakpointUtils,
  ResponsiveValue,
  ResponsiveStyleGenerator,
  DeviceUtils,
  ViewportUtils,
  ComponentConfigGenerator,
  ResponsiveEventManager,
  responsive
}
