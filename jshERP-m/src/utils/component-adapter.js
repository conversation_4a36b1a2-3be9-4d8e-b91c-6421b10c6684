/**
 * 组件适配系统
 * 为Ant Design Vue组件提供移动端适配功能
 */

import mobileAdapter from './mobile-adapter'
import { ComponentConfigGenerator } from './responsive-utils'

/**
 * 组件适配器基类
 */
class BaseComponentAdapter {
  constructor(componentName) {
    this.componentName = componentName
    this.config = mobileAdapter.getComponentConfig(componentName)
  }

  /**
   * 获取适配后的props
   */
  getAdaptedProps(originalProps = {}) {
    return { ...this.config, ...originalProps }
  }

  /**
   * 获取适配后的样式
   */
  getAdaptedStyle(originalStyle = {}) {
    const styleConfig = mobileAdapter.getStyleConfig()
    const adaptedStyle = { ...originalStyle }

    // 移动端样式优化
    if (mobileAdapter.isMobile()) {
      adaptedStyle.touchAction = 'manipulation'
      adaptedStyle.webkitTapHighlightColor = 'transparent'
    }

    return adaptedStyle
  }

  /**
   * 获取适配后的类名
   */
  getAdaptedClass(originalClass = '') {
    const classes = [originalClass]
    
    if (mobileAdapter.isMobile()) {
      classes.push('mobile-adapted')
      classes.push(`mobile-${this.componentName}`)
    }
    
    return classes.filter(Boolean).join(' ')
  }
}

/**
 * 表格适配器
 */
export class TableAdapter extends BaseComponentAdapter {
  constructor() {
    super('table')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      // 移动端表格优化
      return {
        ...baseProps,
        size: 'small',
        scroll: { x: true },
        pagination: {
          ...baseProps.pagination,
          simple: mobileAdapter.getCurrentBreakpoint() === 'xs',
          showSizeChanger: false,
          showQuickJumper: false,
          pageSize: 10
        },
        // 移动端行选择优化
        rowSelection: originalProps.rowSelection ? {
          ...originalProps.rowSelection,
          columnWidth: 40,
          fixed: true
        } : undefined
      }
    }
    
    return baseProps
  }

  /**
   * 适配表格列配置
   */
  adaptColumns(columns = []) {
    if (!mobileAdapter.isMobile()) {
      return columns
    }

    return columns.map(column => {
      const adaptedColumn = { ...column }
      
      // 移动端列宽优化
      if (!adaptedColumn.width && !adaptedColumn.fixed) {
        adaptedColumn.width = 120
      }
      
      // 移动端省略文本
      if (!adaptedColumn.ellipsis) {
        adaptedColumn.ellipsis = true
      }
      
      // 移动端排序优化
      if (adaptedColumn.sorter && typeof adaptedColumn.sorter === 'boolean') {
        adaptedColumn.showSorterTooltip = false
      }
      
      return adaptedColumn
    })
  }
}

/**
 * 表单适配器
 */
export class FormAdapter extends BaseComponentAdapter {
  constructor() {
    super('form')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseProps,
        layout: 'vertical',
        size: 'large',
        colon: false,
        requiredMark: 'optional',
        scrollToFirstError: true
      }
    }
    
    return baseProps
  }

  /**
   * 适配表单项配置
   */
  adaptFormItem(itemProps = {}) {
    if (!mobileAdapter.isMobile()) {
      return itemProps
    }

    return {
      ...itemProps,
      labelCol: undefined,
      wrapperCol: undefined,
      // 移动端标签样式
      labelAlign: 'left'
    }
  }
}

/**
 * 按钮适配器
 */
export class ButtonAdapter extends BaseComponentAdapter {
  constructor() {
    super('button')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseProps,
        size: 'large',
        block: mobileAdapter.getCurrentBreakpoint() === 'xs' && !originalProps.size
      }
    }
    
    return baseProps
  }

  getAdaptedStyle(originalStyle = {}) {
    const baseStyle = super.getAdaptedStyle(originalStyle)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseStyle,
        minHeight: '44px',
        borderRadius: '8px',
        fontSize: '16px'
      }
    }
    
    return baseStyle
  }
}

/**
 * 输入框适配器
 */
export class InputAdapter extends BaseComponentAdapter {
  constructor() {
    super('input')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseProps,
        size: 'large',
        autoComplete: 'off',
        spellCheck: false
      }
    }
    
    return baseProps
  }

  getAdaptedStyle(originalStyle = {}) {
    const baseStyle = super.getAdaptedStyle(originalStyle)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseStyle,
        minHeight: '44px',
        fontSize: '16px' // 防止iOS缩放
      }
    }
    
    return baseStyle
  }
}

/**
 * 选择器适配器
 */
export class SelectAdapter extends BaseComponentAdapter {
  constructor() {
    super('select')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseProps,
        size: 'large',
        dropdownMatchSelectWidth: false,
        dropdownStyle: {
          maxHeight: '50vh',
          ...originalProps.dropdownStyle
        },
        virtual: true,
        listHeight: 200
      }
    }
    
    return baseProps
  }
}

/**
 * 模态框适配器
 */
export class ModalAdapter extends BaseComponentAdapter {
  constructor() {
    super('modal')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      const breakpoint = mobileAdapter.getCurrentBreakpoint()
      
      return {
        ...baseProps,
        width: breakpoint === 'xs' ? '100%' : '95%',
        centered: true,
        destroyOnClose: true,
        keyboard: false,
        style: breakpoint === 'xs' ? {
          top: 0,
          paddingBottom: 0,
          margin: 0,
          maxWidth: '100vw'
        } : baseProps.style,
        bodyStyle: {
          maxHeight: '70vh',
          overflow: 'auto',
          ...baseProps.bodyStyle
        }
      }
    }
    
    return baseProps
  }
}

/**
 * 抽屉适配器
 */
export class DrawerAdapter extends BaseComponentAdapter {
  constructor() {
    super('drawer')
  }

  getAdaptedProps(originalProps = {}) {
    const baseProps = super.getAdaptedProps(originalProps)
    
    if (mobileAdapter.isMobile()) {
      return {
        ...baseProps,
        width: '100%',
        height: '80%',
        placement: 'bottom',
        destroyOnClose: true,
        keyboard: false,
        maskClosable: false
      }
    }
    
    return baseProps
  }
}

/**
 * 组件适配器工厂
 */
export class ComponentAdapterFactory {
  static adapters = {
    table: TableAdapter,
    form: FormAdapter,
    button: ButtonAdapter,
    input: InputAdapter,
    select: SelectAdapter,
    modal: ModalAdapter,
    drawer: DrawerAdapter
  }

  /**
   * 创建适配器实例
   */
  static create(componentType) {
    const AdapterClass = this.adapters[componentType]
    if (AdapterClass) {
      return new AdapterClass()
    }
    return new BaseComponentAdapter(componentType)
  }

  /**
   * 注册自定义适配器
   */
  static register(componentType, AdapterClass) {
    this.adapters[componentType] = AdapterClass
  }

  /**
   * 获取适配后的组件配置
   */
  static getAdaptedConfig(componentType, originalProps = {}, originalStyle = {}) {
    const adapter = this.create(componentType)
    
    return {
      props: adapter.getAdaptedProps(originalProps),
      style: adapter.getAdaptedStyle(originalStyle),
      class: adapter.getAdaptedClass()
    }
  }
}

/**
 * Vue插件安装函数
 */
export function install(Vue) {
  // 全局混入适配器方法
  Vue.mixin({
    methods: {
      $adaptComponent(componentType, props = {}, style = {}) {
        return ComponentAdapterFactory.getAdaptedConfig(componentType, props, style)
      },
      
      $adaptTable(props = {}) {
        const adapter = ComponentAdapterFactory.create('table')
        return adapter.getAdaptedProps(props)
      },
      
      $adaptForm(props = {}) {
        const adapter = ComponentAdapterFactory.create('form')
        return adapter.getAdaptedProps(props)
      },
      
      $adaptButton(props = {}) {
        const adapter = ComponentAdapterFactory.create('button')
        return adapter.getAdaptedProps(props)
      },
      
      $adaptModal(props = {}) {
        const adapter = ComponentAdapterFactory.create('modal')
        return adapter.getAdaptedProps(props)
      },
      
      $adaptDrawer(props = {}) {
        const adapter = ComponentAdapterFactory.create('drawer')
        return adapter.getAdaptedProps(props)
      }
    }
  })

  // 注册全局属性
  Vue.prototype.$componentAdapter = ComponentAdapterFactory
}

// 默认导出
export default {
  install,
  BaseComponentAdapter,
  TableAdapter,
  FormAdapter,
  ButtonAdapter,
  InputAdapter,
  SelectAdapter,
  ModalAdapter,
  DrawerAdapter,
  ComponentAdapterFactory
}
