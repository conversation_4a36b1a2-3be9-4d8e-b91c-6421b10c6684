/**
 * 移动端样式
 * 专门为移动设备优化的样式文件
 */

// 移动端变量 - 更新为现代化配色
@mobile-primary-color: #3B82F6;  // 现代蓝色
@mobile-success-color: #10B981;  // 现代绿色
@mobile-warning-color: #F97316;  // 现代橙色
@mobile-error-color: #EF4444;    // 现代红色
@mobile-info-color: #3B82F6;     // 现代蓝色

// 移动端间距
@mobile-spacing-xs: 8px;
@mobile-spacing-sm: 12px;
@mobile-spacing-md: 16px;
@mobile-spacing-lg: 24px;
@mobile-spacing-xl: 32px;

// 移动端字体
@mobile-font-size-xs: 12px;
@mobile-font-size-sm: 14px;
@mobile-font-size-md: 16px;
@mobile-font-size-lg: 18px;
@mobile-font-size-xl: 20px;

// 移动端圆角
@mobile-border-radius-sm: 4px;
@mobile-border-radius-md: 6px;
@mobile-border-radius-lg: 8px;

// 移动端设备样式
.mobile-device {
  // 基础样式重置
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  // 输入框可选择文本
  input, textarea {
    -webkit-user-select: text;
    user-select: text;
  }

  // 移动端容器
  .mobile-container {
    padding: @mobile-spacing-md;
    min-height: 100vh;
    background-color: #f5f5f5;
  }

  // 移动端卡片
  .mobile-card {
    background: white;
    border-radius: @mobile-border-radius-lg;
    padding: @mobile-spacing-lg;
    margin-bottom: @mobile-spacing-md;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  // 移动端表单
  .mobile-form {
    .ant-form-item {
      margin-bottom: @mobile-spacing-lg;
    }

    .ant-form-item-label {
      padding-bottom: @mobile-spacing-xs;
      
      label {
        font-size: @mobile-font-size-md;
        font-weight: 500;
      }
    }

    .ant-input,
    .ant-select-selector,
    .ant-picker {
      height: 44px;
      font-size: @mobile-font-size-md;
      border-radius: @mobile-border-radius-md;
    }

    .ant-btn {
      height: 44px;
      font-size: @mobile-font-size-md;
      border-radius: @mobile-border-radius-md;

      &.ant-btn-block {
        margin-top: @mobile-spacing-lg;
      }
    }
  }

  // 移动端表格
  .mobile-table {
    .ant-table {
      font-size: @mobile-font-size-sm;
      
      .ant-table-thead > tr > th {
        padding: @mobile-spacing-sm;
        font-size: @mobile-font-size-sm;
        font-weight: 600;
      }

      .ant-table-tbody > tr > td {
        padding: @mobile-spacing-sm;
        font-size: @mobile-font-size-sm;
      }
    }

    .ant-pagination {
      margin-top: @mobile-spacing-lg;
      text-align: center;
      
      .ant-pagination-item,
      .ant-pagination-prev,
      .ant-pagination-next {
        min-width: 36px;
        height: 36px;
        line-height: 34px;
      }
    }
  }

  // 移动端模态框
  .mobile-modal {
    .ant-modal {
      margin: @mobile-spacing-md;
      max-width: calc(100vw - 32px);
    }

    .ant-modal-content {
      border-radius: @mobile-border-radius-lg;
    }

    .ant-modal-header {
      padding: @mobile-spacing-lg;
      border-radius: @mobile-border-radius-lg @mobile-border-radius-lg 0 0;
      
      .ant-modal-title {
        font-size: @mobile-font-size-lg;
        font-weight: 600;
      }
    }

    .ant-modal-body {
      padding: @mobile-spacing-lg;
    }

    .ant-modal-footer {
      padding: @mobile-spacing-lg;
      border-radius: 0 0 @mobile-border-radius-lg @mobile-border-radius-lg;
      
      .ant-btn {
        height: 44px;
        font-size: @mobile-font-size-md;
        border-radius: @mobile-border-radius-md;
        margin-left: @mobile-spacing-sm;
        
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  // 移动端抽屉
  .mobile-drawer {
    .ant-drawer-content {
      border-radius: @mobile-border-radius-lg @mobile-border-radius-lg 0 0;
    }

    .ant-drawer-header {
      padding: @mobile-spacing-lg;
      
      .ant-drawer-title {
        font-size: @mobile-font-size-lg;
        font-weight: 600;
      }
    }

    .ant-drawer-body {
      padding: @mobile-spacing-lg;
    }
  }

  // 移动端列表
  .mobile-list {
    .ant-list-item {
      padding: @mobile-spacing-md;
      border-radius: @mobile-border-radius-md;
      margin-bottom: @mobile-spacing-sm;
      background: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .ant-list-item-meta-title {
      font-size: @mobile-font-size-md;
      font-weight: 500;
    }

    .ant-list-item-meta-description {
      font-size: @mobile-font-size-sm;
      color: #666;
    }
  }

  // 移动端标签页
  .mobile-tabs {
    .ant-tabs-tab {
      padding: @mobile-spacing-md @mobile-spacing-lg;
      font-size: @mobile-font-size-md;
    }

    .ant-tabs-content-holder {
      padding: @mobile-spacing-lg 0;
    }
  }

  // 移动端步骤条
  .mobile-steps {
    .ant-steps-item-title {
      font-size: @mobile-font-size-sm;
    }

    .ant-steps-item-description {
      font-size: @mobile-font-size-xs;
    }
  }

  // 移动端消息提示
  .mobile-message {
    .ant-message-notice-content {
      padding: @mobile-spacing-md @mobile-spacing-lg;
      font-size: @mobile-font-size-md;
      border-radius: @mobile-border-radius-lg;
    }
  }

  // 移动端通知
  .mobile-notification {
    .ant-notification-notice {
      margin-bottom: @mobile-spacing-md;
      border-radius: @mobile-border-radius-lg;
    }

    .ant-notification-notice-message {
      font-size: @mobile-font-size-md;
      font-weight: 600;
    }

    .ant-notification-notice-description {
      font-size: @mobile-font-size-sm;
    }
  }
}

// 移动端响应式断点
@media (max-width: 768px) {
  // 隐藏桌面端元素
  .desktop-only {
    display: none !important;
  }

  // 显示移动端元素
  .mobile-only {
    display: block !important;
  }

  // 移动端布局调整
  .ant-layout-sider {
    position: fixed !important;
    z-index: 1000;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.ant-layout-sider-collapsed {
      transform: translateX(0);
    }
  }

  .ant-layout-content {
    margin-left: 0 !important;
    padding: @mobile-spacing-md;
  }

  // 移动端表格横向滚动
  .ant-table-wrapper {
    overflow-x: auto;
  }

  // 移动端按钮组
  .ant-btn-group {
    display: flex;
    flex-direction: column;
    
    .ant-btn {
      margin-bottom: @mobile-spacing-sm;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 移动端特定设备样式
.mobile-ios {
  // iOS 特定样式
  .ant-input,
  .ant-select-selector {
    -webkit-appearance: none;
  }
}

.mobile-android {
  // Android 特定样式
  .ant-input {
    background-color: transparent;
  }
}
