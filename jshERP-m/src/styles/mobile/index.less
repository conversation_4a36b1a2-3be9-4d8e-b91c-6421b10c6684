/**
 * 移动端样式系统主入口
 * 统一导入所有移动端样式模块
 */

// ==================== 基础样式 ====================
@import './variables.less';
@import './mixins.less';
@import './base.less';

// ==================== 组件样式 ====================
@import './components.less';

// ==================== 响应式样式 ====================
@import './responsive.less';

// ==================== 主题配置 ====================

// 浅色主题
.theme-light {
  --background-color: @theme-light-background;
  --surface-color: @theme-light-surface;
  --text-color: @theme-light-text;
  --border-color: @theme-light-border;
  
  background-color: var(--background-color);
  color: var(--text-color);
}

// 深色主题
.theme-dark {
  --background-color: @theme-dark-background;
  --surface-color: @theme-dark-surface;
  --text-color: @theme-dark-text;
  --border-color: @theme-dark-border;
  
  background-color: var(--background-color);
  color: var(--text-color);
  
  // 深色主题下的组件样式调整
  .ant-card {
    background-color: var(--surface-color);
    border-color: var(--border-color);
  }
  
  .ant-input {
    background-color: var(--surface-color);
    border-color: var(--border-color);
    color: var(--text-color);
    
    &::placeholder {
      color: fade(var(--text-color), 50%);
    }
  }
  
  .ant-select-selector {
    background-color: var(--surface-color) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
  }
  
  .ant-btn {
    &:not(.ant-btn-primary) {
      background-color: var(--surface-color);
      border-color: var(--border-color);
      color: var(--text-color);
    }
  }
  
  .ant-table {
    background-color: var(--surface-color);
    color: var(--text-color);
    
    .ant-table-thead > tr > th {
      background-color: var(--background-color);
      border-color: var(--border-color);
      color: var(--text-color);
    }
    
    .ant-table-tbody > tr > td {
      border-color: var(--border-color);
    }
  }
  
  .ant-modal-content {
    background-color: var(--surface-color);
    color: var(--text-color);
  }
  
  .ant-drawer-content {
    background-color: var(--surface-color);
    color: var(--text-color);
  }
}

// ==================== 移动端专用样式 ====================

// 移动端应用根容器
.mobile-app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  
  // 安全区域适配
  .safe-area-padding();
  
  // 移动端字体优化
  .mobile-only({
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  });
}

// 移动端页面容器
.mobile-page-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .mobile-header {
    flex-shrink: 0;
    height: @mobile-header-height;
    background-color: @background-color-base;
    border-bottom: 1px solid @border-color-light;
    z-index: @zindex-fixed;
  }
  
  .mobile-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background-color: @background-color-light;
  }
  
  .mobile-tabbar {
    flex-shrink: 0;
    height: @mobile-tabbar-height;
    background-color: @background-color-base;
    border-top: 1px solid @border-color-light;
    z-index: @zindex-fixed;
    
    // 安全区域适配
    padding-bottom: @safe-area-inset-bottom;
  }
}

// 移动端内容区域
.mobile-content-wrapper {
  padding: @spacing-md;
  min-height: 100%;
  
  .mobile-only({
    padding: @spacing-sm;
  });
  
  .tablet-only({
    padding: @spacing-lg;
  });
  
  .desktop-only({
    padding: @spacing-xl;
  });
}

// ==================== 动画效果 ====================

// 页面切换动画
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all @animation-duration-mobile-base @ease-base;
}

.page-transition-enter {
  opacity: 0;
  transform: translateX(100%);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

// 模态框动画
.modal-transition-enter-active,
.modal-transition-leave-active {
  transition: all @animation-duration-mobile-base @ease-base;
}

.modal-transition-enter {
  opacity: 0;
  transform: scale(0.9);
}

.modal-transition-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// 抽屉动画
.drawer-transition-enter-active,
.drawer-transition-leave-active {
  transition: all @animation-duration-mobile-base @ease-base;
}

.drawer-transition-enter {
  transform: translateY(100%);
}

.drawer-transition-leave-to {
  transform: translateY(100%);
}

// 列表项动画
.list-item-transition-enter-active,
.list-item-transition-leave-active {
  transition: all @animation-duration-mobile-fast @ease-base;
}

.list-item-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.list-item-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// ==================== 工具类扩展 ====================

// 移动端特定工具类
.mobile-utils {
  // 触摸优化
  .touch-target {
    min-width: @touch-target-min;
    min-height: @touch-target-min;
    .touch-optimized();
  }
  
  .touch-feedback {
    .touch-feedback();
  }
  
  // 安全区域
  .safe-top {
    padding-top: @safe-area-inset-top;
  }
  
  .safe-bottom {
    padding-bottom: @safe-area-inset-bottom;
  }
  
  .safe-left {
    padding-left: @safe-area-inset-left;
  }
  
  .safe-right {
    padding-right: @safe-area-inset-right;
  }
  
  // 滚动优化
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
  
  .hide-scroll {
    .hide-scrollbar();
  }
  
  // 文本处理
  .prevent-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  .break-word {
    word-break: break-word;
    word-wrap: break-word;
  }
  
  // 布局工具
  .full-width {
    width: 100% !important;
  }
  
  .full-height {
    height: 100% !important;
  }
  
  .full-viewport {
    width: 100vw !important;
    height: 100vh !important;
  }
  
  // 居中工具
  .center-absolute {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .center-fixed {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// ==================== 性能优化 ====================

// GPU加速
.gpu-accelerated {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
}

// 减少重绘
.reduce-repaint {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
  -webkit-perspective: 1000px;
}

// 优化滚动
.optimize-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  scroll-behavior: smooth;
}

// ==================== 调试工具 ====================

// 开发环境调试边框
.debug-mode {
  * {
    outline: 1px solid rgba(255, 0, 0, 0.3) !important;
  }
  
  .responsive-container {
    outline: 2px solid rgba(0, 255, 0, 0.5) !important;
  }
  
  .responsive-grid {
    outline: 2px solid rgba(0, 0, 255, 0.5) !important;
  }
}
