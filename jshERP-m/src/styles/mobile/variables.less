/**
 * 移动端样式变量
 * 定义移动端专用的设计令牌和样式变量
 */

// ==================== 颜色系统 ====================

// 主色调
@primary-color: #1890ff;
@primary-color-hover: #40a9ff;
@primary-color-active: #096dd9;
@primary-color-light: #e6f7ff;

// 功能色
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 中性色
@text-color: #333333;
@text-color-secondary: #666666;
@text-color-tertiary: #999999;
@text-color-disabled: #cccccc;

@background-color-base: #ffffff;
@background-color-light: #f7f8fa;
@background-color-dark: #f0f2f5;

@border-color-base: #e5e7eb;
@border-color-light: #f0f0f0;
@border-color-dark: #d9d9d9;

// 移动端专用颜色
@mobile-touch-highlight: rgba(24, 144, 255, 0.1);
@mobile-active-background: rgba(0, 0, 0, 0.05);
@mobile-divider-color: #f0f0f0;

// ==================== 字体系统 ====================

// 字体族
@font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
@font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小 - 移动端优化
@font-size-xs: 12px;
@font-size-sm: 14px;
@font-size-base: 16px;  // 移动端基础字体大小
@font-size-lg: 18px;
@font-size-xl: 20px;
@font-size-xxl: 24px;

// 桌面端字体大小
@font-size-desktop-xs: 10px;
@font-size-desktop-sm: 12px;
@font-size-desktop-base: 14px;
@font-size-desktop-lg: 16px;
@font-size-desktop-xl: 18px;
@font-size-desktop-xxl: 20px;

// 行高
@line-height-base: 1.5;
@line-height-tight: 1.2;
@line-height-relaxed: 1.8;

// 字重
@font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// ==================== 间距系统 ====================

// 基础间距单位
@spacing-unit: 4px;

// 间距等级 - 移动端
@spacing-xs: 8px;   // 2 * @spacing-unit
@spacing-sm: 12px;  // 3 * @spacing-unit
@spacing-md: 16px;  // 4 * @spacing-unit
@spacing-lg: 24px;  // 6 * @spacing-unit
@spacing-xl: 32px;  // 8 * @spacing-unit
@spacing-xxl: 40px; // 10 * @spacing-unit

// 间距等级 - 桌面端
@spacing-desktop-xs: 4px;
@spacing-desktop-sm: 8px;
@spacing-desktop-md: 12px;
@spacing-desktop-lg: 16px;
@spacing-desktop-xl: 24px;
@spacing-desktop-xxl: 32px;

// 容器间距
@container-padding-mobile: 16px;
@container-padding-tablet: 20px;
@container-padding-desktop: 24px;

// ==================== 尺寸系统 ====================

// 触摸目标尺寸
@touch-target-min: 44px;  // 最小触摸目标
@touch-target-recommended: 48px;  // 推荐触摸目标

// 组件高度
@height-xs: 24px;
@height-sm: 32px;
@height-base: 40px;
@height-lg: 48px;  // 移动端推荐高度
@height-xl: 56px;

// 桌面端组件高度
@height-desktop-xs: 20px;
@height-desktop-sm: 24px;
@height-desktop-base: 32px;
@height-desktop-lg: 40px;
@height-desktop-xl: 48px;

// ==================== 圆角系统 ====================

@border-radius-xs: 2px;
@border-radius-sm: 4px;
@border-radius-base: 6px;
@border-radius-lg: 8px;
@border-radius-xl: 12px;
@border-radius-xxl: 16px;
@border-radius-full: 50%;

// 移动端圆角（更大的圆角）
@border-radius-mobile-sm: 4px;
@border-radius-mobile-base: 8px;
@border-radius-mobile-lg: 12px;
@border-radius-mobile-xl: 16px;

// ==================== 阴影系统 ====================

// 基础阴影
@box-shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
@box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
@box-shadow-base: 0 4px 8px rgba(0, 0, 0, 0.15);
@box-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
@box-shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.25);

// 移动端阴影（更明显的阴影）
@box-shadow-mobile-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
@box-shadow-mobile-base: 0 4px 8px rgba(0, 0, 0, 0.15);
@box-shadow-mobile-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
@box-shadow-mobile-xl: 0 12px 24px rgba(0, 0, 0, 0.25);

// ==================== 断点系统 ====================

@screen-xs: 0;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 断点范围
@screen-xs-max: (@screen-sm - 1px);
@screen-sm-max: (@screen-md - 1px);
@screen-md-max: (@screen-lg - 1px);
@screen-lg-max: (@screen-xl - 1px);
@screen-xl-max: (@screen-xxl - 1px);

// ==================== 网格系统 ====================

@grid-columns: 24;
@grid-gutter-width: 16px;  // 移动端网格间距
@grid-gutter-width-desktop: 24px;  // 桌面端网格间距

// 容器最大宽度
@container-max-width-sm: 540px;
@container-max-width-md: 720px;
@container-max-width-lg: 960px;
@container-max-width-xl: 1140px;
@container-max-width-xxl: 1320px;

// ==================== 动画系统 ====================

// 动画时长
@animation-duration-fast: 200ms;
@animation-duration-base: 300ms;
@animation-duration-slow: 500ms;

// 移动端动画时长（稍快）
@animation-duration-mobile-fast: 150ms;
@animation-duration-mobile-base: 250ms;
@animation-duration-mobile-slow: 400ms;

// 缓动函数
@ease-base: cubic-bezier(0.25, 0.1, 0.25, 1);
@ease-in: cubic-bezier(0.42, 0, 1, 1);
@ease-out: cubic-bezier(0, 0, 0.58, 1);
@ease-in-out: cubic-bezier(0.42, 0, 0.58, 1);

// ==================== Z-index 层级 ====================

@zindex-dropdown: 1050;
@zindex-sticky: 1020;
@zindex-fixed: 1030;
@zindex-modal-backdrop: 1040;
@zindex-modal: 1050;
@zindex-popover: 1060;
@zindex-tooltip: 1070;
@zindex-toast: 1080;

// ==================== 移动端专用变量 ====================

// 安全区域
@safe-area-inset-top: env(safe-area-inset-top);
@safe-area-inset-bottom: env(safe-area-inset-bottom);
@safe-area-inset-left: env(safe-area-inset-left);
@safe-area-inset-right: env(safe-area-inset-right);

// 状态栏高度
@status-bar-height: @safe-area-inset-top;
@home-indicator-height: @safe-area-inset-bottom;

// 移动端导航栏高度
@mobile-header-height: 56px;
@mobile-tabbar-height: 50px;

// 移动端特殊尺寸
@mobile-modal-border-radius: 16px;
@mobile-drawer-border-radius: 16px;
@mobile-card-border-radius: 12px;

// 触摸反馈
@mobile-touch-feedback-duration: 150ms;
@mobile-touch-feedback-color: rgba(0, 0, 0, 0.05);

// ==================== 主题变量 ====================

// 浅色主题
@theme-light-background: #ffffff;
@theme-light-surface: #f7f8fa;
@theme-light-text: #333333;
@theme-light-border: #e5e7eb;

// 深色主题
@theme-dark-background: #1f1f1f;
@theme-dark-surface: #2a2a2a;
@theme-dark-text: #ffffff;
@theme-dark-border: #404040;

// ==================== 组件特定变量 ====================

// 按钮
@btn-height-mobile: @height-lg;
@btn-padding-horizontal-mobile: @spacing-lg;
@btn-border-radius-mobile: @border-radius-mobile-base;

// 输入框
@input-height-mobile: @height-lg;
@input-padding-horizontal-mobile: @spacing-md;
@input-border-radius-mobile: @border-radius-mobile-base;

// 表格
@table-row-height-mobile: 48px;
@table-header-height-mobile: 44px;
@table-padding-horizontal-mobile: @spacing-sm;

// 卡片
@card-padding-mobile: @spacing-lg;
@card-border-radius-mobile: @border-radius-mobile-lg;
@card-shadow-mobile: @box-shadow-mobile-sm;

// 模态框
@modal-border-radius-mobile: @mobile-modal-border-radius;
@modal-padding-mobile: @spacing-lg;

// 抽屉
@drawer-border-radius-mobile: @mobile-drawer-border-radius;
@drawer-padding-mobile: @spacing-lg;
