<template>
  <a-config-provider :locale="locale">
    <div id="app" :class="appClass">
      <!-- 移动端布局 -->
      <router-view v-if="isMobile" />
      <!-- 桌面端布局 -->
      <router-view v-else />

      <!-- stagewise-toolbar 暂时禁用以解决依赖冲突 -->
      <!-- <stagewise-toolbar v-if="isDev" :config="stagewiseConfig" /> -->
    </div>
  </a-config-provider>
</template>
<script>
  // import { StagewiseToolbar } from '@stagewise/toolbar-vue'
  // import { VuePlugin } from '@stagewise-plugins/vue'
  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
  import enquireScreen from '@/utils/device'

  export default {
    name: 'App',
    components: {
      // StagewiseToolbar
    },
    data () {
      return {
        locale: zhCN,
        isDev: process.env.NODE_ENV === 'development'
        // stagewiseConfig: {
        //   plugins: [VuePlugin()]
        // }
      }
    },
    computed: {
      appClass() {
        return {
          'desktop-app': true
        }
      }
    },
    created () {
      // 桌面端设备检测逻辑
      let that = this
      enquireScreen(deviceType => {
        // tablet
        if (deviceType === 0) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile')
          that.$store.dispatch('setSidebar', false)
        }
        // mobile
        else if (deviceType === 1) {
          that.$store.commit('TOGGLE_DEVICE', 'mobile')
          that.$store.dispatch('setSidebar', false)
        } else {
          that.$store.commit('TOGGLE_DEVICE', 'desktop')
          that.$store.dispatch('setSidebar', true)
        }
      })

    }
  }
</script>
<style lang="less">
#app {
  height: 100%;

  // 桌面端样式
  &.desktop-app {
    font-size: 14px;
    line-height: 1.5;
    // 保持原有桌面端样式
  }

  // 移动端样式
  &.mobile-app {
    font-size: 14px;
    line-height: 1.4;

    // 移动端特有的全局样式
    * {
      box-sizing: border-box;
      -webkit-tap-highlight-color: transparent;
    }

    // 移动端滚动优化
    .mobile-scroll {
      -webkit-overflow-scrolling: touch;
      overflow-scrolling: touch;
    }

    // 禁用选择和拖拽
    img {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      user-drag: none;
    }

    // 移除点击高亮
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }

    // 移动端字体优化
    body {
      -webkit-text-size-adjust: 100%;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }
}

// 移动端和桌面端的过渡动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>