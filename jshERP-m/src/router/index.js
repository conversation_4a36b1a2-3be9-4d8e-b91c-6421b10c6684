/**
 * 移动端路由管理
 * 基于桌面端路由系统的移动端适配
 */

import Vue from 'vue'
import Router from 'vue-router'
import { MobileAdapter } from '@/utils/mobile-adapter'

// 桌面端路由
import { constantRouterMap } from '@/config/router.config'

// 移动端专用路由
import { mobileRouterMap } from '@/config/mobile-router.config'

//update-begin-author:taoyan date:20191011 for:TASK #3214 【优化】访问online功能测试 浏览器控制台抛出异常
try {
  const originalPush = Router.prototype.push
  Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
  }
} catch (e) {
}
//update-end-author:taoyan date:20191011 for:TASK #3214 【优化】访问online功能测试 浏览器控制台抛出异常

Vue.use(Router)

/**
 * 移动端路由配置
 * 根据设备类型动态选择路由
 */
const mobileAdapter = new MobileAdapter()

const allRoutes = [
  // 基础路由（桌面端兼容）
  ...constantRouterMap,

  // 移动端专用路由
  ...(mobileAdapter.isMobile() ? mobileRouterMap : []),

  // 根路径重定向
  {
    path: '/',
    redirect: mobileAdapter.isMobile() ? '/mobile/dashboard' : '/dashboard/analysis'
  },

  // 404页面
  {
    path: '/404',
    component: () => import('@/views/exception/404')
  },

  // 通配符路由（必须放在最后）
  {
    path: '*',
    redirect: '/404'
  }
]

/**
 * 创建路由实例
 */
const router = new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  },
  routes: allRoutes
})

/**
 * 移动端路由守卫
 */
router.beforeEach((to, from, next) => {
  // 移动端设备检测
  if (mobileAdapter.isMobile()) {
    // 移动端特殊处理
    if (to.path.startsWith('/dashboard') && !to.path.startsWith('/mobile/dashboard')) {
      // 重定向到移动端仪表板
      next('/mobile/dashboard')
      return
    }
  }

  // 页面标题设置
  if (to.meta && to.meta.title) {
    document.title = `${to.meta.title} - jshERP移动端`
  }

  next()
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error)

  // 处理代码分割加载失败
  if (error.message.includes('Loading chunk')) {
    window.location.reload()
  }
})

export default router