<template>
  <div class="mobile-layout">
    <!-- 移动端头部 -->
    <mobile-header 
      v-if="!hideHeader"
      :title="pageTitle"
      :show-back="showBack"
      :show-menu="showMenu"
      @back="handleBack"
      @menu="handleMenu"
    />
    
    <!-- 主要内容区域 -->
    <div class="mobile-content" :class="contentClass">
      <router-view />
    </div>
    
    <!-- 移动端底部导航 -->
    <mobile-tabbar 
      v-if="showTabbar"
      :active-tab="activeTab"
      @change="handleTabChange"
    />
    
    <!-- 移动端侧边菜单 -->
    <mobile-sidebar
      v-model="sidebarVisible"
      :menu-list="menuList"
      @select="handleMenuSelect"
    />
    
    <!-- 全局加载状态 -->
    <mobile-loading v-if="globalLoading" :text="loadingText" />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import MobileHeader from '@/components/mobile/MobileHeader'
import MobileTabbar from '@/components/mobile/MobileTabbar'
import MobileSidebar from '@/components/mobile/MobileSidebar'
import MobileLoading from '@/components/mobile/MobileLoading'
import { mobileNavigation } from '@/config/mobile-router.config'

export default {
  name: 'MobileLayout',
  components: {
    MobileHeader,
    MobileTabbar,
    MobileSidebar,
    MobileLoading
  },
  data() {
    return {
      sidebarVisible: false,
      menuList: []
    }
  },
  computed: {
    ...mapState({
      globalLoading: state => state.globalLoading,
      loadingText: state => state.globalLoadingText
    }),
    ...mapGetters(['permissionList']),
    
    // 页面标题
    pageTitle() {
      return this.$route.meta?.title || 'jshERP移动端'
    },
    
    // 是否隐藏头部
    hideHeader() {
      return this.$route.meta?.hideHeader || false
    },
    
    // 是否显示返回按钮
    showBack() {
      return this.$route.meta?.showBack || false
    },
    
    // 是否显示菜单按钮
    showMenu() {
      return !this.showBack && !this.hideHeader
    },
    
    // 是否显示底部导航
    showTabbar() {
      return !this.$route.meta?.hideTabbar && this.isMainPage
    },
    
    // 是否是主页面
    isMainPage() {
      const mainPaths = ['/mobile/dashboard', '/mobile/material', '/mobile/order', '/mobile/stock', '/mobile/settings']
      return mainPaths.includes(this.$route.path)
    },
    
    // 当前激活的标签
    activeTab() {
      const currentPath = this.$route.path
      const tab = mobileNavigation.find(item => item.path === currentPath)
      return tab ? tab.name : 'dashboard'
    },
    
    // 内容区域样式类
    contentClass() {
      return {
        'has-header': !this.hideHeader,
        'has-tabbar': this.showTabbar,
        'full-height': this.hideHeader && !this.showTabbar
      }
    }
  },
  created() {
    this.initMobileLayout()
  },
  mounted() {
    this.setupMobileViewport()
  },
  methods: {
    /**
     * 初始化移动端布局
     */
    initMobileLayout() {
      // 设置移动端视口
      this.setViewportMeta()
      
      // 加载菜单数据
      this.loadMenuList()
      
      // 监听设备方向变化
      this.setupOrientationChange()
    },
    
    /**
     * 设置移动端视口
     */
    setViewportMeta() {
      const viewport = document.querySelector('meta[name="viewport"]')
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
        )
      }
    },
    
    /**
     * 设置移动端视口样式
     */
    setupMobileViewport() {
      // 设置安全区域
      document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)')
      document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)')
      
      // 禁用双击缩放
      document.addEventListener('touchstart', function(event) {
        if (event.touches.length > 1) {
          event.preventDefault()
        }
      })
      
      let lastTouchEnd = 0
      document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime()
        if (now - lastTouchEnd <= 300) {
          event.preventDefault()
        }
        lastTouchEnd = now
      }, false)
    },
    
    /**
     * 监听设备方向变化
     */
    setupOrientationChange() {
      window.addEventListener('orientationchange', () => {
        setTimeout(() => {
          // 重新计算视口高度
          this.updateViewportHeight()
        }, 100)
      })
    },
    
    /**
     * 更新视口高度
     */
    updateViewportHeight() {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    },
    
    /**
     * 加载菜单列表
     */
    async loadMenuList() {
      try {
        // 这里可以从后端获取菜单数据
        // 暂时使用静态配置
        this.menuList = mobileNavigation
      } catch (error) {
        console.error('加载菜单失败:', error)
      }
    },
    
    /**
     * 处理返回按钮点击
     */
    handleBack() {
      if (this.$route.meta?.customBack) {
        this.$emit('back')
      } else {
        this.$router.go(-1)
      }
    },
    
    /**
     * 处理菜单按钮点击
     */
    handleMenu() {
      this.sidebarVisible = true
    },
    
    /**
     * 处理底部导航切换
     */
    handleTabChange(tabName) {
      const tab = mobileNavigation.find(item => item.name === tabName)
      if (tab && tab.path !== this.$route.path) {
        this.$router.push(tab.path)
      }
    },
    
    /**
     * 处理侧边菜单选择
     */
    handleMenuSelect(menuItem) {
      this.sidebarVisible = false
      
      if (menuItem.path && menuItem.path !== this.$route.path) {
        this.$router.push(menuItem.path)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
  background-color: #f5f5f5;
  overflow: hidden;
}

.mobile-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  
  &.has-header {
    padding-top: 44px; // 头部高度
  }
  
  &.has-tabbar {
    padding-bottom: 50px; // 底部导航高度
  }
  
  &.full-height {
    padding: 0;
  }
}

// 移动端安全区域适配
@supports (padding: max(0px)) {
  .mobile-layout {
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}

// 移动端滚动优化
.mobile-content {
  scroll-behavior: smooth;
  
  // iOS 滚动回弹效果
  -webkit-overflow-scrolling: touch;
  
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  
  // Firefox 隐藏滚动条
  scrollbar-width: none;
}

// 移动端触摸优化
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

// 输入框可选择文本
input, textarea {
  -webkit-user-select: text;
  user-select: text;
}
</style>
