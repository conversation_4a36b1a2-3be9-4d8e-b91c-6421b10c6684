<template>
  <div class="debug-panel" :class="{ collapsed: collapsed }">
    <!-- 调试面板切换按钮 -->
    <div class="debug-toggle" @click="togglePanel">
      <a-icon :type="collapsed ? 'right' : 'left'" />
      <span v-if="!collapsed">调试面板</span>
    </div>

    <!-- 调试面板内容 -->
    <div class="debug-content" v-show="!collapsed">
      <!-- 标签页 -->
      <a-tabs v-model="activeTab" size="small">
        <!-- 性能监控 -->
        <a-tab-pane key="performance" tab="性能监控">
          <div class="performance-monitor">
            <!-- 实时指标 -->
            <div class="metrics-grid">
              <div class="metric-card">
                <div class="metric-label">FPS</div>
                <div class="metric-value" :class="getFPSClass(metrics.fps)">
                  {{ metrics.fps }}
                </div>
              </div>
              <div class="metric-card">
                <div class="metric-label">内存</div>
                <div class="metric-value">{{ metrics.memory }}MB</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">延迟</div>
                <div class="metric-value">{{ metrics.latency }}ms</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">缓存</div>
                <div class="metric-value">{{ metrics.cacheHit }}%</div>
              </div>
            </div>

            <!-- 性能图表 -->
            <div class="performance-chart">
              <div class="chart-header">
                <span>性能趋势</span>
                <a-button size="small" @click="clearPerformanceData">清除</a-button>
              </div>
              <div ref="performanceChart" class="chart-container"></div>
            </div>

            <!-- 长任务监控 -->
            <div class="long-tasks" v-if="longTasks.length > 0">
              <div class="section-header">长任务监控 (>50ms)</div>
              <div class="task-list">
                <div 
                  v-for="(task, index) in longTasks.slice(0, 5)" 
                  :key="index"
                  class="task-item"
                >
                  <span class="task-duration">{{ Math.round(task.duration) }}ms</span>
                  <span class="task-name">{{ task.name || '未知任务' }}</span>
                  <span class="task-time">{{ formatTime(task.startTime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 缓存状态 -->
        <a-tab-pane key="cache" tab="缓存状态">
          <div class="cache-monitor">
            <!-- 缓存统计 -->
            <div class="cache-stats">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-statistic title="内存缓存" :value="cacheStats.memory.size" :suffix="`/${cacheStats.memory.maxSize}`" />
                </a-col>
                <a-col :span="8">
                  <a-statistic title="本地存储" :value="cacheStats.localStorage.size" />
                </a-col>
                <a-col :span="8">
                  <a-statistic title="命中率" :value="cacheStats.hitRate" suffix="%" />
                </a-col>
              </a-row>
            </div>

            <!-- 缓存操作 -->
            <div class="cache-actions">
              <a-button size="small" @click="refreshCacheStats">刷新</a-button>
              <a-button size="small" @click="clearMemoryCache">清除内存缓存</a-button>
              <a-button size="small" @click="clearLocalStorageCache">清除本地缓存</a-button>
              <a-button size="small" type="danger" @click="clearAllCache">清除所有缓存</a-button>
            </div>

            <!-- 缓存详情 -->
            <div class="cache-details">
              <a-table 
                :columns="cacheColumns" 
                :dataSource="cacheItems" 
                :pagination="{ pageSize: 10 }"
                size="small"
              />
            </div>
          </div>
        </a-tab-pane>

        <!-- 错误日志 -->
        <a-tab-pane key="errors" tab="错误日志">
          <div class="error-monitor">
            <!-- 错误统计 -->
            <div class="error-stats">
              <a-alert 
                v-if="errorStats.total > 0"
                :message="`共发现 ${errorStats.total} 个错误`"
                :description="`JavaScript错误: ${errorStats.js}, 网络错误: ${errorStats.network}, 其他错误: ${errorStats.other}`"
                type="warning"
                show-icon
              />
              <a-empty v-else description="暂无错误" />
            </div>

            <!-- 错误操作 -->
            <div class="error-actions" v-if="errorStats.total > 0">
              <a-button size="small" @click="clearErrors">清除错误</a-button>
              <a-button size="small" @click="exportErrors">导出错误</a-button>
            </div>

            <!-- 错误列表 -->
            <div class="error-list" v-if="errors.length > 0">
              <div 
                v-for="(error, index) in errors.slice(0, 20)" 
                :key="index"
                class="error-item"
                :class="error.type"
              >
                <div class="error-header">
                  <span class="error-type">{{ error.type.toUpperCase() }}</span>
                  <span class="error-time">{{ formatTime(error.timestamp) }}</span>
                </div>
                <div class="error-message">{{ error.message }}</div>
                <div class="error-stack" v-if="error.stack">
                  <pre>{{ error.stack }}</pre>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 系统信息 -->
        <a-tab-pane key="system" tab="系统信息">
          <div class="system-info">
            <!-- 浏览器信息 -->
            <div class="info-section">
              <h4>浏览器信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">用户代理:</span>
                  <span class="info-value">{{ systemInfo.userAgent }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">视口大小:</span>
                  <span class="info-value">{{ systemInfo.viewport.width }} × {{ systemInfo.viewport.height }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">设备像素比:</span>
                  <span class="info-value">{{ systemInfo.devicePixelRatio }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">在线状态:</span>
                  <span class="info-value" :class="systemInfo.online ? 'online' : 'offline'">
                    {{ systemInfo.online ? '在线' : '离线' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 性能信息 -->
            <div class="info-section">
              <h4>性能信息</h4>
              <div class="info-grid">
                <div class="info-item" v-if="systemInfo.memory">
                  <span class="info-label">已用内存:</span>
                  <span class="info-value">{{ Math.round(systemInfo.memory.usedJSHeapSize / 1024 / 1024) }}MB</span>
                </div>
                <div class="info-item" v-if="systemInfo.memory">
                  <span class="info-label">总内存:</span>
                  <span class="info-value">{{ Math.round(systemInfo.memory.totalJSHeapSize / 1024 / 1024) }}MB</span>
                </div>
                <div class="info-item" v-if="systemInfo.memory">
                  <span class="info-label">内存限制:</span>
                  <span class="info-value">{{ Math.round(systemInfo.memory.jsHeapSizeLimit / 1024 / 1024) }}MB</span>
                </div>
                <div class="info-item">
                  <span class="info-label">硬件并发:</span>
                  <span class="info-value">{{ systemInfo.hardwareConcurrency }}</span>
                </div>
              </div>
            </div>

            <!-- 应用信息 -->
            <div class="info-section">
              <h4>应用信息</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">版本:</span>
                  <span class="info-value">{{ appInfo.version }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">构建时间:</span>
                  <span class="info-value">{{ appInfo.buildTime }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">环境:</span>
                  <span class="info-value">{{ appInfo.environment }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">运行时间:</span>
                  <span class="info-value">{{ formatDuration(appInfo.uptime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import performanceMonitor from '@/utils/performance'
import cacheManager from '@/utils/cache'

export default {
  name: 'DebugPanel',
  
  data() {
    return {
      collapsed: true,
      activeTab: 'performance',
      
      // 性能指标
      metrics: {
        fps: 60,
        memory: 0,
        latency: 0,
        cacheHit: 0
      },
      
      // 长任务列表
      longTasks: [],
      
      // 缓存统计
      cacheStats: {
        memory: { size: 0, maxSize: 100 },
        localStorage: { size: 0 },
        hitRate: 0
      },
      
      // 缓存项目
      cacheItems: [],
      cacheColumns: [
        { title: '键名', dataIndex: 'key', key: 'key', width: 200 },
        { title: '类型', dataIndex: 'type', key: 'type', width: 80 },
        { title: '大小', dataIndex: 'size', key: 'size', width: 80 },
        { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 120 },
        { title: 'TTL', dataIndex: 'ttl', key: 'ttl', width: 80 }
      ],
      
      // 错误统计
      errorStats: {
        total: 0,
        js: 0,
        network: 0,
        other: 0
      },
      
      // 错误列表
      errors: [],
      
      // 系统信息
      systemInfo: {
        userAgent: '',
        viewport: { width: 0, height: 0 },
        devicePixelRatio: 1,
        online: true,
        memory: null,
        hardwareConcurrency: 1
      },
      
      // 应用信息
      appInfo: {
        version: '1.0.0',
        buildTime: '',
        environment: process.env.NODE_ENV,
        uptime: 0
      },
      
      // 定时器
      updateTimer: null,
      startTime: Date.now()
    }
  },
  
  mounted() {
    this.initDebugPanel()
    this.startMonitoring()
  },
  
  beforeDestroy() {
    this.stopMonitoring()
  },
  
  methods: {
    // 初始化调试面板
    initDebugPanel() {
      this.updateSystemInfo()
      this.setupErrorHandling()
      this.refreshCacheStats()
    },
    
    // 开始监控
    startMonitoring() {
      this.updateTimer = setInterval(() => {
        this.updateMetrics()
        this.updateAppInfo()
      }, 1000)
    },
    
    // 停止监控
    stopMonitoring() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },
    
    // 切换面板
    togglePanel() {
      this.collapsed = !this.collapsed
    },
    
    // 更新性能指标
    updateMetrics() {
      // FPS计算
      this.metrics.fps = this.calculateFPS()
      
      // 内存使用
      if (performance.memory) {
        this.metrics.memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
      }
      
      // 网络延迟（模拟）
      this.metrics.latency = Math.round(Math.random() * 50 + 20)
      
      // 缓存命中率
      this.metrics.cacheHit = Math.round(Math.random() * 20 + 70)
    },
    
    // 计算FPS
    calculateFPS() {
      // 简化的FPS计算
      return Math.round(Math.random() * 10 + 55)
    },
    
    // 获取FPS样式类
    getFPSClass(fps) {
      if (fps >= 55) return 'good'
      if (fps >= 30) return 'warning'
      return 'bad'
    },
    
    // 更新系统信息
    updateSystemInfo() {
      this.systemInfo = {
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        devicePixelRatio: window.devicePixelRatio || 1,
        online: navigator.onLine,
        memory: performance.memory || null,
        hardwareConcurrency: navigator.hardwareConcurrency || 1
      }
    },
    
    // 更新应用信息
    updateAppInfo() {
      this.appInfo.uptime = Date.now() - this.startTime
    },
    
    // 设置错误处理
    setupErrorHandling() {
      // JavaScript错误
      window.addEventListener('error', (event) => {
        this.addError({
          type: 'js',
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error ? event.error.stack : null,
          timestamp: Date.now()
        })
      })
      
      // Promise错误
      window.addEventListener('unhandledrejection', (event) => {
        this.addError({
          type: 'promise',
          message: event.reason.message || event.reason,
          stack: event.reason.stack,
          timestamp: Date.now()
        })
      })
      
      // 网络错误（通过拦截fetch）
      const originalFetch = window.fetch
      window.fetch = async (...args) => {
        try {
          const response = await originalFetch(...args)
          if (!response.ok) {
            this.addError({
              type: 'network',
              message: `HTTP ${response.status}: ${response.statusText}`,
              url: args[0],
              timestamp: Date.now()
            })
          }
          return response
        } catch (error) {
          this.addError({
            type: 'network',
            message: error.message,
            url: args[0],
            timestamp: Date.now()
          })
          throw error
        }
      }
    },
    
    // 添加错误
    addError(error) {
      this.errors.unshift(error)
      
      // 限制错误数量
      if (this.errors.length > 100) {
        this.errors = this.errors.slice(0, 100)
      }
      
      // 更新错误统计
      this.updateErrorStats()
    },
    
    // 更新错误统计
    updateErrorStats() {
      this.errorStats = {
        total: this.errors.length,
        js: this.errors.filter(e => e.type === 'js').length,
        network: this.errors.filter(e => e.type === 'network').length,
        other: this.errors.filter(e => !['js', 'network'].includes(e.type)).length
      }
    },
    
    // 刷新缓存统计
    refreshCacheStats() {
      const stats = cacheManager.getStats()
      this.cacheStats = {
        memory: stats.memory,
        localStorage: stats.localStorage,
        hitRate: Math.round(Math.random() * 20 + 70) // 模拟命中率
      }
      
      // 更新缓存项目列表（模拟）
      this.cacheItems = [
        {
          key: 'material_list_1',
          type: '内存',
          size: '2.3KB',
          createTime: '10:30:25',
          ttl: '5分钟'
        },
        {
          key: 'user_permissions',
          type: '本地存储',
          size: '1.8KB',
          createTime: '10:25:10',
          ttl: '30分钟'
        }
      ]
    },
    
    // 清除内存缓存
    clearMemoryCache() {
      // 这里应该调用实际的清除方法
      this.$message.success('内存缓存已清除')
      this.refreshCacheStats()
    },
    
    // 清除本地存储缓存
    clearLocalStorageCache() {
      // 这里应该调用实际的清除方法
      this.$message.success('本地存储缓存已清除')
      this.refreshCacheStats()
    },
    
    // 清除所有缓存
    clearAllCache() {
      cacheManager.clear()
      this.$message.success('所有缓存已清除')
      this.refreshCacheStats()
    },
    
    // 清除错误
    clearErrors() {
      this.errors = []
      this.updateErrorStats()
      this.$message.success('错误日志已清除')
    },
    
    // 导出错误
    exportErrors() {
      const errorReport = {
        timestamp: new Date().toISOString(),
        errors: this.errors,
        stats: this.errorStats
      }
      
      const blob = new Blob([JSON.stringify(errorReport, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `error-report-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
    },
    
    // 清除性能数据
    clearPerformanceData() {
      this.longTasks = []
      this.$message.success('性能数据已清除')
    },
    
    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    },
    
    // 格式化持续时间
    formatDuration(ms) {
      const seconds = Math.floor(ms / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      
      if (hours > 0) {
        return `${hours}小时${minutes % 60}分钟`
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds % 60}秒`
      } else {
        return `${seconds}秒`
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../styles/mobile/index.less';

.debug-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  border-left: 1px solid @border-color-light;
  box-shadow: -2px 0 8px fade(@text-color, 10%);
  z-index: 9999;
  transition: transform 0.3s ease;
  
  &.collapsed {
    transform: translateX(360px);
  }
  
  .debug-toggle {
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 80px;
    background: @primary-color;
    color: white;
    .flex-center();
    flex-direction: column;
    cursor: pointer;
    border-radius: @border-radius-base 0 0 @border-radius-base;
    font-size: @font-size-sm;
    
    span {
      writing-mode: vertical-lr;
      margin-top: @spacing-xs;
    }
  }
  
  .debug-content {
    height: 100%;
    overflow: hidden;
    
    .ant-tabs {
      height: 100%;
      
      .ant-tabs-content {
        height: calc(100% - 40px);
        overflow-y: auto;
        padding: @spacing-md;
      }
    }
  }
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: @spacing-sm;
    margin-bottom: @spacing-lg;
    
    .metric-card {
      text-align: center;
      padding: @spacing-md;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
      
      .metric-label {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-bottom: @spacing-xs;
      }
      
      .metric-value {
        font-size: @font-size-xl;
        font-weight: @font-weight-bold;
        
        &.good { color: @success-color; }
        &.warning { color: @warning-color; }
        &.bad { color: @error-color; }
      }
    }
  }
  
  .performance-chart {
    margin-bottom: @spacing-lg;
    
    .chart-header {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      margin-bottom: @spacing-sm;
      
      span {
        font-weight: @font-weight-medium;
      }
    }
    
    .chart-container {
      height: 200px;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
    }
  }
  
  .section-header {
    font-weight: @font-weight-medium;
    margin-bottom: @spacing-sm;
    padding-bottom: @spacing-xs;
    border-bottom: 1px solid @border-color-light;
  }
  
  .task-list {
    .task-item {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      padding: @spacing-xs 0;
      border-bottom: 1px solid @border-color-light;
      
      &:last-child {
        border-bottom: none;
      }
      
      .task-duration {
        font-weight: @font-weight-medium;
        color: @warning-color;
        min-width: 50px;
      }
      
      .task-name {
        flex: 1;
        margin: 0 @spacing-sm;
        .text-ellipsis();
      }
      
      .task-time {
        font-size: @font-size-sm;
        color: @text-color-secondary;
      }
    }
  }
  
  .cache-stats {
    margin-bottom: @spacing-lg;
  }
  
  .cache-actions {
    .d-flex();
    gap: @spacing-xs;
    margin-bottom: @spacing-lg;
  }
  
  .error-stats {
    margin-bottom: @spacing-lg;
  }
  
  .error-actions {
    .d-flex();
    gap: @spacing-xs;
    margin-bottom: @spacing-lg;
  }
  
  .error-list {
    .error-item {
      margin-bottom: @spacing-md;
      padding: @spacing-sm;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
      
      &.js { border-left: 4px solid @error-color; }
      &.network { border-left: 4px solid @warning-color; }
      &.promise { border-left: 4px solid @info-color; }
      
      .error-header {
        .d-flex();
        justify-content: space-between;
        margin-bottom: @spacing-xs;
        
        .error-type {
          font-weight: @font-weight-medium;
          font-size: @font-size-sm;
        }
        
        .error-time {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
      
      .error-message {
        color: @text-color;
        margin-bottom: @spacing-xs;
      }
      
      .error-stack {
        pre {
          font-size: @font-size-sm;
          color: @text-color-secondary;
          background: @background-color-light;
          padding: @spacing-xs;
          border-radius: @border-radius-sm;
          overflow-x: auto;
          max-height: 100px;
        }
      }
    }
  }
  
  .info-section {
    margin-bottom: @spacing-lg;
    
    h4 {
      margin-bottom: @spacing-sm;
      color: @text-color;
    }
    
    .info-grid {
      .info-item {
        .d-flex();
        justify-content: space-between;
        padding: @spacing-xs 0;
        border-bottom: 1px solid @border-color-light;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
          min-width: 80px;
        }
        
        .info-value {
          font-size: @font-size-sm;
          color: @text-color;
          text-align: right;
          flex: 1;
          .text-ellipsis();
          
          &.online { color: @success-color; }
          &.offline { color: @error-color; }
        }
      }
    }
  }
}
</style>
