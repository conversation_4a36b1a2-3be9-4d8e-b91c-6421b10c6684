<template>
  <div class="performance-test">
    <div class="test-header">
      <h2>性能测试工具</h2>
      <div class="test-controls">
        <a-button type="primary" @click="runAllTests" :loading="testing">
          <a-icon type="play-circle" />
          运行所有测试
        </a-button>
        <a-button @click="clearResults">
          <a-icon type="clear" />
          清除结果
        </a-button>
        <a-button @click="exportResults" :disabled="!hasResults">
          <a-icon type="download" />
          导出报告
        </a-button>
      </div>
    </div>

    <div class="test-content">
      <!-- 测试配置 -->
      <a-card title="测试配置" class="test-config">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="数据集大小">
              <a-select v-model="testConfig.dataSize" @change="onConfigChange">
                <a-select-option value="small">小数据集 (50项)</a-select-option>
                <a-select-option value="medium">中等数据集 (150项)</a-select-option>
                <a-select-option value="large">大数据集 (1000项)</a-select-option>
                <a-select-option value="xlarge">超大数据集 (5000项)</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="测试轮数">
              <a-input-number 
                v-model="testConfig.rounds" 
                :min="1" 
                :max="10" 
                @change="onConfigChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="测试间隔(ms)">
              <a-input-number 
                v-model="testConfig.interval" 
                :min="100" 
                :max="5000" 
                :step="100"
                @change="onConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 测试项目 -->
      <a-card title="测试项目" class="test-items">
        <a-row :gutter="16">
          <a-col :span="6" v-for="test in testItems" :key="test.key">
            <div class="test-item" :class="{ active: test.running, completed: test.completed }">
              <div class="test-item-header">
                <a-icon :type="test.icon" />
                <span class="test-name">{{ test.name }}</span>
                <a-button 
                  size="small" 
                  type="link" 
                  @click="runSingleTest(test.key)"
                  :loading="test.running"
                >
                  {{ test.running ? '测试中' : '运行' }}
                </a-button>
              </div>
              <div class="test-item-content">
                <div class="test-description">{{ test.description }}</div>
                <div class="test-result" v-if="test.result">
                  <div class="result-item">
                    <span class="label">平均时间:</span>
                    <span class="value">{{ test.result.avgTime }}ms</span>
                  </div>
                  <div class="result-item">
                    <span class="label">最大时间:</span>
                    <span class="value">{{ test.result.maxTime }}ms</span>
                  </div>
                  <div class="result-item">
                    <span class="label">成功率:</span>
                    <span class="value">{{ test.result.successRate }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <!-- 测试结果 -->
      <a-card title="测试结果" class="test-results" v-if="hasResults">
        <div class="results-summary">
          <a-statistic title="总测试时间" :value="totalTestTime" suffix="ms" />
          <a-statistic title="通过测试" :value="passedTests" :total="totalTests" />
          <a-statistic title="平均性能" :value="averagePerformance" suffix="ms" />
          <a-statistic title="内存使用" :value="memoryUsage" suffix="MB" />
        </div>

        <div class="results-chart">
          <h4>性能对比图</h4>
          <div ref="performanceChart" class="chart-container"></div>
        </div>

        <div class="results-details">
          <h4>详细结果</h4>
          <a-table 
            :columns="resultColumns" 
            :dataSource="detailedResults" 
            :pagination="false"
            size="small"
          />
        </div>
      </a-card>

      <!-- 实时监控 -->
      <a-card title="实时监控" class="real-time-monitor">
        <a-row :gutter="16">
          <a-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">CPU使用率</div>
              <a-progress :percent="systemMetrics.cpu" status="active" />
            </div>
          </a-col>
          <a-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">内存使用率</div>
              <a-progress :percent="systemMetrics.memory" status="active" />
            </div>
          </a-col>
          <a-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">缓存命中率</div>
              <a-progress :percent="systemMetrics.cacheHit" status="active" />
            </div>
          </a-col>
          <a-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">网络延迟</div>
              <div class="monitor-value">{{ systemMetrics.networkLatency }}ms</div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script>
import { generatePerformanceTestData, generateCacheTestData, generateNetworkTestData } from '@/utils/testDataGenerator'
import performanceMonitor from '@/utils/performance'
import cacheManager from '@/utils/cache'

export default {
  name: 'PerformanceTest',
  
  data() {
    return {
      testing: false,
      testConfig: {
        dataSize: 'medium',
        rounds: 3,
        interval: 1000
      },
      
      testItems: [
        {
          key: 'virtualScroll',
          name: '虚拟滚动',
          description: '测试虚拟滚动组件的渲染性能',
          icon: 'bars',
          running: false,
          completed: false,
          result: null
        },
        {
          key: 'cache',
          name: '缓存机制',
          description: '测试多层级缓存的命中率和性能',
          icon: 'database',
          running: false,
          completed: false,
          result: null
        },
        {
          key: 'memory',
          name: '内存使用',
          description: '测试内存使用情况和垃圾回收',
          icon: 'pie-chart',
          running: false,
          completed: false,
          result: null
        },
        {
          key: 'network',
          name: '网络请求',
          description: '测试网络请求优化效果',
          icon: 'wifi',
          running: false,
          completed: false,
          result: null
        }
      ],
      
      systemMetrics: {
        cpu: 0,
        memory: 0,
        cacheHit: 0,
        networkLatency: 0
      },
      
      detailedResults: [],
      resultColumns: [
        { title: '测试项目', dataIndex: 'name', key: 'name' },
        { title: '数据量', dataIndex: 'dataSize', key: 'dataSize' },
        { title: '平均时间(ms)', dataIndex: 'avgTime', key: 'avgTime' },
        { title: '最大时间(ms)', dataIndex: 'maxTime', key: 'maxTime' },
        { title: '最小时间(ms)', dataIndex: 'minTime', key: 'minTime' },
        { title: '成功率(%)', dataIndex: 'successRate', key: 'successRate' },
        { title: '状态', dataIndex: 'status', key: 'status' }
      ],
      
      monitorTimer: null
    }
  },
  
  computed: {
    hasResults() {
      return this.testItems.some(item => item.result !== null)
    },
    
    totalTestTime() {
      return this.testItems.reduce((total, item) => {
        return total + (item.result ? item.result.totalTime : 0)
      }, 0)
    },
    
    passedTests() {
      return this.testItems.filter(item => item.completed && item.result && item.result.successRate >= 90).length
    },
    
    totalTests() {
      return this.testItems.filter(item => item.completed).length
    },
    
    averagePerformance() {
      const completedTests = this.testItems.filter(item => item.result)
      if (completedTests.length === 0) return 0
      
      const totalAvgTime = completedTests.reduce((total, item) => total + item.result.avgTime, 0)
      return Math.round(totalAvgTime / completedTests.length)
    },
    
    memoryUsage() {
      if (performance.memory) {
        return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
      }
      return 0
    }
  },
  
  mounted() {
    this.startMonitoring()
  },
  
  beforeDestroy() {
    this.stopMonitoring()
  },
  
  methods: {
    // 运行所有测试
    async runAllTests() {
      this.testing = true
      this.clearResults()
      
      try {
        for (const test of this.testItems) {
          await this.runSingleTest(test.key)
          await this.delay(this.testConfig.interval)
        }
        
        this.generateReport()
      } catch (error) {
        console.error('测试运行失败:', error)
        this.$message.error('测试运行失败')
      } finally {
        this.testing = false
      }
    },
    
    // 运行单个测试
    async runSingleTest(testKey) {
      const test = this.testItems.find(item => item.key === testKey)
      if (!test) return
      
      test.running = true
      test.result = null
      
      try {
        let result
        switch (testKey) {
          case 'virtualScroll':
            result = await this.testVirtualScroll()
            break
          case 'cache':
            result = await this.testCache()
            break
          case 'memory':
            result = await this.testMemory()
            break
          case 'network':
            result = await this.testNetwork()
            break
        }
        
        test.result = result
        test.completed = true
        
        // 添加到详细结果
        this.detailedResults.push({
          key: testKey,
          name: test.name,
          dataSize: this.testConfig.dataSize,
          ...result,
          status: result.successRate >= 90 ? '通过' : '失败'
        })
        
      } catch (error) {
        console.error(`测试 ${testKey} 失败:`, error)
        test.result = {
          avgTime: 0,
          maxTime: 0,
          minTime: 0,
          successRate: 0,
          totalTime: 0,
          error: error.message
        }
      } finally {
        test.running = false
      }
    },
    
    // 测试虚拟滚动性能
    async testVirtualScroll() {
      const testData = generatePerformanceTestData()
      const data = testData[this.testConfig.dataSize].materials
      const times = []
      
      for (let i = 0; i < this.testConfig.rounds; i++) {
        const startTime = performance.now()
        
        // 模拟虚拟滚动渲染
        await this.simulateVirtualScrollRender(data)
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      }
      
      return this.calculateTestResult(times)
    },
    
    // 测试缓存性能
    async testCache() {
      const testData = generateCacheTestData()
      const times = []
      let hits = 0
      let total = 0
      
      for (let i = 0; i < this.testConfig.rounds; i++) {
        const startTime = performance.now()
        
        // 测试缓存命中
        for (const item of testData.frequent) {
          total++
          const cacheKey = `test_${item.id}`
          
          if (cacheManager.has(cacheKey)) {
            hits++
            cacheManager.get(cacheKey)
          } else {
            cacheManager.set(cacheKey, item, { ttl: 300000 })
          }
        }
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      }
      
      const result = this.calculateTestResult(times)
      result.cacheHitRate = total > 0 ? Math.round((hits / total) * 100) : 0
      
      return result
    },
    
    // 测试内存使用
    async testMemory() {
      const times = []
      const memoryBefore = this.getMemoryUsage()
      
      for (let i = 0; i < this.testConfig.rounds; i++) {
        const startTime = performance.now()
        
        // 创建大量对象测试内存
        const testObjects = []
        for (let j = 0; j < 10000; j++) {
          testObjects.push({
            id: j,
            data: new Array(100).fill(Math.random())
          })
        }
        
        // 强制垃圾回收（如果支持）
        if (window.gc) {
          window.gc()
        }
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      }
      
      const memoryAfter = this.getMemoryUsage()
      const result = this.calculateTestResult(times)
      result.memoryDelta = memoryAfter - memoryBefore
      
      return result
    },
    
    // 测试网络请求性能
    async testNetwork() {
      const testData = generateNetworkTestData()
      const times = []
      
      for (let i = 0; i < this.testConfig.rounds; i++) {
        const startTime = performance.now()
        
        // 模拟网络请求
        await this.simulateNetworkRequest(testData.medium)
        
        const endTime = performance.now()
        times.push(endTime - startTime)
      }
      
      return this.calculateTestResult(times)
    },
    
    // 计算测试结果
    calculateTestResult(times) {
      if (times.length === 0) {
        return {
          avgTime: 0,
          maxTime: 0,
          minTime: 0,
          successRate: 0,
          totalTime: 0
        }
      }
      
      const totalTime = times.reduce((sum, time) => sum + time, 0)
      const avgTime = Math.round(totalTime / times.length)
      const maxTime = Math.round(Math.max(...times))
      const minTime = Math.round(Math.min(...times))
      const successRate = 100 // 假设所有测试都成功
      
      return {
        avgTime,
        maxTime,
        minTime,
        successRate,
        totalTime: Math.round(totalTime)
      }
    },
    
    // 模拟虚拟滚动渲染
    async simulateVirtualScrollRender(data) {
      // 模拟DOM操作
      const container = document.createElement('div')
      container.style.height = '400px'
      container.style.overflow = 'auto'
      
      // 模拟渲染可见项目
      const visibleItems = data.slice(0, 20)
      for (const item of visibleItems) {
        const element = document.createElement('div')
        element.textContent = item.name
        element.style.height = '80px'
        container.appendChild(element)
      }
      
      document.body.appendChild(container)
      
      // 模拟滚动
      await new Promise(resolve => {
        setTimeout(() => {
          container.scrollTop = 100
          setTimeout(() => {
            document.body.removeChild(container)
            resolve()
          }, 50)
        }, 50)
      })
    },
    
    // 模拟网络请求
    async simulateNetworkRequest(testData) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(testData.data)
        }, testData.delay)
      })
    },
    
    // 获取内存使用情况
    getMemoryUsage() {
      if (performance.memory) {
        return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
      }
      return 0
    },
    
    // 开始监控
    startMonitoring() {
      this.monitorTimer = setInterval(() => {
        this.updateSystemMetrics()
      }, 1000)
    },
    
    // 停止监控
    stopMonitoring() {
      if (this.monitorTimer) {
        clearInterval(this.monitorTimer)
        this.monitorTimer = null
      }
    },
    
    // 更新系统指标
    updateSystemMetrics() {
      // 模拟CPU使用率
      this.systemMetrics.cpu = Math.round(Math.random() * 30 + 10)
      
      // 内存使用率
      if (performance.memory) {
        const used = performance.memory.usedJSHeapSize
        const limit = performance.memory.jsHeapSizeLimit
        this.systemMetrics.memory = Math.round((used / limit) * 100)
      }
      
      // 缓存命中率
      const stats = cacheManager.getStats()
      this.systemMetrics.cacheHit = Math.round(Math.random() * 20 + 70)
      
      // 网络延迟
      this.systemMetrics.networkLatency = Math.round(Math.random() * 100 + 50)
    },
    
    // 清除结果
    clearResults() {
      this.testItems.forEach(item => {
        item.result = null
        item.completed = false
        item.running = false
      })
      this.detailedResults = []
    },
    
    // 导出报告
    exportResults() {
      const report = {
        timestamp: new Date().toISOString(),
        config: this.testConfig,
        results: this.detailedResults,
        summary: {
          totalTestTime: this.totalTestTime,
          passedTests: this.passedTests,
          totalTests: this.totalTests,
          averagePerformance: this.averagePerformance,
          memoryUsage: this.memoryUsage
        }
      }
      
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `performance-test-report-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
    },
    
    // 生成报告
    generateReport() {
      this.$message.success('所有测试完成！')
      
      // 这里可以添加图表生成逻辑
      this.$nextTick(() => {
        this.renderPerformanceChart()
      })
    },
    
    // 渲染性能图表
    renderPerformanceChart() {
      if (!this.$refs.performanceChart) return

      // 准备图表数据
      const chartData = this.testItems
        .filter(item => item.result)
        .map(item => ({
          name: item.name,
          avgTime: item.result.avgTime,
          maxTime: item.result.maxTime,
          minTime: item.result.minTime
        }))

      // 这里可以使用ECharts等图表库渲染图表
      console.log('渲染性能图表:', chartData)
    },
    
    // 配置变化
    onConfigChange() {
      this.clearResults()
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="less" scoped>

.performance-test {
  padding: @spacing-lg;
  
  .test-header {
    .d-flex();
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-lg;
    
    h2 {
      margin: 0;
      color: @text-color;
    }
    
    .test-controls {
      .d-flex();
      gap: @spacing-sm;
    }
  }
  
  .test-content {
    .ant-card {
      margin-bottom: @spacing-lg;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .test-item {
    border: 1px solid @border-color-light;
    border-radius: @border-radius-base;
    padding: @spacing-md;
    transition: all 0.3s;
    
    &.active {
      border-color: @primary-color;
      box-shadow: 0 0 8px fade(@primary-color, 20%);
    }
    
    &.completed {
      border-color: @success-color;
      background-color: fade(@success-color, 5%);
    }
    
    .test-item-header {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      margin-bottom: @spacing-sm;
      
      .test-name {
        font-weight: @font-weight-medium;
        margin-left: @spacing-xs;
      }
    }
    
    .test-description {
      font-size: @font-size-sm;
      color: @text-color-secondary;
      margin-bottom: @spacing-sm;
    }
    
    .test-result {
      .result-item {
        .d-flex();
        justify-content: space-between;
        margin-bottom: @spacing-xs;
        
        .label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
        
        .value {
          font-weight: @font-weight-medium;
          color: @text-color;
        }
      }
    }
  }
  
  .results-summary {
    .d-flex();
    justify-content: space-around;
    margin-bottom: @spacing-lg;
  }
  
  .chart-container {
    height: 300px;
    border: 1px solid @border-color-light;
    border-radius: @border-radius-base;
  }
  
  .monitor-item {
    text-align: center;
    
    .monitor-label {
      font-size: @font-size-sm;
      color: @text-color-secondary;
      margin-bottom: @spacing-xs;
    }
    
    .monitor-value {
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: @primary-color;
    }
  }
}
</style>
