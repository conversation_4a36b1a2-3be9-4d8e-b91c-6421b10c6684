<template>
  <div class="mobile-list" :class="responsiveClass">
    <!-- 列表头部 -->
    <div v-if="showHeader" class="mobile-list-header">
      <div class="mobile-list-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="mobile-list-actions">
        <slot name="actions">
          <a-button 
            v-if="showRefresh" 
            type="text" 
            size="small" 
            @click="handleRefresh"
            :loading="loading"
          >
            <a-icon type="reload" />
          </a-button>
          <a-button 
            v-if="showAdd" 
            type="text" 
            size="small" 
            @click="handleAdd"
          >
            <a-icon type="plus" />
          </a-button>
        </slot>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div v-if="showSearch" class="mobile-list-search">
      <a-input-search
        v-model="searchValue"
        :placeholder="searchPlaceholder"
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
      />
    </div>

    <!-- 筛选栏 -->
    <div v-if="showFilters && filters.length > 0" class="mobile-list-filters">
      <div class="filter-tabs">
        <a-button
          v-for="filter in filters"
          :key="filter.key"
          :type="activeFilter === filter.key ? 'primary' : 'default'"
          size="small"
          @click="handleFilterChange(filter.key)"
        >
          {{ filter.label }}
        </a-button>
      </div>
    </div>

    <!-- 列表内容 -->
    <div class="mobile-list-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="mobile-list-loading">
        <a-spin size="large" />
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="dataSource.length === 0" class="mobile-list-empty">
        <a-empty :description="emptyText">
          <template v-if="showEmptyAction" slot="description">
            {{ emptyText }}
            <br>
            <a-button type="primary" @click="handleAdd">
              {{ emptyActionText }}
            </a-button>
          </template>
        </a-empty>
      </div>
      
      <!-- 列表项 -->
      <div v-else class="mobile-list-items">
        <div
          v-for="(item, index) in dataSource"
          :key="getItemKey(item, index)"
          class="mobile-list-item"
          :class="getItemClass(item, index)"
          @click="handleItemClick(item, index)"
        >
          <!-- 左侧图标/头像 -->
          <div v-if="showAvatar || $slots.avatar" class="mobile-item-avatar">
            <slot name="avatar" :item="item" :index="index">
              <a-avatar
                v-if="item.avatar"
                :src="item.avatar"
                :size="avatarSize"
              />
              <a-avatar
                v-else
                :size="avatarSize"
                :style="{ backgroundColor: getAvatarColor(item) }"
              >
                {{ getAvatarText(item) }}
              </a-avatar>
            </slot>
          </div>

          <!-- 主要内容 -->
          <div class="mobile-item-content">
            <slot name="item" :item="item" :index="index">
              <!-- 标题行 -->
              <div class="mobile-item-title">
                <span class="title-text">{{ getItemTitle(item) }}</span>
                <span v-if="getItemTime(item)" class="title-time">
                  {{ getItemTime(item) }}
                </span>
              </div>
              
              <!-- 描述行 -->
              <div v-if="getItemDescription(item)" class="mobile-item-description">
                {{ getItemDescription(item) }}
              </div>
              
              <!-- 标签行 -->
              <div v-if="getItemTags(item).length > 0" class="mobile-item-tags">
                <a-tag
                  v-for="tag in getItemTags(item)"
                  :key="tag.key || tag"
                  :color="tag.color"
                  size="small"
                >
                  {{ tag.label || tag }}
                </a-tag>
              </div>
              
              <!-- 额外信息 -->
              <div v-if="getItemExtra(item)" class="mobile-item-extra">
                {{ getItemExtra(item) }}
              </div>
            </slot>
          </div>

          <!-- 右侧操作 -->
          <div v-if="showActions || $slots.actions" class="mobile-item-actions">
            <slot name="actions" :item="item" :index="index">
              <a-dropdown v-if="itemActions.length > 0" placement="bottomRight">
                <a-button type="text" size="small">
                  <a-icon type="more" />
                </a-button>
                <a-menu slot="overlay" @click="handleActionClick($event, item, index)">
                  <a-menu-item
                    v-for="action in itemActions"
                    :key="action.key"
                    :disabled="action.disabled && action.disabled(item)"
                  >
                    <a-icon v-if="action.icon" :type="action.icon" />
                    {{ action.label }}
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </slot>
          </div>

          <!-- 选择框 -->
          <div v-if="selectable" class="mobile-item-selection">
            <a-checkbox
              :checked="isItemSelected(item)"
              @change="handleItemSelect(item, $event)"
              @click.stop
            />
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination && dataSource.length > 0" class="mobile-list-pagination">
        <a-pagination
          v-bind="paginationProps"
          @change="handlePageChange"
          @showSizeChange="handlePageSizeChange"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="loadMore && hasMore" class="mobile-list-load-more">
        <a-button
          type="text"
          :loading="loadingMore"
          @click="handleLoadMore"
          block
        >
          {{ loadMoreText }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'

export default {
  name: 'MobileList',
  mixins: [ResponsiveMixin],
  props: {
    // 基础列表属性
    dataSource: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    
    // 列表配置
    title: {
      type: String,
      default: ''
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    searchPlaceholder: {
      type: String,
      default: '请输入搜索关键词'
    },
    showFilters: {
      type: Boolean,
      default: false
    },
    filters: {
      type: Array,
      default: () => []
    },
    showRefresh: {
      type: Boolean,
      default: true
    },
    showAdd: {
      type: Boolean,
      default: false
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    showEmptyAction: {
      type: Boolean,
      default: false
    },
    emptyActionText: {
      type: String,
      default: '立即添加'
    },
    
    // 项目配置
    itemKey: {
      type: [String, Function],
      default: 'id'
    },
    titleField: {
      type: [String, Function],
      default: 'title'
    },
    descriptionField: {
      type: [String, Function],
      default: 'description'
    },
    timeField: {
      type: [String, Function],
      default: 'time'
    },
    tagsField: {
      type: [String, Function],
      default: 'tags'
    },
    extraField: {
      type: [String, Function],
      default: 'extra'
    },
    
    // 头像配置
    showAvatar: {
      type: Boolean,
      default: false
    },
    avatarSize: {
      type: [String, Number],
      default: 'default'
    },
    avatarField: {
      type: String,
      default: 'avatar'
    },
    
    // 操作配置
    showActions: {
      type: Boolean,
      default: false
    },
    itemActions: {
      type: Array,
      default: () => []
    },
    
    // 选择配置
    selectable: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    
    // 分页配置
    pagination: {
      type: [Object, Boolean],
      default: false
    },
    
    // 加载更多配置
    loadMore: {
      type: Boolean,
      default: false
    },
    hasMore: {
      type: Boolean,
      default: false
    },
    loadingMore: {
      type: Boolean,
      default: false
    },
    loadMoreText: {
      type: String,
      default: '加载更多'
    }
  },

  data() {
    return {
      searchValue: '',
      activeFilter: '',
      selectedItemKeys: []
    }
  },

  computed: {
    // 分页属性
    paginationProps() {
      if (!this.pagination) return {}
      
      return {
        size: this.$isMobile ? 'small' : 'default',
        simple: this.$isMobile && this.$breakpoint === 'xs',
        showSizeChanger: !this.$isMobile,
        showQuickJumper: !this.$isMobile,
        showTotal: !this.$isMobile,
        ...this.pagination
      }
    }
  },

  watch: {
    selectedItems: {
      handler(newVal) {
        this.selectedItemKeys = newVal.map(item => this.getItemKey(item))
      },
      immediate: true
    }
  },

  methods: {
    // 获取项目键
    getItemKey(item, index) {
      if (typeof this.itemKey === 'function') {
        return this.itemKey(item, index)
      }
      return item[this.itemKey] || index
    },

    // 获取项目样式类
    getItemClass(item, index) {
      return [
        {
          'mobile-item-selected': this.isItemSelected(item),
          'mobile-item-clickable': this.$listeners.itemClick
        }
      ]
    },

    // 获取项目标题
    getItemTitle(item) {
      if (typeof this.titleField === 'function') {
        return this.titleField(item)
      }
      return item[this.titleField] || ''
    },

    // 获取项目描述
    getItemDescription(item) {
      if (typeof this.descriptionField === 'function') {
        return this.descriptionField(item)
      }
      return item[this.descriptionField] || ''
    },

    // 获取项目时间
    getItemTime(item) {
      if (typeof this.timeField === 'function') {
        return this.timeField(item)
      }
      return item[this.timeField] || ''
    },

    // 获取项目标签
    getItemTags(item) {
      if (typeof this.tagsField === 'function') {
        return this.tagsField(item) || []
      }
      return item[this.tagsField] || []
    },

    // 获取项目额外信息
    getItemExtra(item) {
      if (typeof this.extraField === 'function') {
        return this.extraField(item)
      }
      return item[this.extraField] || ''
    },

    // 获取头像文本
    getAvatarText(item) {
      const title = this.getItemTitle(item)
      return title ? title.charAt(0).toUpperCase() : '?'
    },

    // 获取头像颜色
    getAvatarColor(item) {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae']
      const key = this.getItemKey(item)
      const index = typeof key === 'string' ? key.length : key
      return colors[index % colors.length]
    },

    // 检查项目是否被选中
    isItemSelected(item) {
      const key = this.getItemKey(item)
      return this.selectedItemKeys.includes(key)
    },

    // 处理项目点击
    handleItemClick(item, index) {
      this.$emit('itemClick', item, index)
    },

    // 处理项目选择
    handleItemSelect(item, event) {
      const key = this.getItemKey(item)
      const checked = event.target.checked
      
      if (checked) {
        this.selectedItemKeys.push(key)
      } else {
        const index = this.selectedItemKeys.indexOf(key)
        if (index > -1) {
          this.selectedItemKeys.splice(index, 1)
        }
      }
      
      const selectedItems = this.dataSource.filter(item => 
        this.selectedItemKeys.includes(this.getItemKey(item))
      )
      
      this.$emit('selectionChange', selectedItems, this.selectedItemKeys)
    },

    // 处理操作点击
    handleActionClick(event, item, index) {
      const action = this.itemActions.find(a => a.key === event.key)
      if (action && action.handler) {
        action.handler(item, index)
      }
      this.$emit('actionClick', event.key, item, index)
    },

    // 处理搜索
    handleSearch(value) {
      this.$emit('search', value)
    },

    // 处理搜索变化
    handleSearchChange(e) {
      this.searchValue = e.target.value
      this.$emit('searchChange', this.searchValue)
    },

    // 处理筛选变化
    handleFilterChange(filterKey) {
      this.activeFilter = filterKey
      this.$emit('filterChange', filterKey)
    },

    // 处理刷新
    handleRefresh() {
      this.$emit('refresh')
    },

    // 处理添加
    handleAdd() {
      this.$emit('add')
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.$emit('pageChange', page, pageSize)
    },

    // 处理页面大小变化
    handlePageSizeChange(current, size) {
      this.$emit('pageSizeChange', current, size)
    },

    // 处理加载更多
    handleLoadMore() {
      this.$emit('loadMore')
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../styles/mobile/index.less';

.mobile-list {
  .mobile-card();
  
  .mobile-list-header {
    .flex-between();
    padding: @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .mobile-list-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
    }
    
    .mobile-list-actions {
      .d-flex();
      gap: @spacing-xs;
    }
  }
  
  .mobile-list-search {
    padding: @spacing-md @spacing-lg;
    border-bottom: 1px solid @border-color-light;
  }
  
  .mobile-list-filters {
    padding: @spacing-sm @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .filter-tabs {
      .d-flex();
      gap: @spacing-xs;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      
      .ant-btn {
        flex-shrink: 0;
        border-radius: @border-radius-lg;
      }
    }
  }
  
  .mobile-list-content {
    .mobile-list-loading {
      .flex-center();
      padding: @spacing-xxl;
    }
    
    .mobile-list-empty {
      .flex-center();
      padding: @spacing-xxl;
    }
    
    .mobile-list-items {
      .mobile-list-item {
        .d-flex();
        align-items: flex-start;
        padding: @spacing-lg;
        border-bottom: 1px solid @border-color-light;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.mobile-item-clickable {
          .touch-feedback();
          cursor: pointer;
        }
        
        &.mobile-item-selected {
          background-color: @primary-color-light;
        }
        
        .mobile-item-avatar {
          margin-right: @spacing-md;
          flex-shrink: 0;
        }
        
        .mobile-item-content {
          flex: 1;
          min-width: 0;
          
          .mobile-item-title {
            .flex-between();
            align-items: flex-start;
            margin-bottom: @spacing-xs;
            
            .title-text {
              font-size: @font-size-base;
              font-weight: @font-weight-medium;
              color: @text-color;
              .text-ellipsis();
              flex: 1;
            }
            
            .title-time {
              font-size: @font-size-sm;
              color: @text-color-tertiary;
              margin-left: @spacing-sm;
              flex-shrink: 0;
            }
          }
          
          .mobile-item-description {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-bottom: @spacing-xs;
            .text-ellipsis-2();
          }
          
          .mobile-item-tags {
            margin-bottom: @spacing-xs;
            
            .ant-tag {
              margin-right: @spacing-xs;
              margin-bottom: @spacing-xs;
            }
          }
          
          .mobile-item-extra {
            font-size: @font-size-sm;
            color: @text-color-tertiary;
          }
        }
        
        .mobile-item-actions {
          margin-left: @spacing-sm;
          flex-shrink: 0;
        }
        
        .mobile-item-selection {
          margin-left: @spacing-sm;
          flex-shrink: 0;
          .flex-center();
        }
      }
    }
    
    .mobile-list-pagination {
      padding: @spacing-lg;
      text-align: center;
      border-top: 1px solid @border-color-light;
    }
    
    .mobile-list-load-more {
      padding: @spacing-lg;
      border-top: 1px solid @border-color-light;
    }
  }
}
</style>
