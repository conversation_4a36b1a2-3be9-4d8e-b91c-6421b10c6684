<template>
  <a-modal
    v-bind="modalProps"
    :visible="visible"
    :title="title"
    :width="modalWidth"
    :centered="centered"
    :closable="closable"
    :maskClosable="maskClosable"
    :keyboard="keyboard"
    :destroyOnClose="destroyOnClose"
    :forceRender="forceRender"
    :getContainer="getContainer"
    :zIndex="zIndex"
    :bodyStyle="bodyStyle"
    :maskStyle="maskStyle"
    :wrapClassName="wrapClassName"
    :class="modalClass"
    @ok="handleOk"
    @cancel="handleCancel"
    @afterClose="handleAfterClose"
  >
    <!-- 自定义标题 -->
    <template v-if="$slots.title" slot="title">
      <slot name="title" />
    </template>

    <!-- 模态框内容 -->
    <div class="mobile-modal-content">
      <slot />
    </div>

    <!-- 自定义底部 -->
    <template v-if="$slots.footer" slot="footer">
      <slot name="footer" />
    </template>
    
    <!-- 默认底部 -->
    <template v-else-if="showFooter" slot="footer">
      <div class="mobile-modal-footer">
        <a-button
          v-if="showCancel"
          :size="buttonSize"
          @click="handleCancel"
        >
          {{ cancelText }}
        </a-button>
        <a-button
          v-if="showOk"
          type="primary"
          :size="buttonSize"
          :loading="confirmLoading"
          @click="handleOk"
        >
          {{ okText }}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { ComponentAdapterFactory } from '@/utils/component-adapter'

export default {
  name: 'MobileModal',
  mixins: [ResponsiveMixin],
  props: {
    // 基础模态框属性
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: 520
    },
    centered: {
      type: Boolean,
      default: false
    },
    closable: {
      type: Boolean,
      default: true
    },
    maskClosable: {
      type: Boolean,
      default: true
    },
    keyboard: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    forceRender: {
      type: Boolean,
      default: false
    },
    getContainer: {
      type: [String, Function],
      default: undefined
    },
    zIndex: {
      type: Number,
      default: 1000
    },
    bodyStyle: {
      type: Object,
      default: () => ({})
    },
    maskStyle: {
      type: Object,
      default: () => ({})
    },
    wrapClassName: {
      type: String,
      default: ''
    },
    
    // 底部按钮配置
    showFooter: {
      type: Boolean,
      default: true
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    showOk: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    okText: {
      type: String,
      default: '确定'
    },
    confirmLoading: {
      type: Boolean,
      default: false
    },
    
    // 移动端特有配置
    fullscreen: {
      type: Boolean,
      default: false
    },
    slideUp: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    // 模态框适配器
    modalAdapter() {
      return ComponentAdapterFactory.create('modal')
    },

    // 适配后的模态框属性
    modalProps() {
      const baseProps = this.modalAdapter.getAdaptedProps()
      
      if (this.$isMobile) {
        return {
          ...baseProps,
          centered: !this.slideUp,
          maskClosable: this.maskClosable && !this.fullscreen,
          keyboard: this.keyboard && !this.fullscreen
        }
      }
      
      return baseProps
    },

    // 模态框宽度
    modalWidth() {
      if (this.$isMobile) {
        return this.fullscreen ? '100%' : '90%'
      }
      return this.width
    },

    // 模态框样式类
    modalClass() {
      return [
        'mobile-modal',
        {
          'mobile-modal-fullscreen': this.$isMobile && this.fullscreen,
          'mobile-modal-slide-up': this.$isMobile && this.slideUp,
          'mobile-modal-mobile': this.$isMobile
        },
        this.wrapClassName
      ]
    },

    // 按钮尺寸
    buttonSize() {
      return this.$isMobile ? 'large' : 'default'
    }
  },

  methods: {
    // 处理确定
    handleOk(e) {
      this.$emit('ok', e)
    },

    // 处理取消
    handleCancel(e) {
      this.$emit('cancel', e)
    },

    // 处理关闭后
    handleAfterClose() {
      this.$emit('afterClose')
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../styles/mobile/index.less';

// 移动端模态框样式
.mobile-modal {
  &.mobile-modal-mobile {
    .ant-modal {
      margin: 0;
      padding-bottom: 0;
      
      .ant-modal-content {
        border-radius: @border-radius-lg @border-radius-lg 0 0;
        overflow: hidden;
        
        .ant-modal-header {
          padding: @spacing-lg;
          border-bottom: 1px solid @border-color-light;
          
          .ant-modal-title {
            font-size: @font-size-lg;
            font-weight: @font-weight-semibold;
            text-align: center;
          }
        }
        
        .ant-modal-close {
          top: @spacing-md;
          right: @spacing-md;
          width: @touch-target-min;
          height: @touch-target-min;
          .flex-center();
          
          .ant-modal-close-x {
            width: 100%;
            height: 100%;
            .flex-center();
            font-size: @font-size-lg;
          }
        }
        
        .ant-modal-body {
          padding: @spacing-lg;
          max-height: 60vh;
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
          
          .mobile-modal-content {
            // 内容样式
          }
        }
        
        .ant-modal-footer {
          padding: @spacing-lg;
          border-top: 1px solid @border-color-light;
          text-align: center;
          
          .mobile-modal-footer {
            .d-flex();
            gap: @spacing-md;
            
            .ant-btn {
              flex: 1;
              min-height: @touch-target-min;
              border-radius: @border-radius-md;
              font-size: @font-size-base;
            }
          }
        }
      }
    }
  }
  
  // 全屏模态框
  &.mobile-modal-fullscreen {
    .ant-modal {
      top: 0;
      height: 100vh;
      max-width: 100vw;
      
      .ant-modal-content {
        height: 100vh;
        border-radius: 0;
        display: flex;
        flex-direction: column;
        
        .ant-modal-body {
          flex: 1;
          max-height: none;
          overflow-y: auto;
        }
        
        .ant-modal-footer {
          flex-shrink: 0;
        }
      }
    }
  }
  
  // 底部滑入模态框
  &.mobile-modal-slide-up {
    .ant-modal {
      top: auto;
      bottom: 0;
      
      .ant-modal-content {
        border-radius: @border-radius-lg @border-radius-lg 0 0;
        margin-bottom: 0;
        
        // 添加拖拽指示器
        &::before {
          content: '';
          position: absolute;
          top: @spacing-sm;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 4px;
          background-color: @border-color-base;
          border-radius: 2px;
          z-index: 1;
        }
        
        .ant-modal-header {
          padding-top: (@spacing-lg + @spacing-sm);
        }
      }
    }
  }
}

// 全局模态框动画
:global(.mobile-modal-fullscreen) {
  .ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

:global(.mobile-modal-slide-up) {
  .ant-modal-wrap {
    .ant-modal {
      animation-name: slideUpIn;
      animation-duration: @animation-duration-mobile-base;
      animation-timing-function: @ease-out;
    }
  }
  
  &.ant-modal-wrap-leave {
    .ant-modal {
      animation-name: slideUpOut;
      animation-duration: @animation-duration-mobile-base;
      animation-timing-function: @ease-in;
    }
  }
}

@keyframes slideUpIn {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideUpOut {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

// 桌面端样式保持不变
.desktop-only {
  .mobile-modal {
    .ant-modal {
      margin: 48px auto;
      
      .ant-modal-content {
        border-radius: @border-radius-lg;
        
        .ant-modal-header {
          padding: @spacing-lg @spacing-xl;
          
          .ant-modal-title {
            text-align: left;
            font-size: @font-size-lg;
          }
        }
        
        .ant-modal-body {
          padding: @spacing-lg @spacing-xl;
          max-height: 60vh;
        }
        
        .ant-modal-footer {
          padding: @spacing-lg @spacing-xl;
          text-align: right;
          
          .mobile-modal-footer {
            justify-content: flex-end;
            
            .ant-btn {
              flex: none;
              min-width: 80px;
            }
          }
        }
      }
    }
  }
}
</style>
