<template>
  <div class="mobile-form" :class="responsiveClass">
    <a-form
      ref="form"
      :form="form"
      v-bind="formProps"
      @submit="handleSubmit"
    >
      <!-- 表单头部 -->
      <div v-if="showHeader" class="mobile-form-header">
        <div class="mobile-form-title">
          <slot name="title">{{ title }}</slot>
        </div>
        <div class="mobile-form-actions">
          <slot name="headerActions">
            <a-button 
              v-if="showReset" 
              type="text" 
              size="small" 
              @click="handleReset"
            >
              重置
            </a-button>
          </slot>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="mobile-form-content">
        <!-- 分组表单 -->
        <template v-if="groups.length > 0">
          <div
            v-for="(group, groupIndex) in groups"
            :key="group.key || groupIndex"
            class="mobile-form-group"
          >
            <div v-if="group.title" class="mobile-form-group-title">
              {{ group.title }}
            </div>
            <div class="mobile-form-group-content">
              <template v-for="field in group.fields">
                <mobile-form-item
                  :key="field.key"
                  :field="field"
                  :form="form"
                  :disabled="disabled"
                  @change="handleFieldChange"
                >
                  <template v-for="slot in Object.keys($scopedSlots)" :slot="slot" slot-scope="scope">
                    <slot :name="slot" v-bind="scope" />
                  </template>
                </mobile-form-item>
              </template>
            </div>
          </div>
        </template>

        <!-- 普通表单 -->
        <template v-else>
          <template v-for="field in fields">
            <mobile-form-item
              :key="field.key"
              :field="field"
              :form="form"
              :disabled="disabled"
              @change="handleFieldChange"
            >
              <template v-for="slot in Object.keys($scopedSlots)" :slot="slot" slot-scope="scope">
                <slot :name="slot" v-bind="scope" />
              </template>
            </mobile-form-item>
          </template>
        </template>

        <!-- 自定义内容 -->
        <div v-if="$slots.default" class="mobile-form-custom">
          <slot />
        </div>
      </div>

      <!-- 表单底部操作 -->
      <div v-if="showFooter" class="mobile-form-footer">
        <slot name="footer">
          <div class="mobile-form-buttons">
            <a-button
              v-if="showCancel"
              :size="buttonSize"
              @click="handleCancel"
            >
              {{ cancelText }}
            </a-button>
            <a-button
              v-if="showSubmit"
              type="primary"
              :size="buttonSize"
              :loading="submitting"
              @click="handleSubmit"
            >
              {{ submitText }}
            </a-button>
          </div>
        </slot>
      </div>
    </a-form>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { ComponentAdapterFactory } from '@/utils/component-adapter'
import MobileFormItem from './MobileFormItem.vue'

export default {
  name: 'MobileForm',
  components: {
    MobileFormItem
  },
  mixins: [ResponsiveMixin],
  props: {
    // 基础表单属性
    form: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      default: () => []
    },
    groups: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 表单配置
    title: {
      type: String,
      default: ''
    },
    showHeader: {
      type: Boolean,
      default: false
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    showReset: {
      type: Boolean,
      default: false
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    showSubmit: {
      type: Boolean,
      default: true
    },
    
    // 按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    submitText: {
      type: String,
      default: '确定'
    },
    
    // 提交状态
    submitting: {
      type: Boolean,
      default: false
    },
    
    // 布局配置
    layout: {
      type: String,
      default: 'vertical'
    },
    labelCol: {
      type: Object,
      default: null
    },
    wrapperCol: {
      type: Object,
      default: null
    }
  },

  computed: {
    // 表单适配器
    formAdapter() {
      return ComponentAdapterFactory.create('form')
    },

    // 适配后的表单属性
    formProps() {
      const baseProps = this.formAdapter.getAdaptedProps({
        layout: this.layout,
        labelCol: this.labelCol,
        wrapperCol: this.wrapperCol
      })

      // 移动端强制垂直布局
      if (this.$isMobile) {
        return {
          ...baseProps,
          layout: 'vertical',
          labelCol: undefined,
          wrapperCol: undefined
        }
      }

      return baseProps
    },

    // 按钮尺寸
    buttonSize() {
      return this.$isMobile ? 'large' : 'default'
    }
  },

  methods: {
    // 处理提交
    handleSubmit(e) {
      e.preventDefault()
      
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$emit('submit', values)
        } else {
          this.$emit('submitError', err)
          
          // 移动端滚动到第一个错误字段
          if (this.$isMobile) {
            this.scrollToFirstError()
          }
        }
      })
    },

    // 处理重置
    handleReset() {
      this.form.resetFields()
      this.$emit('reset')
    },

    // 处理取消
    handleCancel() {
      this.$emit('cancel')
    },

    // 处理字段变化
    handleFieldChange(field, value) {
      this.$emit('fieldChange', field, value)
    },

    // 滚动到第一个错误字段
    scrollToFirstError() {
      this.$nextTick(() => {
        const errorField = this.$el.querySelector('.ant-form-item-has-error')
        if (errorField) {
          errorField.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      })
    },

    // 验证表单
    validate(callback) {
      return this.form.validateFields(callback)
    },

    // 验证指定字段
    validateField(fields, callback) {
      return this.form.validateFields(fields, callback)
    },

    // 重置验证
    resetValidation() {
      this.form.resetFields()
    },

    // 设置字段值
    setFieldsValue(values) {
      this.form.setFieldsValue(values)
    },

    // 获取字段值
    getFieldsValue(fields) {
      return this.form.getFieldsValue(fields)
    },

    // 获取字段错误
    getFieldsError(fields) {
      return this.form.getFieldsError(fields)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../styles/mobile.less";

.mobile-form {
  .mobile-card();
  
  .mobile-form-header {
    .flex-between();
    padding: @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .mobile-form-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
    }
    
    .mobile-form-actions {
      .d-flex();
      gap: @spacing-xs;
    }
  }
  
  .mobile-form-content {
    padding: @spacing-lg;
    
    .mobile-form-group {
      margin-bottom: @spacing-xl;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .mobile-form-group-title {
        font-size: @font-size-base;
        font-weight: @font-weight-semibold;
        color: @text-color;
        margin-bottom: @spacing-md;
        padding-bottom: @spacing-sm;
        border-bottom: 1px solid @border-color-light;
      }
      
      .mobile-form-group-content {
        // 表单项间距由 MobileFormItem 控制
      }
    }
    
    .mobile-form-custom {
      margin-top: @spacing-lg;
    }
  }
  
  .mobile-form-footer {
    padding: @spacing-lg;
    border-top: 1px solid @border-color-light;
    background-color: @background-color-light;
    
    .mobile-form-buttons {
      .d-flex();
      gap: @spacing-md;
      
      .mobile-only({
        flex-direction: column;
        
        .ant-btn {
          width: 100%;
          margin: 0;
        }
      });
      
      .desktop-only({
        justify-content: flex-end;
        
        .ant-btn {
          min-width: 80px;
        }
      });
    }
  }
}

// 移动端表单优化
.mobile-only {
  .mobile-form {
    .ant-form-item {
      margin-bottom: @spacing-lg;
      
      .ant-form-item-label {
        padding-bottom: @spacing-sm;
        
        label {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          
          &.ant-form-item-required::before {
            margin-right: @spacing-xs;
          }
        }
      }
      
      .ant-form-item-control {
        .ant-input,
        .ant-select-selector,
        .ant-picker,
        .ant-input-number {
          min-height: @touch-target-min;
          font-size: @font-size-base;
          border-radius: @border-radius-md;
        }
        
        .ant-input-affix-wrapper {
          min-height: @touch-target-min;
          
          .ant-input {
            min-height: auto;
          }
        }
        
        .ant-select {
          .ant-select-selector {
            min-height: @touch-target-min;
            
            .ant-select-selection-item {
              line-height: (@touch-target-min - 2px);
            }
          }
        }
        
        .ant-checkbox-wrapper,
        .ant-radio-wrapper {
          min-height: @touch-target-min;
          .flex-center();
          
          .ant-checkbox,
          .ant-radio {
            margin-right: @spacing-sm;
          }
        }
        
        .ant-checkbox-group,
        .ant-radio-group {
          .ant-checkbox-wrapper,
          .ant-radio-wrapper {
            margin-bottom: @spacing-sm;
            margin-right: @spacing-lg;
          }
        }
      }
      
      .ant-form-item-explain {
        margin-top: @spacing-xs;
        font-size: @font-size-sm;
      }
    }
  }
}
</style>
