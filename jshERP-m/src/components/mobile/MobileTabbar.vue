<template>
  <div class="mobile-tabbar" :class="tabbarClass">
    <div 
      v-for="tab in tabs" 
      :key="tab.name"
      class="tabbar-item"
      :class="{ 'tabbar-item-active': activeTab === tab.name }"
      @click="handleTabClick(tab)"
    >
      <!-- 图标 -->
      <div class="tabbar-icon">
        <a-icon :type="tab.icon" />
        <div v-if="tab.badge" class="tabbar-badge" :class="getBadgeClass(tab.badge)">
          {{ getBadgeText(tab.badge) }}
        </div>
      </div>
      
      <!-- 标题 -->
      <div class="tabbar-title">{{ tab.title }}</div>
    </div>
  </div>
</template>

<script>
import { mobileNavigation } from '@/config/mobile-router.config'

export default {
  name: 'MobileTabbar',
  props: {
    activeTab: {
      type: String,
      default: 'dashboard'
    },
    fixed: {
      type: <PERSON>olean,
      default: true
    },
    theme: {
      type: String,
      default: 'light', // light | dark
      validator: value => ['light', 'dark'].includes(value)
    },
    customTabs: {
      type: Array,
      default: null
    }
  },
  computed: {
    tabs() {
      return this.customTabs || mobileNavigation
    },
    
    tabbarClass() {
      return {
        'tabbar-fixed': this.fixed,
        [`tabbar-${this.theme}`]: true
      }
    }
  },
  methods: {
    handleTabClick(tab) {
      if (tab.name !== this.activeTab) {
        this.$emit('change', tab.name, tab)
      }
    },
    
    getBadgeClass(badge) {
      if (typeof badge === 'string') {
        return {
          'badge-text': true,
          'badge-new': badge === 'new',
          'badge-hot': badge === 'hot'
        }
      } else if (typeof badge === 'number') {
        return {
          'badge-number': true,
          'badge-large': badge > 99
        }
      }
      return {}
    },
    
    getBadgeText(badge) {
      if (typeof badge === 'number') {
        return badge > 99 ? '99+' : badge.toString()
      }
      return badge
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-tabbar {
  display: flex;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #e8e8e8;
  z-index: 1000;
  
  &.tabbar-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  &.tabbar-dark {
    background-color: #1f1f1f;
    border-top-color: #333;
  }
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:active {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .tabbar-dark &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.tabbar-icon {
  position: relative;
  margin-bottom: 2px;
  
  .anticon {
    font-size: 20px;
    color: #999;
    transition: color 0.2s ease;
  }
  
  .tabbar-item-active & .anticon {
    color: #1890ff;
  }
  
  .tabbar-dark & .anticon {
    color: #666;
  }
  
  .tabbar-dark .tabbar-item-active & .anticon {
    color: #1890ff;
  }
}

.tabbar-title {
  font-size: 10px;
  color: #999;
  transition: color 0.2s ease;
  white-space: nowrap;
  
  .tabbar-item-active & {
    color: #1890ff;
  }
  
  .tabbar-dark & {
    color: #666;
  }
  
  .tabbar-dark .tabbar-item-active & {
    color: #1890ff;
  }
}

.tabbar-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 16px;
  height: 16px;
  padding: 0 4px;
  background-color: #ff4d4f;
  color: #fff;
  font-size: 10px;
  line-height: 16px;
  text-align: center;
  border-radius: 8px;
  transform: scale(0.8);
  
  &.badge-text {
    padding: 0 6px;
    border-radius: 8px;
    
    &.badge-new {
      background-color: #52c41a;
    }
    
    &.badge-hot {
      background-color: #fa541c;
    }
  }
  
  &.badge-number {
    &.badge-large {
      padding: 0 4px;
      min-width: 20px;
      height: 16px;
    }
  }
}

// 安全区域适配
@supports (padding: max(0px)) {
  .mobile-tabbar.tabbar-fixed {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
    height: calc(50px + max(0px, env(safe-area-inset-bottom)));
  }
}

// 响应式设计
@media (max-width: 375px) {
  .tabbar-title {
    font-size: 9px;
  }
  
  .tabbar-icon .anticon {
    font-size: 18px;
  }
}

// 动画效果
.tabbar-item {
  &:active {
    transform: scale(0.95);
  }
}

// 激活状态动画
.tabbar-item-active {
  .tabbar-icon {
    animation: tabbar-bounce 0.3s ease;
  }
}

@keyframes tabbar-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

// 毛玻璃效果（可选）
.mobile-tabbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
</style>
