<template>
  <div class="mobile-table" :class="responsiveClass">
    <!-- 移动端表格头部 -->
    <div v-if="showHeader" class="mobile-table-header">
      <div class="mobile-table-title">
        <slot name="title">{{ title }}</slot>
      </div>
      <div class="mobile-table-actions">
        <slot name="actions">
          <a-button 
            v-if="showRefresh" 
            type="text" 
            size="small" 
            @click="handleRefresh"
            :loading="loading"
          >
            <a-icon type="reload" />
          </a-button>
          <a-button 
            v-if="showSettings" 
            type="text" 
            size="small" 
            @click="showColumnSettings = true"
          >
            <a-icon type="setting" />
          </a-button>
        </slot>
      </div>
    </div>

    <!-- 移动端搜索栏 -->
    <div v-if="showSearch" class="mobile-table-search">
      <a-input-search
        v-model="searchValue"
        :placeholder="searchPlaceholder"
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
      />
    </div>

    <!-- 移动端筛选栏 -->
    <div v-if="showFilters && filters.length > 0" class="mobile-table-filters">
      <div class="filter-tags">
        <a-tag
          v-for="filter in activeFilters"
          :key="filter.key"
          closable
          @close="removeFilter(filter)"
        >
          {{ filter.label }}: {{ filter.value }}
        </a-tag>
      </div>
      <a-button type="text" size="small" @click="showFilterDrawer = true">
        <a-icon type="filter" />
        筛选
      </a-button>
    </div>

    <!-- 移动端表格内容 -->
    <div class="mobile-table-content">
      <!-- 桌面端表格 -->
      <a-table
        v-if="!isMobile"
        v-bind="tableProps"
        :columns="adaptedColumns"
        :dataSource="dataSource"
        :loading="loading"
        :pagination="adaptedPagination"
        :rowSelection="adaptedRowSelection"
        @change="handleTableChange"
      >
        <template v-for="slot in Object.keys($scopedSlots)" :slot="slot" slot-scope="scope">
          <slot :name="slot" v-bind="scope" />
        </template>
      </a-table>

      <!-- 移动端卡片列表 -->
      <div v-else class="mobile-table-list">
        <div v-if="loading" class="mobile-table-loading">
          <a-spin size="large" />
        </div>
        
        <div v-else-if="dataSource.length === 0" class="mobile-table-empty">
          <a-empty :description="emptyText" />
        </div>
        
        <div v-else class="mobile-table-items">
          <div
            v-for="(record, index) in dataSource"
            :key="getRowKey(record, index)"
            class="mobile-table-item"
            :class="{ 'selected': isRowSelected(record) }"
            @click="handleRowClick(record, index)"
          >
            <!-- 行选择 -->
            <div v-if="rowSelection" class="mobile-item-selection">
              <a-checkbox
                :checked="isRowSelected(record)"
                @change="handleRowSelect(record, $event)"
                @click.stop
              />
            </div>

            <!-- 卡片内容 -->
            <div class="mobile-item-content">
              <slot name="mobileItem" :record="record" :index="index">
                <div class="mobile-item-main">
                  <div class="mobile-item-title">
                    {{ getMobileTitle(record) }}
                  </div>
                  <div class="mobile-item-subtitle">
                    {{ getMobileSubtitle(record) }}
                  </div>
                </div>
                <div class="mobile-item-meta">
                  <div class="mobile-item-fields">
                    <div
                      v-for="field in mobileFields"
                      :key="field.key"
                      class="mobile-item-field"
                    >
                      <span class="field-label">{{ field.title }}:</span>
                      <span class="field-value">
                        <template v-if="field.customRender">
                          <component
                            :is="field.customRender"
                            :text="record[field.dataIndex]"
                            :record="record"
                            :index="index"
                          />
                        </template>
                        <template v-else>
                          {{ record[field.dataIndex] }}
                        </template>
                      </span>
                    </div>
                  </div>
                  <div v-if="hasActions" class="mobile-item-actions">
                    <slot name="mobileActions" :record="record" :index="index">
                      <a-button-group size="small">
                        <a-button
                          v-for="action in mobileActions"
                          :key="action.key"
                          :type="action.type || 'default'"
                          :icon="action.icon"
                          @click.stop="action.handler(record, index)"
                        >
                          {{ action.title }}
                        </a-button>
                      </a-button-group>
                    </slot>
                  </div>
                </div>
              </slot>
            </div>

            <!-- 展开内容 -->
            <div v-if="expandable && expandedRows.includes(getRowKey(record, index))" class="mobile-item-expanded">
              <slot name="expandedRowRender" :record="record" :index="index" />
            </div>
          </div>
        </div>

        <!-- 移动端分页 -->
        <div v-if="pagination && dataSource.length > 0" class="mobile-table-pagination">
          <a-pagination
            v-bind="adaptedPagination"
            @change="handlePageChange"
            @showSizeChange="handlePageSizeChange"
          />
        </div>
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <a-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      placement="bottom"
      :height="400"
    >
      <div class="mobile-filter-content">
        <div
          v-for="filter in filters"
          :key="filter.key"
          class="mobile-filter-item"
        >
          <div class="filter-label">{{ filter.title }}</div>
          <div class="filter-control">
            <component
              :is="filter.component || 'a-select'"
              v-model="filterValues[filter.key]"
              v-bind="filter.props"
              @change="handleFilterChange(filter.key, $event)"
            />
          </div>
        </div>
      </div>
      <div class="mobile-filter-actions">
        <a-button @click="clearFilters">清空</a-button>
        <a-button type="primary" @click="applyFilters">确定</a-button>
      </div>
    </a-drawer>

    <!-- 列设置抽屉 -->
    <a-drawer
      v-model="showColumnSettings"
      title="列设置"
      placement="right"
      :width="300"
    >
      <div class="mobile-column-settings">
        <div
          v-for="column in columns"
          :key="column.key || column.dataIndex"
          class="column-setting-item"
        >
          <a-checkbox
            :checked="!hiddenColumns.includes(column.key || column.dataIndex)"
            @change="toggleColumn(column.key || column.dataIndex, $event)"
          >
            {{ column.title }}
          </a-checkbox>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { ComponentAdapterFactory } from '@/utils/component-adapter'

export default {
  name: 'MobileTable',
  mixins: [ResponsiveMixin],
  props: {
    // 基础表格属性
    columns: {
      type: Array,
      required: true
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: [Object, Boolean],
      default: () => ({})
    },
    rowSelection: {
      type: Object,
      default: null
    },
    rowKey: {
      type: [String, Function],
      default: 'id'
    },
    
    // 移动端特有属性
    title: {
      type: String,
      default: ''
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    searchPlaceholder: {
      type: String,
      default: '请输入搜索关键词'
    },
    showFilters: {
      type: Boolean,
      default: false
    },
    filters: {
      type: Array,
      default: () => []
    },
    showRefresh: {
      type: Boolean,
      default: true
    },
    showSettings: {
      type: Boolean,
      default: true
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    
    // 移动端显示配置
    mobileTitle: {
      type: [String, Function],
      default: null
    },
    mobileSubtitle: {
      type: [String, Function],
      default: null
    },
    mobileFields: {
      type: Array,
      default: () => []
    },
    mobileActions: {
      type: Array,
      default: () => []
    },
    
    // 展开配置
    expandable: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      searchValue: '',
      showFilterDrawer: false,
      showColumnSettings: false,
      filterValues: {},
      activeFilters: [],
      hiddenColumns: [],
      expandedRows: [],
      selectedRowKeys: []
    }
  },

  computed: {
    // 表格适配器
    tableAdapter() {
      return ComponentAdapterFactory.create('table')
    },

    // 适配后的表格属性
    tableProps() {
      return this.tableAdapter.getAdaptedProps({
        size: this.$isMobile ? 'small' : 'default',
        scroll: this.$isMobile ? { x: true } : undefined,
        bordered: !this.$isMobile
      })
    },

    // 适配后的列配置
    adaptedColumns() {
      if (this.$isMobile) {
        return this.tableAdapter.adaptColumns(this.visibleColumns)
      }
      return this.visibleColumns
    },

    // 可见列
    visibleColumns() {
      return this.columns.filter(col => 
        !this.hiddenColumns.includes(col.key || col.dataIndex)
      )
    },

    // 适配后的分页配置
    adaptedPagination() {
      if (!this.pagination) return false
      
      const config = this.tableAdapter.getAdaptedProps().pagination
      return {
        ...config,
        ...this.pagination,
        simple: this.$isMobile && this.$breakpoint === 'xs'
      }
    },

    // 适配后的行选择配置
    adaptedRowSelection() {
      if (!this.rowSelection) return null
      
      return {
        ...this.rowSelection,
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.handleRowSelectionChange,
        columnWidth: this.$isMobile ? 40 : undefined,
        fixed: this.$isMobile
      }
    },

    // 是否有操作列
    hasActions() {
      return this.mobileActions.length > 0 || this.$scopedSlots.mobileActions
    }
  },

  methods: {
    // 获取行键
    getRowKey(record, index) {
      if (typeof this.rowKey === 'function') {
        return this.rowKey(record, index)
      }
      return record[this.rowKey] || index
    },

    // 检查行是否被选中
    isRowSelected(record) {
      const key = this.getRowKey(record)
      return this.selectedRowKeys.includes(key)
    },

    // 获取移动端标题
    getMobileTitle(record) {
      if (typeof this.mobileTitle === 'function') {
        return this.mobileTitle(record)
      }
      if (typeof this.mobileTitle === 'string') {
        return record[this.mobileTitle]
      }
      // 默认使用第一列数据
      const firstColumn = this.columns[0]
      return firstColumn ? record[firstColumn.dataIndex] : ''
    },

    // 获取移动端副标题
    getMobileSubtitle(record) {
      if (typeof this.mobileSubtitle === 'function') {
        return this.mobileSubtitle(record)
      }
      if (typeof this.mobileSubtitle === 'string') {
        return record[this.mobileSubtitle]
      }
      // 默认使用第二列数据
      const secondColumn = this.columns[1]
      return secondColumn ? record[secondColumn.dataIndex] : ''
    },

    // 处理表格变化
    handleTableChange(pagination, filters, sorter, extra) {
      this.$emit('change', pagination, filters, sorter, extra)
    },

    // 处理行点击
    handleRowClick(record, index) {
      this.$emit('rowClick', record, index)
    },

    // 处理行选择
    handleRowSelect(record, event) {
      const key = this.getRowKey(record)
      const checked = event.target.checked
      
      if (checked) {
        this.selectedRowKeys.push(key)
      } else {
        const index = this.selectedRowKeys.indexOf(key)
        if (index > -1) {
          this.selectedRowKeys.splice(index, 1)
        }
      }
      
      this.handleRowSelectionChange(this.selectedRowKeys)
    },

    // 处理行选择变化
    handleRowSelectionChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.$emit('selectionChange', selectedRowKeys, selectedRows)
    },

    // 处理搜索
    handleSearch(value) {
      this.$emit('search', value)
    },

    // 处理搜索变化
    handleSearchChange(e) {
      this.searchValue = e.target.value
      this.$emit('searchChange', this.searchValue)
    },

    // 处理刷新
    handleRefresh() {
      this.$emit('refresh')
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.$emit('pageChange', page, pageSize)
    },

    // 处理页面大小变化
    handlePageSizeChange(current, size) {
      this.$emit('pageSizeChange', current, size)
    },

    // 处理筛选变化
    handleFilterChange(key, value) {
      this.filterValues[key] = value
    },

    // 应用筛选
    applyFilters() {
      this.activeFilters = Object.keys(this.filterValues)
        .filter(key => this.filterValues[key] !== undefined && this.filterValues[key] !== '')
        .map(key => {
          const filter = this.filters.find(f => f.key === key)
          return {
            key,
            label: filter.title,
            value: this.filterValues[key]
          }
        })
      
      this.showFilterDrawer = false
      this.$emit('filter', this.filterValues)
    },

    // 清空筛选
    clearFilters() {
      this.filterValues = {}
      this.activeFilters = []
      this.$emit('filter', {})
    },

    // 移除筛选
    removeFilter(filter) {
      delete this.filterValues[filter.key]
      this.activeFilters = this.activeFilters.filter(f => f.key !== filter.key)
      this.$emit('filter', this.filterValues)
    },

    // 切换列显示
    toggleColumn(columnKey, event) {
      const checked = event.target.checked
      if (checked) {
        const index = this.hiddenColumns.indexOf(columnKey)
        if (index > -1) {
          this.hiddenColumns.splice(index, 1)
        }
      } else {
        this.hiddenColumns.push(columnKey)
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../styles/mobile.less";

.mobile-table {
  .mobile-card();
  
  .mobile-table-header {
    .flex-between();
    padding: @spacing-md @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .mobile-table-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
    }
    
    .mobile-table-actions {
      .d-flex();
      gap: @spacing-xs;
    }
  }
  
  .mobile-table-search {
    padding: @spacing-md @spacing-lg;
    border-bottom: 1px solid @border-color-light;
  }
  
  .mobile-table-filters {
    .flex-between();
    padding: @spacing-sm @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .filter-tags {
      .d-flex();
      flex-wrap: wrap;
      gap: @spacing-xs;
    }
  }
  
  .mobile-table-content {
    .mobile-table-list {
      .mobile-table-loading {
        .flex-center();
        padding: @spacing-xxl;
      }
      
      .mobile-table-empty {
        .flex-center();
        padding: @spacing-xxl;
      }
      
      .mobile-table-items {
        .mobile-table-item {
          .d-flex();
          padding: @spacing-md @spacing-lg;
          border-bottom: 1px solid @border-color-light;
          .touch-feedback();
          
          &.selected {
            background-color: @primary-color-light;
          }
          
          .mobile-item-selection {
            margin-right: @spacing-md;
            .flex-center();
          }
          
          .mobile-item-content {
            flex: 1;
            
            .mobile-item-main {
              margin-bottom: @spacing-sm;
              
              .mobile-item-title {
                font-size: @font-size-base;
                font-weight: @font-weight-medium;
                color: @text-color;
                margin-bottom: @spacing-xs;
                .text-ellipsis();
              }
              
              .mobile-item-subtitle {
                font-size: @font-size-sm;
                color: @text-color-secondary;
                .text-ellipsis();
              }
            }
            
            .mobile-item-meta {
              .mobile-item-fields {
                .mobile-item-field {
                  .d-flex();
                  margin-bottom: @spacing-xs;
                  
                  .field-label {
                    min-width: 80px;
                    font-size: @font-size-sm;
                    color: @text-color-tertiary;
                  }
                  
                  .field-value {
                    flex: 1;
                    font-size: @font-size-sm;
                    color: @text-color-secondary;
                  }
                }
              }
              
              .mobile-item-actions {
                margin-top: @spacing-sm;
                text-align: right;
              }
            }
          }
          
          .mobile-item-expanded {
            margin-top: @spacing-md;
            padding-top: @spacing-md;
            border-top: 1px solid @border-color-light;
          }
        }
      }
      
      .mobile-table-pagination {
        padding: @spacing-lg;
        text-align: center;
      }
    }
  }
}

.mobile-filter-content {
  .mobile-filter-item {
    margin-bottom: @spacing-lg;
    
    .filter-label {
      font-size: @font-size-base;
      font-weight: @font-weight-medium;
      margin-bottom: @spacing-sm;
    }
    
    .filter-control {
      width: 100%;
    }
  }
}

.mobile-filter-actions {
  .flex-between();
  padding-top: @spacing-lg;
  border-top: 1px solid @border-color-light;
  
  .ant-btn {
    flex: 1;
    margin: 0 @spacing-xs;
    
    &:first-child {
      margin-left: 0;
    }
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.mobile-column-settings {
  .column-setting-item {
    padding: @spacing-md 0;
    border-bottom: 1px solid @border-color-light;
    
    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
