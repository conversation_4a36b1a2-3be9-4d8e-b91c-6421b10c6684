/**
 * 移动端组件库入口文件
 * 统一导出所有移动端组件
 */

import MobileTable from './MobileTable.vue'
import MobileForm from './MobileForm.vue'
import MobileFormItem from './MobileFormItem.vue'
import MobileModal from './MobileModal.vue'
import MobileDrawer from './MobileDrawer.vue'
import MobileList from './MobileList.vue'

// 组件列表
const components = [
  MobileTable,
  MobileForm,
  MobileFormItem,
  MobileModal,
  MobileDrawer,
  MobileList
]

// 安装函数
const install = function(Vue) {
  // 注册所有组件
  components.forEach(component => {
    Vue.component(component.name, component)
  })
}

// 自动安装
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export {
  // 组件
  MobileTable,
  MobileForm,
  MobileFormItem,
  MobileModal,
  MobileDrawer,
  MobileList,
  
  // 安装函数
  install
}

export default {
  install,
  MobileTable,
  MobileForm,
  MobileFormItem,
  MobileModal,
  MobileDrawer,
  MobileList
}
