<template>
  <div class="mobile-header" :class="headerClass">
    <!-- 左侧区域 -->
    <div class="header-left">
      <div 
        v-if="showBack" 
        class="header-btn back-btn"
        @click="handleBack"
      >
        <a-icon type="left" />
      </div>
      <div 
        v-else-if="showMenu" 
        class="header-btn menu-btn"
        @click="handleMenu"
      >
        <a-icon type="menu" />
      </div>
    </div>
    
    <!-- 中间标题区域 -->
    <div class="header-center">
      <div class="header-title">{{ title }}</div>
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <slot name="right">
        <div 
          v-if="showSearch" 
          class="header-btn search-btn"
          @click="handleSearch"
        >
          <a-icon type="search" />
        </div>
        <div 
          v-if="showMore" 
          class="header-btn more-btn"
          @click="handleMore"
        >
          <a-icon type="ellipsis" />
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileHeader',
  props: {
    title: {
      type: String,
      default: 'jshERP移动端'
    },
    showBack: {
      type: Boolean,
      default: false
    },
    showMenu: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    showMore: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: true
    },
    transparent: {
      type: Boolean,
      default: false
    },
    theme: {
      type: String,
      default: 'light', // light | dark
      validator: value => ['light', 'dark'].includes(value)
    }
  },
  computed: {
    headerClass() {
      return {
        'header-fixed': this.fixed,
        'header-transparent': this.transparent,
        [`header-${this.theme}`]: true
      }
    }
  },
  methods: {
    handleBack() {
      this.$emit('back')
    },
    
    handleMenu() {
      this.$emit('menu')
    },
    
    handleSearch() {
      this.$emit('search')
    },
    
    handleMore() {
      this.$emit('more')
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-header {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  z-index: 1000;
  
  &.header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  
  &.header-transparent {
    background-color: transparent;
    border-bottom: none;
  }
  
  &.header-dark {
    background-color: #1f1f1f;
    border-bottom-color: #333;
    color: #fff;
  }
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  min-width: 44px;
}

.header-left {
  justify-content: flex-start;
}

.header-right {
  justify-content: flex-end;
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}

.header-title {
  font-size: 17px;
  font-weight: 600;
  color: #333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  
  .header-dark & {
    color: #fff;
  }
}

.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  &:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.95);
  }
  
  .header-dark &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .header-dark &:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .anticon {
    font-size: 18px;
    color: #333;
    
    .header-dark & {
      color: #fff;
    }
  }
}

// 安全区域适配
@supports (padding: max(0px)) {
  .mobile-header.header-fixed {
    padding-top: max(0px, env(safe-area-inset-top));
    height: calc(44px + max(0px, env(safe-area-inset-top)));
  }
}

// 响应式设计
@media (max-width: 375px) {
  .header-title {
    max-width: 150px;
    font-size: 16px;
  }
}

// 动画效果
.mobile-header {
  transition: all 0.3s ease;
}

// 毛玻璃效果（可选）
.header-transparent {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
</style>
