<template>
  <div class="pos-sale">
    <!-- 移动端头部 -->
    <mobile-header 
      title="POS销售" 
      :showBack="true"
      :showActions="true"
      @back="handleBack"
    >
      <template slot="actions">
        <a-button 
          type="link" 
          size="small" 
          @click="showHistory = true"
        >
          <a-icon type="history" />
          历史
        </a-button>
      </template>
    </mobile-header>

    <!-- 购物车摘要 -->
    <div class="cart-summary" v-if="cartItems.length > 0">
      <div class="cart-info">
        <div class="cart-count">
          <a-icon type="shopping-cart" />
          {{ cartItems.length }}件商品
        </div>
        <div class="cart-total">
          合计：<span class="amount">¥{{ formatMoney(cartTotal) }}</span>
        </div>
      </div>
      <a-button 
        type="primary" 
        size="large"
        @click="showCheckout = true"
        :disabled="cartItems.length === 0"
      >
        去结算
      </a-button>
    </div>

    <!-- 商品搜索 -->
    <div class="product-search">
      <a-input-search
        v-model="searchKeyword"
        placeholder="搜索商品名称、条码"
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
      >
        <template slot="enterButton">
          <a-icon type="search" />
        </template>
      </a-input-search>
      
      <!-- 扫码按钮 -->
      <a-button 
        class="scan-btn"
        @click="startScan"
        :loading="scanning"
      >
        <a-icon type="scan" />
        扫码
      </a-button>
    </div>

    <!-- 商品分类 -->
    <div class="category-tabs">
      <div class="category-scroll">
        <div 
          v-for="category in categories"
          :key="category.id"
          class="category-item"
          :class="{ active: selectedCategoryId === category.id }"
          @click="selectCategory(category.id)"
        >
          {{ category.name }}
        </div>
      </div>
    </div>

    <!-- 商品网格 -->
    <div class="product-grid">
      <div 
        v-for="product in filteredProducts"
        :key="product.id"
        class="product-card"
        @click="addToCart(product)"
      >
        <div class="product-image">
          <img 
            :src="product.imgUrl || '/img/default-product.png'" 
            :alt="product.name"
            @error="handleImageError"
          />
          <div v-if="getCartQuantity(product.id) > 0" class="quantity-badge">
            {{ getCartQuantity(product.id) }}
          </div>
        </div>
        <div class="product-info">
          <div class="product-name">{{ product.name }}</div>
          <div class="product-price">¥{{ formatMoney(product.commodityDecimalPrice) }}</div>
          <div class="product-stock">库存: {{ product.currentStock || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- 购物车抽屉 -->
    <mobile-drawer
      v-model="showCart"
      title="购物车"
      placement="bottom"
      :height="400"
    >
      <div class="cart-content">
        <div class="cart-items">
          <div 
            v-for="item in cartItems"
            :key="item.id"
            class="cart-item"
          >
            <div class="item-image">
              <img :src="item.imgUrl || '/img/default-product.png'" :alt="item.name" />
            </div>
            <div class="item-info">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-price">¥{{ formatMoney(item.unitPrice) }}</div>
            </div>
            <div class="item-controls">
              <a-button 
                size="small" 
                @click="decreaseQuantity(item.id)"
                :disabled="item.quantity <= 1"
              >
                <a-icon type="minus" />
              </a-button>
              <span class="quantity">{{ item.quantity }}</span>
              <a-button 
                size="small" 
                @click="increaseQuantity(item.id)"
              >
                <a-icon type="plus" />
              </a-button>
            </div>
            <div class="item-total">
              ¥{{ formatMoney(item.quantity * item.unitPrice) }}
            </div>
          </div>
        </div>
        
        <div class="cart-footer">
          <div class="cart-total-info">
            <div class="total-amount">
              合计：¥{{ formatMoney(cartTotal) }}
            </div>
          </div>
          <a-button 
            type="primary" 
            size="large" 
            block
            @click="showCheckout = true"
          >
            去结算 ({{ cartItems.length }}件)
          </a-button>
        </div>
      </div>
    </mobile-drawer>

    <!-- 结算页面 -->
    <mobile-modal
      v-model="showCheckout"
      title="结算"
      :fullscreen="true"
      :showFooter="false"
    >
      <checkout-form
        :cartItems="cartItems"
        :totalAmount="cartTotal"
        @submit="handleCheckoutSubmit"
        @cancel="showCheckout = false"
      />
    </mobile-modal>

    <!-- 历史订单 -->
    <mobile-drawer
      v-model="showHistory"
      title="销售历史"
      placement="right"
      :width="320"
    >
      <sale-history @select="loadHistoryOrder" />
    </mobile-drawer>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileHeader, MobileDrawer, MobileModal } from '@/components/mobile'
import CheckoutForm from './components/CheckoutForm'
import SaleHistory from './components/SaleHistory'
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'POSSale',
  mixins: [ResponsiveMixin],
  components: {
    MobileHeader,
    MobileDrawer,
    MobileModal,
    CheckoutForm,
    SaleHistory
  },

  data() {
    return {
      // 搜索相关
      searchKeyword: '',
      scanning: false,
      
      // 分类相关
      categories: [
        { id: '', name: '全部' }
      ],
      selectedCategoryId: '',
      
      // 商品相关
      products: [],
      filteredProducts: [],
      loading: false,
      
      // 购物车相关
      cartItems: [],
      showCart: false,
      showCheckout: false,
      
      // 历史订单
      showHistory: false
    }
  },

  computed: {
    // 购物车总金额
    cartTotal() {
      return this.cartItems.reduce((sum, item) => {
        return sum + (item.quantity * item.unitPrice)
      }, 0)
    }
  },

  mounted() {
    this.loadCategories()
    this.loadProducts()
  },

  methods: {
    // 返回上一页
    handleBack() {
      this.$router.go(-1)
    },

    // 加载商品分类
    async loadCategories() {
      try {
        const response = await getAction('/materialCategory/list')
        if (response.success) {
          this.categories = [
            { id: '', name: '全部' },
            ...response.result
          ]
        }
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    },

    // 加载商品列表
    async loadProducts() {
      this.loading = true
      try {
        const params = {
          pageNo: 1,
          pageSize: 100,
          categoryId: this.selectedCategoryId,
          keyword: this.searchKeyword
        }
        
        const response = await getAction('/material/list', params)
        if (response.success) {
          this.products = response.result.records || []
          this.filterProducts()
        }
      } catch (error) {
        console.error('加载商品失败:', error)
        this.$message.error('加载商品失败')
      } finally {
        this.loading = false
      }
    },

    // 筛选商品
    filterProducts() {
      let filtered = this.products
      
      // 按分类筛选
      if (this.selectedCategoryId) {
        filtered = filtered.filter(p => p.categoryId === this.selectedCategoryId)
      }
      
      // 按关键词筛选
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(p => 
          p.name.toLowerCase().includes(keyword) ||
          p.barCode?.toLowerCase().includes(keyword)
        )
      }
      
      this.filteredProducts = filtered
    },

    // 选择分类
    selectCategory(categoryId) {
      this.selectedCategoryId = categoryId
      this.filterProducts()
    },

    // 搜索处理
    handleSearch() {
      this.filterProducts()
    },

    handleSearchChange() {
      // 实时搜索
      setTimeout(() => {
        this.filterProducts()
      }, 300)
    },

    // 扫码功能
    startScan() {
      this.scanning = true
      // 这里可以集成扫码功能
      setTimeout(() => {
        this.scanning = false
        this.$message.info('扫码功能开发中...')
      }, 1000)
    },

    // 添加到购物车
    addToCart(product) {
      const existingItem = this.cartItems.find(item => item.id === product.id)
      
      if (existingItem) {
        existingItem.quantity += 1
      } else {
        this.cartItems.push({
          id: product.id,
          name: product.name,
          imgUrl: product.imgUrl,
          unitPrice: product.commodityDecimalPrice || 0,
          quantity: 1,
          stock: product.currentStock || 0
        })
      }
      
      // 触觉反馈
      if (navigator.vibrate) {
        navigator.vibrate(50)
      }
    },

    // 获取购物车中商品数量
    getCartQuantity(productId) {
      const item = this.cartItems.find(item => item.id === productId)
      return item ? item.quantity : 0
    },

    // 增加数量
    increaseQuantity(itemId) {
      const item = this.cartItems.find(item => item.id === itemId)
      if (item) {
        item.quantity += 1
      }
    },

    // 减少数量
    decreaseQuantity(itemId) {
      const item = this.cartItems.find(item => item.id === itemId)
      if (item && item.quantity > 1) {
        item.quantity -= 1
      } else if (item && item.quantity === 1) {
        // 移除商品
        const index = this.cartItems.findIndex(item => item.id === itemId)
        this.cartItems.splice(index, 1)
      }
    },

    // 处理结算提交
    async handleCheckoutSubmit(orderData) {
      try {
        const response = await postAction('/depotHead/addDepotHeadAndDetail', {
          ...orderData,
          type: '出库',
          subType: '零售',
          materialList: this.cartItems.map(item => ({
            materialId: item.id,
            operNumber: item.quantity,
            unitPrice: item.unitPrice,
            allPrice: item.quantity * item.unitPrice
          }))
        })
        
        if (response.success) {
          this.$message.success('销售成功!')
          this.cartItems = []
          this.showCheckout = false
          // 可以跳转到订单详情或打印页面
        } else {
          this.$message.error(response.message || '销售失败')
        }
      } catch (error) {
        console.error('提交订单失败:', error)
        this.$message.error('提交订单失败')
      }
    },

    // 加载历史订单
    loadHistoryOrder(order) {
      // 加载历史订单到当前购物车
      this.cartItems = order.items || []
      this.showHistory = false
    },

    // 图片加载错误处理
    handleImageError(event) {
      event.target.src = '/img/default-product.png'
    },

    // 格式化金额
    formatMoney(amount) {
      return Number(amount || 0).toFixed(2)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mobile/variables.less';

.pos-sale {
  background-color: @background-color-light;
  min-height: 100vh;
  padding-bottom: 80px; // 为购物车摘要留空间

  // 购物车摘要（固定在底部）
  .cart-summary {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: @spacing-md @spacing-lg;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .cart-info {
      flex: 1;

      .cart-count {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-bottom: @spacing-xs;

        .anticon {
          margin-right: @spacing-xs;
          color: @primary-color;
        }
      }

      .cart-total {
        font-size: @font-size-base;
        color: @text-color;

        .amount {
          font-size: @font-size-lg;
          font-weight: 600;
          color: @primary-color;
        }
      }
    }

    .ant-btn {
      margin-left: @spacing-md;
      min-width: 100px;
    }
  }

  // 商品搜索
  .product-search {
    padding: @spacing-lg;
    background: white;
    margin-bottom: @spacing-sm;
    display: flex;
    gap: @spacing-md;

    .ant-input-search {
      flex: 1;
    }

    .scan-btn {
      min-width: 60px;
      .flex-center();

      .anticon {
        font-size: 18px;
      }
    }
  }

  // 商品分类
  .category-tabs {
    background: white;
    padding: @spacing-md @spacing-lg;
    margin-bottom: @spacing-sm;

    .category-scroll {
      display: flex;
      overflow-x: auto;
      gap: @spacing-md;

      &::-webkit-scrollbar {
        display: none;
      }

      .category-item {
        flex-shrink: 0;
        padding: @spacing-sm @spacing-md;
        background: @background-color-light;
        border-radius: 20px;
        font-size: @font-size-sm;
        color: @text-color-secondary;
        cursor: pointer;
        transition: all 0.3s;

        &.active {
          background: @primary-color;
          color: white;
        }

        &:hover {
          background: @primary-color-light;
          color: @primary-color;
        }
      }
    }
  }

  // 商品网格
  .product-grid {
    padding: 0 @spacing-lg @spacing-lg;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: @spacing-md;

    .product-card {
      background: white;
      border-radius: 12px;
      padding: @spacing-md;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s;
      position: relative;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      .product-image {
        position: relative;
        width: 100%;
        height: 120px;
        margin-bottom: @spacing-sm;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
          background: @background-color-light;
        }

        .quantity-badge {
          position: absolute;
          top: -6px;
          right: -6px;
          background: @error-color;
          color: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          .flex-center();
          font-size: @font-size-xs;
          font-weight: 600;
        }
      }

      .product-info {
        .product-name {
          font-size: @font-size-base;
          color: @text-color;
          margin-bottom: @spacing-xs;
          font-weight: 500;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-price {
          font-size: @font-size-lg;
          color: @primary-color;
          font-weight: 600;
          margin-bottom: @spacing-xs;
        }

        .product-stock {
          font-size: @font-size-xs;
          color: @text-color-tertiary;
        }
      }
    }
  }

  // 购物车内容
  .cart-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .cart-items {
      flex: 1;
      overflow-y: auto;
      padding: @spacing-md;

      .cart-item {
        display: flex;
        align-items: center;
        padding: @spacing-md;
        background: white;
        border-radius: 8px;
        margin-bottom: @spacing-sm;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);

        .item-image {
          width: 60px;
          height: 60px;
          margin-right: @spacing-md;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
          }
        }

        .item-info {
          flex: 1;

          .item-name {
            font-size: @font-size-base;
            color: @text-color;
            margin-bottom: @spacing-xs;
            font-weight: 500;
          }

          .item-price {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }

        .item-controls {
          display: flex;
          align-items: center;
          margin: 0 @spacing-md;

          .ant-btn {
            width: 32px;
            height: 32px;
            .flex-center();
            border-radius: 50%;
          }

          .quantity {
            margin: 0 @spacing-md;
            font-size: @font-size-base;
            font-weight: 500;
            min-width: 24px;
            text-align: center;
          }
        }

        .item-total {
          font-size: @font-size-base;
          color: @primary-color;
          font-weight: 600;
          min-width: 60px;
          text-align: right;
        }
      }
    }

    .cart-footer {
      padding: @spacing-lg;
      background: white;
      border-top: 1px solid @border-color-split;

      .cart-total-info {
        margin-bottom: @spacing-md;

        .total-amount {
          font-size: @font-size-lg;
          color: @text-color;
          font-weight: 600;
          text-align: center;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 480px) {
  .pos-sale {
    .product-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: @spacing-sm;
      padding: 0 @spacing-md @spacing-md;

      .product-card {
        padding: @spacing-sm;

        .product-image {
          height: 100px;
        }

        .product-info {
          .product-name {
            font-size: @font-size-sm;
          }

          .product-price {
            font-size: @font-size-base;
          }
        }
      }
    }
  }
}
</style>
