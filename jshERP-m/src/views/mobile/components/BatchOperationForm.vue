<template>
  <div class="batch-operation-form">
    <!-- 操作类型说明 -->
    <div class="operation-info">
      <div class="info-title">{{ operationTitle }}</div>
      <div class="info-description">{{ operationDescription }}</div>
      <div class="info-count">已选择 {{ materials.length }} 个商品</div>
    </div>

    <!-- 批量编辑表单 -->
    <div v-if="type === 'edit'" class="batch-edit-form">
      <mobile-form
        ref="editForm"
        :fields="editFields"
        :model="editModel"
        @change="handleFormChange"
      />
    </div>

    <!-- 批量修正库存表单 -->
    <div v-if="type === 'stock'" class="batch-stock-form">
      <div class="stock-operation-type">
        <a-radio-group v-model="stockOperationType" @change="handleStockTypeChange">
          <a-radio value="set">设置库存</a-radio>
          <a-radio value="adjust">调整库存</a-radio>
        </a-radio-group>
      </div>
      
      <mobile-form
        ref="stockForm"
        :fields="stockFields"
        :model="stockModel"
        @change="handleFormChange"
      />
      
      <!-- 库存预览 -->
      <div class="stock-preview">
        <div class="preview-title">库存变化预览</div>
        <div class="preview-list">
          <div 
            v-for="material in materials" 
            :key="material.id"
            class="preview-item"
          >
            <div class="material-info">
              <div class="material-name">{{ material.name }}</div>
              <div class="material-code">{{ material.materialNumber }}</div>
            </div>
            <div class="stock-change">
              <span class="current-stock">{{ material.currentStock || 0 }}</span>
              <a-icon type="arrow-right" />
              <span class="new-stock">{{ calculateNewStock(material) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量修正价格表单 -->
    <div v-if="type === 'price'" class="batch-price-form">
      <div class="price-operation-type">
        <a-radio-group v-model="priceOperationType" @change="handlePriceTypeChange">
          <a-radio value="set">设置价格</a-radio>
          <a-radio value="adjust">调整价格</a-radio>
        </a-radio-group>
      </div>
      
      <mobile-form
        ref="priceForm"
        :fields="priceFields"
        :model="priceModel"
        @change="handleFormChange"
      />
      
      <!-- 价格预览 -->
      <div class="price-preview">
        <div class="preview-title">价格变化预览</div>
        <div class="preview-list">
          <div 
            v-for="material in materials" 
            :key="material.id"
            class="preview-item"
          >
            <div class="material-info">
              <div class="material-name">{{ material.name }}</div>
              <div class="material-code">{{ material.materialNumber }}</div>
            </div>
            <div class="price-changes">
              <div class="price-change" v-if="priceModel.purchasePrice !== ''">
                <span class="price-label">采购价:</span>
                <span class="current-price">¥{{ material.purchaseDecimalPrice || 0 }}</span>
                <a-icon type="arrow-right" />
                <span class="new-price">¥{{ calculateNewPrice(material, 'purchase') }}</span>
              </div>
              <div class="price-change" v-if="priceModel.retailPrice !== ''">
                <span class="price-label">零售价:</span>
                <span class="current-price">¥{{ material.commodityDecimalPrice || 0 }}</span>
                <a-icon type="arrow-right" />
                <span class="new-price">¥{{ calculateNewPrice(material, 'retail') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中商品列表 -->
    <div class="selected-materials">
      <div class="materials-title">选中的商品</div>
      <div class="materials-list">
        <div 
          v-for="material in materials" 
          :key="material.id"
          class="material-item"
        >
          <a-avatar
            :size="32"
            :src="material.imgUrl"
            :style="{ backgroundColor: material.imgUrl ? 'transparent' : '#f56a00' }"
          >
            {{ material.imgUrl ? '' : (material.name ? material.name.charAt(0) : '商') }}
          </a-avatar>
          <div class="material-content">
            <div class="material-name">{{ material.name }}</div>
            <div class="material-info">
              <span>编号: {{ material.materialNumber || '无' }}</span>
              <span>库存: {{ material.currentStock || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { MobileForm } from '@/components/mobile'

export default {
  name: 'BatchOperationForm',
  components: {
    MobileForm
  },
  
  props: {
    type: {
      type: String,
      required: true,
      validator: value => ['edit', 'stock', 'price'].includes(value)
    },
    materials: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      // 批量编辑
      editModel: {
        materialCategoryId: '',
        enabled: '',
        lowSafeStock: '',
        highSafeStock: '',
        remark: ''
      },
      
      // 批量库存
      stockModel: {
        stockValue: '',
        reason: ''
      },
      stockOperationType: 'set', // set: 设置, adjust: 调整
      
      // 批量价格
      priceModel: {
        purchasePrice: '',
        retailPrice: '',
        wholesalePrice: '',
        adjustType: 'amount' // amount: 金额, percent: 百分比
      },
      priceOperationType: 'set', // set: 设置, adjust: 调整
      
      // 表单字段配置
      editFields: [
        {
          key: 'materialCategoryId',
          type: 'select',
          label: '商品分类',
          placeholder: '不修改请留空',
          options: []
        },
        {
          key: 'enabled',
          type: 'radio',
          label: '状态',
          options: [
            { value: '', label: '不修改' },
            { value: '1', label: '启用' },
            { value: '0', label: '禁用' }
          ]
        },
        {
          key: 'lowSafeStock',
          type: 'number',
          label: '最低库存',
          placeholder: '不修改请留空',
          min: 0,
          precision: 0
        },
        {
          key: 'highSafeStock',
          type: 'number',
          label: '最高库存',
          placeholder: '不修改请留空',
          min: 0,
          precision: 0
        },
        {
          key: 'remark',
          type: 'textarea',
          label: '备注',
          placeholder: '不修改请留空',
          rows: 3
        }
      ],
      
      stockFields: [
        {
          key: 'stockValue',
          type: 'number',
          label: '库存数量',
          placeholder: '请输入库存数量',
          required: true,
          min: 0,
          precision: 0
        },
        {
          key: 'reason',
          type: 'textarea',
          label: '调整原因',
          placeholder: '请输入调整原因',
          required: true,
          rows: 3
        }
      ],
      
      priceFields: [
        {
          key: 'purchasePrice',
          type: 'number',
          label: '采购价格',
          placeholder: '不修改请留空',
          min: 0,
          precision: 2
        },
        {
          key: 'retailPrice',
          type: 'number',
          label: '零售价格',
          placeholder: '不修改请留空',
          min: 0,
          precision: 2
        },
        {
          key: 'wholesalePrice',
          type: 'number',
          label: '批发价格',
          placeholder: '不修改请留空',
          min: 0,
          precision: 2
        }
      ]
    }
  },

  computed: {
    operationTitle() {
      const titleMap = {
        edit: '批量编辑商品',
        stock: '批量修正库存',
        price: '批量修正价格'
      }
      return titleMap[this.type] || '批量操作'
    },

    operationDescription() {
      const descMap = {
        edit: '批量修改选中商品的基本信息，留空的字段将不会被修改',
        stock: '批量修正选中商品的库存数量，请谨慎操作',
        price: '批量修正选中商品的价格信息，留空的价格将不会被修改'
      }
      return descMap[this.type] || ''
    }
  },

  watch: {
    stockOperationType(newType) {
      // 更新库存字段标签
      const stockField = this.stockFields.find(f => f.key === 'stockValue')
      if (stockField) {
        stockField.label = newType === 'set' ? '设置库存' : '调整数量'
        stockField.placeholder = newType === 'set' ? '请输入新的库存数量' : '请输入调整数量（正数增加，负数减少）'
      }
    },

    priceOperationType(newType) {
      // 更新价格字段配置
      this.priceFields.forEach(field => {
        if (newType === 'adjust') {
          field.placeholder = field.placeholder.replace('不修改请留空', '调整金额（正数增加，负数减少）')
        } else {
          field.placeholder = field.placeholder.replace('调整金额（正数增加，负数减少）', '不修改请留空')
        }
      })
    }
  },

  methods: {
    // 处理表单变化
    handleFormChange(field, value) {
      this.$emit('change', field, value)
    },

    // 处理库存操作类型变化
    handleStockTypeChange(e) {
      this.stockOperationType = e.target.value
      this.stockModel.stockValue = ''
    },

    // 处理价格操作类型变化
    handlePriceTypeChange(e) {
      this.priceOperationType = e.target.value
      this.priceModel = {
        purchasePrice: '',
        retailPrice: '',
        wholesalePrice: '',
        adjustType: 'amount'
      }
    },

    // 计算新库存
    calculateNewStock(material) {
      const currentStock = Number(material.currentStock || 0)
      const stockValue = Number(this.stockModel.stockValue || 0)
      
      if (this.stockOperationType === 'set') {
        return stockValue
      } else {
        return Math.max(0, currentStock + stockValue)
      }
    },

    // 计算新价格
    calculateNewPrice(material, priceType) {
      const priceFieldMap = {
        purchase: 'purchasePrice',
        retail: 'retailPrice',
        wholesale: 'wholesalePrice'
      }
      
      const materialPriceMap = {
        purchase: 'purchaseDecimalPrice',
        retail: 'commodityDecimalPrice',
        wholesale: 'wholesaleDecimalPrice'
      }
      
      const currentPrice = Number(material[materialPriceMap[priceType]] || 0)
      const newPriceValue = Number(this.priceModel[priceFieldMap[priceType]] || 0)
      
      if (this.priceOperationType === 'set') {
        return newPriceValue.toFixed(2)
      } else {
        return Math.max(0, currentPrice + newPriceValue).toFixed(2)
      }
    },

    // 获取操作数据
    getOperationData() {
      switch (this.type) {
        case 'edit':
          return {
            type: 'edit',
            data: this.editModel,
            materials: this.materials.map(m => m.id)
          }
        case 'stock':
          return {
            type: 'stock',
            operationType: this.stockOperationType,
            data: this.stockModel,
            materials: this.materials.map(m => ({
              id: m.id,
              currentStock: m.currentStock,
              newStock: this.calculateNewStock(m)
            }))
          }
        case 'price':
          return {
            type: 'price',
            operationType: this.priceOperationType,
            data: this.priceModel,
            materials: this.materials.map(m => ({
              id: m.id,
              currentPrices: {
                purchase: m.purchaseDecimalPrice,
                retail: m.commodityDecimalPrice,
                wholesale: m.wholesaleDecimalPrice
              },
              newPrices: {
                purchase: this.calculateNewPrice(m, 'purchase'),
                retail: this.calculateNewPrice(m, 'retail'),
                wholesale: this.calculateNewPrice(m, 'wholesale')
              }
            }))
          }
        default:
          return null
      }
    },

    // 验证表单
    async validate() {
      const formRef = this.$refs[`${this.type}Form`]
      if (formRef) {
        return await formRef.validate()
      }
      return true
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../../styles/mobile/index.less';

.batch-operation-form {
  .operation-info {
    padding: @spacing-lg;
    background-color: @background-color-light;
    border-radius: @border-radius-md;
    margin-bottom: @spacing-lg;
    
    .info-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-sm;
    }
    
    .info-description {
      font-size: @font-size-sm;
      color: @text-color-secondary;
      margin-bottom: @spacing-sm;
      line-height: 1.5;
    }
    
    .info-count {
      font-size: @font-size-sm;
      color: @primary-color;
      font-weight: @font-weight-medium;
    }
  }
  
  .stock-operation-type,
  .price-operation-type {
    margin-bottom: @spacing-lg;
    
    .ant-radio-group {
      width: 100%;
      
      .ant-radio-wrapper {
        margin-right: @spacing-xl;
      }
    }
  }
  
  .stock-preview,
  .price-preview {
    margin-top: @spacing-lg;
    
    .preview-title {
      font-size: @font-size-base;
      font-weight: @font-weight-medium;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .preview-list {
      max-height: 200px;
      overflow-y: auto;
      
      .preview-item {
        .flex-between();
        align-items: center;
        padding: @spacing-md;
        background-color: @background-color-light;
        border-radius: @border-radius-sm;
        margin-bottom: @spacing-sm;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .material-info {
          flex: 1;
          
          .material-name {
            font-size: @font-size-sm;
            color: @text-color;
            margin-bottom: @spacing-xs;
          }
          
          .material-code {
            font-size: @font-size-xs;
            color: @text-color-tertiary;
          }
        }
        
        .stock-change {
          .flex-center();
          gap: @spacing-sm;
          
          .current-stock,
          .new-stock {
            font-size: @font-size-sm;
            font-weight: @font-weight-medium;
          }
          
          .current-stock {
            color: @text-color-secondary;
          }
          
          .new-stock {
            color: @primary-color;
          }
        }
        
        .price-changes {
          .price-change {
            .d-flex();
            align-items: center;
            gap: @spacing-xs;
            margin-bottom: @spacing-xs;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .price-label {
              font-size: @font-size-xs;
              color: @text-color-tertiary;
              min-width: 50px;
            }
            
            .current-price,
            .new-price {
              font-size: @font-size-xs;
              font-weight: @font-weight-medium;
            }
            
            .current-price {
              color: @text-color-secondary;
            }
            
            .new-price {
              color: @primary-color;
            }
          }
        }
      }
    }
  }
  
  .selected-materials {
    margin-top: @spacing-xl;
    
    .materials-title {
      font-size: @font-size-base;
      font-weight: @font-weight-medium;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .materials-list {
      max-height: 200px;
      overflow-y: auto;
      
      .material-item {
        .d-flex();
        align-items: center;
        padding: @spacing-md;
        background-color: @background-color-light;
        border-radius: @border-radius-sm;
        margin-bottom: @spacing-sm;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .material-content {
          margin-left: @spacing-md;
          flex: 1;
          
          .material-name {
            font-size: @font-size-sm;
            color: @text-color;
            margin-bottom: @spacing-xs;
          }
          
          .material-info {
            .d-flex();
            gap: @spacing-md;
            
            span {
              font-size: @font-size-xs;
              color: @text-color-tertiary;
            }
          }
        }
      }
    }
  }
}
</style>
