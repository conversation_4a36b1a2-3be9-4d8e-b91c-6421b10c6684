<template>
  <div class="inventory-form">
    <!-- 基本信息 -->
    <div class="form-section">
      <div class="section-title">基本信息</div>
      <mobile-form
        ref="basicForm"
        :fields="basicFields"
        :model="formModel"
        :rules="formRules"
        :readonly="mode === 'view'"
        @change="handleFormChange"
      />
    </div>

    <!-- 仓库信息 -->
    <div class="form-section" v-if="inventoryType !== 'adjust'">
      <div class="section-title">仓库信息</div>
      <mobile-form
        ref="depotForm"
        :fields="depotFields"
        :model="formModel"
        :rules="formRules"
        :readonly="mode === 'view'"
        @change="handleFormChange"
      />
    </div>

    <!-- 商品明细 -->
    <div class="form-section">
      <div class="section-title">
        {{ inventoryType === 'adjust' ? '库存调整' : '商品明细' }}
        <a-button 
          v-if="mode !== 'view' && inventoryType !== 'adjust'" 
          type="link" 
          size="small" 
          @click="showMaterialSelector = true"
        >
          <a-icon type="plus" />
          添加商品
        </a-button>
      </div>
      
      <!-- 库存调整模式 - 单商品调整 -->
      <div v-if="inventoryType === 'adjust'" class="adjust-content">
        <div class="material-info">
          <div class="material-header">
            <a-avatar
              :size="48"
              :src="inventoryData.imgUrl"
              :style="{ backgroundColor: inventoryData.imgUrl ? 'transparent' : '#f56a00' }"
            >
              {{ inventoryData.imgUrl ? '' : (inventoryData.name ? inventoryData.name.charAt(0) : '商') }}
            </a-avatar>
            <div class="material-details">
              <div class="material-name">{{ inventoryData.name }}</div>
              <div class="material-code">编号: {{ inventoryData.materialNumber || '无' }}</div>
              <div class="current-stock">当前库存: {{ inventoryData.currentStock || 0 }}</div>
            </div>
          </div>
        </div>
        
        <mobile-form
          ref="adjustForm"
          :fields="adjustFields"
          :model="formModel"
          :rules="formRules"
          :readonly="mode === 'view'"
          @change="handleFormChange"
        />
        
        <!-- 调整预览 -->
        <div class="adjust-preview">
          <div class="preview-title">调整预览</div>
          <div class="preview-content">
            <div class="stock-change">
              <span class="current">{{ inventoryData.currentStock || 0 }}</span>
              <a-icon type="arrow-right" />
              <span class="new">{{ calculateNewStock() }}</span>
            </div>
            <div class="change-amount" :class="getChangeClass()">
              {{ getChangeText() }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 盘点/调拨模式 - 商品明细列表 -->
      <div v-else class="material-list">
        <div 
          v-for="(item, index) in formModel.materialList" 
          :key="index"
          class="material-item"
        >
          <div class="material-info">
            <div class="material-name">{{ item.materialName }}</div>
            <div class="material-details">
              <span class="material-code">编号: {{ item.materialNumber || '无' }}</span>
              <span class="material-unit">单位: {{ item.unitName || '个' }}</span>
            </div>
          </div>
          
          <!-- 盘点模式字段 -->
          <div v-if="inventoryType === 'check'" class="check-inputs">
            <div class="input-group">
              <label>账面库存</label>
              <div class="stock-display">{{ item.bookStock || 0 }}</div>
            </div>
            <div class="input-group">
              <label>实际库存</label>
              <a-input-number
                v-model="item.realStock"
                :min="0"
                :precision="0"
                :readonly="mode === 'view'"
                @change="calculateDifference(item, index)"
              />
            </div>
            <div class="input-group">
              <label>盘点差异</label>
              <div class="diff-display" :class="getDiffClass(item.difference)">
                {{ item.difference || 0 }}
              </div>
            </div>
          </div>
          
          <!-- 调拨模式字段 -->
          <div v-if="inventoryType === 'transfer'" class="transfer-inputs">
            <div class="input-group">
              <label>可用库存</label>
              <div class="stock-display">{{ item.availableStock || 0 }}</div>
            </div>
            <div class="input-group">
              <label>调拨数量</label>
              <a-input-number
                v-model="item.transferNumber"
                :min="0"
                :max="item.availableStock"
                :precision="0"
                :readonly="mode === 'view'"
                @change="validateTransferNumber(item, index)"
              />
            </div>
          </div>
          
          <div class="material-actions" v-if="mode !== 'view'">
            <a-button 
              type="text" 
              size="small" 
              @click="removeMaterial(index)"
              :disabled="formModel.materialList.length <= 1"
            >
              <a-icon type="delete" />
            </a-button>
          </div>
        </div>
        
        <div v-if="formModel.materialList.length === 0" class="empty-materials">
          <a-empty description="暂无商品明细" />
          <a-button 
            v-if="mode !== 'view'" 
            type="primary" 
            @click="showMaterialSelector = true"
          >
            添加商品
          </a-button>
        </div>
      </div>
    </div>

    <!-- 其他信息 -->
    <div class="form-section">
      <div class="section-title">其他信息</div>
      <mobile-form
        ref="otherForm"
        :fields="otherFields"
        :model="formModel"
        :readonly="mode === 'view'"
        @change="handleFormChange"
      />
    </div>

    <!-- 商品选择器 -->
    <mobile-modal
      v-model="showMaterialSelector"
      title="选择商品"
      :fullscreen="$isMobile"
      @ok="confirmMaterialSelection"
      @cancel="showMaterialSelector = false"
    >
      <material-selector
        ref="materialSelector"
        :multiple="true"
        :selectedMaterials="selectedMaterialIds"
        @change="handleMaterialSelectionChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { MobileForm, MobileModal } from '@/components/mobile'
import { getAction } from '@/api/manage'
import MaterialSelector from './MaterialSelector'

export default {
  name: 'InventoryForm',
  components: {
    MobileForm,
    MobileModal,
    MaterialSelector
  },
  
  props: {
    mode: {
      type: String,
      default: 'add',
      validator: value => ['add', 'edit', 'view', 'adjust'].includes(value)
    },
    inventoryType: {
      type: String,
      required: true,
      validator: value => ['check', 'transfer', 'adjust'].includes(value)
    },
    inventoryData: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      formModel: {
        number: '',
        operTime: new Date(),
        depotId: '',
        outDepotId: '',
        inDepotId: '',
        remark: '',
        materialList: [],
        // 调整模式字段
        adjustType: 'set', // set: 设置, adjust: 调整
        adjustNumber: '',
        adjustReason: ''
      },

      formRules: {
        operTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        depotId: [
          { required: true, message: '请选择仓库', trigger: 'change' }
        ],
        outDepotId: [
          { required: true, message: '请选择调出仓库', trigger: 'change' }
        ],
        inDepotId: [
          { required: true, message: '请选择调入仓库', trigger: 'change' }
        ],
        adjustNumber: [
          { required: true, message: '请输入调整数量', trigger: 'blur' }
        ],
        adjustReason: [
          { required: true, message: '请输入调整原因', trigger: 'blur' }
        ]
      },

      showMaterialSelector: false,
      selectedMaterials: [],
      depotOptions: []
    }
  },

  computed: {
    // 基本信息字段
    basicFields() {
      const typeTextMap = {
        check: '盘点',
        transfer: '调拨',
        adjust: '调整'
      }
      const typeText = typeTextMap[this.inventoryType] || '库存'
      
      return [
        {
          key: 'number',
          type: 'input',
          label: `${typeText}单号`,
          placeholder: '留空自动生成',
          readonly: this.mode === 'edit'
        },
        {
          key: 'operTime',
          type: 'date',
          label: `${typeText}日期`,
          placeholder: '请选择日期',
          required: true
        }
      ]
    },

    // 仓库信息字段
    depotFields() {
      if (this.inventoryType === 'check') {
        return [
          {
            key: 'depotId',
            type: 'select',
            label: '盘点仓库',
            placeholder: '请选择仓库',
            options: this.depotOptions,
            required: true
          }
        ]
      } else if (this.inventoryType === 'transfer') {
        return [
          {
            key: 'outDepotId',
            type: 'select',
            label: '调出仓库',
            placeholder: '请选择调出仓库',
            options: this.depotOptions,
            required: true
          },
          {
            key: 'inDepotId',
            type: 'select',
            label: '调入仓库',
            placeholder: '请选择调入仓库',
            options: this.depotOptions,
            required: true
          }
        ]
      }
      return []
    },

    // 调整字段
    adjustFields() {
      return [
        {
          key: 'adjustType',
          type: 'radio',
          label: '调整方式',
          options: [
            { value: 'set', label: '设置库存' },
            { value: 'adjust', label: '调整数量' }
          ]
        },
        {
          key: 'adjustNumber',
          type: 'number',
          label: this.formModel.adjustType === 'set' ? '设置库存' : '调整数量',
          placeholder: this.formModel.adjustType === 'set' ? '请输入新的库存数量' : '请输入调整数量（正数增加，负数减少）',
          required: true,
          min: this.formModel.adjustType === 'set' ? 0 : undefined,
          precision: 0
        },
        {
          key: 'adjustReason',
          type: 'textarea',
          label: '调整原因',
          placeholder: '请输入调整原因',
          required: true,
          rows: 3
        }
      ]
    },

    // 其他信息字段
    otherFields() {
      return [
        {
          key: 'remark',
          type: 'textarea',
          label: '备注',
          placeholder: '请输入备注信息',
          rows: 3
        }
      ]
    },

    // 已选择的商品ID
    selectedMaterialIds() {
      return this.formModel.materialList.map(item => item.materialId)
    }
  },

  watch: {
    inventoryData: {
      handler(newData) {
        if (newData) {
          this.formModel = { 
            ...this.formModel, 
            ...newData,
            materialList: newData.materialList || []
          }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    },

    'formModel.outDepotId'(newVal) {
      // 调出仓库变化时，清空调入仓库（如果相同）
      if (newVal && newVal === this.formModel.inDepotId) {
        this.formModel.inDepotId = ''
      }
    },

    'formModel.inDepotId'(newVal) {
      // 调入仓库变化时，清空调出仓库（如果相同）
      if (newVal && newVal === this.formModel.outDepotId) {
        this.formModel.outDepotId = ''
      }
    }
  },

  created() {
    this.loadDepotOptions()
  },

  methods: {
    // 加载仓库选项
    async loadDepotOptions() {
      try {
        const res = await getAction('/depot/list')
        if (res.success) {
          this.depotOptions = res.result.map(item => ({
            value: item.id,
            label: item.name
          }))
        }
      } catch (error) {
        console.error('加载仓库选项失败:', error)
      }
    },

    // 处理表单变化
    handleFormChange(field, value) {
      this.formModel[field] = value
      this.$emit('change', field, value, this.formModel)
    },

    // 处理商品选择变化
    handleMaterialSelectionChange(materials) {
      this.selectedMaterials = materials
    },

    // 确认商品选择
    confirmMaterialSelection() {
      this.selectedMaterials.forEach(material => {
        const existingIndex = this.formModel.materialList.findIndex(
          item => item.materialId === material.id
        )
        
        if (existingIndex === -1) {
          const newItem = {
            materialId: material.id,
            materialName: material.name,
            materialNumber: material.materialNumber,
            unitName: material.unitName
          }
          
          if (this.inventoryType === 'check') {
            newItem.bookStock = material.currentStock || 0
            newItem.realStock = material.currentStock || 0
            newItem.difference = 0
          } else if (this.inventoryType === 'transfer') {
            newItem.availableStock = material.currentStock || 0
            newItem.transferNumber = 0
          }
          
          this.formModel.materialList.push(newItem)
        }
      })
      
      this.showMaterialSelector = false
    },

    // 移除商品
    removeMaterial(index) {
      this.formModel.materialList.splice(index, 1)
    },

    // 计算盘点差异
    calculateDifference(item, index) {
      const bookStock = Number(item.bookStock) || 0
      const realStock = Number(item.realStock) || 0
      item.difference = realStock - bookStock
      
      this.$set(this.formModel.materialList, index, item)
    },

    // 验证调拨数量
    validateTransferNumber(item, index) {
      const transferNumber = Number(item.transferNumber) || 0
      const availableStock = Number(item.availableStock) || 0
      
      if (transferNumber > availableStock) {
        item.transferNumber = availableStock
        this.$message.warning('调拨数量不能超过可用库存')
      }
      
      this.$set(this.formModel.materialList, index, item)
    },

    // 计算新库存（调整模式）
    calculateNewStock() {
      const currentStock = Number(this.inventoryData.currentStock) || 0
      const adjustNumber = Number(this.formModel.adjustNumber) || 0
      
      if (this.formModel.adjustType === 'set') {
        return adjustNumber
      } else {
        return Math.max(0, currentStock + adjustNumber)
      }
    },

    // 获取变化样式类
    getChangeClass() {
      const currentStock = Number(this.inventoryData.currentStock) || 0
      const newStock = this.calculateNewStock()
      
      if (newStock > currentStock) {
        return 'increase'
      } else if (newStock < currentStock) {
        return 'decrease'
      }
      return 'no-change'
    },

    // 获取变化文本
    getChangeText() {
      const currentStock = Number(this.inventoryData.currentStock) || 0
      const newStock = this.calculateNewStock()
      const change = newStock - currentStock
      
      if (change > 0) {
        return `+${change}`
      } else if (change < 0) {
        return `${change}`
      }
      return '无变化'
    },

    // 获取差异样式类
    getDiffClass(difference) {
      const diff = Number(difference) || 0
      if (diff > 0) {
        return 'positive'
      } else if (diff < 0) {
        return 'negative'
      }
      return 'zero'
    },

    // 获取表单数据
    getFormData() {
      const data = { ...this.formModel }
      
      if (this.inventoryType === 'adjust') {
        // 调整模式：添加商品信息
        data.materialId = this.inventoryData.id
        data.currentStock = this.inventoryData.currentStock
        data.newStock = this.calculateNewStock()
      }
      
      return data
    },

    // 验证表单
    async validate() {
      try {
        const basicValid = await this.$refs.basicForm.validate()
        
        if (this.inventoryType !== 'adjust') {
          const depotValid = await this.$refs.depotForm.validate()
          
          // 验证商品明细
          if (this.formModel.materialList.length === 0) {
            this.$message.error('请至少添加一个商品')
            return false
          }

          // 验证调拨仓库不能相同
          if (this.inventoryType === 'transfer' && this.formModel.outDepotId === this.formModel.inDepotId) {
            this.$message.error('调出仓库和调入仓库不能相同')
            return false
          }
        } else {
          // 调整模式验证
          const adjustValid = await this.$refs.adjustForm.validate()
          return basicValid && adjustValid
        }

        return basicValid
      } catch (error) {
        return false
      }
    },

    // 重置表单
    resetForm() {
      this.formModel = {
        number: '',
        operTime: new Date(),
        depotId: '',
        outDepotId: '',
        inDepotId: '',
        remark: '',
        materialList: [],
        adjustType: 'set',
        adjustNumber: '',
        adjustReason: ''
      }
      
      if (this.$refs.basicForm) {
        this.$refs.basicForm.resetFields()
      }
      if (this.$refs.depotForm) {
        this.$refs.depotForm.resetFields()
      }
      if (this.$refs.adjustForm) {
        this.$refs.adjustForm.resetFields()
      }
      if (this.$refs.otherForm) {
        this.$refs.otherForm.resetFields()
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../../styles/mobile/index.less';

.inventory-form {
  .form-section {
    margin-bottom: @spacing-lg;

    .section-title {
      font-size: @font-size-base;
      font-weight: @font-weight-medium;
      color: @text-color;
      margin-bottom: @spacing-md;
      padding: 0 @spacing-md;
      .flex-between();
      align-items: center;

      .ant-btn {
        font-size: @font-size-sm;
        padding: @spacing-xs @spacing-sm;
      }
    }
  }

  .adjust-content {
    .material-info {
      .mobile-card();
      margin-bottom: @spacing-md;

      .material-header {
        .d-flex();
        align-items: center;
        gap: @spacing-md;

        .material-details {
          flex: 1;

          .material-name {
            font-size: @font-size-base;
            font-weight: @font-weight-medium;
            color: @text-color;
            margin-bottom: @spacing-xs;
          }

          .material-code,
          .current-stock {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-bottom: @spacing-xs;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .current-stock {
            color: @primary-color;
            font-weight: @font-weight-medium;
          }
        }
      }
    }

    .adjust-preview {
      .mobile-card();

      .preview-title {
        font-size: @font-size-sm;
        font-weight: @font-weight-medium;
        color: @text-color;
        margin-bottom: @spacing-md;
      }

      .preview-content {
        text-align: center;

        .stock-change {
          .d-flex();
          justify-content: center;
          align-items: center;
          gap: @spacing-md;
          margin-bottom: @spacing-sm;

          .current,
          .new {
            font-size: @font-size-lg;
            font-weight: @font-weight-semibold;
            padding: @spacing-sm @spacing-md;
            border-radius: @border-radius-sm;
            background-color: @background-color-light;
          }

          .new {
            color: @primary-color;
            background-color: @primary-color-light;
          }

          .anticon {
            color: @text-color-tertiary;
          }
        }

        .change-amount {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;

          &.increase {
            color: @success-color;
          }

          &.decrease {
            color: @error-color;
          }

          &.no-change {
            color: @text-color-tertiary;
          }
        }
      }
    }
  }

  .material-list {
    .material-item {
      .mobile-card();
      margin-bottom: @spacing-md;

      .material-info {
        margin-bottom: @spacing-md;

        .material-name {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          margin-bottom: @spacing-xs;
        }

        .material-details {
          .d-flex();
          gap: @spacing-md;

          .material-code,
          .material-unit {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }
      }

      .check-inputs,
      .transfer-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: @spacing-md;
        margin-bottom: @spacing-md;

        .input-group {
          label {
            display: block;
            font-size: @font-size-xs;
            color: @text-color-secondary;
            margin-bottom: @spacing-xs;
          }

          .stock-display,
          .diff-display {
            .flex-center();
            height: 32px;
            padding: 0 @spacing-sm;
            background-color: @background-color-light;
            border-radius: @border-radius-sm;
            font-size: @font-size-sm;
            font-weight: @font-weight-medium;
          }

          .diff-display {
            &.positive {
              color: @success-color;
              background-color: #F0FDF4;
            }

            &.negative {
              color: @error-color;
              background-color: #FEF2F2;
            }

            &.zero {
              color: @text-color-tertiary;
            }
          }
        }
      }

      .transfer-inputs {
        grid-template-columns: 1fr 1fr;
      }

      .material-actions {
        text-align: right;

        .ant-btn {
          color: @error-color;

          &:hover {
            color: @error-color;
            background-color: #FEF2F2;
          }
        }
      }
    }

    .empty-materials {
      text-align: center;
      padding: @spacing-xxl;

      .ant-btn {
        margin-top: @spacing-md;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .inventory-form {
    .material-list {
      .material-item {
        .check-inputs {
          grid-template-columns: 1fr;
          gap: @spacing-sm;
        }

        .transfer-inputs {
          grid-template-columns: 1fr;
          gap: @spacing-sm;
        }
      }
    }
  }
}
</style>
