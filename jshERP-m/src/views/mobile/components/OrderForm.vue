<template>
  <div class="order-form">
    <!-- 订单基本信息 -->
    <div class="form-section">
      <div class="section-title">基本信息</div>
      <mobile-form
        ref="basicForm"
        :fields="basicFields"
        :model="formModel"
        :rules="formRules"
        :readonly="mode === 'view'"
        @change="handleFormChange"
      />
    </div>

    <!-- 商品明细 -->
    <div class="form-section">
      <div class="section-title">
        商品明细
        <a-button 
          v-if="mode !== 'view'" 
          type="link" 
          size="small" 
          @click="showMaterialSelector = true"
        >
          <a-icon type="plus" />
          添加商品
        </a-button>
      </div>
      
      <div class="material-list">
        <div 
          v-for="(item, index) in formModel.materialList" 
          :key="index"
          class="material-item"
        >
          <div class="material-info">
            <div class="material-name">{{ item.materialName }}</div>
            <div class="material-code">编号: {{ item.materialNumber || '无' }}</div>
            <div class="material-unit">单位: {{ item.unitName || '个' }}</div>
          </div>
          
          <div class="material-quantity">
            <a-input-number
              v-model="item.operNumber"
              :min="0"
              :precision="2"
              :readonly="mode === 'view'"
              @change="calculateItemAmount(item, index)"
            />
          </div>
          
          <div class="material-price">
            <a-input-number
              v-model="item.unitPrice"
              :min="0"
              :precision="2"
              :readonly="mode === 'view'"
              @change="calculateItemAmount(item, index)"
            />
          </div>
          
          <div class="material-amount">
            ¥{{ formatMoney(item.allPrice) }}
          </div>
          
          <div class="material-actions" v-if="mode !== 'view'">
            <a-button 
              type="text" 
              size="small" 
              @click="removeMaterial(index)"
              :disabled="formModel.materialList.length <= 1"
            >
              <a-icon type="delete" />
            </a-button>
          </div>
        </div>
        
        <div v-if="formModel.materialList.length === 0" class="empty-materials">
          <a-empty description="暂无商品明细" />
          <a-button 
            v-if="mode !== 'view'" 
            type="primary" 
            @click="showMaterialSelector = true"
          >
            添加商品
          </a-button>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="form-section">
      <div class="section-title">金额信息</div>
      <div class="amount-summary">
        <div class="amount-row">
          <span class="amount-label">商品总额:</span>
          <span class="amount-value">¥{{ formatMoney(totalAmount) }}</span>
        </div>
        <div class="amount-row">
          <span class="amount-label">优惠金额:</span>
          <a-input-number
            v-model="formModel.discountMoney"
            :min="0"
            :max="totalAmount"
            :precision="2"
            :readonly="mode === 'view'"
            @change="calculateFinalAmount"
            style="width: 120px;"
          />
        </div>
        <div class="amount-row">
          <span class="amount-label">其他费用:</span>
          <a-input-number
            v-model="formModel.otherMoney"
            :precision="2"
            :readonly="mode === 'view'"
            @change="calculateFinalAmount"
            style="width: 120px;"
          />
        </div>
        <div class="amount-row total">
          <span class="amount-label">应付金额:</span>
          <span class="amount-value final">¥{{ formatMoney(finalAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- 其他信息 -->
    <div class="form-section">
      <div class="section-title">其他信息</div>
      <mobile-form
        ref="otherForm"
        :fields="otherFields"
        :model="formModel"
        :readonly="mode === 'view'"
        @change="handleFormChange"
      />
    </div>

    <!-- 商品选择器 -->
    <mobile-modal
      v-model="showMaterialSelector"
      title="选择商品"
      :fullscreen="$isMobile"
      @ok="confirmMaterialSelection"
      @cancel="showMaterialSelector = false"
    >
      <material-selector
        ref="materialSelector"
        :multiple="true"
        :selectedMaterials="selectedMaterialIds"
        @change="handleMaterialSelectionChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { MobileForm, MobileModal } from '@/components/mobile'
import { getAction } from '@/api/manage'
import MaterialSelector from './MaterialSelector'

export default {
  name: 'OrderForm',
  components: {
    MobileForm,
    MobileModal,
    MaterialSelector
  },
  
  props: {
    mode: {
      type: String,
      default: 'add', // add, edit, view
      validator: value => ['add', 'edit', 'view'].includes(value)
    },
    orderType: {
      type: String,
      required: true,
      validator: value => ['sale', 'purchase'].includes(value)
    },
    orderData: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      formModel: {
        number: '',
        organId: '',
        operTime: new Date(),
        remark: '',
        discountMoney: 0,
        otherMoney: 0,
        materialList: []
      },

      // 表单验证规则
      formRules: {
        organId: [
          { required: true, message: `请选择${this.orderType === 'sale' ? '客户' : '供应商'}`, trigger: 'change' }
        ],
        operTime: [
          { required: true, message: '请选择订单日期', trigger: 'change' }
        ]
      },

      // 商品选择器
      showMaterialSelector: false,
      selectedMaterials: [],

      // 选项数据
      organOptions: []
    }
  },

  computed: {
    // 基本信息字段
    basicFields() {
      return [
        {
          key: 'number',
          type: 'input',
          label: '订单编号',
          placeholder: '留空自动生成',
          readonly: this.mode === 'edit'
        },
        {
          key: 'organId',
          type: 'select',
          label: this.orderType === 'sale' ? '客户' : '供应商',
          placeholder: `请选择${this.orderType === 'sale' ? '客户' : '供应商'}`,
          options: this.organOptions,
          required: true
        },
        {
          key: 'operTime',
          type: 'date',
          label: '订单日期',
          placeholder: '请选择订单日期',
          required: true
        }
      ]
    },

    // 其他信息字段
    otherFields() {
      return [
        {
          key: 'remark',
          type: 'textarea',
          label: '备注',
          placeholder: '请输入备注信息',
          rows: 3
        }
      ]
    },

    // 商品总额
    totalAmount() {
      return this.formModel.materialList.reduce((sum, item) => {
        return sum + (Number(item.allPrice) || 0)
      }, 0)
    },

    // 最终金额
    finalAmount() {
      return this.totalAmount - (Number(this.formModel.discountMoney) || 0) + (Number(this.formModel.otherMoney) || 0)
    },

    // 已选择的商品ID
    selectedMaterialIds() {
      return this.formModel.materialList.map(item => item.materialId)
    }
  },

  watch: {
    orderData: {
      handler(newData) {
        if (newData) {
          this.formModel = { 
            ...this.formModel, 
            ...newData,
            materialList: newData.materialList || []
          }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    },

    orderType: {
      handler() {
        this.loadOrganOptions()
      },
      immediate: true
    }
  },

  methods: {
    // 加载客户/供应商选项
    async loadOrganOptions() {
      try {
        const apiPath = this.orderType === 'sale' ? '/customer/list' : '/vendor/list'
        const res = await getAction(apiPath)
        if (res.success) {
          this.organOptions = res.result.map(item => ({
            value: item.id,
            label: item.supplier || item.name
          }))
        }
      } catch (error) {
        console.error('加载选项失败:', error)
      }
    },

    // 处理表单变化
    handleFormChange(field, value) {
      this.formModel[field] = value
      this.$emit('change', field, value, this.formModel)
    },

    // 处理商品选择变化
    handleMaterialSelectionChange(materials) {
      this.selectedMaterials = materials
    },

    // 确认商品选择
    confirmMaterialSelection() {
      this.selectedMaterials.forEach(material => {
        // 检查是否已存在
        const existingIndex = this.formModel.materialList.findIndex(
          item => item.materialId === material.id
        )
        
        if (existingIndex === -1) {
          // 添加新商品
          this.formModel.materialList.push({
            materialId: material.id,
            materialName: material.name,
            materialNumber: material.materialNumber,
            unitName: material.unitName,
            operNumber: 1,
            unitPrice: this.orderType === 'sale' ? material.commodityDecimalPrice : material.purchaseDecimalPrice,
            allPrice: this.orderType === 'sale' ? material.commodityDecimalPrice : material.purchaseDecimalPrice
          })
        }
      })
      
      this.showMaterialSelector = false
      this.calculateTotalAmount()
    },

    // 移除商品
    removeMaterial(index) {
      this.formModel.materialList.splice(index, 1)
      this.calculateTotalAmount()
    },

    // 计算单项金额
    calculateItemAmount(item, index) {
      const quantity = Number(item.operNumber) || 0
      const price = Number(item.unitPrice) || 0
      item.allPrice = quantity * price
      
      this.$set(this.formModel.materialList, index, item)
      this.calculateTotalAmount()
    },

    // 计算总金额
    calculateTotalAmount() {
      this.calculateFinalAmount()
    },

    // 计算最终金额
    calculateFinalAmount() {
      // 触发响应式更新
      this.$forceUpdate()
    },

    // 获取表单数据
    getFormData() {
      return {
        ...this.formModel,
        totalPrice: this.totalAmount,
        changeAmount: this.finalAmount
      }
    },

    // 验证表单
    async validate() {
      try {
        const basicValid = await this.$refs.basicForm.validate()
        
        // 验证商品明细
        if (this.formModel.materialList.length === 0) {
          this.$message.error('请至少添加一个商品')
          return false
        }

        // 验证商品明细数据
        for (let i = 0; i < this.formModel.materialList.length; i++) {
          const item = this.formModel.materialList[i]
          if (!item.operNumber || item.operNumber <= 0) {
            this.$message.error(`第${i + 1}个商品的数量必须大于0`)
            return false
          }
          if (!item.unitPrice || item.unitPrice <= 0) {
            this.$message.error(`第${i + 1}个商品的单价必须大于0`)
            return false
          }
        }

        return basicValid
      } catch (error) {
        return false
      }
    },

    // 重置表单
    resetForm() {
      this.formModel = {
        number: '',
        organId: '',
        operTime: new Date(),
        remark: '',
        discountMoney: 0,
        otherMoney: 0,
        materialList: []
      }
      
      if (this.$refs.basicForm) {
        this.$refs.basicForm.resetFields()
      }
      if (this.$refs.otherForm) {
        this.$refs.otherForm.resetFields()
      }
    },

    // 格式化金额
    formatMoney(amount) {
      return Number(amount || 0).toFixed(2)
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../styles/mobile.less";

.order-form {
  .form-section {
    margin-bottom: @spacing-xl;
    
    .section-title {
      .flex-between();
      align-items: center;
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
      padding-bottom: @spacing-sm;
      border-bottom: 1px solid @border-color-light;
    }
  }
  
  .material-list {
    .material-item {
      .mobile-card();
      padding: @spacing-md;
      margin-bottom: @spacing-sm;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .material-info {
        margin-bottom: @spacing-md;
        
        .material-name {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          margin-bottom: @spacing-xs;
        }
        
        .material-code,
        .material-unit {
          font-size: @font-size-sm;
          color: @text-color-secondary;
          margin-right: @spacing-md;
        }
      }
      
      .material-quantity,
      .material-price {
        margin-bottom: @spacing-sm;
        
        .ant-input-number {
          width: 100%;
        }
      }
      
      .material-amount {
        font-size: @font-size-lg;
        font-weight: @font-weight-semibold;
        color: @primary-color;
        text-align: right;
        margin-bottom: @spacing-sm;
      }
      
      .material-actions {
        text-align: right;
      }
    }
    
    .empty-materials {
      .flex-center();
      flex-direction: column;
      padding: @spacing-xl;
      
      .ant-btn {
        margin-top: @spacing-md;
      }
    }
  }
  
  .amount-summary {
    .mobile-card();
    padding: @spacing-lg;
    
    .amount-row {
      .flex-between();
      align-items: center;
      margin-bottom: @spacing-md;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.total {
        padding-top: @spacing-md;
        border-top: 1px solid @border-color-light;
        
        .amount-label {
          font-weight: @font-weight-semibold;
        }
        
        .amount-value.final {
          font-size: @font-size-xl;
          font-weight: @font-weight-bold;
          color: @primary-color;
        }
      }
      
      .amount-label {
        font-size: @font-size-base;
        color: @text-color;
      }
      
      .amount-value {
        font-size: @font-size-base;
        font-weight: @font-weight-medium;
        color: @text-color;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .order-form {
    .material-list {
      .material-item {
        .material-quantity,
        .material-price {
          .ant-input-number {
            font-size: @font-size-lg;
            height: 44px;
          }
        }
      }
    }
    
    .amount-summary {
      .amount-row {
        .ant-input-number {
          font-size: @font-size-lg;
          height: 44px;
        }
      }
    }
  }
}
</style>
