<template>
  <div class="material-selector">
    <!-- 搜索栏 -->
    <div class="selector-header">
      <a-input-search
        v-model="searchValue"
        placeholder="搜索商品名称、编号"
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
      />
      
      <!-- 分类筛选 -->
      <div class="category-filter">
        <a-button 
          type="text" 
          @click="showCategoryDrawer = true"
          :class="{ active: selectedCategoryId }"
        >
          <a-icon type="filter" />
          {{ selectedCategoryName || '全部分类' }}
        </a-button>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="selector-content">
      <mobile-list
        :dataSource="materialList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="false"
        :showAvatar="true"
        :selectable="multiple"
        :selectedItems="selectedMaterials"
        :pagination="pagination"
        titleField="name"
        descriptionField="description"
        avatarField="imgUrl"
        @itemClick="handleItemClick"
        @selectionChange="handleSelectionChange"
        @pageChange="handlePageChange"
        @refresh="loadMaterialList"
      >
        <!-- 自定义商品项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="material-item-content">
            <!-- 商品头部 -->
            <div class="material-item-header">
              <div class="material-name">{{ item.name }}</div>
              <div class="material-price">
                ¥{{ formatPrice(item.commodityDecimalPrice) }}
              </div>
            </div>
            
            <!-- 商品信息 -->
            <div class="material-item-info">
              <div class="info-row">
                <span class="info-label">编号:</span>
                <span class="info-value">{{ item.materialNumber || '无' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">分类:</span>
                <span class="info-value">{{ item.materialCategoryName || '无' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">单位:</span>
                <span class="info-value">{{ item.unitName || '个' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">库存:</span>
                <span class="info-value" :class="getStockClass(item.currentStock)">
                  {{ item.currentStock || 0 }}
                </span>
              </div>
            </div>
            
            <!-- 商品状态 -->
            <div class="material-item-status">
              <a-tag 
                :color="item.enabled === '1' ? 'green' : 'red'"
                size="small"
              >
                {{ item.enabled === '1' ? '启用' : '禁用' }}
              </a-tag>
              
              <a-tag 
                v-if="item.currentStock <= item.lowSafeStock"
                color="orange"
                size="small"
              >
                库存不足
              </a-tag>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 已选择商品显示 -->
    <div v-if="multiple && selectedMaterials.length > 0" class="selected-materials">
      <div class="selected-header">
        <span>已选择 {{ selectedMaterials.length }} 个商品</span>
        <a-button type="text" size="small" @click="clearSelection">
          清空
        </a-button>
      </div>
      
      <div class="selected-list">
        <a-tag
          v-for="material in selectedMaterials"
          :key="material.id"
          closable
          @close="removeMaterial(material)"
        >
          {{ material.name }}
        </a-tag>
      </div>
    </div>

    <!-- 分类选择抽屉 -->
    <mobile-drawer
      v-model="showCategoryDrawer"
      title="选择分类"
      placement="right"
      :width="280"
    >
      <div class="category-tree">
        <div 
          class="category-item"
          :class="{ active: !selectedCategoryId }"
          @click="selectCategory(null, '全部分类')"
        >
          <a-icon type="folder" />
          <span>全部分类</span>
        </div>
        
        <div 
          v-for="category in categoryList" 
          :key="category.id"
          class="category-item"
          :class="{ active: selectedCategoryId === category.id }"
          @click="selectCategory(category.id, category.name)"
        >
          <a-icon type="folder-open" />
          <span>{{ category.name }}</span>
          <span class="category-count">({{ category.materialCount || 0 }})</span>
        </div>
      </div>
    </mobile-drawer>
  </div>
</template>

<script>
import { MobileList, MobileDrawer } from '@/components/mobile'
import { getAction } from '@/api/manage'

export default {
  name: 'MaterialSelector',
  components: {
    MobileList,
    MobileDrawer
  },
  
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    selectedMaterials: {
      type: Array,
      default: () => []
    },
    excludeMaterials: {
      type: Array,
      default: () => []
    },
    onlyEnabled: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      // 搜索和筛选
      searchValue: '',
      selectedCategoryId: null,
      selectedCategoryName: '',
      showCategoryDrawer: false,
      
      // 列表数据
      materialList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },
      
      // 分类数据
      categoryList: [],
      
      // 内部选择状态
      internalSelectedMaterials: []
    }
  },

  computed: {
    // 当前选中的商品（支持外部传入和内部管理）
    currentSelectedMaterials() {
      return this.selectedMaterials.length > 0 ? this.selectedMaterials : this.internalSelectedMaterials
    }
  },

  watch: {
    selectedMaterials: {
      handler(newVal) {
        this.internalSelectedMaterials = [...newVal]
      },
      immediate: true
    }
  },

  created() {
    this.loadCategoryList()
    this.loadMaterialList()
  },

  methods: {
    // 加载分类列表
    async loadCategoryList() {
      try {
        const res = await getAction('/materialCategory/list')
        if (res.success) {
          this.categoryList = res.result.map(item => ({
            ...item,
            materialCount: item.materialCount || 0
          }))
        }
      } catch (error) {
        console.error('加载分类列表失败:', error)
      }
    },

    // 加载商品列表
    async loadMaterialList() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          search: this.searchValue
        }
        
        // 添加分类筛选
        if (this.selectedCategoryId) {
          params.materialCategoryId = this.selectedCategoryId
        }
        
        // 只显示启用的商品
        if (this.onlyEnabled) {
          params.enabled = '1'
        }
        
        const res = await getAction('/material/list', params)
        if (res.success) {
          this.materialList = res.result.records
            .filter(item => {
              // 排除指定的商品
              return !this.excludeMaterials.some(excludeId => excludeId === item.id)
            })
            .map(item => ({
              ...item,
              description: `编号: ${item.materialNumber || '无'} | 库存: ${item.currentStock || 0}`
            }))
          
          this.pagination.total = res.result.total
        }
      } catch (error) {
        console.error('加载商品列表失败:', error)
        this.$message.error('加载商品列表失败')
      } finally {
        this.loading = false
      }
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.pagination.current = 1
      this.loadMaterialList()
    },

    // 处理搜索变化
    handleSearchChange(e) {
      if (!e.target.value) {
        this.handleSearch('')
      }
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadMaterialList()
    },

    // 处理商品项点击
    handleItemClick(item) {
      if (this.multiple) {
        // 多选模式：切换选择状态
        const index = this.internalSelectedMaterials.findIndex(m => m.id === item.id)
        if (index > -1) {
          this.internalSelectedMaterials.splice(index, 1)
        } else {
          this.internalSelectedMaterials.push(item)
        }
        this.emitChange()
      } else {
        // 单选模式：直接选择
        this.internalSelectedMaterials = [item]
        this.emitChange()
      }
    },

    // 处理选择变化（来自MobileList组件）
    handleSelectionChange(selectedItems) {
      this.internalSelectedMaterials = selectedItems
      this.emitChange()
    },

    // 选择分类
    selectCategory(categoryId, categoryName) {
      this.selectedCategoryId = categoryId
      this.selectedCategoryName = categoryName
      this.showCategoryDrawer = false
      this.pagination.current = 1
      this.loadMaterialList()
    },

    // 移除选中的商品
    removeMaterial(material) {
      const index = this.internalSelectedMaterials.findIndex(m => m.id === material.id)
      if (index > -1) {
        this.internalSelectedMaterials.splice(index, 1)
        this.emitChange()
      }
    },

    // 清空选择
    clearSelection() {
      this.internalSelectedMaterials = []
      this.emitChange()
    },

    // 发出变化事件
    emitChange() {
      this.$emit('change', this.internalSelectedMaterials)
    },

    // 获取库存样式类
    getStockClass(stock) {
      const stockNum = Number(stock || 0)
      if (stockNum <= 0) {
        return 'stock-empty'
      } else if (stockNum <= 10) {
        return 'stock-low'
      }
      return 'stock-normal'
    },

    // 格式化价格
    formatPrice(price) {
      return Number(price || 0).toFixed(2)
    },

    // 获取选中的商品
    getSelectedMaterials() {
      return this.internalSelectedMaterials
    },

    // 重置选择器
    reset() {
      this.searchValue = ''
      this.selectedCategoryId = null
      this.selectedCategoryName = ''
      this.internalSelectedMaterials = []
      this.pagination.current = 1
      this.loadMaterialList()
    }
  }
}
</script>

<style lang="less" scoped>

.material-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .selector-header {
    padding: @spacing-md;
    background-color: white;
    border-bottom: 1px solid @border-color-light;
    
    .category-filter {
      margin-top: @spacing-sm;
      
      .ant-btn {
        .flex-center();
        gap: @spacing-xs;
        
        &.active {
          color: @primary-color;
          background-color: @primary-color-light;
        }
      }
    }
  }
  
  .selector-content {
    flex: 1;
    overflow: hidden;
    
    .material-item-content {
      width: 100%;
      
      .material-item-header {
        .flex-between();
        align-items: flex-start;
        margin-bottom: @spacing-sm;
        
        .material-name {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          flex: 1;
          .text-ellipsis();
        }
        
        .material-price {
          font-size: @font-size-base;
          font-weight: @font-weight-semibold;
          color: @primary-color;
        }
      }
      
      .material-item-info {
        margin-bottom: @spacing-sm;
        
        .info-row {
          .d-flex();
          align-items: center;
          margin-bottom: @spacing-xs;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .info-label {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            min-width: 40px;
          }
          
          .info-value {
            font-size: @font-size-sm;
            color: @text-color;
            
            &.stock-empty {
              color: @error-color;
            }
            
            &.stock-low {
              color: @warning-color;
            }
            
            &.stock-normal {
              color: @success-color;
            }
          }
        }
      }
      
      .material-item-status {
        .ant-tag {
          margin-right: @spacing-xs;
          margin-bottom: 0;
        }
      }
    }
  }
  
  .selected-materials {
    background-color: white;
    border-top: 1px solid @border-color-light;
    padding: @spacing-md;
    
    .selected-header {
      .flex-between();
      align-items: center;
      margin-bottom: @spacing-sm;
      
      span {
        font-size: @font-size-sm;
        color: @text-color-secondary;
      }
    }
    
    .selected-list {
      .ant-tag {
        margin-right: @spacing-xs;
        margin-bottom: @spacing-xs;
      }
    }
  }
  
  .category-tree {
    padding: @spacing-md;
    
    .category-item {
      .d-flex();
      align-items: center;
      padding: @spacing-md;
      border-radius: @border-radius-sm;
      cursor: pointer;
      .touch-feedback();
      
      &:hover {
        background-color: @background-color-light;
      }
      
      &.active {
        background-color: @primary-color-light;
        color: @primary-color;
      }
      
      .anticon {
        margin-right: @spacing-sm;
      }
      
      span {
        flex: 1;
      }
      
      .category-count {
        font-size: @font-size-xs;
        color: @text-color-tertiary;
      }
    }
  }
}
</style>
