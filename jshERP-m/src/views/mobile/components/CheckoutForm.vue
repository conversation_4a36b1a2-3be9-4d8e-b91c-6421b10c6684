<template>
  <div class="checkout-form">
    <!-- 订单摘要 -->
    <div class="order-summary">
      <div class="summary-header">
        <h3>订单摘要</h3>
        <div class="item-count">{{ cartItems.length }}件商品</div>
      </div>
      
      <div class="summary-items">
        <div 
          v-for="item in cartItems"
          :key="item.id"
          class="summary-item"
        >
          <div class="item-info">
            <span class="item-name">{{ item.name }}</span>
            <span class="item-spec">x{{ item.quantity }}</span>
          </div>
          <div class="item-amount">¥{{ formatMoney(item.quantity * item.unitPrice) }}</div>
        </div>
      </div>
      
      <div class="summary-total">
        <div class="total-row">
          <span>商品总额</span>
          <span>¥{{ formatMoney(totalAmount) }}</span>
        </div>
        <div class="total-row discount" v-if="formData.discountAmount > 0">
          <span>优惠金额</span>
          <span>-¥{{ formatMoney(formData.discountAmount) }}</span>
        </div>
        <div class="total-row final">
          <span>实付金额</span>
          <span class="final-amount">¥{{ formatMoney(finalAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- 结算信息 -->
    <div class="checkout-info">
      <mobile-form
        ref="checkoutForm"
        :fields="formFields"
        :model="formData"
        :rules="formRules"
        @change="handleFormChange"
      />
    </div>

    <!-- 支付方式 -->
    <div class="payment-methods">
      <div class="section-title">支付方式</div>
      <div class="payment-options">
        <div 
          v-for="method in paymentMethods"
          :key="method.value"
          class="payment-option"
          :class="{ active: formData.paymentMethod === method.value }"
          @click="selectPaymentMethod(method.value)"
        >
          <div class="payment-icon">
            <a-icon :type="method.icon" />
          </div>
          <div class="payment-label">{{ method.label }}</div>
          <div class="payment-check">
            <a-icon v-if="formData.paymentMethod === method.value" type="check-circle" />
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="checkout-actions">
      <a-button 
        size="large" 
        @click="handleCancel"
      >
        取消
      </a-button>
      <a-button 
        type="primary" 
        size="large" 
        @click="handleSubmit"
        :loading="submitting"
      >
        确认支付 ¥{{ formatMoney(finalAmount) }}
      </a-button>
    </div>
  </div>
</template>

<script>
import { MobileForm } from '@/components/mobile'
import { getAction } from '@/api/manage'

export default {
  name: 'CheckoutForm',
  components: {
    MobileForm
  },
  
  props: {
    cartItems: {
      type: Array,
      default: () => []
    },
    totalAmount: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      submitting: false,
      
      // 表单数据
      formData: {
        customerName: '',
        customerPhone: '',
        paymentMethod: 'cash',
        discountAmount: 0,
        remark: ''
      },
      
      // 表单验证规则
      formRules: {
        paymentMethod: [
          { required: true, message: '请选择支付方式', trigger: 'change' }
        ]
      },
      
      // 支付方式选项
      paymentMethods: [
        { value: 'cash', label: '现金支付', icon: 'dollar' },
        { value: 'wechat', label: '微信支付', icon: 'wechat' },
        { value: 'alipay', label: '支付宝', icon: 'alipay' },
        { value: 'card', label: '刷卡支付', icon: 'credit-card' }
      ]
    }
  },

  computed: {
    // 表单字段配置
    formFields() {
      return [
        {
          key: 'customerName',
          type: 'input',
          label: '客户姓名',
          placeholder: '请输入客户姓名（可选）'
        },
        {
          key: 'customerPhone',
          type: 'input',
          label: '客户电话',
          placeholder: '请输入客户电话（可选）'
        },
        {
          key: 'discountAmount',
          type: 'inputNumber',
          label: '优惠金额',
          placeholder: '请输入优惠金额',
          min: 0,
          max: this.totalAmount,
          precision: 2
        },
        {
          key: 'remark',
          type: 'textarea',
          label: '备注',
          placeholder: '请输入备注信息（可选）',
          rows: 2
        }
      ]
    },
    
    // 最终金额
    finalAmount() {
      return Math.max(0, this.totalAmount - (this.formData.discountAmount || 0))
    }
  },

  methods: {
    // 选择支付方式
    selectPaymentMethod(method) {
      this.formData.paymentMethod = method
    },
    
    // 表单变化处理
    handleFormChange(field, value) {
      this.formData[field] = value
    },
    
    // 提交结算
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.checkoutForm.validate()
        if (!valid) {
          return
        }
        
        this.submitting = true
        
        // 构建订单数据
        const orderData = {
          type: '出库',
          subType: '零售',
          operTime: new Date().toISOString(),
          totalPrice: this.finalAmount,
          discountMoney: this.formData.discountAmount || 0,
          payType: this.getPayTypeCode(this.formData.paymentMethod),
          remark: this.formData.remark,
          customerName: this.formData.customerName,
          customerPhone: this.formData.customerPhone,
          materialList: this.cartItems.map(item => ({
            materialId: item.id,
            materialName: item.name,
            operNumber: item.quantity,
            unitPrice: item.unitPrice,
            allPrice: item.quantity * item.unitPrice
          }))
        }
        
        // 提交订单
        this.$emit('submit', orderData)
        
      } catch (error) {
        console.error('提交结算失败:', error)
        this.$message.error('提交失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    
    // 取消结算
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 获取支付方式代码
    getPayTypeCode(method) {
      const payTypeMap = {
        cash: '现金',
        wechat: '微信',
        alipay: '支付宝',
        card: '银行卡'
      }
      return payTypeMap[method] || '现金'
    },
    
    // 格式化金额
    formatMoney(amount) {
      return Number(amount || 0).toFixed(2)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mobile/variables.less';

.checkout-form {
  padding: @spacing-lg;
  background-color: @background-color-light;
  min-height: 100vh;
  
  // 订单摘要
  .order-summary {
    background: white;
    border-radius: 12px;
    padding: @spacing-lg;
    margin-bottom: @spacing-lg;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .summary-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: @spacing-md;
      
      h3 {
        margin: 0;
        font-size: @font-size-lg;
        color: @text-color;
      }
      
      .item-count {
        font-size: @font-size-sm;
        color: @text-color-secondary;
      }
    }
    
    .summary-items {
      margin-bottom: @spacing-lg;
      
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: @spacing-sm 0;
        border-bottom: 1px solid @border-color-split;
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-info {
          flex: 1;
          
          .item-name {
            font-size: @font-size-base;
            color: @text-color;
            margin-right: @spacing-sm;
          }
          
          .item-spec {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }
        
        .item-amount {
          font-size: @font-size-base;
          color: @text-color;
          font-weight: 500;
        }
      }
    }
    
    .summary-total {
      border-top: 1px solid @border-color-split;
      padding-top: @spacing-md;
      
      .total-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @spacing-sm;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &.discount {
          color: @success-color;
        }
        
        &.final {
          font-size: @font-size-lg;
          font-weight: 600;
          color: @text-color;
          
          .final-amount {
            color: @primary-color;
          }
        }
      }
    }
  }
  
  // 结算信息
  .checkout-info {
    background: white;
    border-radius: 12px;
    padding: @spacing-lg;
    margin-bottom: @spacing-lg;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
  
  // 支付方式
  .payment-methods {
    background: white;
    border-radius: 12px;
    padding: @spacing-lg;
    margin-bottom: @spacing-lg;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .section-title {
      font-size: @font-size-base;
      font-weight: 600;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .payment-options {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: @spacing-md;
      
      .payment-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: @spacing-lg @spacing-md;
        border: 2px solid @border-color-base;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        
        &.active {
          border-color: @primary-color;
          background: @primary-color-light;
        }
        
        .payment-icon {
          font-size: 24px;
          color: @text-color-secondary;
          margin-bottom: @spacing-sm;
        }
        
        .payment-label {
          font-size: @font-size-sm;
          color: @text-color;
          text-align: center;
        }
        
        .payment-check {
          position: absolute;
          top: 8px;
          right: 8px;
          color: @primary-color;
          font-size: 16px;
        }
        
        &.active {
          .payment-icon {
            color: @primary-color;
          }
        }
      }
    }
  }
  
  // 操作按钮
  .checkout-actions {
    display: flex;
    gap: @spacing-md;
    
    .ant-btn {
      flex: 1;
      height: 48px;
      font-size: @font-size-base;
      font-weight: 500;
    }
  }
}
</style>
