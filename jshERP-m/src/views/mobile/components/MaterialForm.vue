<template>
  <div class="material-form">
    <mobile-form
      ref="form"
      :fields="formFields"
      :model="formModel"
      :rules="formRules"
      :readonly="mode === 'view'"
      @change="handleFormChange"
      @validate="handleValidate"
    />
  </div>
</template>

<script>
import { MobileForm } from '@/components/mobile'
import { getAction } from '@/api/manage'

export default {
  name: 'MaterialForm',
  components: {
    MobileForm
  },
  
  props: {
    mode: {
      type: String,
      default: 'add', // add, edit, view
      validator: value => ['add', 'edit', 'view'].includes(value)
    },
    materialData: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      formModel: {
        name: '',
        materialNumber: '',
        materialCategoryId: '',
        materialOther: '',
        unitId: '',
        barCode: '',
        purchaseDecimalPrice: '',
        commodityDecimalPrice: '',
        wholesaleDecimalPrice: '',
        retailPrice: '',
        lowSafeStock: '',
        highSafeStock: '',
        enabled: '1',
        enableSerialNumber: '0',
        enableBatchNumber: '0',
        expiryNum: '',
        remark: '',
        imgUrl: ''
      },

      // 表单字段配置
      formFields: [
        {
          key: 'name',
          type: 'input',
          label: '商品名称',
          placeholder: '请输入商品名称',
          required: true,
          group: 'basic'
        },
        {
          key: 'materialNumber',
          type: 'input',
          label: '商品编号',
          placeholder: '留空自动生成',
          group: 'basic'
        },
        {
          key: 'materialCategoryId',
          type: 'select',
          label: '商品分类',
          placeholder: '请选择商品分类',
          options: [],
          required: true,
          group: 'basic'
        },
        {
          key: 'unitId',
          type: 'select',
          label: '基本单位',
          placeholder: '请选择基本单位',
          options: [],
          required: true,
          group: 'basic'
        },
        {
          key: 'barCode',
          type: 'input',
          label: '条形码',
          placeholder: '请输入条形码',
          group: 'basic'
        },
        {
          key: 'materialOther',
          type: 'input',
          label: '规格',
          placeholder: '请输入商品规格',
          group: 'basic'
        },
        {
          key: 'imgUrl',
          type: 'upload',
          label: '商品图片',
          accept: 'image/*',
          listType: 'picture-card',
          maxCount: 1,
          group: 'basic'
        },
        {
          key: 'purchaseDecimalPrice',
          type: 'number',
          label: '采购价格',
          placeholder: '请输入采购价格',
          min: 0,
          precision: 2,
          group: 'price'
        },
        {
          key: 'commodityDecimalPrice',
          type: 'number',
          label: '零售价格',
          placeholder: '请输入零售价格',
          min: 0,
          precision: 2,
          group: 'price'
        },
        {
          key: 'wholesaleDecimalPrice',
          type: 'number',
          label: '批发价格',
          placeholder: '请输入批发价格',
          min: 0,
          precision: 2,
          group: 'price'
        },
        {
          key: 'retailPrice',
          type: 'number',
          label: '最低售价',
          placeholder: '请输入最低售价',
          min: 0,
          precision: 2,
          group: 'price'
        },
        {
          key: 'lowSafeStock',
          type: 'number',
          label: '最低库存',
          placeholder: '请输入最低库存',
          min: 0,
          precision: 0,
          group: 'stock'
        },
        {
          key: 'highSafeStock',
          type: 'number',
          label: '最高库存',
          placeholder: '请输入最高库存',
          min: 0,
          precision: 0,
          group: 'stock'
        },
        {
          key: 'expiryNum',
          type: 'number',
          label: '保质期(天)',
          placeholder: '请输入保质期天数',
          min: 0,
          precision: 0,
          group: 'stock'
        },
        {
          key: 'enabled',
          type: 'radio',
          label: '状态',
          options: [
            { value: '1', label: '启用' },
            { value: '0', label: '禁用' }
          ],
          group: 'setting'
        },
        {
          key: 'enableSerialNumber',
          type: 'switch',
          label: '启用序列号',
          group: 'setting'
        },
        {
          key: 'enableBatchNumber',
          type: 'switch',
          label: '启用批次号',
          group: 'setting'
        },
        {
          key: 'remark',
          type: 'textarea',
          label: '备注',
          placeholder: '请输入备注信息',
          rows: 3,
          group: 'other'
        }
      ],

      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入商品名称', trigger: 'blur' },
          { min: 1, max: 50, message: '商品名称长度在1到50个字符', trigger: 'blur' }
        ],
        materialCategoryId: [
          { required: true, message: '请选择商品分类', trigger: 'change' }
        ],
        unitId: [
          { required: true, message: '请选择基本单位', trigger: 'change' }
        ],
        purchaseDecimalPrice: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的价格格式', trigger: 'blur' }
        ],
        commodityDecimalPrice: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的价格格式', trigger: 'blur' }
        ],
        wholesaleDecimalPrice: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的价格格式', trigger: 'blur' }
        ],
        retailPrice: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的价格格式', trigger: 'blur' }
        ]
      },

      // 选项数据
      categoryOptions: [],
      unitOptions: []
    }
  },

  computed: {
    // 分组后的表单字段
    groupedFields() {
      const groups = {
        basic: { title: '基本信息', fields: [] },
        price: { title: '价格信息', fields: [] },
        stock: { title: '库存信息', fields: [] },
        setting: { title: '设置选项', fields: [] },
        other: { title: '其他信息', fields: [] }
      }

      this.formFields.forEach(field => {
        const group = field.group || 'other'
        if (groups[group]) {
          groups[group].fields.push(field)
        }
      })

      return groups
    }
  },

  watch: {
    materialData: {
      handler(newData) {
        if (newData) {
          this.formModel = { ...this.formModel, ...newData }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    }
  },

  created() {
    this.loadOptions()
  },

  methods: {
    // 加载选项数据
    async loadOptions() {
      await Promise.all([
        this.loadCategoryOptions(),
        this.loadUnitOptions()
      ])
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const res = await getAction('/materialCategory/list')
        if (res.success) {
          this.categoryOptions = res.result.map(item => ({
            value: item.id,
            label: item.name
          }))
          
          // 更新表单字段选项
          const categoryField = this.formFields.find(f => f.key === 'materialCategoryId')
          if (categoryField) {
            categoryField.options = this.categoryOptions
          }
        }
      } catch (error) {
        console.error('加载分类选项失败:', error)
      }
    },

    // 加载单位选项
    async loadUnitOptions() {
      try {
        const res = await getAction('/unit/list')
        if (res.success) {
          this.unitOptions = res.result.map(item => ({
            value: item.id,
            label: item.name
          }))
          
          // 更新表单字段选项
          const unitField = this.formFields.find(f => f.key === 'unitId')
          if (unitField) {
            unitField.options = this.unitOptions
          }
        }
      } catch (error) {
        console.error('加载单位选项失败:', error)
      }
    },

    // 处理表单变化
    handleFormChange(field, value) {
      this.formModel[field] = value
      this.$emit('change', field, value, this.formModel)
    },

    // 处理表单验证
    handleValidate(valid, errors) {
      this.$emit('validate', valid, errors)
    },

    // 获取表单数据
    getFormData() {
      return { ...this.formModel }
    },

    // 验证表单
    async validate() {
      if (this.$refs.form) {
        return await this.$refs.form.validate()
      }
      return false
    },

    // 重置表单
    resetForm() {
      this.formModel = {
        name: '',
        materialNumber: '',
        materialCategoryId: '',
        materialOther: '',
        unitId: '',
        barCode: '',
        purchaseDecimalPrice: '',
        commodityDecimalPrice: '',
        wholesaleDecimalPrice: '',
        retailPrice: '',
        lowSafeStock: '',
        highSafeStock: '',
        enabled: '1',
        enableSerialNumber: '0',
        enableBatchNumber: '0',
        expiryNum: '',
        remark: '',
        imgUrl: ''
      }
      
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },

    // 设置表单数据
    setFormData(data) {
      this.formModel = { ...this.formModel, ...data }
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../styles/mobile.less";

.material-form {
  padding: @spacing-md;
  
  .form-group {
    margin-bottom: @spacing-xl;
    
    .group-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
      padding-bottom: @spacing-sm;
      border-bottom: 1px solid @border-color-light;
    }
  }
}
</style>
