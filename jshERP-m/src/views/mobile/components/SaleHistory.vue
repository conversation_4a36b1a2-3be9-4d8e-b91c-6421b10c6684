<template>
  <div class="sale-history">
    <!-- 搜索栏 -->
    <div class="history-search">
      <a-input-search
        v-model="searchKeyword"
        placeholder="搜索订单号、客户"
        @search="handleSearch"
        allowClear
      />
    </div>

    <!-- 日期筛选 -->
    <div class="date-filter">
      <a-button 
        v-for="period in datePeriods"
        :key="period.value"
        :type="selectedPeriod === period.value ? 'primary' : 'default'"
        size="small"
        @click="selectPeriod(period.value)"
      >
        {{ period.label }}
      </a-button>
    </div>

    <!-- 历史订单列表 -->
    <div class="history-list">
      <mobile-list
        :dataSource="historyList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="true"
        :showAvatar="false"
        :pagination="pagination"
        :itemActions="itemActions"
        titleField="title"
        descriptionField="description"
        timeField="timeText"
        extraField="amountInfo"
        @itemClick="handleItemClick"
        @actionClick="handleActionClick"
        @pageChange="handlePageChange"
        @refresh="loadHistoryList"
      >
        <!-- 自定义订单项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="history-item-content">
            <!-- 订单头部 -->
            <div class="history-item-header">
              <div class="order-number">{{ item.number }}</div>
              <div class="order-status" :class="item.statusClass">
                {{ item.statusText }}
              </div>
            </div>
            
            <!-- 订单信息 -->
            <div class="history-item-body">
              <div class="order-info">
                <div class="customer-info" v-if="item.customerName">
                  <a-icon type="user" />
                  {{ item.customerName }}
                </div>
                <div class="order-time">
                  <a-icon type="clock-circle" />
                  {{ item.operTime }}
                </div>
                <div class="item-count">
                  <a-icon type="shopping" />
                  {{ item.itemCount }}件商品
                </div>
              </div>
              
              <div class="order-amount">
                <div class="amount-label">实付金额</div>
                <div class="amount-value">¥{{ formatMoney(item.totalPrice) }}</div>
              </div>
            </div>
            
            <!-- 商品预览 -->
            <div class="order-items-preview" v-if="item.items && item.items.length > 0">
              <div class="items-summary">
                <span 
                  v-for="(product, index) in item.items.slice(0, 3)"
                  :key="product.id"
                  class="item-name"
                >
                  {{ product.name }}{{ index < Math.min(item.items.length, 3) - 1 ? '、' : '' }}
                </span>
                <span v-if="item.items.length > 3" class="more-items">
                  等{{ item.items.length }}件
                </span>
              </div>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && historyList.length === 0" class="empty-state">
      <a-empty 
        description="暂无销售记录"
        :image="require('@/assets/images/empty.svg')"
      />
    </div>
  </div>
</template>

<script>
import { MobileList } from '@/components/mobile'
import { getAction } from '@/api/manage'
import { formatDate } from '@/utils/util'

export default {
  name: 'SaleHistory',
  components: {
    MobileList
  },

  data() {
    return {
      loading: false,
      searchKeyword: '',
      selectedPeriod: 'today',
      
      // 历史订单列表
      historyList: [],
      
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },
      
      // 日期筛选选项
      datePeriods: [
        { value: 'today', label: '今日' },
        { value: 'week', label: '本周' },
        { value: 'month', label: '本月' },
        { value: 'all', label: '全部' }
      ],
      
      // 操作按钮
      itemActions: [
        {
          key: 'copy',
          label: '复制',
          icon: 'copy',
          type: 'default'
        },
        {
          key: 'detail',
          label: '详情',
          icon: 'eye',
          type: 'primary'
        }
      ]
    }
  },

  mounted() {
    this.loadHistoryList()
  },

  methods: {
    // 加载历史订单列表
    async loadHistoryList() {
      this.loading = true
      try {
        const params = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          type: '出库',
          subType: '零售',
          keyword: this.searchKeyword,
          ...this.getDateRange()
        }
        
        const response = await getAction('/depotHead/list', params)
        if (response.success) {
          const records = response.result.records || []
          this.historyList = records.map(this.formatHistoryItem)
          this.pagination.total = response.result.total || 0
        }
      } catch (error) {
        console.error('加载历史订单失败:', error)
        this.$message.error('加载历史订单失败')
      } finally {
        this.loading = false
      }
    },

    // 格式化历史订单项
    formatHistoryItem(item) {
      return {
        id: item.id,
        number: item.number,
        customerName: item.organName,
        operTime: formatDate(item.operTime, 'MM-DD HH:mm'),
        totalPrice: item.totalPrice || 0,
        itemCount: item.materialList?.length || 0,
        items: item.materialList || [],
        statusText: this.getStatusText(item.status),
        statusClass: this.getStatusClass(item.status),
        title: item.number,
        description: `${item.organName || '散客'} · ${formatDate(item.operTime, 'MM-DD HH:mm')}`,
        timeText: formatDate(item.operTime, 'MM-DD HH:mm'),
        amountInfo: `¥${this.formatMoney(item.totalPrice)}`
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '未审核',
        1: '已审核',
        2: '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: 'status-pending',
        1: 'status-approved',
        2: 'status-completed'
      }
      return classMap[status] || 'status-unknown'
    },

    // 获取日期范围
    getDateRange() {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      
      switch (this.selectedPeriod) {
        case 'today':
          return {
            startTime: today.toISOString(),
            endTime: new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString()
          }
        case 'week':
          const weekStart = new Date(today.getTime() - (today.getDay() || 7) * 24 * 60 * 60 * 1000)
          return {
            startTime: weekStart.toISOString(),
            endTime: now.toISOString()
          }
        case 'month':
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
          return {
            startTime: monthStart.toISOString(),
            endTime: now.toISOString()
          }
        default:
          return {}
      }
    },

    // 选择时间段
    selectPeriod(period) {
      this.selectedPeriod = period
      this.pagination.current = 1
      this.loadHistoryList()
    },

    // 搜索处理
    handleSearch() {
      this.pagination.current = 1
      this.loadHistoryList()
    },

    // 处理订单项点击
    handleItemClick(item) {
      // 选择历史订单，复制到当前购物车
      this.$emit('select', {
        items: item.items.map(product => ({
          id: product.materialId,
          name: product.materialName,
          unitPrice: product.unitPrice,
          quantity: product.operNumber,
          imgUrl: product.imgUrl
        }))
      })
    },

    // 处理操作按钮点击
    handleActionClick(action, item) {
      switch (action.key) {
        case 'copy':
          this.copyOrder(item)
          break
        case 'detail':
          this.viewDetail(item)
          break
      }
    },

    // 复制订单
    copyOrder(item) {
      this.handleItemClick(item)
      this.$message.success('订单已复制到购物车')
    },

    // 查看详情
    viewDetail(item) {
      // 跳转到订单详情页面
      this.$router.push(`/mobile/orders/${item.id}`)
    },

    // 处理分页变化
    handlePageChange(page) {
      this.pagination.current = page
      this.loadHistoryList()
    },

    // 格式化金额
    formatMoney(amount) {
      return Number(amount || 0).toFixed(2)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mobile/variables.less';

.sale-history {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  // 搜索栏
  .history-search {
    padding: @spacing-md;
    background: white;
    border-bottom: 1px solid @border-color-split;
  }
  
  // 日期筛选
  .date-filter {
    padding: @spacing-md;
    background: white;
    border-bottom: 1px solid @border-color-split;
    display: flex;
    gap: @spacing-sm;
    overflow-x: auto;
    
    .ant-btn {
      flex-shrink: 0;
    }
  }
  
  // 历史列表
  .history-list {
    flex: 1;
    overflow: hidden;
  }
  
  // 历史订单项内容
  .history-item-content {
    .history-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: @spacing-sm;
      
      .order-number {
        font-size: @font-size-base;
        font-weight: 600;
        color: @text-color;
      }
      
      .order-status {
        padding: @spacing-xs @spacing-sm;
        border-radius: 12px;
        font-size: @font-size-xs;
        font-weight: 500;
        
        &.status-pending {
          background: #FFF7E6;
          color: #FA8C16;
        }
        
        &.status-approved {
          background: #F6FFED;
          color: #52C41A;
        }
        
        &.status-completed {
          background: #E6F7FF;
          color: #1890FF;
        }
      }
    }
    
    .history-item-body {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: @spacing-sm;
      
      .order-info {
        flex: 1;
        
        > div {
          display: flex;
          align-items: center;
          margin-bottom: @spacing-xs;
          font-size: @font-size-sm;
          color: @text-color-secondary;
          
          .anticon {
            margin-right: @spacing-xs;
            font-size: 12px;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      .order-amount {
        text-align: right;
        
        .amount-label {
          font-size: @font-size-xs;
          color: @text-color-tertiary;
          margin-bottom: @spacing-xs;
        }
        
        .amount-value {
          font-size: @font-size-lg;
          font-weight: 600;
          color: @primary-color;
        }
      }
    }
    
    .order-items-preview {
      .items-summary {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        line-height: 1.4;
        
        .item-name {
          // 商品名称样式
        }
        
        .more-items {
          color: @text-color-tertiary;
        }
      }
    }
  }
  
  // 空状态
  .empty-state {
    flex: 1;
    .flex-center();
    padding: @spacing-xl;
  }
}
</style>
