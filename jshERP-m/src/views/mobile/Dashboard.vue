<template>
  <div class="mobile-dashboard">
    <!-- 顶部核心数据展示区 -->
    <div class="dashboard-hero">
      <div class="hero-background">
        <div class="hero-content">
          <!-- 今日核心数据 -->
          <div class="today-stats">
            <div class="main-stat">
              <div class="stat-amount">{{ formatMoney(stats.todaySales) }}</div>
              <div class="stat-label">今日销售</div>
            </div>
            <div class="secondary-stats">
              <div class="secondary-stat">
                <div class="stat-amount">{{ stats.orderCount }}</div>
                <div class="stat-label">今日订单</div>
              </div>
              <div class="secondary-stat">
                <div class="stat-amount">{{ formatMoney(stats.todayProfit) }}</div>
                <div class="stat-label">今日毛利</div>
              </div>
            </div>
          </div>

          <!-- 快捷功能区 -->
          <div class="quick-actions">
            <div class="quick-action" @click="navigateTo('/mobile/messages')">
              <div class="action-icon">
                <a-icon type="bell" />
                <a-badge v-if="messageCount > 0" :count="messageCount" class="action-badge" />
              </div>
              <div class="action-label">消息中心</div>
            </div>
            <div class="quick-action" @click="navigateTo('/mobile/receivables')">
              <div class="action-icon">
                <a-icon type="calendar" />
              </div>
              <div class="action-label">应收应付</div>
            </div>
            <div class="quick-action" @click="navigateTo('/mobile/inventory')">
              <div class="action-icon">
                <a-icon type="alert" />
                <a-badge v-if="stats.lowStockCount > 0" :count="stats.lowStockCount" class="action-badge" />
              </div>
              <div class="action-label">库存预警</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="dashboard-stats">
      <div class="stats-section">
        <div class="section-title">昨日数据</div>
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yesterdaySales) }}</div>
            <div class="stat-label">昨日销售</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yesterdayPurchase) }}</div>
            <div class="stat-label">昨日零售</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yesterdayPurchaseAmount) }}</div>
            <div class="stat-label">昨日采购</div>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <div class="section-title">本月累计</div>
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.monthSales) }}</div>
            <div class="stat-label">本月销售</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.monthPurchase) }}</div>
            <div class="stat-label">本月零售</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.monthPurchaseAmount) }}</div>
            <div class="stat-label">本月采购</div>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <div class="section-title">今年累计</div>
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yearSales) }}</div>
            <div class="stat-label">今年销售</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yearPurchase) }}</div>
            <div class="stat-label">今年零售</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yearPurchaseAmount) }}</div>
            <div class="stat-label">今年采购</div>
          </div>
        </div>
      </div>

      <div class="stats-section">
        <div class="section-title">库存总额</div>
        <div class="stats-row">
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.inventoryAmount) }}</div>
            <div class="stat-label">库存总金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.accountsReceivable) }}</div>
            <div class="stat-label">账户总金额</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatMoney(stats.yearProfit) }}</div>
            <div class="stat-label">今年总利润</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 业务操作快捷入口 -->
    <div class="dashboard-actions">
      <div class="section-title">业务操作</div>
      <div class="action-grid">
        <div class="action-item" @click="quickAction('pos')">
          <div class="action-icon pos-icon">
            <a-icon type="shopping" />
          </div>
          <div class="action-label">POS销售</div>
        </div>

        <div class="action-item" @click="quickAction('sale')">
          <div class="action-icon sale-icon">
            <a-icon type="file-text" />
          </div>
          <div class="action-label">销售订单</div>
        </div>

        <div class="action-item" @click="quickAction('purchase')">
          <div class="action-icon purchase-icon">
            <a-icon type="shopping-cart" />
          </div>
          <div class="action-label">采购订单</div>
        </div>

        <div class="action-item" @click="quickAction('inventory')">
          <div class="action-icon inventory-icon">
            <a-icon type="audit" />
          </div>
          <div class="action-label">库存盘点</div>
        </div>

        <div class="action-item" @click="quickAction('material')">
          <div class="action-icon material-icon">
            <a-icon type="database" />
          </div>
          <div class="action-label">商品管理</div>
        </div>

        <div class="action-item" @click="quickAction('report')">
          <div class="action-icon report-icon">
            <a-icon type="bar-chart" />
          </div>
          <div class="action-label">数据报表</div>
        </div>
      </div>
    </div>

    <!-- 最近动态 -->
    <div class="dashboard-activities">
      <div class="section-title">
        最近动态
        <a-button type="link" size="small" @click="viewAllActivities">查看全部</a-button>
      </div>
      
      <mobile-list
        :dataSource="activities"
        :loading="activitiesLoading"
        :showHeader="false"
        :showSearch="false"
        :showActions="false"
        :showAvatar="true"
        :pagination="false"
        titleField="title"
        descriptionField="description"
        timeField="timeText"
        @itemClick="handleActivityClick"
      />
    </div>

    <!-- 销售趋势图表 -->
    <div class="dashboard-chart">
      <div class="section-title">销售趋势</div>
      <div class="chart-container">
        <div ref="salesChart" class="chart"></div>
      </div>
    </div>

    <!-- 待办事项 -->
    <div class="dashboard-todos">
      <div class="section-title">
        待办事项
        <a-badge :count="todos.length" />
      </div>
      
      <div class="todo-list">
        <div 
          v-for="todo in todos" 
          :key="todo.id"
          class="todo-item"
          @click="handleTodoClick(todo)"
        >
          <div class="todo-content">
            <div class="todo-title">{{ todo.title }}</div>
            <div class="todo-time">{{ todo.timeText }}</div>
          </div>
          <div class="todo-priority" :class="`priority-${todo.priority}`">
            {{ getPriorityText(todo.priority) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList } from '@/components/mobile'
import { getAction } from '@/api/manage'
import * as echarts from 'echarts'

export default {
  name: 'MobileDashboard',
  mixins: [ResponsiveMixin],
  components: {
    MobileList
  },
  
  data() {
    return {
      // 统计数据
      stats: {
        materialCount: 0,
        orderCount: 0,
        lowStockCount: 0,
        todaySales: 0,
        todayProfit: 0,
        yesterdaySales: 0,
        yesterdayPurchase: 0,
        yesterdayPurchaseAmount: 0,
        monthSales: 0,
        monthPurchase: 0,
        monthPurchaseAmount: 0,
        yearSales: 0,
        yearPurchase: 0,
        yearPurchaseAmount: 0,
        inventoryAmount: 0,
        accountsReceivable: 0,
        yearProfit: 0
      },

      // 消息数量
      messageCount: 0,

      // 最近动态
      activities: [],
      activitiesLoading: false,

      // 待办事项
      todos: [],

      // 图表实例
      salesChart: null
    }
  },

  mounted() {
    this.loadDashboardData()
    this.initSalesChart()
  },

  beforeDestroy() {
    if (this.salesChart) {
      this.salesChart.dispose()
    }
  },

  methods: {
    // 加载仪表板数据
    async loadDashboardData() {
      try {
        await Promise.all([
          this.loadStats(),
          this.loadActivities(),
          this.loadTodos()
        ])
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const res = await getAction('/dashboard/stats')
        if (res.success) {
          this.stats = res.result
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载最近动态
    async loadActivities() {
      this.activitiesLoading = true
      try {
        const res = await getAction('/dashboard/activities', { limit: 10 })
        if (res.success) {
          this.activities = res.result.map(item => ({
            ...item,
            timeText: this.formatTime(item.createTime),
            avatar: this.getActivityAvatar(item.type)
          }))
        }
      } catch (error) {
        console.error('加载动态失败:', error)
      } finally {
        this.activitiesLoading = false
      }
    },

    // 加载待办事项
    async loadTodos() {
      try {
        const res = await getAction('/dashboard/todos')
        if (res.success) {
          this.todos = res.result.map(item => ({
            ...item,
            timeText: this.formatTime(item.dueTime)
          }))
        }
      } catch (error) {
        console.error('加载待办事项失败:', error)
      }
    },

    // 初始化销售趋势图表
    initSalesChart() {
      this.$nextTick(() => {
        if (this.$refs.salesChart) {
          this.salesChart = echarts.init(this.$refs.salesChart)
          this.updateSalesChart()
        }
      })
    },

    // 更新销售趋势图表
    async updateSalesChart() {
      try {
        const res = await getAction('/dashboard/sales-trend')
        if (res.success && this.salesChart) {
          const option = {
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: res.result.dates,
              axisLine: { lineStyle: { color: '#E5E7EB' } },
              axisLabel: { color: '#6B7280' }
            },
            yAxis: {
              type: 'value',
              axisLine: { lineStyle: { color: '#E5E7EB' } },
              axisLabel: { color: '#6B7280' },
              splitLine: { lineStyle: { color: '#F3F4F6' } }
            },
            series: [{
              data: res.result.values,
              type: 'line',
              smooth: true,
              lineStyle: { color: '#3B82F6', width: 3 },
              itemStyle: { color: '#3B82F6' },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0, y: 0, x2: 0, y2: 1,
                  colorStops: [
                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                    { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
                  ]
                }
              }
            }]
          }
          this.salesChart.setOption(option)
        }
      } catch (error) {
        console.error('加载销售趋势失败:', error)
      }
    },

    // 导航到指定页面
    navigateTo(path) {
      this.$router.push(path)
    },

    // 快捷操作
    quickAction(type) {
      switch (type) {
        case 'pos':
          // POS销售 - 跳转到零售出库页面
          this.$router.push('/mobile/pos-sale')
          break
        case 'sale':
          this.$router.push('/mobile/orders?type=sale&action=add')
          break
        case 'purchase':
          this.$router.push('/mobile/orders?type=purchase&action=add')
          break
        case 'inventory':
          this.$router.push('/mobile/inventory?action=add')
          break
        case 'material':
          this.$router.push('/mobile/material')
          break
        case 'report':
          this.$router.push('/mobile/reports')
          break
      }
    },

    // 处理动态点击
    handleActivityClick(activity) {
      // 根据动态类型跳转到相应页面
      if (activity.linkUrl) {
        this.$router.push(activity.linkUrl)
      }
    },

    // 查看全部动态
    viewAllActivities() {
      this.$router.push('/mobile/activities')
    },

    // 处理待办事项点击
    handleTodoClick(todo) {
      if (todo.linkUrl) {
        this.$router.push(todo.linkUrl)
      }
    },

    // 获取优先级文本
    getPriorityText(priority) {
      const map = {
        high: '高',
        medium: '中',
        low: '低'
      }
      return map[priority] || '中'
    },

    // 获取动态头像
    getActivityAvatar(type) {
      const avatarMap = {
        order: '📋',
        material: '📦',
        inventory: '📊',
        financial: '💰'
      }
      return avatarMap[type] || '📋'
    },

    // 格式化金额
    formatMoney(amount) {
      if (!amount) return '¥0'
      return `¥${Number(amount).toLocaleString()}`
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return `${Math.floor(diff / 86400000)}天前`
    }
  }
}
</script>

<style lang="less" scoped>

.mobile-dashboard {
  background-color: @background-color-light;
  min-height: 100vh;

  // 顶部核心数据展示区
  .dashboard-hero {
    margin-bottom: @spacing-lg;

    .hero-background {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
      border-radius: 0 0 24px 24px;
      padding: @spacing-xl @spacing-lg @spacing-lg;
      position: relative;
      overflow: hidden;

      // 背景装饰
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -10%;
        width: 150px;
        height: 150px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
      }

      .hero-content {
        position: relative;
        z-index: 1;
      }

      .today-stats {
        margin-bottom: @spacing-xl;

        .main-stat {
          text-align: center;
          margin-bottom: @spacing-lg;

          .stat-amount {
            font-size: 36px;
            font-weight: 700;
            color: white;
            line-height: 1.2;
            margin-bottom: @spacing-xs;
          }

          .stat-label {
            font-size: @font-size-base;
            color: rgba(255, 255, 255, 0.9);
          }
        }

        .secondary-stats {
          display: flex;
          justify-content: space-around;

          .secondary-stat {
            text-align: center;

            .stat-amount {
              font-size: 20px;
              font-weight: 600;
              color: white;
              line-height: 1.2;
              margin-bottom: @spacing-xs;
            }

            .stat-label {
              font-size: @font-size-sm;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }

      .quick-actions {
        display: flex;
        justify-content: space-around;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        padding: @spacing-md;
        backdrop-filter: blur(10px);

        .quick-action {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: @spacing-sm;
          border-radius: 12px;
          .touch-feedback();
          cursor: pointer;

          .action-icon {
            position: relative;
            width: 44px;
            height: 44px;
            .flex-center();
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin-bottom: @spacing-xs;

            .anticon {
              font-size: 20px;
              color: white;
            }

            .action-badge {
              position: absolute;
              top: -4px;
              right: -4px;
            }
          }

          .action-label {
            font-size: @font-size-xs;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
          }
        }
      }
    }
  }

  // 数据统计卡片
  .dashboard-stats {
    padding: 0 @spacing-lg;
    margin-bottom: @spacing-xl;

    .stats-section {
      margin-bottom: @spacing-lg;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: @font-size-base;
        font-weight: 600;
        color: @text-color;
        margin-bottom: @spacing-md;
        padding-left: @spacing-sm;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: @primary-color;
          border-radius: 2px;
        }
      }

      .stats-row {
        display: flex;
        gap: @spacing-sm;

        .stat-item {
          flex: 1;
          background: white;
          border-radius: 12px;
          padding: @spacing-md;
          text-align: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

          .stat-value {
            font-size: @font-size-lg;
            font-weight: 600;
            color: @text-color;
            line-height: 1.2;
            margin-bottom: @spacing-xs;
          }

          .stat-label {
            font-size: @font-size-xs;
            color: @text-color-secondary;
          }
        }
      }
    }
  }
  
  // 业务操作快捷入口
  .dashboard-actions {
    padding: 0 @spacing-lg;
    margin-bottom: @spacing-xl;

    .section-title {
      font-size: @font-size-base;
      font-weight: 600;
      color: @text-color;
      margin-bottom: @spacing-md;
      padding-left: @spacing-sm;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: @primary-color;
        border-radius: 2px;
      }
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: @spacing-md;

      .action-item {
        background: white;
        border-radius: 12px;
        padding: @spacing-lg @spacing-md;
        .flex-center();
        flex-direction: column;
        .touch-feedback();
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .action-icon {
          width: 48px;
          height: 48px;
          .flex-center();
          border-radius: 12px;
          margin-bottom: @spacing-sm;

          .anticon {
            font-size: 24px;
          }

          // 不同业务类型的图标颜色
          &.pos-icon {
            background: #F0F9FF;
            .anticon {
              color: #0EA5E9;
            }
          }

          &.sale-icon {
            background: #FEF3C7;
            .anticon {
              color: #D97706;
            }
          }

          &.purchase-icon {
            background: #ECFDF5;
            .anticon {
              color: #10B981;
            }
          }

          &.inventory-icon {
            background: #FDF2F8;
            .anticon {
              color: #EC4899;
            }
          }

          &.material-icon {
            background: #F3E8FF;
            .anticon {
              color: #8B5CF6;
            }
          }

          &.report-icon {
            background: #FFF7ED;
            .anticon {
              color: #EA580C;
            }
          }
        }

        .action-label {
          font-size: @font-size-sm;
          color: @text-color;
          text-align: center;
          font-weight: 500;
        }
      }
    }
  }
  
  .dashboard-activities {
    margin-bottom: @spacing-xl;
    
    .section-title {
      .flex-between();
      align-items: center;
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
  }
  
  .dashboard-chart {
    margin-bottom: @spacing-xl;
    
    .section-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .chart-container {
      .mobile-card();
      padding: @spacing-lg;
      
      .chart {
        width: 100%;
        height: 200px;
      }
    }
  }
  
  .dashboard-todos {
    .section-title {
      .flex-between();
      align-items: center;
      font-size: @font-size-lg;
      font-weight: @font-weight-semibold;
      color: @text-color;
      margin-bottom: @spacing-md;
    }
    
    .todo-list {
      .todo-item {
        .mobile-card();
        .flex-between();
        align-items: center;
        padding: @spacing-md @spacing-lg;
        margin-bottom: @spacing-sm;
        .touch-feedback();
        cursor: pointer;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .todo-content {
          flex: 1;
          
          .todo-title {
            font-size: @font-size-base;
            color: @text-color;
            margin-bottom: @spacing-xs;
          }
          
          .todo-time {
            font-size: @font-size-sm;
            color: @text-color-tertiary;
          }
        }
        
        .todo-priority {
          padding: @spacing-xs @spacing-sm;
          border-radius: @border-radius-sm;
          font-size: @font-size-xs;
          font-weight: @font-weight-medium;
          
          &.priority-high {
            background-color: #FEF2F2;
            color: #DC2626;
          }
          
          &.priority-medium {
            background-color: #FEF3C7;
            color: #D97706;
          }
          
          &.priority-low {
            background-color: #F0FDF4;
            color: #16A34A;
          }
        }
      }
    }
  }
}
</style>
