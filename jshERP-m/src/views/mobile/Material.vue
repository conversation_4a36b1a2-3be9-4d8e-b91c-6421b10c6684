<template>
  <div class="mobile-material">
    <!-- 移动端头部 -->
    <mobile-header
      title="商品管理"
      :showBack="false"
      :showActions="true"
    >
      <template slot="actions">
        <a-button
          type="link"
          size="small"
          @click="handleAdd"
        >
          <a-icon type="plus" />
          添加
        </a-button>
      </template>
    </mobile-header>

    <!-- 搜索和筛选区域 -->
    <div class="material-search">
      <div class="search-bar">
        <a-input-search
          v-model="searchValue"
          placeholder="搜索商品名称、编号、条码"
          @search="handleSearch"
          @change="handleSearchChange"
          allowClear
        >
          <template slot="enterButton">
            <a-icon type="search" />
          </template>
        </a-input-search>

        <!-- 扫码搜索按钮 -->
        <a-button
          class="scan-btn"
          @click="startScan"
          :loading="scanning"
        >
          <a-icon type="scan" />
        </a-button>
      </div>

      <!-- 快捷筛选标签 -->
      <div class="filter-tags">
        <div class="filter-scroll">
          <div
            v-for="filter in quickFilters"
            :key="filter.key"
            class="filter-tag"
            :class="{ active: activeFilter === filter.key }"
            @click="selectQuickFilter(filter.key)"
          >
            {{ filter.label }}
          </div>
        </div>
        <a-button
          type="text"
          size="small"
          @click="showFilterDrawer = true"
        >
          <a-icon type="filter" />
          筛选
        </a-button>
      </div>
    </div>

    <!-- 功能快捷入口 -->
    <div class="material-actions">
      <div class="action-grid">
        <div class="action-item" @click="quickAction('inventory')">
          <div class="action-icon inventory-icon">
            <a-icon type="database" />
          </div>
          <div class="action-label">库存盘点</div>
        </div>
        <div class="action-item" @click="quickAction('price')">
          <div class="action-icon price-icon">
            <a-icon type="dollar" />
          </div>
          <div class="action-label">价格调整</div>
        </div>
        <div class="action-item" @click="quickAction('category')">
          <div class="action-icon category-icon">
            <a-icon type="appstore" />
          </div>
          <div class="action-label">分类管理</div>
        </div>
        <div class="action-item" @click="quickAction('import')">
          <div class="action-icon import-icon">
            <a-icon type="upload" />
          </div>
          <div class="action-label">批量导入</div>
        </div>
      </div>
    </div>

    <!-- 商品卡片网格 -->
    <div class="material-content">
      <div class="material-grid" v-if="!loading && materialList.length > 0">
        <div
          v-for="material in materialList"
          :key="material.id"
          class="material-card"
          @click="handleItemClick(material)"
        >
          <!-- 商品图片 -->
          <div class="material-image">
            <img
              :src="material.imgUrl || '/img/default-product.png'"
              :alt="material.name"
              @error="handleImageError"
            />
            <div v-if="material.lowStock" class="stock-warning-badge">
              <a-icon type="exclamation" />
            </div>
          </div>

          <!-- 商品信息 -->
          <div class="material-info">
            <div class="material-name">{{ material.name }}</div>
            <div class="material-code">{{ material.materialNumber || '无编号' }}</div>
            <div class="material-price">¥{{ formatPrice(material.retailPrice) }}</div>

            <!-- 库存状态 -->
            <div class="material-stock">
              <div class="stock-info" :class="getStockClass(material)">
                <a-icon :type="getStockIcon(material)" />
                库存 {{ material.currentStock || 0 }}{{ material.unitName || '个' }}
              </div>
            </div>

            <!-- 商品标签 -->
            <div class="material-tags" v-if="material.tags && material.tags.length">
              <a-tag
                v-for="tag in material.tags.slice(0, 2)"
                :key="tag.key"
                :color="tag.color"
                size="small"
              >
                {{ tag.label }}
              </a-tag>
              <span v-if="material.tags.length > 2" class="more-tags">
                +{{ material.tags.length - 2 }}
              </span>
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="material-actions-overlay">
            <a-button
              type="primary"
              size="small"
              @click.stop="quickEdit(material)"
            >
              <a-icon type="edit" />
            </a-button>
            <a-button
              size="small"
              @click.stop="quickStock(material)"
            >
              <a-icon type="database" />
            </a-button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <a-spin size="large" />
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && materialList.length === 0" class="empty-state">
        <a-empty
          description="暂无商品数据"
          :image="require('@/assets/images/empty.svg')"
        >
          <a-button type="primary" @click="handleAdd">
            <a-icon type="plus" />
            添加商品
          </a-button>
        </a-empty>
      </div>

      <!-- 分页 -->
      <div v-if="!loading && materialList.length > 0" class="material-pagination">
        <a-pagination
          v-model="pagination.current"
          :total="pagination.total"
          :pageSize="pagination.pageSize"
          :showSizeChanger="false"
          :showQuickJumper="false"
          :showTotal="(total, range) => `共 ${total} 件商品`"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 底部批量操作栏 -->
    <div v-if="selectionMode && selectedMaterials.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedMaterials.length }} 项
      </div>
      <div class="batch-buttons">
        <a-button @click="batchEdit">批量编辑</a-button>
        <a-button @click="batchSetStock">修正库存</a-button>
        <a-button @click="batchSetPrice">修正价格</a-button>
        <a-button type="danger" @click="batchDelete">删除</a-button>
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <mobile-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      placement="bottom"
      :showFooter="true"
      @confirm="applyFilter"
      @cancel="resetFilter"
    >
      <mobile-form
        :fields="filterFields"
        :model="filterModel"
        @change="handleFilterChange"
      />
    </mobile-drawer>

    <!-- 分类选择抽屉 -->
    <mobile-drawer
      v-model="showCategoryDrawer"
      title="选择分类"
      placement="right"
      :width="280"
    >
      <div class="category-tree">
        <a-tree
          :treeData="categoryTree"
          :selectedKeys="selectedCategoryKeys"
          @select="handleCategorySelect"
          :showIcon="false"
        />
      </div>
    </mobile-drawer>

    <!-- 商品详情/编辑模态框 -->
    <mobile-modal
      v-model="showMaterialModal"
      :title="materialModalTitle"
      :fullscreen="$isMobile"
      :showFooter="materialModalMode !== 'view'"
      @ok="handleMaterialSave"
      @cancel="handleMaterialCancel"
    >
      <material-form
        ref="materialForm"
        :mode="materialModalMode"
        :materialData="currentMaterial"
        @change="handleMaterialFormChange"
      />
    </mobile-modal>

    <!-- 批量操作模态框 -->
    <mobile-modal
      v-model="showBatchModal"
      :title="batchModalTitle"
      @ok="handleBatchSave"
      @cancel="showBatchModal = false"
    >
      <batch-operation-form
        ref="batchForm"
        :type="batchType"
        :materials="selectedMaterials"
        @change="handleBatchFormChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList, MobileDrawer, MobileModal, MobileForm } from '@/components/mobile'
import VirtualMobileList from '@/components/mobile/VirtualMobileList'
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import MaterialForm from './components/MaterialForm'
import BatchOperationForm from './components/BatchOperationForm'
import { PageCache } from '@/utils/cache'
import { recordPerformance } from '@/utils/performance'
import { virtualScrollConfig } from '@/config/performance'

export default {
  name: 'MobileMaterial',
  mixins: [ResponsiveMixin],
  components: {
    MobileList,
    VirtualMobileList,
    MobileDrawer,
    MobileModal,
    MobileForm,
    MaterialForm,
    BatchOperationForm
  },

  data() {
    return {
      // 搜索和筛选
      searchValue: '',
      scanning: false,
      showFilterDrawer: false,
      showCategoryDrawer: false,
      filterModel: {},
      selectedCategoryKeys: [],
      activeFilter: 'all',

      // 快捷筛选选项
      quickFilters: [
        { key: 'all', label: '全部' },
        { key: 'low_stock', label: '库存不足' },
        { key: 'no_stock', label: '零库存' },
        { key: 'new', label: '新商品' },
        { key: 'hot', label: '热销' }
      ],

      // 列表数据
      materialList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },

      // 性能优化配置
      virtualScrollConfig: virtualScrollConfig.scenarios.material,
      pageCache: new PageCache('material'),

      // 选择模式
      selectionMode: false,
      selectedMaterials: [],

      // 模态框
      showMaterialModal: false,
      materialModalMode: 'add', // add, edit, view
      materialModalTitle: '',
      currentMaterial: null,

      // 批量操作
      showBatchModal: false,
      batchModalTitle: '',
      batchType: '',

      // 分类树
      categoryTree: [],
      
      // 筛选字段配置
      filterFields: [
        {
          key: 'categoryId',
          type: 'select',
          label: '商品分类',
          options: [],
          placeholder: '请选择分类'
        },
        {
          key: 'enabled',
          type: 'radio',
          label: '状态',
          options: [
            { value: '', label: '全部' },
            { value: '1', label: '启用' },
            { value: '0', label: '禁用' }
          ]
        },
        {
          key: 'stockStatus',
          type: 'radio',
          label: '库存状态',
          options: [
            { value: '', label: '全部' },
            { value: 'normal', label: '正常' },
            { value: 'low', label: '库存不足' },
            { value: 'zero', label: '零库存' }
          ]
        }
      ],
      
      // 操作菜单
      itemActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit' },
        { key: 'copy', label: '复制', icon: 'copy' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.hasStock }
      ]
    }
  },

  computed: {
    materialModalTitle() {
      const titleMap = {
        add: '添加商品',
        edit: '编辑商品',
        view: '商品详情'
      }
      return titleMap[this.materialModalMode] || '商品信息'
    },

    batchModalTitle() {
      const titleMap = {
        edit: '批量编辑',
        stock: '批量修正库存',
        price: '批量修正价格'
      }
      return titleMap[this.batchType] || '批量操作'
    },

    // 是否使用虚拟滚动
    useVirtualScroll() {
      return this.materialList.length > virtualScrollConfig.default.threshold
    }
  },

  created() {
    // 恢复页面状态
    this.restorePageState()

    this.loadMaterialList()
    this.loadCategoryTree()
    this.initFilterOptions()
  },

  beforeDestroy() {
    // 保存页面状态
    this.savePageState()
  },

  methods: {
    // 加载商品列表
    async loadMaterialList() {
      const startTime = performance.now()
      this.loading = true

      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          search: this.searchValue,
          ...this.filterModel,
          ...this.getQuickFilterParams()
        }

        // 尝试从缓存获取数据
        const cacheKey = `material_list_${JSON.stringify(params)}`
        let cachedData = this.pageCache.getListData(this.pagination.current)

        if (cachedData && !this.searchValue && Object.keys(this.filterModel).length === 0) {
          this.materialList = cachedData.records
          this.pagination.total = cachedData.total
          this.loading = false
          recordPerformance('material_list_cache_hit', performance.now() - startTime)
          return
        }

        const res = await getAction('/material/list', params)
        if (res.success) {
          const processedData = res.result.records.map(item => ({
            ...item,
            updateTimeText: this.formatTime(item.updateTime),
            tags: this.getMaterialTags(item),
            stockInfo: this.getStockInfo(item)
          }))

          this.materialList = processedData
          this.pagination.total = res.result.total

          // 缓存数据（仅在无搜索和筛选时）
          if (!this.searchValue && Object.keys(this.filterModel).length === 0) {
            this.pageCache.saveListData({
              records: processedData,
              total: res.result.total
            }, this.pagination.current)
          }

          // 记录性能指标
          recordPerformance('material_list_load', performance.now() - startTime)
        }
      } catch (error) {
        console.error('加载商品列表失败:', error)
        this.$message.error('加载商品列表失败')
        recordPerformance('material_list_error', performance.now() - startTime)
      } finally {
        this.loading = false
      }
    },

    // 加载分类树
    async loadCategoryTree() {
      try {
        const res = await getAction('/materialCategory/tree')
        if (res.success) {
          this.categoryTree = res.result
        }
      } catch (error) {
        console.error('加载分类树失败:', error)
      }
    },

    // 初始化筛选选项
    async initFilterOptions() {
      try {
        const res = await getAction('/materialCategory/list')
        if (res.success) {
          const categoryField = this.filterFields.find(f => f.key === 'categoryId')
          if (categoryField) {
            categoryField.options = res.result.map(item => ({
              value: item.id,
              label: item.name
            }))
          }
        }
      } catch (error) {
        console.error('初始化筛选选项失败:', error)
      }
    },

    // 扫码搜索
    startScan() {
      this.scanning = true
      // 这里可以集成扫码功能
      setTimeout(() => {
        this.scanning = false
        this.$message.info('扫码功能开发中...')
      }, 1000)
    },

    // 选择快捷筛选
    selectQuickFilter(filterKey) {
      this.activeFilter = filterKey
      this.pagination.current = 1
      this.loadMaterialList()
    },

    // 获取快捷筛选参数
    getQuickFilterParams() {
      switch (this.activeFilter) {
        case 'low_stock':
          return { stockStatus: 'low' }
        case 'no_stock':
          return { stockStatus: 'zero' }
        case 'new':
          return { isNew: true }
        case 'hot':
          return { isHot: true }
        default:
          return {}
      }
    },

    // 快捷操作
    quickAction(type) {
      switch (type) {
        case 'inventory':
          this.$router.push('/mobile/inventory/check')
          break
        case 'price':
          this.showBatchModal = true
          this.batchType = 'price'
          this.batchModalTitle = '批量调价'
          break
        case 'category':
          this.$router.push('/mobile/material/category')
          break
        case 'import':
          this.$router.push('/mobile/material/import')
          break
      }
    },

    // 快捷编辑
    quickEdit(material) {
      this.currentMaterial = material
      this.materialModalMode = 'edit'
      this.showMaterialModal = true
    },

    // 快捷库存调整
    quickStock(material) {
      this.selectedMaterials = [material]
      this.showBatchModal = true
      this.batchType = 'stock'
      this.batchModalTitle = '库存调整'
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.pagination.current = 1
      this.loadMaterialList()
    },

    // 处理搜索变化
    handleSearchChange(e) {
      if (!e.target.value) {
        this.handleSearch('')
      }
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadMaterialList()
    },

    // 处理商品项点击
    handleItemClick(item) {
      this.currentMaterial = item
      this.materialModalMode = 'view'
      this.showMaterialModal = true
    },

    // 处理操作点击
    handleActionClick(actionKey, item) {
      switch (actionKey) {
        case 'view':
          this.handleItemClick(item)
          break
        case 'edit':
          this.handleEdit(item)
          break
        case 'copy':
          this.handleCopy(item)
          break
        case 'delete':
          this.handleDelete(item)
          break
      }
    },

    // 处理选择变化
    handleSelectionChange(selectedItems) {
      this.selectedMaterials = selectedItems
    },

    // 添加商品
    handleAdd() {
      this.currentMaterial = null
      this.materialModalMode = 'add'
      this.showMaterialModal = true
    },

    // 编辑商品
    handleEdit(item) {
      this.currentMaterial = item
      this.materialModalMode = 'edit'
      this.showMaterialModal = true
    },

    // 复制商品
    handleCopy(item) {
      this.currentMaterial = { ...item, id: null, materialNumber: null }
      this.materialModalMode = 'add'
      this.showMaterialModal = true
    },

    // 删除商品
    handleDelete(item) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除商品"${item.name}"吗？`,
        onOk: async () => {
          try {
            const res = await deleteAction('/material/delete', { id: item.id })
            if (res.success) {
              this.$message.success('删除成功')
              this.loadMaterialList()
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        }
      })
    },

    // 获取商品标签
    getMaterialTags(item) {
      const tags = []
      
      if (item.enabled === '0') {
        tags.push({ key: 'disabled', label: '已禁用', color: 'red' })
      }
      
      if (item.currentStock <= item.lowSafeStock) {
        tags.push({ key: 'lowStock', label: '库存不足', color: 'orange' })
      }
      
      if (item.isSerialNumber === '1') {
        tags.push({ key: 'serial', label: '序列号', color: 'blue' })
      }
      
      return tags
    },

    // 获取库存信息
    getStockInfo(item) {
      return `成本: ¥${item.purchaseDecimalPrice || 0}`
    },

    // 获取库存样式类
    getStockClass(item) {
      if (item.currentStock <= 0) return 'stock-zero'
      if (item.currentStock <= item.lowSafeStock) return 'stock-low'
      return 'stock-normal'
    },

    // 获取库存图标
    getStockIcon(material) {
      const stock = material.currentStock || 0
      const safeStock = material.lowSafeStock || 0

      if (stock === 0) return 'close-circle'
      if (stock <= safeStock) return 'exclamation-circle'
      return 'check-circle'
    },

    // 图片加载错误处理
    handleImageError(event) {
      event.target.src = '/img/default-product.png'
    },

    // 格式化价格
    formatPrice(price) {
      return Number(price || 0).toFixed(2)
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleDateString()
    },

    // 保存页面状态
    savePageState() {
      const state = {
        searchValue: this.searchValue,
        filterModel: this.filterModel,
        pagination: this.pagination,
        selectedMaterials: this.selectedMaterials,
        selectionMode: this.selectionMode
      }
      this.pageCache.saveState(state)
    },

    // 恢复页面状态
    restorePageState() {
      const state = this.pageCache.restoreState()
      if (state) {
        this.searchValue = state.searchValue || ''
        this.filterModel = state.filterModel || {}
        this.pagination = { ...this.pagination, ...state.pagination }
        this.selectedMaterials = state.selectedMaterials || []
        this.selectionMode = state.selectionMode || false
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mobile/variables.less';

.mobile-material {
  background-color: @background-color-light;
  min-height: 100vh;

  // 搜索和筛选区域
  .material-search {
    background: white;
    padding: @spacing-lg;
    margin-bottom: @spacing-sm;

    .search-bar {
      display: flex;
      gap: @spacing-md;
      margin-bottom: @spacing-md;

      .ant-input-search {
        flex: 1;
      }

      .scan-btn {
        min-width: 60px;
        .flex-center();

        .anticon {
          font-size: 18px;
        }
      }
    }

    .filter-tags {
      display: flex;
      align-items: center;
      gap: @spacing-md;

      .filter-scroll {
        flex: 1;
        display: flex;
        overflow-x: auto;
        gap: @spacing-sm;

        &::-webkit-scrollbar {
          display: none;
        }

        .filter-tag {
          flex-shrink: 0;
          padding: @spacing-xs @spacing-md;
          background: @background-color-light;
          border-radius: 16px;
          font-size: @font-size-sm;
          color: @text-color-secondary;
          cursor: pointer;
          transition: all 0.3s;

          &.active {
            background: @primary-color;
            color: white;
          }
        }
      }
    }
  }

  // 功能快捷入口
  .material-actions {
    background: white;
    padding: @spacing-lg;
    margin-bottom: @spacing-sm;

    .action-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: @spacing-lg;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: @spacing-lg @spacing-md;
        cursor: pointer;
        transition: all 0.3s;
        border-radius: 12px;

        &:active {
          transform: scale(0.95);
          background: @background-color-light;
        }

        .action-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          .flex-center();
          margin-bottom: @spacing-sm;
          font-size: 20px;
          color: white;

          &.inventory-icon {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
          }

          &.price-icon {
            background: linear-gradient(135deg, #fa8c16, #ffa940);
          }

          &.category-icon {
            background: linear-gradient(135deg, #52c41a, #73d13d);
          }

          &.import-icon {
            background: linear-gradient(135deg, #722ed1, #9254de);
          }
        }

        .action-label {
          font-size: @font-size-sm;
          color: @text-color;
          text-align: center;
        }
      }
    }
  }

  // 商品内容区域
  .material-content {
    flex: 1;
    padding: 0 @spacing-lg @spacing-lg;

    // 商品卡片网格
    .material-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: @spacing-md;

      .material-card {
        background: white;
        border-radius: 12px;
        padding: @spacing-md;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        overflow: hidden;

        &:active {
          transform: scale(0.98);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .material-image {
          position: relative;
          width: 100%;
          height: 120px;
          margin-bottom: @spacing-sm;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
            background: @background-color-light;
          }

          .stock-warning-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: @error-color;
            color: white;
            border-radius: 50%;
            .flex-center();
            font-size: 12px;
          }
        }

        .material-info {
          .material-name {
            font-size: @font-size-base;
            color: @text-color;
            font-weight: 500;
            margin-bottom: @spacing-xs;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .material-code {
            font-size: @font-size-xs;
            color: @text-color-tertiary;
            margin-bottom: @spacing-xs;
          }

          .material-price {
            font-size: @font-size-lg;
            color: @primary-color;
            font-weight: 600;
            margin-bottom: @spacing-sm;
          }

          .material-stock {
            margin-bottom: @spacing-sm;

            .stock-info {
              display: flex;
              align-items: center;
              font-size: @font-size-xs;

              .anticon {
                margin-right: @spacing-xs;
                font-size: 12px;
              }

              &.stock-normal {
                color: @success-color;
              }

              &.stock-low {
                color: @warning-color;
              }

              &.stock-zero {
                color: @error-color;
              }
            }
          }

          .material-tags {
            display: flex;
            align-items: center;
            gap: @spacing-xs;

            .ant-tag {
              margin: 0;
              font-size: @font-size-xs;
            }

            .more-tags {
              font-size: @font-size-xs;
              color: @text-color-tertiary;
            }
          }
        }

        .material-actions-overlay {
          position: absolute;
          top: @spacing-md;
          right: @spacing-md;
          display: flex;
          gap: @spacing-xs;
          opacity: 0;
          transition: opacity 0.3s;

          .ant-btn {
            width: 32px;
            height: 32px;
            .flex-center();
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }

        &:hover .material-actions-overlay {
          opacity: 1;
        }
      }
    }

    // 加载状态
    .loading-state {
      .flex-center();
      flex-direction: column;
      padding: @spacing-xl;

      .loading-text {
        margin-top: @spacing-md;
        color: @text-color-secondary;
      }
    }

    // 空状态
    .empty-state {
      .flex-center();
      padding: @spacing-xl;
    }

    // 分页
    .material-pagination {
      margin-top: @spacing-lg;
      text-align: center;
    }
  }
}
    padding: @spacing-md @spacing-lg;
    border-bottom: 1px solid @border-color-light;
    
    .search-section {
      margin-bottom: @spacing-md;
    }
    
    .action-section {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      
      .ant-btn {
        .flex-center();
        gap: @spacing-xs;
      }
    }
  }
  
  .material-content {
    flex: 1;
    overflow: hidden;
    
    .material-item-content {
      width: 100%;
      
      .material-item-title {
        .flex-between();
        align-items: flex-start;
        margin-bottom: @spacing-xs;
        
        .material-name {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          flex: 1;
          .text-ellipsis();
        }
        
        .material-price {
          font-size: @font-size-lg;
          font-weight: @font-weight-semibold;
          color: @primary-color;
          margin-left: @spacing-sm;
        }
      }
      
      .material-item-info {
        .d-flex();
        gap: @spacing-lg;
        margin-bottom: @spacing-xs;
        
        .material-code,
        .material-unit {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
      
      .material-item-stock {
        .d-flex();
        gap: @spacing-lg;
        margin-bottom: @spacing-xs;
        
        .stock-current {
          font-size: @font-size-sm;
          font-weight: @font-weight-medium;
          
          &.stock-normal {
            color: @success-color;
          }
          
          &.stock-low {
            color: @warning-color;
          }
          
          &.stock-zero {
            color: @error-color;
          }
        }
        
        .stock-warning {
          font-size: @font-size-sm;
          color: @text-color-tertiary;
        }
      }
      
      .material-item-tags {
        .ant-tag {
          margin-right: @spacing-xs;
          margin-bottom: @spacing-xs;
        }
      }
    }
  }
  
  .batch-actions {
    background-color: white;
    padding: @spacing-md @spacing-lg;
    border-top: 1px solid @border-color-light;
    .flex-between();
    align-items: center;
    
    .batch-info {
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
    
    .batch-buttons {
      .d-flex();
      gap: @spacing-sm;
      
      .ant-btn {
        font-size: @font-size-sm;
        padding: @spacing-xs @spacing-sm;
      }
    }
  }
  
  .category-tree {
    padding: @spacing-md;
    
    .ant-tree {
      .ant-tree-node-content-wrapper {
        padding: @spacing-sm;
        border-radius: @border-radius-sm;
        
        &:hover {
          background-color: @background-color-light;
        }
        
        &.ant-tree-node-selected {
          background-color: @primary-color-light;
          color: @primary-color;
        }
      }
    }
  }
}
</style>
