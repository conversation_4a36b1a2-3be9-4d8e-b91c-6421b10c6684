<template>
  <div class="mobile-inventory">
    <!-- 顶部标签切换 -->
    <div class="inventory-header">
      <a-tabs v-model="activeInventoryType" @change="handleInventoryTypeChange">
        <a-tab-pane key="check" tab="库存盘点" />
        <a-tab-pane key="transfer" tab="库存调拨" />
        <a-tab-pane key="warning" tab="库存预警" />
      </a-tabs>

      <!-- 搜索栏 -->
      <div class="search-section">
        <a-input-search
          v-model="searchValue"
          :placeholder="searchPlaceholder"
          @search="handleSearch"
          @change="handleSearchChange"
          allowClear
        />
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <a-button type="text" @click="showFilterDrawer = true">
          <a-icon type="filter" />
          筛选
        </a-button>
        <a-button type="text" @click="showStatsDrawer = true">
          <a-icon type="bar-chart" />
          统计
        </a-button>
        <a-button
          type="primary"
          @click="handleAdd"
          v-if="activeInventoryType !== 'warning'"
        >
          <a-icon type="plus" />
          新建
        </a-button>
      </div>
    </div>

    <!-- 库存盘点列表 -->
    <div v-if="activeInventoryType === 'check'" class="inventory-content">
      <mobile-list
        :dataSource="inventoryList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="true"
        :showAvatar="false"
        :selectable="selectionMode"
        :selectedItems="selectedItems"
        :pagination="pagination"
        :itemActions="checkActions"
        titleField="title"
        descriptionField="description"
        timeField="timeText"
        tagsField="tags"
        @itemClick="handleItemClick"
        @selectionChange="handleSelectionChange"
        @actionClick="handleActionClick"
        @pageChange="handlePageChange"
        @refresh="loadInventoryList"
      >
        <!-- 自定义盘点项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="check-item-content">
            <!-- 盘点头部 -->
            <div class="check-item-header">
              <div class="check-number">{{ item.number }}</div>
              <div class="check-status" :class="`status-${item.status}`">
                {{ getCheckStatusText(item.status) }}
              </div>
            </div>

            <!-- 盘点信息 -->
            <div class="check-item-info">
              <div class="info-row">
                <span class="info-label">仓库:</span>
                <span class="info-value">{{ item.depotName || '全部仓库' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">盘点商品:</span>
                <span class="info-value">{{ item.materialCount || 0 }} 个</span>
              </div>
              <div class="info-row">
                <span class="info-label">差异商品:</span>
                <span class="info-value diff">{{ item.diffCount || 0 }} 个</span>
              </div>
            </div>

            <!-- 盘点时间和操作人 -->
            <div class="check-item-footer">
              <div class="check-time">
                <a-icon type="clock-circle" />
                {{ formatTime(item.operTime) }}
              </div>
              <div class="check-operator">
                <a-icon type="user" />
                {{ item.operPersonName || '系统' }}
              </div>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 库存调拨列表 -->
    <div v-if="activeInventoryType === 'transfer'" class="inventory-content">
      <mobile-list
        :dataSource="inventoryList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="true"
        :showAvatar="false"
        :selectable="selectionMode"
        :selectedItems="selectedItems"
        :pagination="pagination"
        :itemActions="transferActions"
        titleField="title"
        descriptionField="description"
        timeField="timeText"
        tagsField="tags"
        @itemClick="handleItemClick"
        @selectionChange="handleSelectionChange"
        @actionClick="handleActionClick"
        @pageChange="handlePageChange"
        @refresh="loadInventoryList"
      >
        <!-- 自定义调拨项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="transfer-item-content">
            <!-- 调拨头部 -->
            <div class="transfer-item-header">
              <div class="transfer-number">{{ item.number }}</div>
              <div class="transfer-status" :class="`status-${item.status}`">
                {{ getTransferStatusText(item.status) }}
              </div>
            </div>

            <!-- 调拨信息 -->
            <div class="transfer-item-info">
              <div class="info-row">
                <span class="info-label">调出仓库:</span>
                <span class="info-value">{{ item.outDepotName || '无' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">调入仓库:</span>
                <span class="info-value">{{ item.inDepotName || '无' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">调拨商品:</span>
                <span class="info-value">{{ item.materialCount || 0 }} 个</span>
              </div>
            </div>

            <!-- 调拨时间和操作人 -->
            <div class="transfer-item-footer">
              <div class="transfer-time">
                <a-icon type="clock-circle" />
                {{ formatTime(item.operTime) }}
              </div>
              <div class="transfer-operator">
                <a-icon type="user" />
                {{ item.operPersonName || '系统' }}
              </div>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 库存预警列表 -->
    <div v-if="activeInventoryType === 'warning'" class="inventory-content">
      <mobile-list
        :dataSource="inventoryList"
        :loading="loading"
        :showHeader="false"
        :showSearch="false"
        :showActions="true"
        :showAvatar="true"
        :selectable="false"
        :pagination="pagination"
        :itemActions="warningActions"
        titleField="name"
        descriptionField="description"
        avatarField="imgUrl"
        @itemClick="handleItemClick"
        @actionClick="handleActionClick"
        @pageChange="handlePageChange"
        @refresh="loadInventoryList"
      >
        <!-- 自定义预警项内容 -->
        <template slot="item" slot-scope="{ item }">
          <div class="warning-item-content">
            <!-- 商品头部 -->
            <div class="warning-item-header">
              <div class="material-name">{{ item.name }}</div>
              <div class="warning-level" :class="`level-${item.warningLevel}`">
                {{ getWarningLevelText(item.warningLevel) }}
              </div>
            </div>

            <!-- 库存信息 -->
            <div class="warning-item-info">
              <div class="info-row">
                <span class="info-label">当前库存:</span>
                <span class="info-value current-stock">{{ item.currentStock || 0 }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">安全库存:</span>
                <span class="info-value">{{ item.lowSafeStock || 0 }} - {{ item.highSafeStock || 0 }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">所属仓库:</span>
                <span class="info-value">{{ item.depotName || '默认仓库' }}</span>
              </div>
            </div>

            <!-- 预警原因 -->
            <div class="warning-item-reason">
              <a-tag :color="getWarningColor(item.warningLevel)" size="small">
                {{ getWarningReason(item) }}
              </a-tag>
            </div>
          </div>
        </template>
      </mobile-list>
    </div>

    <!-- 底部批量操作栏 -->
    <div v-if="selectionMode && selectedItems.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedItems.length }} 项
      </div>
      <div class="batch-buttons">
        <a-button @click="batchApprove" v-if="canBatchApprove">批量审核</a-button>
        <a-button @click="batchExport">导出</a-button>
        <a-button type="danger" @click="batchDelete" v-if="canBatchDelete">删除</a-button>
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <mobile-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      placement="bottom"
      :showFooter="true"
      @confirm="applyFilter"
      @cancel="resetFilter"
    >
      <mobile-form
        :fields="filterFields"
        :model="filterModel"
        @change="handleFilterChange"
      />
    </mobile-drawer>

    <!-- 统计抽屉 -->
    <mobile-drawer
      v-model="showStatsDrawer"
      title="库存统计"
      placement="right"
      :width="320"
    >
      <div class="stats-content">
        <div class="stats-cards">
          <div class="stats-card">
            <div class="stats-value">{{ statsData.totalMaterials || 0 }}</div>
            <div class="stats-label">商品总数</div>
          </div>
          <div class="stats-card">
            <div class="stats-value">{{ statsData.totalStock || 0 }}</div>
            <div class="stats-label">库存总量</div>
          </div>
          <div class="stats-card warning">
            <div class="stats-value">{{ statsData.warningCount || 0 }}</div>
            <div class="stats-label">预警商品</div>
          </div>
          <div class="stats-card danger">
            <div class="stats-value">{{ statsData.emptyCount || 0 }}</div>
            <div class="stats-label">缺货商品</div>
          </div>
        </div>
      </div>
    </mobile-drawer>

    <!-- 详情/编辑模态框 -->
    <mobile-modal
      v-model="showInventoryModal"
      :title="inventoryModalTitle"
      :fullscreen="$isMobile"
      :showFooter="inventoryModalMode !== 'view'"
      @ok="handleInventorySave"
      @cancel="handleInventoryCancel"
    >
      <inventory-form
        ref="inventoryForm"
        :mode="inventoryModalMode"
        :inventoryType="activeInventoryType"
        :inventoryData="currentInventory"
        @change="handleInventoryFormChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList, MobileDrawer, MobileModal, MobileForm } from '@/components/mobile'
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import InventoryForm from './components/InventoryForm'

export default {
  name: 'MobileInventory',
  mixins: [ResponsiveMixin],
  components: {
    MobileList,
    MobileDrawer,
    MobileModal,
    MobileForm,
    InventoryForm
  },

  data() {
    return {
      // 库存类型
      activeInventoryType: 'check', // check: 盘点, transfer: 调拨, warning: 预警

      // 搜索和筛选
      searchValue: '',
      showFilterDrawer: false,
      showStatsDrawer: false,
      filterModel: {},

      // 列表数据
      inventoryList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },

      // 选择模式
      selectionMode: false,
      selectedItems: [],

      // 模态框
      showInventoryModal: false,
      inventoryModalMode: 'add', // add, edit, view
      currentInventory: null,

      // 统计数据
      statsData: {},

      // 筛选字段配置
      filterFields: [],

      // 操作菜单
      checkActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit', disabled: item => item.status !== '0' },
        { key: 'approve', label: '审核', icon: 'check', disabled: item => item.status !== '0' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.status !== '0' }
      ],

      transferActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit', disabled: item => item.status !== '0' },
        { key: 'approve', label: '审核', icon: 'check', disabled: item => item.status !== '0' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.status !== '0' }
      ],

      warningActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'purchase', label: '采购补货', icon: 'shopping-cart' },
        { key: 'adjust', label: '调整库存', icon: 'edit' }
      ]
    }
  },

  computed: {
    searchPlaceholder() {
      const placeholderMap = {
        check: '搜索盘点单号、备注',
        transfer: '搜索调拨单号、仓库名称',
        warning: '搜索商品名称、编号'
      }
      return placeholderMap[this.activeInventoryType] || '搜索'
    },

    inventoryModalTitle() {
      const typeTextMap = {
        check: '库存盘点',
        transfer: '库存调拨',
        warning: '库存预警'
      }
      const typeText = typeTextMap[this.activeInventoryType] || '库存管理'
      const titleMap = {
        add: `新建${typeText}`,
        edit: `编辑${typeText}`,
        view: `${typeText}详情`
      }
      return titleMap[this.inventoryModalMode] || '库存信息'
    },

    // 批量操作权限
    canBatchApprove() {
      return this.selectedItems.some(item => item.status === '0')
    },

    canBatchDelete() {
      return this.selectedItems.every(item => item.status === '0')
    }
  },

  watch: {
    activeInventoryType: {
      handler() {
        this.initFilterFields()
        this.loadStatsData()
        this.resetFilter()
        this.loadInventoryList()
      },
      immediate: true
    }
  },

  created() {
    // 从路由参数获取库存类型
    if (this.$route.query.type) {
      this.activeInventoryType = this.$route.query.type
    }

    // 如果是添加操作，直接打开添加模态框
    if (this.$route.query.action === 'add') {
      this.$nextTick(() => {
        this.handleAdd()
      })
    }
  },

  methods: {
    // 初始化筛选字段
    initFilterFields() {
      const commonFields = [
        {
          key: 'operTimeStart',
          type: 'date',
          label: '开始日期',
          placeholder: '请选择开始日期'
        },
        {
          key: 'operTimeEnd',
          type: 'date',
          label: '结束日期',
          placeholder: '请选择结束日期'
        }
      ]

      if (this.activeInventoryType === 'check') {
        this.filterFields = [
          {
            key: 'status',
            type: 'select',
            label: '盘点状态',
            options: [
              { value: '0', label: '未审核' },
              { value: '1', label: '已审核' }
            ],
            placeholder: '请选择状态'
          },
          {
            key: 'depotId',
            type: 'select',
            label: '仓库',
            options: [],
            placeholder: '请选择仓库'
          },
          ...commonFields
        ]
      } else if (this.activeInventoryType === 'transfer') {
        this.filterFields = [
          {
            key: 'status',
            type: 'select',
            label: '调拨状态',
            options: [
              { value: '0', label: '未审核' },
              { value: '1', label: '已审核' }
            ],
            placeholder: '请选择状态'
          },
          {
            key: 'outDepotId',
            type: 'select',
            label: '调出仓库',
            options: [],
            placeholder: '请选择调出仓库'
          },
          {
            key: 'inDepotId',
            type: 'select',
            label: '调入仓库',
            options: [],
            placeholder: '请选择调入仓库'
          },
          ...commonFields
        ]
      } else if (this.activeInventoryType === 'warning') {
        this.filterFields = [
          {
            key: 'warningLevel',
            type: 'select',
            label: '预警级别',
            options: [
              { value: '1', label: '轻微预警' },
              { value: '2', label: '严重预警' },
              { value: '3', label: '紧急预警' }
            ],
            placeholder: '请选择预警级别'
          },
          {
            key: 'depotId',
            type: 'select',
            label: '仓库',
            options: [],
            placeholder: '请选择仓库'
          },
          {
            key: 'materialCategoryId',
            type: 'select',
            label: '商品分类',
            options: [],
            placeholder: '请选择分类'
          }
        ]
      }
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        const apiPath = `/inventory/${this.activeInventoryType}/stats`
        const res = await getAction(apiPath)
        if (res.success) {
          this.statsData = res.result
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载库存列表
    async loadInventoryList() {
      this.loading = true
      try {
        const apiPathMap = {
          check: '/inventoryCheck/list',
          transfer: '/inventoryTransfer/list',
          warning: '/inventoryWarning/list'
        }

        const apiPath = apiPathMap[this.activeInventoryType]
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          search: this.searchValue,
          ...this.filterModel
        }

        const res = await getAction(apiPath, params)
        if (res.success) {
          this.inventoryList = res.result.records.map(item => ({
            ...item,
            timeText: this.formatTime(item.operTime),
            tags: this.getInventoryTags(item),
            title: this.getInventoryTitle(item),
            description: this.getInventoryDescription(item)
          }))

          this.pagination.total = res.result.total
        }
      } catch (error) {
        console.error('加载库存列表失败:', error)
        this.$message.error('加载库存列表失败')
      } finally {
        this.loading = false
      }
    },

    // 处理库存类型变化
    handleInventoryTypeChange(activeKey) {
      this.activeInventoryType = activeKey
      this.selectedItems = []
      this.selectionMode = false
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.pagination.current = 1
      this.loadInventoryList()
    },

    // 处理搜索变化
    handleSearchChange(e) {
      if (!e.target.value) {
        this.handleSearch('')
      }
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadInventoryList()
    },

    // 处理项目点击
    handleItemClick(item) {
      this.currentInventory = item
      this.inventoryModalMode = 'view'
      this.showInventoryModal = true
    },

    // 处理操作点击
    handleActionClick(actionKey, item) {
      switch (actionKey) {
        case 'view':
          this.handleItemClick(item)
          break
        case 'edit':
          this.handleEdit(item)
          break
        case 'approve':
          this.handleApprove(item)
          break
        case 'delete':
          this.handleDelete(item)
          break
        case 'purchase':
          this.handlePurchase(item)
          break
        case 'adjust':
          this.handleAdjust(item)
          break
      }
    },

    // 处理选择变化
    handleSelectionChange(selectedItems) {
      this.selectedItems = selectedItems
    },

    // 添加
    handleAdd() {
      this.currentInventory = null
      this.inventoryModalMode = 'add'
      this.showInventoryModal = true
    },

    // 编辑
    handleEdit(item) {
      this.currentInventory = item
      this.inventoryModalMode = 'edit'
      this.showInventoryModal = true
    },

    // 审核
    handleApprove(item) {
      this.$confirm({
        title: '确认审核',
        content: `确定要审核"${item.number || item.name}"吗？`,
        onOk: async () => {
          try {
            const apiPathMap = {
              check: '/inventoryCheck/approve',
              transfer: '/inventoryTransfer/approve'
            }
            const apiPath = apiPathMap[this.activeInventoryType]
            const res = await postAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('审核成功')
              this.loadInventoryList()
            }
          } catch (error) {
            this.$message.error('审核失败')
          }
        }
      })
    },

    // 删除
    handleDelete(item) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除"${item.number || item.name}"吗？`,
        onOk: async () => {
          try {
            const apiPathMap = {
              check: '/inventoryCheck/delete',
              transfer: '/inventoryTransfer/delete'
            }
            const apiPath = apiPathMap[this.activeInventoryType]
            const res = await deleteAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('删除成功')
              this.loadInventoryList()
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        }
      })
    },

    // 采购补货
    handlePurchase(item) {
      // 跳转到采购订单页面
      this.$router.push({
        path: '/mobile/orders',
        query: {
          type: 'purchase',
          action: 'add',
          materialId: item.id
        }
      })
    },

    // 调整库存
    handleAdjust(item) {
      // 打开库存调整模态框
      this.currentInventory = item
      this.inventoryModalMode = 'adjust'
      this.showInventoryModal = true
    },

    // 应用筛选
    applyFilter() {
      this.pagination.current = 1
      this.loadInventoryList()
      this.showFilterDrawer = false
    },

    // 重置筛选
    resetFilter() {
      this.filterModel = {}
      this.loadInventoryList()
    },

    // 获取库存标签
    getInventoryTags(item) {
      const tags = []

      if (this.activeInventoryType === 'check' || this.activeInventoryType === 'transfer') {
        if (item.status === '0') {
          tags.push({ key: 'pending', label: '待审核', color: 'orange' })
        } else if (item.status === '1') {
          tags.push({ key: 'approved', label: '已审核', color: 'green' })
        }
      } else if (this.activeInventoryType === 'warning') {
        const levelMap = {
          '1': { label: '轻微', color: 'blue' },
          '2': { label: '严重', color: 'orange' },
          '3': { label: '紧急', color: 'red' }
        }
        const levelInfo = levelMap[item.warningLevel] || { label: '未知', color: 'gray' }
        tags.push({ key: 'level', label: levelInfo.label, color: levelInfo.color })
      }

      return tags
    },

    // 获取库存标题
    getInventoryTitle(item) {
      if (this.activeInventoryType === 'warning') {
        return item.name
      }
      return item.number
    },

    // 获取库存描述
    getInventoryDescription(item) {
      if (this.activeInventoryType === 'check') {
        return `仓库: ${item.depotName || '全部'} | 商品: ${item.materialCount || 0}个`
      } else if (this.activeInventoryType === 'transfer') {
        return `${item.outDepotName || '无'} → ${item.inDepotName || '无'}`
      } else if (this.activeInventoryType === 'warning') {
        return `库存: ${item.currentStock || 0} | 安全库存: ${item.lowSafeStock || 0}-${item.highSafeStock || 0}`
      }
      return ''
    },

    // 获取盘点状态文本
    getCheckStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '已审核'
      }
      return statusMap[status] || '未知'
    },

    // 获取调拨状态文本
    getTransferStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '已审核'
      }
      return statusMap[status] || '未知'
    },

    // 获取预警级别文本
    getWarningLevelText(level) {
      const levelMap = {
        '1': '轻微预警',
        '2': '严重预警',
        '3': '紧急预警'
      }
      return levelMap[level] || '未知'
    },

    // 获取预警颜色
    getWarningColor(level) {
      const colorMap = {
        '1': 'blue',
        '2': 'orange',
        '3': 'red'
      }
      return colorMap[level] || 'gray'
    },

    // 获取预警原因
    getWarningReason(item) {
      const currentStock = Number(item.currentStock || 0)
      const lowSafeStock = Number(item.lowSafeStock || 0)
      const highSafeStock = Number(item.highSafeStock || 0)

      if (currentStock <= 0) {
        return '库存为零'
      } else if (currentStock < lowSafeStock) {
        return '库存不足'
      } else if (currentStock > highSafeStock) {
        return '库存过多'
      }
      return '库存异常'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleDateString()
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../styles/mobile.less';

.mobile-inventory {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: @background-color-light;

  .inventory-header {
    background-color: white;
    border-bottom: 1px solid @border-color-light;

    .ant-tabs {
      .ant-tabs-bar {
        margin-bottom: 0;
        border-bottom: 1px solid @border-color-light;
      }
    }

    .search-section {
      padding: @spacing-md @spacing-lg;
    }

    .action-section {
      .d-flex();
      justify-content: space-between;
      align-items: center;
      padding: 0 @spacing-lg @spacing-md;

      .ant-btn {
        .flex-center();
        gap: @spacing-xs;
      }
    }
  }

  .inventory-content {
    flex: 1;
    overflow: hidden;

    .check-item-content,
    .transfer-item-content,
    .warning-item-content {
      width: 100%;

      .check-item-header,
      .transfer-item-header,
      .warning-item-header {
        .flex-between();
        align-items: flex-start;
        margin-bottom: @spacing-sm;

        .check-number,
        .transfer-number,
        .material-name {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          flex: 1;
          .text-ellipsis();
        }

        .check-status,
        .transfer-status,
        .warning-level {
          padding: @spacing-xs @spacing-sm;
          border-radius: @border-radius-sm;
          font-size: @font-size-xs;
          font-weight: @font-weight-medium;

          &.status-0 {
            background-color: #FEF3C7;
            color: #D97706;
          }

          &.status-1 {
            background-color: #F0FDF4;
            color: #16A34A;
          }

          &.level-1 {
            background-color: #EFF6FF;
            color: #2563EB;
          }

          &.level-2 {
            background-color: #FEF3C7;
            color: #D97706;
          }

          &.level-3 {
            background-color: #FEF2F2;
            color: #DC2626;
          }
        }
      }

      .check-item-info,
      .transfer-item-info,
      .warning-item-info {
        margin-bottom: @spacing-sm;

        .info-row {
          .d-flex();
          align-items: center;
          margin-bottom: @spacing-xs;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            min-width: 70px;
          }

          .info-value {
            font-size: @font-size-sm;
            color: @text-color;

            &.diff {
              color: @warning-color;
              font-weight: @font-weight-medium;
            }

            &.current-stock {
              font-weight: @font-weight-medium;
              color: @primary-color;
            }
          }
        }
      }

      .check-item-footer,
      .transfer-item-footer {
        .flex-between();
        align-items: center;
        margin-bottom: @spacing-sm;

        .check-time,
        .transfer-time,
        .check-operator,
        .transfer-operator {
          .d-flex();
          align-items: center;
          gap: @spacing-xs;
          font-size: @font-size-xs;
          color: @text-color-tertiary;
        }
      }

      .warning-item-reason {
        .ant-tag {
          margin-bottom: 0;
        }
      }
    }
  }

  .batch-actions {
    background-color: white;
    padding: @spacing-md @spacing-lg;
    border-top: 1px solid @border-color-light;
    .flex-between();
    align-items: center;

    .batch-info {
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }

    .batch-buttons {
      .d-flex();
      gap: @spacing-sm;

      .ant-btn {
        font-size: @font-size-sm;
        padding: @spacing-xs @spacing-sm;
      }
    }
  }

  .stats-content {
    padding: @spacing-md;

    .stats-cards {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: @spacing-md;

      .stats-card {
        .mobile-card();
        padding: @spacing-lg;
        text-align: center;

        &.warning {
          border-left: 4px solid @warning-color;
        }

        &.danger {
          border-left: 4px solid @error-color;
        }

        .stats-value {
          font-size: @font-size-xxl;
          font-weight: @font-weight-bold;
          color: @text-color;
          margin-bottom: @spacing-xs;
        }

        .stats-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
}
</style>