<template>
  <div class="mobile-inventory">
    <!-- 移动端头部 -->
    <mobile-header
      title="库存数据"
      :showBack="false"
      :showActions="true"
    >
      <template slot="actions">
        <a-button
          type="link"
          size="small"
          @click="showStatsDrawer = true"
        >
          <a-icon type="bar-chart" />
          统计
        </a-button>
      </template>
    </mobile-header>

    <!-- 库存概览卡片 -->
    <div class="inventory-overview">
      <div class="overview-cards">
        <div class="overview-card total-card">
          <div class="card-icon">
            <a-icon type="database" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ formatNumber(overviewData.totalProducts) }}</div>
            <div class="card-label">商品总数</div>
          </div>
        </div>

        <div class="overview-card value-card">
          <div class="card-icon">
            <a-icon type="dollar" />
          </div>
          <div class="card-content">
            <div class="card-value">¥{{ formatMoney(overviewData.totalValue) }}</div>
            <div class="card-label">库存总值</div>
          </div>
        </div>

        <div class="overview-card warning-card">
          <div class="card-icon">
            <a-icon type="exclamation-circle" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ overviewData.warningCount }}</div>
            <div class="card-label">预警商品</div>
          </div>
        </div>

        <div class="overview-card zero-card">
          <div class="card-icon">
            <a-icon type="close-circle" />
          </div>
          <div class="card-content">
            <div class="card-value">{{ overviewData.zeroStockCount }}</div>
            <div class="card-label">零库存</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存管理功能区 -->
    <div class="inventory-functions">
      <div class="section-title">库存管理</div>
      <div class="function-grid">
        <div class="function-item" @click="quickAction('check')">
          <div class="function-icon check-icon">
            <a-icon type="audit" />
          </div>
          <div class="function-label">库存盘点</div>
          <div class="function-desc">盘点库存数量</div>
        </div>

        <div class="function-item" @click="quickAction('adjust')">
          <div class="function-icon adjust-icon">
            <a-icon type="edit" />
          </div>
          <div class="function-label">库存调整</div>
          <div class="function-desc">调整库存数量</div>
        </div>

        <div class="function-item" @click="quickAction('transfer')">
          <div class="function-icon transfer-icon">
            <a-icon type="swap" />
          </div>
          <div class="function-label">库存调拨</div>
          <div class="function-desc">仓库间调拨</div>
        </div>

        <div class="function-item" @click="quickAction('warning')">
          <div class="function-icon warning-icon">
            <a-icon type="alert" />
          </div>
          <div class="function-label">库存预警</div>
          <div class="function-desc">查看预警商品</div>
        </div>

        <div class="function-item" @click="quickAction('report')">
          <div class="function-icon report-icon">
            <a-icon type="file-text" />
          </div>
          <div class="function-label">库存报表</div>
          <div class="function-desc">库存统计分析</div>
        </div>

        <div class="function-item" @click="quickAction('flow')">
          <div class="function-icon flow-icon">
            <a-icon type="branches" />
          </div>
          <div class="function-label">库存流水</div>
          <div class="function-desc">出入库记录</div>
        </div>
      </div>
    </div>

    <!-- 快捷查询区 -->
    <div class="inventory-search">
      <div class="section-title">快捷查询</div>
      <div class="search-bar">
        <a-input-search
          v-model="searchValue"
          placeholder="搜索商品名称、编号、条码"
          @search="handleSearch"
          @change="handleSearchChange"
          allowClear
        >
          <template slot="enterButton">
            <a-icon type="search" />
          </template>
        </a-input-search>

        <!-- 扫码查询按钮 -->
        <a-button
          class="scan-btn"
          @click="startScan"
          :loading="scanning"
        >
          <a-icon type="scan" />
        </a-button>
      </div>

      <!-- 快捷筛选 -->
      <div class="quick-filters">
        <div
          v-for="filter in quickFilters"
          :key="filter.key"
          class="filter-chip"
          :class="{ active: activeFilter === filter.key }"
          @click="selectQuickFilter(filter.key)"
        >
          <a-icon :type="filter.icon" />
          {{ filter.label }}
        </div>
      </div>
    </div>

    <!-- 库存商品列表 -->
    <div class="inventory-content">
      <div class="inventory-list" v-if="!loading && inventoryList.length > 0">
        <div
          v-for="item in inventoryList"
          :key="item.id"
          class="inventory-item"
          @click="handleItemClick(item)"
        >
          <!-- 商品图片 -->
          <div class="item-image">
            <img
              :src="item.imgUrl || '/img/default-product.png'"
              :alt="item.name"
              @error="handleImageError"
            />
            <div v-if="item.stockStatus === 'warning'" class="stock-warning-badge">
              <a-icon type="exclamation" />
            </div>
            <div v-if="item.stockStatus === 'zero'" class="stock-zero-badge">
              <a-icon type="close" />
            </div>
          </div>

          <!-- 商品信息 -->
          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-code">编号: {{ item.materialNumber || '无' }}</div>

            <!-- 库存信息 -->
            <div class="stock-info">
              <div class="stock-current" :class="getStockClass(item)">
                <a-icon :type="getStockIcon(item)" />
                当前库存: {{ item.currentStock || 0 }}{{ item.unitName || '个' }}
              </div>
              <div class="stock-value">
                库存价值: ¥{{ formatMoney(item.stockValue) }}
              </div>
            </div>

            <!-- 仓库分布 -->
            <div class="depot-info" v-if="item.depotStocks && item.depotStocks.length">
              <div class="depot-label">仓库分布:</div>
              <div class="depot-list">
                <div
                  v-for="depot in item.depotStocks.slice(0, 3)"
                  :key="depot.depotId"
                  class="depot-item"
                >
                  {{ depot.depotName }}: {{ depot.stock }}
                </div>
                <div v-if="item.depotStocks.length > 3" class="depot-more">
                  +{{ item.depotStocks.length - 3 }}个仓库
                </div>
              </div>
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="item-actions">
            <a-button
              type="primary"
              size="small"
              @click.stop="quickAdjust(item)"
            >
              调整
            </a-button>
            <a-button
              size="small"
              @click.stop="viewDetail(item)"
            >
              详情
            </a-button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <a-spin size="large" />
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && inventoryList.length === 0" class="empty-state">
        <a-empty
          description="暂无库存数据"
          :image="require('@/assets/images/empty.svg')"
        >
          <a-button type="primary" @click="loadInventoryList">
            <a-icon type="reload" />
            重新加载
          </a-button>
        </a-empty>
      </div>

      <!-- 分页 -->
      <div v-if="!loading && inventoryList.length > 0" class="inventory-pagination">
        <a-pagination
          v-model="pagination.current"
          :total="pagination.total"
          :pageSize="pagination.pageSize"
          :showSizeChanger="false"
          :showQuickJumper="false"
          :showTotal="(total, range) => `共 ${total} 件商品`"
          @change="handlePageChange"
        />
      </div>
    </div>





    <!-- 筛选抽屉 -->
    <mobile-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      placement="bottom"
      :showFooter="true"
      @confirm="applyFilter"
      @cancel="resetFilter"
    >
      <mobile-form
        :fields="filterFields"
        :model="filterModel"
        @change="handleFilterChange"
      />
    </mobile-drawer>

    <!-- 统计抽屉 -->
    <mobile-drawer
      v-model="showStatsDrawer"
      title="库存统计"
      placement="right"
      :width="320"
    >
      <div class="stats-content">
        <div class="stats-cards">
          <div class="stats-card">
            <div class="stats-value">{{ statsData.totalMaterials || 0 }}</div>
            <div class="stats-label">商品总数</div>
          </div>
          <div class="stats-card">
            <div class="stats-value">{{ statsData.totalStock || 0 }}</div>
            <div class="stats-label">库存总量</div>
          </div>
          <div class="stats-card warning">
            <div class="stats-value">{{ statsData.warningCount || 0 }}</div>
            <div class="stats-label">预警商品</div>
          </div>
          <div class="stats-card danger">
            <div class="stats-value">{{ statsData.emptyCount || 0 }}</div>
            <div class="stats-label">缺货商品</div>
          </div>
        </div>
      </div>
    </mobile-drawer>

    <!-- 详情/编辑模态框 -->
    <mobile-modal
      v-model="showInventoryModal"
      :title="inventoryModalTitle"
      :fullscreen="$isMobile"
      :showFooter="inventoryModalMode !== 'view'"
      @ok="handleInventorySave"
      @cancel="handleInventoryCancel"
    >
      <inventory-form
        ref="inventoryForm"
        :mode="inventoryModalMode"
        :inventoryType="activeInventoryType"
        :inventoryData="currentInventory"
        @change="handleInventoryFormChange"
      />
    </mobile-modal>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList, MobileDrawer, MobileModal, MobileForm } from '@/components/mobile'
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import InventoryForm from './components/InventoryForm'

export default {
  name: 'MobileInventory',
  mixins: [ResponsiveMixin],
  components: {
    MobileList,
    MobileDrawer,
    MobileModal,
    MobileForm,
    InventoryForm
  },

  data() {
    return {
      // 搜索和筛选
      searchValue: '',
      scanning: false,
      showFilterDrawer: false,
      showStatsDrawer: false,
      filterModel: {},
      activeFilter: 'all',

      // 快捷筛选选项
      quickFilters: [
        { key: 'all', label: '全部', icon: 'appstore' },
        { key: 'warning', label: '预警', icon: 'exclamation-circle' },
        { key: 'zero', label: '零库存', icon: 'close-circle' },
        { key: 'normal', label: '正常', icon: 'check-circle' }
      ],

      // 库存概览数据
      overviewData: {
        totalProducts: 0,
        totalValue: 0,
        warningCount: 0,
        zeroStockCount: 0
      },

      // 列表数据
      inventoryList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: false,
        showQuickJumper: false
      },

      // 选择模式
      selectionMode: false,
      selectedItems: [],

      // 模态框
      showInventoryModal: false,
      inventoryModalMode: 'add', // add, edit, view
      currentInventory: null,

      // 统计数据
      statsData: {},

      // 筛选字段配置
      filterFields: [],

      // 操作菜单
      checkActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit', disabled: item => item.status !== '0' },
        { key: 'approve', label: '审核', icon: 'check', disabled: item => item.status !== '0' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.status !== '0' }
      ],

      transferActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'edit', label: '编辑', icon: 'edit', disabled: item => item.status !== '0' },
        { key: 'approve', label: '审核', icon: 'check', disabled: item => item.status !== '0' },
        { key: 'delete', label: '删除', icon: 'delete', disabled: item => item.status !== '0' }
      ],

      warningActions: [
        { key: 'view', label: '查看详情', icon: 'eye' },
        { key: 'purchase', label: '采购补货', icon: 'shopping-cart' },
        { key: 'adjust', label: '调整库存', icon: 'edit' }
      ]
    }
  },

  computed: {
    searchPlaceholder() {
      const placeholderMap = {
        check: '搜索盘点单号、备注',
        transfer: '搜索调拨单号、仓库名称',
        warning: '搜索商品名称、编号'
      }
      return placeholderMap[this.activeInventoryType] || '搜索'
    },

    inventoryModalTitle() {
      const typeTextMap = {
        check: '库存盘点',
        transfer: '库存调拨',
        warning: '库存预警'
      }
      const typeText = typeTextMap[this.activeInventoryType] || '库存管理'
      const titleMap = {
        add: `新建${typeText}`,
        edit: `编辑${typeText}`,
        view: `${typeText}详情`
      }
      return titleMap[this.inventoryModalMode] || '库存信息'
    },

    // 批量操作权限
    canBatchApprove() {
      return this.selectedItems.some(item => item.status === '0')
    },

    canBatchDelete() {
      return this.selectedItems.every(item => item.status === '0')
    }
  },

  watch: {
    activeInventoryType: {
      handler() {
        this.initFilterFields()
        this.loadStatsData()
        this.resetFilter()
        this.loadInventoryList()
      },
      immediate: true
    }
  },

  created() {
    // 加载概览数据
    this.loadOverviewData()

    // 加载库存列表
    this.loadInventoryList()

    // 从路由参数获取筛选条件
    if (this.$route.query.filter) {
      this.activeFilter = this.$route.query.filter
    }
  },

  methods: {
    // 快捷操作
    quickAction(action) {
      switch (action) {
        case 'check':
          this.$router.push('/mobile/inventory/check')
          break
        case 'adjust':
          this.$router.push('/mobile/inventory/adjust')
          break
        case 'transfer':
          this.$router.push('/mobile/inventory/transfer')
          break
        case 'warning':
          this.selectQuickFilter('warning')
          break
        case 'report':
          this.$router.push('/mobile/inventory/report')
          break
        case 'flow':
          this.$router.push('/mobile/inventory/flow')
          break
      }
    },

    // 选择快捷筛选
    selectQuickFilter(key) {
      this.activeFilter = key
      this.pagination.current = 1
      this.loadInventoryList()
    },

    // 开始扫码
    startScan() {
      this.scanning = true
      // 这里集成扫码功能
      setTimeout(() => {
        this.scanning = false
        // 模拟扫码结果
        this.searchValue = '123456789'
        this.handleSearch(this.searchValue)
      }, 2000)
    },

    // 快捷调整
    quickAdjust(item) {
      this.$router.push({
        path: '/mobile/inventory/adjust',
        query: { materialId: item.id }
      })
    },

    // 查看详情
    viewDetail(item) {
      this.$router.push({
        path: '/mobile/inventory/detail',
        query: { materialId: item.id }
      })
    },

    // 获取库存状态样式类
    getStockClass(item) {
      if (!item.currentStock || item.currentStock === 0) {
        return 'stock-zero'
      } else if (item.currentStock <= (item.lowSafeStock || 0)) {
        return 'stock-warning'
      }
      return 'stock-normal'
    },

    // 获取库存状态图标
    getStockIcon(item) {
      if (!item.currentStock || item.currentStock === 0) {
        return 'close-circle'
      } else if (item.currentStock <= (item.lowSafeStock || 0)) {
        return 'exclamation-circle'
      }
      return 'check-circle'
    },

    // 处理图片错误
    handleImageError(event) {
      event.target.src = '/img/default-product.png'
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

    // 格式化金额
    formatMoney(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

    // 加载库存概览数据
    async loadOverviewData() {
      try {
        const res = await getAction('/material/inventory/overview')
        if (res.success) {
          this.overviewData = res.result
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
      }
    },

    // 初始化筛选字段
    initFilterFields() {
      const commonFields = [
        {
          key: 'operTimeStart',
          type: 'date',
          label: '开始日期',
          placeholder: '请选择开始日期'
        },
        {
          key: 'operTimeEnd',
          type: 'date',
          label: '结束日期',
          placeholder: '请选择结束日期'
        }
      ]

      if (this.activeInventoryType === 'check') {
        this.filterFields = [
          {
            key: 'status',
            type: 'select',
            label: '盘点状态',
            options: [
              { value: '0', label: '未审核' },
              { value: '1', label: '已审核' }
            ],
            placeholder: '请选择状态'
          },
          {
            key: 'depotId',
            type: 'select',
            label: '仓库',
            options: [],
            placeholder: '请选择仓库'
          },
          ...commonFields
        ]
      } else if (this.activeInventoryType === 'transfer') {
        this.filterFields = [
          {
            key: 'status',
            type: 'select',
            label: '调拨状态',
            options: [
              { value: '0', label: '未审核' },
              { value: '1', label: '已审核' }
            ],
            placeholder: '请选择状态'
          },
          {
            key: 'outDepotId',
            type: 'select',
            label: '调出仓库',
            options: [],
            placeholder: '请选择调出仓库'
          },
          {
            key: 'inDepotId',
            type: 'select',
            label: '调入仓库',
            options: [],
            placeholder: '请选择调入仓库'
          },
          ...commonFields
        ]
      } else if (this.activeInventoryType === 'warning') {
        this.filterFields = [
          {
            key: 'warningLevel',
            type: 'select',
            label: '预警级别',
            options: [
              { value: '1', label: '轻微预警' },
              { value: '2', label: '严重预警' },
              { value: '3', label: '紧急预警' }
            ],
            placeholder: '请选择预警级别'
          },
          {
            key: 'depotId',
            type: 'select',
            label: '仓库',
            options: [],
            placeholder: '请选择仓库'
          },
          {
            key: 'materialCategoryId',
            type: 'select',
            label: '商品分类',
            options: [],
            placeholder: '请选择分类'
          }
        ]
      }
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        const apiPath = `/inventory/${this.activeInventoryType}/stats`
        const res = await getAction(apiPath)
        if (res.success) {
          this.statsData = res.result
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载库存列表
    async loadInventoryList() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          search: this.searchValue,
          ...this.filterModel
        }

        // 根据筛选条件添加参数
        if (this.activeFilter !== 'all') {
          switch (this.activeFilter) {
            case 'warning':
              params.stockStatus = 'warning'
              break
            case 'zero':
              params.stockStatus = 'zero'
              break
            case 'normal':
              params.stockStatus = 'normal'
              break
          }
        }

        const res = await getAction('/material/inventory/list', params)
        if (res.success) {
          this.inventoryList = res.result.records.map(item => ({
            ...item,
            stockStatus: this.getStockStatus(item),
            stockValue: (item.currentStock || 0) * (item.retailPrice || 0),
            depotStocks: item.depotStocks || []
          }))

          this.pagination.total = res.result.total
        }
      } catch (error) {
        console.error('加载库存列表失败:', error)
        this.$message.error('加载库存列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取库存状态
    getStockStatus(item) {
      if (!item.currentStock || item.currentStock === 0) {
        return 'zero'
      } else if (item.currentStock <= (item.lowSafeStock || 0)) {
        return 'warning'
      }
      return 'normal'
    },

    // 处理库存类型变化
    handleInventoryTypeChange(activeKey) {
      this.activeInventoryType = activeKey
      this.selectedItems = []
      this.selectionMode = false
    },

    // 处理搜索
    handleSearch(value) {
      this.searchValue = value
      this.pagination.current = 1
      this.loadInventoryList()
    },

    // 处理搜索变化
    handleSearchChange(e) {
      if (!e.target.value) {
        this.handleSearch('')
      }
    },

    // 处理分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadInventoryList()
    },

    // 处理项目点击
    handleItemClick(item) {
      this.currentInventory = item
      this.inventoryModalMode = 'view'
      this.showInventoryModal = true
    },

    // 处理操作点击
    handleActionClick(actionKey, item) {
      switch (actionKey) {
        case 'view':
          this.handleItemClick(item)
          break
        case 'edit':
          this.handleEdit(item)
          break
        case 'approve':
          this.handleApprove(item)
          break
        case 'delete':
          this.handleDelete(item)
          break
        case 'purchase':
          this.handlePurchase(item)
          break
        case 'adjust':
          this.handleAdjust(item)
          break
      }
    },

    // 处理选择变化
    handleSelectionChange(selectedItems) {
      this.selectedItems = selectedItems
    },

    // 添加
    handleAdd() {
      this.currentInventory = null
      this.inventoryModalMode = 'add'
      this.showInventoryModal = true
    },

    // 编辑
    handleEdit(item) {
      this.currentInventory = item
      this.inventoryModalMode = 'edit'
      this.showInventoryModal = true
    },

    // 审核
    handleApprove(item) {
      this.$confirm({
        title: '确认审核',
        content: `确定要审核"${item.number || item.name}"吗？`,
        onOk: async () => {
          try {
            const apiPathMap = {
              check: '/inventoryCheck/approve',
              transfer: '/inventoryTransfer/approve'
            }
            const apiPath = apiPathMap[this.activeInventoryType]
            const res = await postAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('审核成功')
              this.loadInventoryList()
            }
          } catch (error) {
            this.$message.error('审核失败')
          }
        }
      })
    },

    // 删除
    handleDelete(item) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除"${item.number || item.name}"吗？`,
        onOk: async () => {
          try {
            const apiPathMap = {
              check: '/inventoryCheck/delete',
              transfer: '/inventoryTransfer/delete'
            }
            const apiPath = apiPathMap[this.activeInventoryType]
            const res = await deleteAction(apiPath, { id: item.id })
            if (res.success) {
              this.$message.success('删除成功')
              this.loadInventoryList()
            }
          } catch (error) {
            this.$message.error('删除失败')
          }
        }
      })
    },

    // 采购补货
    handlePurchase(item) {
      // 跳转到采购订单页面
      this.$router.push({
        path: '/mobile/orders',
        query: {
          type: 'purchase',
          action: 'add',
          materialId: item.id
        }
      })
    },

    // 调整库存
    handleAdjust(item) {
      // 打开库存调整模态框
      this.currentInventory = item
      this.inventoryModalMode = 'adjust'
      this.showInventoryModal = true
    },

    // 应用筛选
    applyFilter() {
      this.pagination.current = 1
      this.loadInventoryList()
      this.showFilterDrawer = false
    },

    // 重置筛选
    resetFilter() {
      this.filterModel = {}
      this.loadInventoryList()
    },

    // 获取库存标签
    getInventoryTags(item) {
      const tags = []

      if (this.activeInventoryType === 'check' || this.activeInventoryType === 'transfer') {
        if (item.status === '0') {
          tags.push({ key: 'pending', label: '待审核', color: 'orange' })
        } else if (item.status === '1') {
          tags.push({ key: 'approved', label: '已审核', color: 'green' })
        }
      } else if (this.activeInventoryType === 'warning') {
        const levelMap = {
          '1': { label: '轻微', color: 'blue' },
          '2': { label: '严重', color: 'orange' },
          '3': { label: '紧急', color: 'red' }
        }
        const levelInfo = levelMap[item.warningLevel] || { label: '未知', color: 'gray' }
        tags.push({ key: 'level', label: levelInfo.label, color: levelInfo.color })
      }

      return tags
    },

    // 获取库存标题
    getInventoryTitle(item) {
      if (this.activeInventoryType === 'warning') {
        return item.name
      }
      return item.number
    },

    // 获取库存描述
    getInventoryDescription(item) {
      if (this.activeInventoryType === 'check') {
        return `仓库: ${item.depotName || '全部'} | 商品: ${item.materialCount || 0}个`
      } else if (this.activeInventoryType === 'transfer') {
        return `${item.outDepotName || '无'} → ${item.inDepotName || '无'}`
      } else if (this.activeInventoryType === 'warning') {
        return `库存: ${item.currentStock || 0} | 安全库存: ${item.lowSafeStock || 0}-${item.highSafeStock || 0}`
      }
      return ''
    },

    // 获取盘点状态文本
    getCheckStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '已审核'
      }
      return statusMap[status] || '未知'
    },

    // 获取调拨状态文本
    getTransferStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '已审核'
      }
      return statusMap[status] || '未知'
    },

    // 获取预警级别文本
    getWarningLevelText(level) {
      const levelMap = {
        '1': '轻微预警',
        '2': '严重预警',
        '3': '紧急预警'
      }
      return levelMap[level] || '未知'
    },

    // 获取预警颜色
    getWarningColor(level) {
      const colorMap = {
        '1': 'blue',
        '2': 'orange',
        '3': 'red'
      }
      return colorMap[level] || 'gray'
    },

    // 获取预警原因
    getWarningReason(item) {
      const currentStock = Number(item.currentStock || 0)
      const lowSafeStock = Number(item.lowSafeStock || 0)
      const highSafeStock = Number(item.highSafeStock || 0)

      if (currentStock <= 0) {
        return '库存为零'
      } else if (currentStock < lowSafeStock) {
        return '库存不足'
      } else if (currentStock > highSafeStock) {
        return '库存过多'
      }
      return '库存异常'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleDateString()
    },

    // 重置筛选
    resetFilter() {
      this.activeFilter = 'all'
      this.searchKeyword = ''
      this.loadInventoryList()
    },

    // 应用筛选
    applyFilter() {
      this.loadInventoryList()
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

    // 格式化金额
    formatMoney(amount) {
      if (!amount) return '¥0.00'
      return `¥${Number(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
    },

    // 处理图片错误
    handleImageError(event) {
      event.target.src = require('@/assets/images/default-product.png')
    }
  }
}
</script>

<style lang="less" scoped>
@import '@/styles/mobile/variables.less';
@import '@/styles/mobile/mixins.less';

.mobile-inventory {
  .mobile-page();
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  min-height: 100vh;

  // 库存概览卡片
  .inventory-overview {
    padding: @spacing-md;
    margin-bottom: @spacing-md;

    .overview-cards {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: @spacing-md;

      .overview-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: @border-radius-lg;
        padding: @spacing-lg;
        text-align: center;
        box-shadow: 0 4px 20px rgba(255, 107, 53, 0.15);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 30px rgba(255, 107, 53, 0.25);
        }

        .card-icon {
          font-size: 24px;
          margin-bottom: @spacing-sm;

          .anticon {
            color: #ff6b35;
          }
        }

        .card-content {
          .card-value {
            font-size: @font-size-xxl;
            font-weight: @font-weight-bold;
            color: @text-color;
            margin-bottom: @spacing-xs;
          }

          .card-label {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }

        &.total-card .card-icon .anticon {
          color: #1890ff;
        }

        &.value-card .card-icon .anticon {
          color: #52c41a;
        }

        &.warning-card .card-icon .anticon {
          color: #faad14;
        }

        &.zero-card .card-icon .anticon {
          color: #f5222d;
        }
      }
    }
  }

  // 库存管理功能区
  .inventory-functions {
    background: @white;
    margin: 0 @spacing-md @spacing-md;
    border-radius: @border-radius-lg;
    padding: @spacing-lg;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .section-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: @text-color;
      margin-bottom: @spacing-lg;
      padding-bottom: @spacing-sm;
      border-bottom: 2px solid #ff6b35;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 30px;
        height: 2px;
        background: #ff6b35;
      }
    }

    .function-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: @spacing-md;

      .function-item {
        background: linear-gradient(135deg, #fff5f0 0%, #fff2e8 100%);
        border-radius: @border-radius-lg;
        padding: @spacing-lg;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 107, 53, 0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(255, 107, 53, 0.15);
          border-color: #ff6b35;
        }

        &:active {
          transform: scale(0.98);
        }

        .function-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin: 0 auto @spacing-sm;
          .d-flex();
          align-items: center;
          justify-content: center;
          font-size: 20px;
          color: @white;

          &.check-icon {
            background: linear-gradient(135deg, #ff9a56, #ff6b35);
          }

          &.adjust-icon {
            background: linear-gradient(135deg, #ffa940, #fa8c16);
          }

          &.transfer-icon {
            background: linear-gradient(135deg, #ffbb96, #ff7a45);
          }

          &.warning-icon {
            background: linear-gradient(135deg, #ffd666, #faad14);
          }

          &.report-icon {
            background: linear-gradient(135deg, #b7eb8f, #52c41a);
          }

          &.flow-icon {
            background: linear-gradient(135deg, #87e8de, #13c2c2);
          }
        }

        .function-label {
          font-size: @font-size-base;
          font-weight: @font-weight-medium;
          color: @text-color;
          margin-bottom: @spacing-xs;
        }

        .function-desc {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }

  // 快捷查询区
  .inventory-search {
    background: @white;
    margin: 0 @spacing-md @spacing-md;
    border-radius: @border-radius-lg;
    padding: @spacing-lg;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .section-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: @text-color;
      margin-bottom: @spacing-lg;
      padding-bottom: @spacing-sm;
      border-bottom: 2px solid #ff6b35;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 30px;
        height: 2px;
        background: #ff6b35;
      }
    }

    .search-bar {
      .d-flex();
      gap: @spacing-sm;
      margin-bottom: @spacing-lg;

      .ant-input-search {
        flex: 1;

        .ant-input {
          border-radius: @border-radius-lg;
          border-color: rgba(255, 107, 53, 0.3);
          font-size: @font-size-base;

          &:focus {
            border-color: #ff6b35;
            box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
          }
        }

        .ant-input-search-button {
          border-radius: 0 @border-radius-lg @border-radius-lg 0;
          background: #ff6b35;
          border-color: #ff6b35;

          &:hover {
            background: #ff5722;
            border-color: #ff5722;
          }
        }
      }

      .scan-btn {
        width: 48px;
        height: 48px;
        border-radius: @border-radius-lg;
        background: linear-gradient(135deg, #ff9a56, #ff6b35);
        border: none;
        color: @white;
        font-size: 18px;
        .d-flex();
        align-items: center;
        justify-content: center;

        &:hover {
          background: linear-gradient(135deg, #ff8a45, #ff5722);
          transform: translateY(-1px);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }

    .quick-filters {
      .d-flex();
      gap: @spacing-sm;
      flex-wrap: wrap;

      .filter-chip {
        padding: @spacing-sm @spacing-md;
        border-radius: @border-radius-lg;
        background: #f5f5f5;
        border: 1px solid @border-color-base;
        font-size: @font-size-sm;
        color: @text-color-secondary;
        cursor: pointer;
        transition: all 0.3s ease;
        .d-flex();
        align-items: center;
        gap: @spacing-xs;

        &:hover {
          background: rgba(255, 107, 53, 0.1);
          border-color: #ff6b35;
          color: #ff6b35;
        }

        &.active {
          background: #ff6b35;
          border-color: #ff6b35;
          color: @white;
        }
      }
    }
  }

  // 库存商品列表
  .inventory-content {
    background: @white;
    margin: 0 @spacing-md;
    border-radius: @border-radius-lg;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .inventory-list {
      .inventory-item {
        .d-flex();
        padding: @spacing-lg;
        border-bottom: 1px solid @border-color-split;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 107, 53, 0.05);
        }

        &:last-child {
          border-bottom: none;
        }

        .item-image {
          width: 80px;
          height: 80px;
          border-radius: @border-radius-base;
          overflow: hidden;
          margin-right: @spacing-md;
          position: relative;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .stock-warning-badge,
          .stock-zero-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            .d-flex();
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: @white;
          }

          .stock-warning-badge {
            background: @warning-color;
          }

          .stock-zero-badge {
            background: @error-color;
          }
        }

        .item-info {
          flex: 1;
          min-width: 0;

          .item-name {
            font-size: @font-size-base;
            font-weight: @font-weight-medium;
            color: @text-color;
            margin-bottom: @spacing-xs;
            .text-ellipsis();
          }

          .item-code {
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin-bottom: @spacing-sm;
          }

          .stock-info {
            margin-bottom: @spacing-sm;

            .stock-current {
              font-size: @font-size-sm;
              margin-bottom: @spacing-xs;
              .d-flex();
              align-items: center;
              gap: @spacing-xs;

              &.stock-normal {
                color: @success-color;
              }

              &.stock-warning {
                color: @warning-color;
              }

              &.stock-zero {
                color: @error-color;
              }
            }

            .stock-value {
              font-size: @font-size-sm;
              color: @text-color-secondary;
            }
          }

          .depot-info {
            .depot-label {
              font-size: @font-size-sm;
              color: @text-color-secondary;
              margin-bottom: @spacing-xs;
            }

            .depot-list {
              .d-flex();
              flex-wrap: wrap;
              gap: @spacing-xs;

              .depot-item,
              .depot-more {
                font-size: @font-size-xs;
                padding: 2px @spacing-xs;
                background: #f5f5f5;
                border-radius: @border-radius-sm;
                color: @text-color-secondary;
              }

              .depot-more {
                color: #ff6b35;
              }
            }
          }
        }

        .item-actions {
          .d-flex();
          flex-direction: column;
          gap: @spacing-xs;
          flex-shrink: 0;

          .ant-btn {
            font-size: @font-size-sm;
            height: 32px;
            padding: 0 @spacing-sm;

            &.ant-btn-primary {
              background: #ff6b35;
              border-color: #ff6b35;

              &:hover {
                background: #ff5722;
                border-color: #ff5722;
              }
            }
          }
        }
      }
    }

    .loading-state {
      padding: @spacing-xxl;
      text-align: center;

      .loading-text {
        margin-top: @spacing-md;
        color: @text-color-secondary;
      }
    }

    .empty-state {
      padding: @spacing-xxl;
      text-align: center;

      .ant-btn {
        background: #ff6b35;
        border-color: #ff6b35;

        &:hover {
          background: #ff5722;
          border-color: #ff5722;
        }
      }
    }

    .inventory-pagination {
      padding: @spacing-lg;
      text-align: center;
      border-top: 1px solid @border-color-split;

      .ant-pagination {
        .ant-pagination-item-active {
          border-color: #ff6b35;

          a {
            color: #ff6b35;
          }
        }

        .ant-pagination-item:hover {
          border-color: #ff6b35;

          a {
            color: #ff6b35;
          }
        }
      }
    }
  }
}
</style>