/**
 * 库存盘点业务逻辑混入
 * 提供盘点相关的通用方法和数据处理逻辑
 */

import { getAction, postAction, putAction, deleteAction } from '@/api/manage'

export const InventoryMixin = {
  data() {
    return {
      // 盘点状态映射
      statusMap: {
        '0': { text: '草稿', color: 'orange' },
        '1': { text: '盘点中', color: 'blue' },
        '2': { text: '已完成', color: 'green' }
      },
      
      // 盘点类型选项
      checkTypeOptions: [
        { label: '全盘', value: '全盘' },
        { label: '部分盘点', value: '部分盘点' },
        { label: '循环盘点', value: '循环盘点' },
        { label: '抽盘', value: '抽盘' }
      ],
      
      // 仓库列表
      depotList: [],
      
      // 商品列表
      materialList: [],
      
      // 用户列表
      userList: []
    }
  },
  
  methods: {
    /**
     * 获取盘点状态显示信息
     * @param {string} status 状态值
     * @returns {object} 状态显示信息
     */
    getStatusInfo(status) {
      return this.statusMap[status] || { text: '未知', color: 'default' }
    },
    
    /**
     * 格式化盘点状态
     * @param {string} status 状态值
     * @returns {string} 格式化后的状态文本
     */
    formatStatus(status) {
      const info = this.getStatusInfo(status)
      return info.text
    },
    
    /**
     * 生成盘点单据编号
     * @returns {string} 单据编号
     */
    generateInventoryNumber() {
      const now = new Date()
      const year = now.getFullYear().toString()
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const day = now.getDate().toString().padStart(2, '0')
      const hour = now.getHours().toString().padStart(2, '0')
      const minute = now.getMinutes().toString().padStart(2, '0')
      const second = now.getSeconds().toString().padStart(2, '0')
      
      return `PD${year}${month}${day}${hour}${minute}${second}`
    },
    
    /**
     * 计算盈亏数量
     * @param {number} actualQuantity 实际数量
     * @param {number} bookQuantity 账面数量
     * @returns {number} 盈亏数量
     */
    calculateDifferenceQuantity(actualQuantity, bookQuantity) {
      const actual = parseFloat(actualQuantity) || 0
      const book = parseFloat(bookQuantity) || 0
      return actual - book
    },
    
    /**
     * 计算盈亏金额
     * @param {number} differenceQuantity 盈亏数量
     * @param {number} unitPrice 单价
     * @returns {number} 盈亏金额
     */
    calculateDifferenceAmount(differenceQuantity, unitPrice) {
      const quantity = parseFloat(differenceQuantity) || 0
      const price = parseFloat(unitPrice) || 0
      return quantity * price
    },
    
    /**
     * 获取差异值的CSS类名
     * @param {number} value 差异值
     * @returns {string} CSS类名
     */
    getDifferenceClass(value) {
      const num = parseFloat(value) || 0
      if (num > 0) return 'text-profit'
      if (num < 0) return 'text-loss'
      return 'text-normal'
    },
    
    /**
     * 格式化金额显示
     * @param {number} amount 金额
     * @param {number} precision 精度，默认2位小数
     * @returns {string} 格式化后的金额
     */
    formatAmount(amount, precision = 2) {
      const num = parseFloat(amount) || 0
      return num.toFixed(precision)
    },
    
    /**
     * 格式化数量显示
     * @param {number} quantity 数量
     * @param {number} precision 精度，默认2位小数
     * @returns {string} 格式化后的数量
     */
    formatQuantity(quantity, precision = 2) {
      const num = parseFloat(quantity) || 0
      return num.toFixed(precision)
    },
    
    /**
     * 加载仓库列表
     */
    async loadDepotList() {
      try {
        const res = await getAction('/depot/list')
        if (res.success) {
          this.depotList = res.result || []
        }
      } catch (error) {
        console.error('加载仓库列表失败:', error)
        this.$message.error('加载仓库列表失败')
      }
    },
    
    /**
     * 加载商品列表
     */
    async loadMaterialList() {
      try {
        const res = await getAction('/material/list')
        if (res.success) {
          this.materialList = res.result || []
        }
      } catch (error) {
        console.error('加载商品列表失败:', error)
        this.$message.error('加载商品列表失败')
      }
    },
    
    /**
     * 加载用户列表
     */
    async loadUserList() {
      try {
        const res = await getAction('/user/list')
        if (res.success) {
          this.userList = res.result || []
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$message.error('加载用户列表失败')
      }
    },
    
    /**
     * 获取商品当前库存
     * @param {number} materialId 商品ID
     * @param {number} depotId 仓库ID
     * @returns {Promise<number>} 当前库存数量
     */
    async getCurrentStock(materialId, depotId) {
      try {
        const res = await getAction('/material/getCurrentStock', {
          materialId,
          depotId
        })
        if (res.success) {
          return parseFloat(res.result) || 0
        }
        return 0
      } catch (error) {
        console.error('获取当前库存失败:', error)
        return 0
      }
    },
    
    /**
     * 验证盘点明细数据
     * @param {Array} detailData 明细数据
     * @returns {object} 验证结果
     */
    validateInventoryDetail(detailData) {
      const errors = []
      
      if (!detailData || detailData.length === 0) {
        errors.push('盘点明细不能为空')
        return { valid: false, errors }
      }
      
      detailData.forEach((item, index) => {
        if (!item.materialId) {
          errors.push(`第${index + 1}行：商品不能为空`)
        }
        
        if (item.actualQuantity === null || item.actualQuantity === undefined) {
          errors.push(`第${index + 1}行：实际数量不能为空`)
        }
        
        if (isNaN(parseFloat(item.actualQuantity))) {
          errors.push(`第${index + 1}行：实际数量格式错误`)
        }
      })
      
      return {
        valid: errors.length === 0,
        errors
      }
    },
    
    /**
     * 保存盘点单据
     * @param {object} headerData 单据头数据
     * @param {Array} detailData 明细数据
     * @returns {Promise<boolean>} 保存结果
     */
    async saveInventoryData(headerData, detailData) {
      try {
        // 验证数据
        const validation = this.validateInventoryDetail(detailData)
        if (!validation.valid) {
          this.$message.error(validation.errors[0])
          return false
        }
        
        const saveData = {
          ...headerData,
          type: '其它',
          subType: '盘点',
          items: detailData
        }
        
        const res = headerData.id 
          ? await putAction('/depotHead/update', saveData)
          : await postAction('/depotHead/add', saveData)
        
        if (res.success) {
          this.$message.success('保存成功')
          return true
        } else {
          this.$message.error(res.message || '保存失败')
          return false
        }
      } catch (error) {
        console.error('保存盘点数据失败:', error)
        this.$message.error('保存失败')
        return false
      }
    },
    
    /**
     * 删除盘点单据
     * @param {number} id 单据ID
     * @returns {Promise<boolean>} 删除结果
     */
    async deleteInventoryData(id) {
      try {
        const res = await deleteAction('/depotHead/delete', { id })
        if (res.success) {
          this.$message.success('删除成功')
          return true
        } else {
          this.$message.error(res.message || '删除失败')
          return false
        }
      } catch (error) {
        console.error('删除盘点数据失败:', error)
        this.$message.error('删除失败')
        return false
      }
    }
  }
}

export default InventoryMixin
