<template>
  <a-modal
    title="操作"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="确定"
  >
    <a-spin :spinning="confirmLoading">
      <div style="padding: 20px;">
        <div style="margin-bottom: 20px;">
          <span>模板：</span>
          <a @click="downloadTemplate" style="color: #1890ff;">Excel模板下载</a>
        </div>
        
        <div style="margin-bottom: 20px;">
          <span>仓库：</span>
          <a-select 
            v-model="selectedDepot"
            placeholder="请选择仓库"
            style="width: 200px; margin-left: 10px;">
            <a-select-option v-for="depot in depotList" :key="depot.id" :value="depot.id">
              {{ depot.depotName }}
            </a-select-option>
          </a-select>
        </div>
        
        <div style="margin-bottom: 20px;">
          <span>文件：</span>
          <a-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            accept=".xlsx,.xls"
            style="margin-left: 10px;">
            <a-button type="primary" icon="upload">导入</a-button>
          </a-upload>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'ImportStockModalSimple',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      selectedDepot: undefined,
      fileList: [],
      depotList: []
    }
  },
  created() {
    this.loadDepotList()
  },
  methods: {
    // 显示弹窗
    show() {
      this.visible = true
      this.selectedDepot = undefined
      this.fileList = []
    },
    
    // 隐藏弹窗
    hide() {
      this.visible = false
    },
    
    // 确定
    handleOk() {
      if (!this.selectedDepot) {
        this.$message.warning('请选择仓库')
        return
      }
      if (this.fileList.length === 0) {
        this.$message.warning('请选择要导入的文件')
        return
      }
      
      this.confirmLoading = true
      // TODO: 实现导入逻辑
      setTimeout(() => {
        this.confirmLoading = false
        this.visible = false
        this.$message.success('导入成功')
        this.$emit('ok', [])
      }, 1000)
    },
    
    // 取消
    handleCancel() {
      this.visible = false
    },
    
    // 下载模板
    downloadTemplate() {
      // TODO: 实现模板下载
      this.$message.info('模板下载功能开发中...')
    },
    
    // 文件上传前处理
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }
      
      this.fileList = [file]
      return false // 阻止自动上传
    },
    
    // 移除文件
    handleRemove() {
      this.fileList = []
    },
    
    // 加载仓库列表
    loadDepotList() {
      getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
// jshERP标准样式
</style>
