<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :keyboard="false"
    :forceRender="true"
    fullscreen
    switchHelp
    switchFullscreen
    @cancel="handleCancel"
    style="top:20px;height: 95%;">
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="handleOkAndAdd">保存并新增</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="handleOk">保存</a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row class="form-row" :gutter="24">
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请日期">
              <j-date v-decorator="['operTime', validatorRules.operTime]" :show-time="true"/>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请编号">
              <a-input placeholder="请输入申请编号" v-decorator.trim="[ 'number' ]" />
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="关联单据">
              <a-input-search placeholder="请选择关联单据" v-decorator="[ 'linkNumber' ]" @search="onSearchLinkNumber" :readOnly="true"/>
            </a-form-item>
          </a-col>
        </a-row>
        <j-editable-table
          :ref="refKeys[0]"
          :loading="materialTable.loading"
          :columns="materialTable.columns"
          :dataSource="materialTable.dataSource"
          :maxHeight="300"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"
          @valueChange="onValueChange"
          @added="onAdded"
          @deleted="onDeleted">
        </j-editable-table>
      </a-form>
    </a-spin>
  </j-modal>
</template>

<script>
import { BillModalMixin } from '@/views/bill/mixins/BillModalMixin'
import JModal from '@/components/jeecg/JModal'
import JEditableTable from '@/components/jeecg/JEditableTable'
import JDate from '@/components/jeecg/JDate'

export default {
  name: 'InventoryCheckModalNew',
  mixins: [BillModalMixin],
  components: {
    JModal,
    JEditableTable,
    JDate
  },
  data() {
    return {
      // 表单验证规则
      validatorRules: {
        operTime: { rules: [{ required: true, message: '请选择申请日期!' }] },
        number: { rules: [{ required: true, message: '请输入申请编号!' }] }
      },
      // 表格配置
      materialTable: {
        loading: false,
        columns: [
          {
            title: '序号',
            dataIndex: 'serialNumber',
            width: 60,
            align: 'center'
          },
          {
            title: '商品名称',
            dataIndex: 'materialName',
            width: 200,
            type: 'select',
            options: []
          },
          {
            title: '条码',
            dataIndex: 'barCode',
            width: 120
          },
          {
            title: '名称',
            dataIndex: 'name',
            width: 150
          },
          {
            title: '规格',
            dataIndex: 'materialModel',
            width: 120
          },
          {
            title: '库存存',
            dataIndex: 'stock',
            width: 100,
            align: 'right'
          },
          {
            title: '单位',
            dataIndex: 'materialUnit',
            width: 80
          },
          {
            title: '数量',
            dataIndex: 'operNumber',
            width: 100,
            align: 'right',
            type: 'inputNumber'
          },
          {
            title: '金额',
            dataIndex: 'allPrice',
            width: 120,
            align: 'right'
          },
          {
            title: '备注',
            dataIndex: 'remark',
            width: 150,
            type: 'input'
          }
        ],
        dataSource: []
      },
      refKeys: ['materialTable']
    }
  },
  methods: {
    // 新增
    add() {
      this.visible = true
      this.isUpdate = false
      this.title = '新增'
      this.generateNumber()
    },
    
    // 编辑
    edit(record) {
      this.visible = true
      this.isUpdate = true
      this.title = '编辑'
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.form.setFieldsValue(this.model)
      })
    },
    
    // 查看详情
    detail(record) {
      this.visible = true
      this.isUpdate = true
      this.title = '查看详情'
      this.model = Object.assign({}, record)
      this.$nextTick(() => {
        this.form.setFieldsValue(this.model)
      })
    },
    
    // 生成申请编号
    generateNumber() {
      const now = new Date()
      const dateStr = now.getFullYear().toString() + 
                     (now.getMonth() + 1).toString().padStart(2, '0') + 
                     now.getDate().toString().padStart(2, '0')
      const timeStr = now.getHours().toString().padStart(2, '0') + 
                     now.getMinutes().toString().padStart(2, '0') + 
                     now.getSeconds().toString().padStart(2, '0')
      const number = `FP${dateStr}${timeStr}`
      this.$nextTick(() => {
        this.form.setFieldsValue({ 
          number: number,
          operTime: now
        })
      })
    },
    
    // 搜索关联单据
    onSearchLinkNumber() {
      this.$message.info('关联单据搜索功能开发中...')
    },
    
    // 保存并新增
    handleOkAndAdd() {
      this.handleOk(true)
    },
    
    // 表格值变更
    onValueChange(event) {
      // 处理表格数据变更
    },
    
    // 添加行
    onAdded(event) {
      // 处理添加行
    },
    
    // 删除行
    onDeleted(event) {
      // 处理删除行
    }
  }
}
</script>

<style lang="less" scoped>
// jshERP标准样式
</style>
