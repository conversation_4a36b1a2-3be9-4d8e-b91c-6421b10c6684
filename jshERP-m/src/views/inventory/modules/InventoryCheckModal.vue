<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    :confirmLoading="confirmLoading"
    :keyboard="false"
    :forceRender="true"
    fullscreen
    switchHelp
    switchFullscreen
    @cancel="handleCancel"
    style="top:20px;height: 95%;">
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="handleOkAndAdd">保存并新增</a-button>
      <a-button type="primary" :loading="confirmLoading" @click="handleOk">保存</a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-row class="form-row" :gutter="24">
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请日期">
              <j-date v-decorator="['operTime', validatorRules.operTime]" :show-time="true"/>
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请编号">
              <a-input placeholder="请输入申请编号" v-decorator.trim="[ 'number' ]" />
            </a-form-item>
          </a-col>
          <a-col :lg="6" :md="12" :sm="24">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="关联单据">
              <a-input-search placeholder="请选择关联单据" v-decorator="[ 'linkNumber' ]" @search="onSearchLinkNumber" :readOnly="true"/>
            </a-form-item>
          </a-col>
        </a-row>
        <j-editable-table
          :ref="refKeys[0]"
          :loading="materialTable.loading"
          :columns="materialTable.columns"
          :dataSource="materialTable.dataSource"
          :maxHeight="300"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"
          @valueChange="onValueChange"
          @added="onAdded"
          @deleted="onDeleted">
        </j-editable-table>
      </a-form>
    </a-spin>
  </j-modal>
</template>

        <a-row :gutter="24">
          <!-- 仓库选择 -->
          <a-col :lg="12" :md="24" :sm="24">
            <a-form-item label="盘点仓库">
              <a-select
                v-decorator="['depotId', validatorRules.depotId]"
                placeholder="请选择盘点仓库"
                show-search
                option-filter-prop="children"
              >
                <a-select-option 
                  v-for="depot in depotList" 
                  :key="depot.id" 
                  :value="depot.id"
                >
                  {{ depot.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <!-- 盘点类型 -->
          <a-col :lg="12" :md="24" :sm="24">
            <a-form-item label="盘点类型">
              <a-select
                v-decorator="['checkType', validatorRules.checkType]"
                placeholder="请选择盘点类型"
              >
                <a-select-option value="全盘">全盘</a-select-option>
                <a-select-option value="部分盘点">部分盘点</a-select-option>
                <a-select-option value="循环盘点">循环盘点</a-select-option>
                <a-select-option value="抽盘">抽盘</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <!-- 操作员 -->
          <a-col :lg="12" :md="24" :sm="24">
            <a-form-item label="操作员">
              <a-select
                v-decorator="['operPersonId', validatorRules.operPersonId]"
                placeholder="请选择操作员"
                show-search
                option-filter-prop="children"
              >
                <a-select-option 
                  v-for="user in userList" 
                  :key="user.id" 
                  :value="user.id"
                >
                  {{ user.username }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <!-- 状态 -->
          <a-col :lg="12" :md="24" :sm="24">
            <a-form-item label="状态">
              <a-select
                v-decorator="['status', validatorRules.status]"
                placeholder="请选择状态"
              >
                <a-select-option value="0">草稿</a-select-option>
                <a-select-option value="1">盘点中</a-select-option>
                <a-select-option value="2">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 盘点范围 -->
        <a-form-item label="盘点范围">
          <a-radio-group v-decorator="['checkScope', { initialValue: 'all' }]">
            <a-radio value="all">全部商品</a-radio>
            <a-radio value="category">按分类</a-radio>
            <a-radio value="material">指定商品</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 商品分类选择 -->
        <a-form-item 
          label="商品分类" 
          v-show="form.getFieldValue('checkScope') === 'category'"
        >
          <a-tree-select
            v-decorator="['categoryIds']"
            :tree-data="categoryTreeData"
            placeholder="请选择商品分类"
            multiple
            tree-checkable
            show-checked-strategy="SHOW_PARENT"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 指定商品选择 -->
        <a-form-item 
          label="指定商品" 
          v-show="form.getFieldValue('checkScope') === 'material'"
        >
          <a-select
            v-decorator="['materialIds']"
            placeholder="请选择商品"
            mode="multiple"
            show-search
            option-filter-prop="children"
            style="width: 100%"
          >
            <a-select-option 
              v-for="material in materialList" 
              :key="material.id" 
              :value="material.id"
            >
              {{ material.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 备注 -->
        <a-form-item label="备注">
          <a-textarea
            v-decorator="['remark']"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, postAction, putAction } from '@/api/manage'

export default {
  name: 'InventoryCheckModal',
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      
      // 验证规则
      validatorRules: {
        number: {
          rules: [
            { required: true, message: '单据编号不能为空!' }
          ]
        },
        operTime: {
          rules: [
            { required: true, message: '请选择单据日期!' }
          ]
        },
        depotId: {
          rules: [
            { required: true, message: '请选择盘点仓库!' }
          ]
        },
        checkType: {
          rules: [
            { required: true, message: '请选择盘点类型!' }
          ]
        },
        operPersonId: {
          rules: [
            { required: true, message: '请选择操作员!' }
          ]
        },
        status: {
          rules: [
            { required: true, message: '请选择状态!' }
          ]
        }
      },
      
      // 数据列表
      depotList: [],
      userList: [],
      categoryTreeData: [],
      materialList: [],
      
      // 编辑模式
      isEdit: false,
      model: {}
    }
  },
  
  methods: {
    // 新增
    add() {
      this.isEdit = false
      this.visible = true
      this.model = {}
      
      this.$nextTick(() => {
        this.form.resetFields()
        this.generateNumber()
        this.form.setFieldsValue({
          operTime: new Date(),
          status: '0',
          checkScope: 'all'
        })
      })
      
      this.loadBasicData()
    },
    
    // 编辑
    edit(record) {
      this.isEdit = true
      this.visible = true
      this.model = Object.assign({}, record)
      
      this.$nextTick(() => {
        this.form.setFieldsValue({
          ...this.model,
          operTime: this.model.operTime ? new Date(this.model.operTime) : null
        })
      })
      
      this.loadBasicData()
    },
    
    // 生成单据编号
    generateNumber() {
      const now = new Date()
      const dateStr = now.getFullYear().toString() + 
                     (now.getMonth() + 1).toString().padStart(2, '0') + 
                     now.getDate().toString().padStart(2, '0')
      const timeStr = now.getHours().toString().padStart(2, '0') + 
                     now.getMinutes().toString().padStart(2, '0') + 
                     now.getSeconds().toString().padStart(2, '0')
      const number = `PD${dateStr}${timeStr}`
      
      this.form.setFieldsValue({ number })
    },
    
    // 加载基础数据
    loadBasicData() {
      this.loadDepotList()
      this.loadUserList()
      this.loadCategoryTree()
      this.loadMaterialList()
    },
    
    // 加载仓库列表
    loadDepotList() {
      getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      })
    },
    
    // 加载用户列表
    loadUserList() {
      getAction('/user/list').then(res => {
        if (res.success) {
          this.userList = res.result || []
        }
      })
    },
    
    // 加载商品分类树
    loadCategoryTree() {
      getAction('/materialCategory/tree').then(res => {
        if (res.success) {
          this.categoryTreeData = this.formatTreeData(res.result || [])
        }
      })
    },
    
    // 加载商品列表
    loadMaterialList() {
      getAction('/material/list').then(res => {
        if (res.success) {
          this.materialList = res.result || []
        }
      })
    },
    
    // 格式化树形数据
    formatTreeData(data) {
      return data.map(item => ({
        title: item.name,
        value: item.id,
        key: item.id,
        children: item.children ? this.formatTreeData(item.children) : []
      }))
    },
    
    // 确定
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          
          const formData = {
            ...values,
            type: '其它',
            subType: '盘点',
            operTime: values.operTime ? values.operTime.format('YYYY-MM-DD HH:mm:ss') : null
          }
          
          if (this.isEdit) {
            formData.id = this.model.id
          }
          
          const apiMethod = this.isEdit ? putAction : postAction
          const url = this.isEdit ? '/depotHead/update' : '/depotHead/add'
          
          apiMethod(url, formData).then(res => {
            if (res.success) {
              this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
              this.$emit('ok')
              this.handleCancel()
            } else {
              this.$message.error(res.message || '操作失败')
            }
          }).catch(err => {
            this.$message.error('操作失败')
            console.error(err)
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },
    
    // 取消
    handleCancel() {
      this.visible = false
      this.form.resetFields()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-form-item {
  margin-bottom: 16px;
}
</style>
