<template>
  <a-modal
    :title="title"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="确定"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <!-- 模板下载 -->
        <a-form-item label="模板">
          <a-button type="link" icon="download" @click="downloadTemplate">
            Excel模板下载
          </a-button>
          <span style="margin-left: 8px; color: #999;">
            请先下载模板，按格式填写数据后上传
          </span>
        </a-form-item>

        <!-- 仓库选择 -->
        <a-form-item label="仓库" has-feedback>
          <a-select
            v-decorator="['depotId', validatorRules.depotId]"
            placeholder="请选择仓库"
            show-search
            option-filter-prop="children"
          >
            <a-select-option 
              v-for="depot in depotList" 
              :key="depot.id" 
              :value="depot.id"
            >
              {{ depot.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 文件上传 -->
        <a-form-item label="文件" has-feedback>
          <a-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            :remove="handleRemove"
            accept=".xlsx,.xls"
          >
            <a-button icon="upload">
              选择文件
            </a-button>
          </a-upload>
          <div style="margin-top: 8px; color: #999;">
            支持格式：.xlsx、.xls，文件大小不超过10MB
          </div>
        </a-form-item>

        <!-- 导入选项 -->
        <a-form-item label="导入选项">
          <a-checkbox-group v-model="importOptions">
            <a-checkbox value="overwrite">覆盖已存在的数据</a-checkbox>
            <a-checkbox value="skipEmpty">跳过空行</a-checkbox>
            <a-checkbox value="autoCalculate">自动计算盈亏</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <!-- 预览数据 -->
        <a-form-item label="数据预览" v-if="previewData.length > 0">
          <div class="preview-container">
            <a-table
              :columns="previewColumns"
              :dataSource="previewData"
              :pagination="{ pageSize: 5, showSizeChanger: false }"
              size="small"
              bordered
            />
            <div class="preview-summary">
              <span>预览数据：{{ previewData.length }} 条</span>
              <span style="margin-left: 16px;">
                有效数据：{{ validDataCount }} 条
              </span>
              <span style="margin-left: 16px; color: #ff4d4f;" v-if="errorDataCount > 0">
                错误数据：{{ errorDataCount }} 条
              </span>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
// 注意：XLSX需要单独安装，如果没有安装请使用以下命令：
// npm install xlsx
// 如果无法安装，可以使用原生FileReader API替代

export default {
  name: 'ImportStockModal',
  data() {
    return {
      title: '操作',
      visible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      
      // 验证规则
      validatorRules: {
        depotId: {
          rules: [
            { required: true, message: '请选择仓库!' }
          ]
        }
      },
      
      // 仓库列表
      depotList: [],
      
      // 文件列表
      fileList: [],
      
      // 导入选项
      importOptions: ['skipEmpty', 'autoCalculate'],
      
      // 预览数据
      previewData: [],
      previewColumns: [
        {
          title: '商品名称',
          dataIndex: 'materialName',
          width: 150
        },
        {
          title: '规格',
          dataIndex: 'materialModel',
          width: 100
        },
        {
          title: '账面数量',
          dataIndex: 'bookQuantity',
          width: 100,
          align: 'right'
        },
        {
          title: '实际数量',
          dataIndex: 'actualQuantity',
          width: 100,
          align: 'right'
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 80,
          customRender: (text, record) => {
            if (record.hasError) {
              return <a-tag color="red">错误</a-tag>
            }
            return <a-tag color="green">正常</a-tag>
          }
        }
      ]
    }
  },
  
  computed: {
    validDataCount() {
      return this.previewData.filter(item => !item.hasError).length
    },
    
    errorDataCount() {
      return this.previewData.filter(item => item.hasError).length
    }
  },
  
  methods: {
    // 显示弹窗
    show() {
      this.visible = true
      this.loadDepotList()
      this.resetForm()
    },
    
    // 重置表单
    resetForm() {
      this.form.resetFields()
      this.fileList = []
      this.previewData = []
      this.importOptions = ['skipEmpty', 'autoCalculate']
    },
    
    // 加载仓库列表
    loadDepotList() {
      getAction('/depot/list').then(res => {
        if (res.success) {
          this.depotList = res.result || []
        }
      }).catch(err => {
        console.error('加载仓库列表失败:', err)
      })
    },
    
    // 下载模板
    downloadTemplate() {
      try {
        // 检查是否有XLSX库
        if (typeof XLSX === 'undefined') {
          // 如果没有XLSX库，提供简单的CSV下载
          this.downloadCSVTemplate()
          return
        }

        // 创建模板数据
        const templateData = [
          {
            '商品名称': '示例商品1',
            '规格': 'M',
            '账面数量': 100,
            '实际数量': 95,
            '备注': '盘点备注'
          },
          {
            '商品名称': '示例商品2',
            '规格': 'L',
            '账面数量': 50,
            '实际数量': 52,
            '备注': ''
          }
        ]

        // 创建工作簿
        const ws = XLSX.utils.json_to_sheet(templateData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, '库存盘点模板')

        // 下载文件
        XLSX.writeFile(wb, '库存盘点导入模板.xlsx')
      } catch (error) {
        console.error('下载模板失败:', error)
        this.$message.error('下载模板失败，请联系管理员')
      }
    },

    // CSV模板下载（备用方案）
    downloadCSVTemplate() {
      const csvContent = '商品名称,规格,账面数量,实际数量,备注\n示例商品1,M,100,95,盘点备注\n示例商品2,L,50,52,'
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', '库存盘点导入模板.csv')
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    
    // 文件上传前处理
    beforeUpload(file) {
      // 检查文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      
      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }
      
      // 读取文件内容
      this.readExcelFile(file)
      
      // 更新文件列表
      this.fileList = [file]
      
      return false // 阻止自动上传
    },
    
    // 移除文件
    handleRemove() {
      this.fileList = []
      this.previewData = []
    },
    
    // 读取Excel文件
    readExcelFile(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          // 检查文件类型
          if (file.name.toLowerCase().endsWith('.csv')) {
            // 处理CSV文件
            const text = e.target.result
            this.parseCSVData(text)
          } else {
            // 处理Excel文件
            if (typeof XLSX === 'undefined') {
              this.$message.error('系统不支持Excel文件，请使用CSV格式')
              return
            }

            const data = new Uint8Array(e.target.result)
            const workbook = XLSX.read(data, { type: 'array' })
            const sheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[sheetName]
            const jsonData = XLSX.utils.sheet_to_json(worksheet)

            this.parseExcelData(jsonData)
          }
        } catch (error) {
          this.$message.error('文件解析失败，请检查文件格式')
          console.error('文件解析错误:', error)
        }
      }

      // 根据文件类型选择读取方式
      if (file.name.toLowerCase().endsWith('.csv')) {
        reader.readAsText(file, 'UTF-8')
      } else {
        reader.readAsArrayBuffer(file)
      }
    },

    // 解析CSV数据
    parseCSVData(csvText) {
      try {
        const lines = csvText.split('\n')
        const headers = lines[0].split(',').map(h => h.trim())
        const jsonData = []

        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const values = lines[i].split(',').map(v => v.trim())
            const row = {}
            headers.forEach((header, index) => {
              row[header] = values[index] || ''
            })
            jsonData.push(row)
          }
        }

        this.parseExcelData(jsonData)
      } catch (error) {
        this.$message.error('CSV文件解析失败')
        console.error('CSV解析错误:', error)
      }
    },
    
    // 解析Excel数据
    parseExcelData(jsonData) {
      this.previewData = jsonData.map((row, index) => {
        const item = {
          key: index,
          materialName: row['商品名称'] || '',
          materialModel: row['规格'] || '',
          bookQuantity: parseFloat(row['账面数量']) || 0,
          actualQuantity: parseFloat(row['实际数量']) || 0,
          remark: row['备注'] || '',
          hasError: false,
          errorMessage: ''
        }
        
        // 数据验证
        if (!item.materialName) {
          item.hasError = true
          item.errorMessage = '商品名称不能为空'
        }
        
        if (isNaN(item.actualQuantity)) {
          item.hasError = true
          item.errorMessage = '实际数量格式错误'
        }
        
        return item
      })
    },
    
    // 确定
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.fileList.length === 0) {
            this.$message.error('请选择要导入的文件')
            return
          }
          
          if (this.validDataCount === 0) {
            this.$message.error('没有有效的数据可以导入')
            return
          }
          
          this.confirmLoading = true
          
          // 模拟导入处理
          setTimeout(() => {
            const validData = this.previewData.filter(item => !item.hasError)
            this.$emit('ok', validData)
            this.confirmLoading = false
            this.visible = false
            this.$message.success(`成功导入 ${validData.length} 条数据`)
          }, 1500)
        }
      })
    },
    
    // 取消
    handleCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
.preview-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
  
  .preview-summary {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8e8e8;
    font-size: 12px;
    color: #666;
  }
}

/deep/ .ant-upload-list {
  margin-top: 8px;
}
</style>
