<template>
  <a-row :gutter="24">
    <a-col :span="24">
      <!-- 单据头信息 -->
      <a-card title="盘点单信息" class="inventory-header-card">
        <a-form layout="inline">
          <a-form-item label="单据日期">
            <a-date-picker 
              v-model="headerForm.operTime" 
              :disabled="isViewMode"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="单据编号">
            <a-input 
              v-model="headerForm.number" 
              disabled
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="状态">
            <a-tag v-if="headerForm.status == '0'" color="orange">草稿</a-tag>
            <a-tag v-if="headerForm.status == '1'" color="blue">盘点中</a-tag>
            <a-tag v-if="headerForm.status == '2'" color="green">已完成</a-tag>
          </a-form-item>
          <a-form-item>
            <a-button 
              type="primary" 
              icon="import" 
              @click="handleImport"
              :disabled="isViewMode || headerForm.status == '2'"
            >
              导入库存盘点数据
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 盘点明细 -->
      <a-card title="盘点明细" class="inventory-detail-card">
        <div class="table-operator">
          <a-button 
            type="primary" 
            icon="plus" 
            @click="handleAddRow"
            :disabled="isViewMode || headerForm.status == '2'"
          >
            添加行
          </a-button>
          <a-button 
            icon="save" 
            @click="handleSave"
            :disabled="isViewMode || headerForm.status == '2'"
            :loading="saveLoading"
          >
            保存
          </a-button>
          <a-button 
            type="primary" 
            icon="check" 
            @click="handleSubmit"
            :disabled="isViewMode || headerForm.status == '2'"
            :loading="submitLoading"
          >
            提交
          </a-button>
        </div>

        <a-table
          :columns="detailColumns"
          :dataSource="detailDataSource"
          :pagination="false"
          bordered
          size="middle"
          rowKey="id"
          :scroll="{ x: 1200 }"
        >
          <!-- 序号列 -->
          <template slot="serialNumber" slot-scope="text, record, index">
            {{ index + 1 }}
          </template>

          <!-- 商品名称列 -->
          <template slot="materialName" slot-scope="text, record">
            <a-select
              v-model="record.materialId"
              placeholder="请选择商品"
              show-search
              option-filter-prop="children"
              style="width: 100%"
              :disabled="isViewMode || headerForm.status == '2'"
              @change="onMaterialChange(record)"
            >
              <a-select-option 
                v-for="item in materialList" 
                :key="item.id" 
                :value="item.id"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </template>

          <!-- 规格列 -->
          <template slot="materialModel" slot-scope="text, record">
            {{ record.materialModel || '-' }}
          </template>

          <!-- 账面数量列 -->
          <template slot="bookQuantity" slot-scope="text, record">
            <span>{{ record.bookQuantity || 0 }}</span>
          </template>

          <!-- 实际数量列 -->
          <template slot="actualQuantity" slot-scope="text, record">
            <a-input-number
              v-model="record.actualQuantity"
              :min="0"
              :precision="2"
              style="width: 100%"
              :disabled="isViewMode || headerForm.status == '2'"
              @change="onQuantityChange(record)"
            />
          </template>

          <!-- 盈亏数量列 -->
          <template slot="differenceQuantity" slot-scope="text, record">
            <span :class="getDifferenceClass(record.differenceQuantity)">
              {{ record.differenceQuantity || 0 }}
            </span>
          </template>

          <!-- 单价列 -->
          <template slot="unitPrice" slot-scope="text, record">
            {{ record.unitPrice || 0 }}
          </template>

          <!-- 盈亏金额列 -->
          <template slot="differenceAmount" slot-scope="text, record">
            <span :class="getDifferenceClass(record.differenceAmount)">
              {{ record.differenceAmount || 0 }}
            </span>
          </template>

          <!-- 备注列 -->
          <template slot="remark" slot-scope="text, record">
            <a-input
              v-model="record.remark"
              placeholder="请输入备注"
              :disabled="isViewMode || headerForm.status == '2'"
            />
          </template>

          <!-- 操作列 -->
          <template slot="action" slot-scope="text, record, index">
            <a-button 
              type="danger" 
              size="small" 
              icon="delete"
              @click="handleDeleteRow(index)"
              :disabled="isViewMode || headerForm.status == '2'"
            >
              删除
            </a-button>
          </template>
        </a-table>

        <!-- 合计信息 -->
        <div class="summary-info">
          <a-row>
            <a-col :span="12">
              <span>合计行数：{{ detailDataSource.length }}</span>
            </a-col>
            <a-col :span="12" style="text-align: right">
              <span>盈亏金额合计：</span>
              <span :class="getDifferenceClass(totalDifferenceAmount)">
                {{ totalDifferenceAmount.toFixed(2) }}
              </span>
            </a-col>
          </a-row>
        </div>
      </a-card>

      <!-- 导入弹窗 -->
      <import-stock-modal ref="importModal" @ok="onImportSuccess" />
    </a-col>
  </a-row>
</template>

<script>
import ImportStockModal from './modules/ImportStockModal'
import { getAction, postAction, putAction } from '@/api/manage'

export default {
  name: 'InventoryInputList',
  components: {
    ImportStockModal
  },
  data() {
    return {
      // 单据头信息
      headerForm: {
        id: null,
        number: '',
        operTime: null,
        status: '0',
        type: '其它',
        subType: '盘点'
      },
      // 明细数据
      detailDataSource: [],
      // 商品列表
      materialList: [],
      // 加载状态
      saveLoading: false,
      submitLoading: false,
      // 是否查看模式
      isViewMode: false,
      // 表格列配置
      detailColumns: [
        {
          title: '序号',
          dataIndex: 'serialNumber',
          width: 60,
          align: 'center',
          scopedSlots: { customRender: 'serialNumber' }
        },
        {
          title: '商品名称',
          dataIndex: 'materialName',
          width: 200,
          scopedSlots: { customRender: 'materialName' }
        },
        {
          title: '规格',
          dataIndex: 'materialModel',
          width: 120,
          scopedSlots: { customRender: 'materialModel' }
        },
        {
          title: '账面数量',
          dataIndex: 'bookQuantity',
          width: 100,
          align: 'right',
          scopedSlots: { customRender: 'bookQuantity' }
        },
        {
          title: '实际数量',
          dataIndex: 'actualQuantity',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'actualQuantity' }
        },
        {
          title: '盈亏数量',
          dataIndex: 'differenceQuantity',
          width: 100,
          align: 'right',
          scopedSlots: { customRender: 'differenceQuantity' }
        },
        {
          title: '单价',
          dataIndex: 'unitPrice',
          width: 100,
          align: 'right',
          scopedSlots: { customRender: 'unitPrice' }
        },
        {
          title: '盈亏金额',
          dataIndex: 'differenceAmount',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'differenceAmount' }
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 150,
          scopedSlots: { customRender: 'remark' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  computed: {
    // 计算盈亏金额合计
    totalDifferenceAmount() {
      return this.detailDataSource.reduce((total, item) => {
        return total + (parseFloat(item.differenceAmount) || 0)
      }, 0)
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      const { id, mode } = this.$route.query
      this.isViewMode = mode === 'view'
      
      if (id) {
        this.loadHeaderData(id)
        this.loadDetailData(id)
      } else {
        this.generateNumber()
      }
      
      this.loadMaterialList()
    },
    
    // 生成单据编号
    generateNumber() {
      const now = new Date()
      const dateStr = now.getFullYear().toString() + 
                     (now.getMonth() + 1).toString().padStart(2, '0') + 
                     now.getDate().toString().padStart(2, '0')
      const timeStr = now.getHours().toString().padStart(2, '0') + 
                     now.getMinutes().toString().padStart(2, '0') + 
                     now.getSeconds().toString().padStart(2, '0')
      this.headerForm.number = `PD${dateStr}${timeStr}`
      this.headerForm.operTime = now
    },
    
    // 加载商品列表
    loadMaterialList() {
      getAction('/material/list').then(res => {
        if (res.success) {
          this.materialList = res.result || []
        }
      })
    },
    
    // 商品变更事件
    onMaterialChange(record) {
      const material = this.materialList.find(item => item.id === record.materialId)
      if (material) {
        record.materialName = material.name
        record.materialModel = material.model
        record.unitPrice = material.purchaseDecimal || 0
        // TODO: 获取账面库存数量
        record.bookQuantity = 0
        this.onQuantityChange(record)
      }
    },
    
    // 数量变更事件
    onQuantityChange(record) {
      const actualQuantity = parseFloat(record.actualQuantity) || 0
      const bookQuantity = parseFloat(record.bookQuantity) || 0
      const unitPrice = parseFloat(record.unitPrice) || 0
      
      record.differenceQuantity = actualQuantity - bookQuantity
      record.differenceAmount = record.differenceQuantity * unitPrice
    },
    
    // 获取差异样式类
    getDifferenceClass(value) {
      const num = parseFloat(value) || 0
      if (num > 0) return 'text-success'
      if (num < 0) return 'text-danger'
      return ''
    },
    
    // 添加行
    handleAddRow() {
      this.detailDataSource.push({
        id: Date.now(),
        materialId: null,
        materialName: '',
        materialModel: '',
        bookQuantity: 0,
        actualQuantity: 0,
        differenceQuantity: 0,
        unitPrice: 0,
        differenceAmount: 0,
        remark: ''
      })
    },
    
    // 删除行
    handleDeleteRow(index) {
      this.detailDataSource.splice(index, 1)
    },
    
    // 导入
    handleImport() {
      this.$refs.importModal.show()
    },
    
    // 导入成功回调
    onImportSuccess(data) {
      this.detailDataSource = data
      this.$message.success('导入成功')
    },
    
    // 保存
    handleSave() {
      this.saveLoading = true
      // TODO: 实现保存逻辑
      setTimeout(() => {
        this.saveLoading = false
        this.$message.success('保存成功')
      }, 1000)
    },
    
    // 提交
    handleSubmit() {
      this.submitLoading = true
      // TODO: 实现提交逻辑
      setTimeout(() => {
        this.submitLoading = false
        this.headerForm.status = '1'
        this.$message.success('提交成功')
      }, 1000)
    }
  }
}
</script>

<style lang="less" scoped>
.inventory-header-card,
.inventory-detail-card {
  margin-bottom: 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-operator {
  margin-bottom: 16px;
  
  .ant-btn {
    margin-right: 8px;
    border-radius: 4px;
  }
}

.summary-info {
  margin-top: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  font-weight: 500;
}

.text-success {
  color: #52c41a;
}

.text-danger {
  color: #ff4d4f;
}
</style>
