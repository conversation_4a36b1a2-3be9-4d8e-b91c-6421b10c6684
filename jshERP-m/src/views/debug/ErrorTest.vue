<template>
  <div class="error-test">
    <div class="test-header">
      <h2>错误测试</h2>
      <div class="test-controls">
        <a-button type="primary" @click="runAllErrorTests" :loading="testing">
          <a-icon type="play-circle" />
          运行所有测试
        </a-button>
        <a-button @click="clearErrorLogs">
          <a-icon type="clear" />
          清除错误日志
        </a-button>
        <a-button @click="exportErrorReport">
          <a-icon type="download" />
          导出错误报告
        </a-button>
      </div>
    </div>

    <div class="test-content">
      <!-- 错误处理系统状态 -->
      <a-card title="错误处理系统状态" class="test-card">
        <div class="error-system-status">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总错误数" :value="errorStats.total" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="今日错误" :value="errorStats.today" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="严重错误" :value="errorStats.critical" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="处理成功率" :value="errorStats.handledRate" suffix="%" />
            </a-col>
          </a-row>
          
          <div class="error-level-distribution">
            <h4>错误级别分布</h4>
            <div class="level-bars">
              <div 
                v-for="(level, key) in errorStats.byLevel" 
                :key="key"
                class="level-bar"
              >
                <span class="level-name">{{ getLevelName(key) }}</span>
                <div class="level-progress">
                  <a-progress 
                    :percent="getPercentage(level, errorStats.total)" 
                    :stroke-color="getLevelColor(key)"
                    :show-info="false"
                  />
                </div>
                <span class="level-count">{{ level }}</span>
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 错误触发测试 -->
      <a-card title="错误触发测试" class="test-card">
        <div class="error-test-section">
          <div class="error-trigger-list">
            <div 
              v-for="(errorTest, index) in errorTriggerTests" 
              :key="index"
              class="error-trigger-item"
              :class="{ testing: errorTest.testing }"
            >
              <div class="trigger-header">
                <div class="trigger-info">
                  <span class="trigger-name">{{ errorTest.name }}</span>
                  <a-tag :color="getLevelColor(errorTest.level)">{{ getLevelName(errorTest.level) }}</a-tag>
                </div>
                <div class="trigger-actions">
                  <a-button 
                    size="small" 
                    @click="triggerError(errorTest)" 
                    :loading="errorTest.testing"
                    :type="errorTest.safe ? 'default' : 'danger'"
                  >
                    {{ errorTest.testing ? '测试中' : '触发错误' }}
                  </a-button>
                </div>
              </div>
              
              <div class="trigger-description">{{ errorTest.description }}</div>
              
              <div class="trigger-result" v-if="errorTest.result">
                <a-alert 
                  :type="errorTest.result.success ? 'success' : 'error'"
                  :message="errorTest.result.message"
                  :description="errorTest.result.details"
                  show-icon
                />
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 错误恢复测试 -->
      <a-card title="错误恢复测试" class="test-card">
        <div class="error-test-section">
          <div class="recovery-test-controls">
            <a-button @click="testErrorRecovery" :loading="recoveryTesting">
              测试错误恢复
            </a-button>
            <a-button @click="testErrorBoundary" :loading="boundaryTesting">
              测试错误边界
            </a-button>
          </div>
          
          <div class="recovery-result" v-if="recoveryResult">
            <a-alert 
              :type="recoveryResult.success ? 'success' : 'error'"
              :message="recoveryResult.message"
              :description="recoveryResult.details"
              show-icon
            />
          </div>
          
          <!-- 错误恢复演示组件 -->
          <div class="recovery-demo">
            <h4>错误恢复演示</h4>
            <error-boundary @error="handleBoundaryError">
              <error-prone-component 
                ref="errorProneComponent"
                :should-error="shouldTriggerComponentError"
                @recovered="handleComponentRecovery"
              />
            </error-boundary>
          </div>
        </div>
      </a-card>

      <!-- 错误日志查看 -->
      <a-card title="错误日志" class="test-card">
        <div class="error-log-section">
          <div class="log-controls">
            <a-select v-model="logFilter.level" style="width: 120px" @change="filterLogs">
              <a-select-option value="">所有级别</a-select-option>
              <a-select-option value="low">低级</a-select-option>
              <a-select-option value="medium">中级</a-select-option>
              <a-select-option value="high">高级</a-select-option>
              <a-select-option value="critical">严重</a-select-option>
            </a-select>
            
            <a-select v-model="logFilter.type" style="width: 150px" @change="filterLogs">
              <a-select-option value="">所有类型</a-select-option>
              <a-select-option value="js_error">JavaScript错误</a-select-option>
              <a-select-option value="promise_rejection">Promise拒绝</a-select-option>
              <a-select-option value="network_error">网络错误</a-select-option>
              <a-select-option value="component_error">组件错误</a-select-option>
              <a-select-option value="custom_error">自定义错误</a-select-option>
            </a-select>
            
            <a-range-picker 
              v-model="logFilter.dateRange" 
              @change="filterLogs"
              style="width: 250px"
            />
            
            <a-button @click="refreshErrorLogs">
              <a-icon type="reload" />
              刷新
            </a-button>
          </div>
          
          <div class="error-log-list">
            <div 
              v-for="(error, index) in filteredErrorLogs" 
              :key="error.id || index"
              class="error-log-item"
              :class="error.level"
            >
              <div class="log-header">
                <div class="log-info">
                  <span class="log-time">{{ formatTime(error.timestamp) }}</span>
                  <a-tag :color="getLevelColor(error.level)">{{ getLevelName(error.level) }}</a-tag>
                  <a-tag>{{ getTypeName(error.type) }}</a-tag>
                </div>
                <div class="log-actions">
                  <a-button size="small" @click="viewErrorDetails(error)">详情</a-button>
                </div>
              </div>
              
              <div class="log-message">{{ error.message }}</div>
              
              <div class="log-details" v-if="error.showDetails">
                <div class="detail-item" v-if="error.filename">
                  <strong>文件:</strong> {{ error.filename }}:{{ error.lineno }}:{{ error.colno }}
                </div>
                <div class="detail-item" v-if="error.url">
                  <strong>URL:</strong> {{ error.url }}
                </div>
                <div class="detail-item" v-if="error.userAgent">
                  <strong>浏览器:</strong> {{ error.userAgent }}
                </div>
                <div class="detail-item" v-if="error.stack">
                  <strong>堆栈:</strong>
                  <pre class="stack-trace">{{ error.stack }}</pre>
                </div>
              </div>
            </div>
            
            <a-empty v-if="filteredErrorLogs.length === 0" description="暂无错误日志" />
          </div>
          
          <div class="log-pagination" v-if="filteredErrorLogs.length > 0">
            <a-pagination
              v-model="logPagination.current"
              :total="filteredErrorLogs.length"
              :page-size="logPagination.pageSize"
              :show-size-changer="false"
              size="small"
            />
          </div>
        </div>
      </a-card>
    </div>

    <!-- 测试结果汇总 -->
    <a-card title="错误测试结果汇总" v-if="hasTestResults" class="test-summary">
      <div class="summary-stats">
        <a-statistic title="总测试数" :value="totalTests" />
        <a-statistic title="通过测试" :value="passedTests" />
        <a-statistic title="失败测试" :value="failedTests" />
        <a-statistic title="错误处理率" :value="errorHandlingRate" suffix="%" />
      </div>
      
      <div class="summary-details">
        <a-table 
          :columns="summaryColumns" 
          :dataSource="testSummary" 
          :pagination="false"
          size="small"
        />
      </div>
    </a-card>

    <!-- 错误详情弹窗 -->
    <a-modal
      title="错误详情"
      :visible="errorDetailVisible"
      @ok="errorDetailVisible = false"
      @cancel="errorDetailVisible = false"
      width="800px"
    >
      <div class="error-detail-content" v-if="selectedError">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="错误ID">{{ selectedError.id }}</a-descriptions-item>
          <a-descriptions-item label="错误类型">{{ getTypeName(selectedError.type) }}</a-descriptions-item>
          <a-descriptions-item label="错误级别">
            <a-tag :color="getLevelColor(selectedError.level)">{{ getLevelName(selectedError.level) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发生时间">{{ formatTime(selectedError.timestamp) }}</a-descriptions-item>
          <a-descriptions-item label="错误消息" :span="2">{{ selectedError.message }}</a-descriptions-item>
          <a-descriptions-item label="文件位置" :span="2" v-if="selectedError.filename">
            {{ selectedError.filename }}:{{ selectedError.lineno }}:{{ selectedError.colno }}
          </a-descriptions-item>
          <a-descriptions-item label="页面URL" :span="2" v-if="selectedError.url">
            {{ selectedError.url }}
          </a-descriptions-item>
          <a-descriptions-item label="用户代理" :span="2" v-if="selectedError.userAgent">
            {{ selectedError.userAgent }}
          </a-descriptions-item>
        </a-descriptions>
        
        <div class="stack-trace-section" v-if="selectedError.stack">
          <h4>堆栈跟踪</h4>
          <pre class="stack-trace">{{ selectedError.stack }}</pre>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import errorHandler from '@/utils/errorHandler'
import moment from 'moment'

// 错误边界组件
const ErrorBoundary = {
  name: 'ErrorBoundary',
  data() {
    return {
      hasError: false,
      error: null
    }
  },
  errorCaptured(err, instance, info) {
    this.hasError = true
    this.error = err
    this.$emit('error', { error: err, instance, info })
    return false // 阻止错误继续传播
  },
  render(h) {
    if (this.hasError) {
      return h('div', {
        class: 'error-boundary-fallback'
      }, [
        h('h3', '组件发生错误'),
        h('p', this.error ? this.error.message : '未知错误'),
        h('a-button', {
          props: { type: 'primary' },
          on: {
            click: () => {
              this.hasError = false
              this.error = null
            }
          }
        }, '重试')
      ])
    }
    return this.$slots.default
  }
}

// 容易出错的演示组件
const ErrorProneComponent = {
  name: 'ErrorProneComponent',
  props: {
    shouldError: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      counter: 0,
      hasRecovered: false
    }
  },
  watch: {
    shouldError(newVal) {
      if (newVal && !this.hasRecovered) {
        this.triggerError()
      }
    }
  },
  methods: {
    triggerError() {
      // 模拟组件错误
      throw new Error('演示组件错误: 这是一个故意触发的错误')
    },

    recover() {
      this.hasRecovered = true
      this.counter = 0
      this.$emit('recovered')
    }
  },
  render(h) {
    return h('div', {
      class: 'error-prone-component'
    }, [
      h('h4', '错误演示组件'),
      h('p', `计数器: ${this.counter}`),
      h('a-button', {
        on: { click: () => this.counter++ }
      }, '增加计数'),
      h('a-button', {
        props: { type: 'danger' },
        on: { click: this.triggerError }
      }, '触发错误'),
      h('a-button', {
        props: { type: 'primary' },
        on: { click: this.recover }
      }, '恢复组件')
    ])
  }
}

export default {
  name: 'ErrorTest',

  components: {
    ErrorBoundary,
    ErrorProneComponent
  },

  data() {
    return {
      testing: false,

      // 错误统计
      errorStats: {
        total: 0,
        today: 0,
        critical: 0,
        handledRate: 0,
        byLevel: {
          low: 0,
          medium: 0,
          high: 0,
          critical: 0
        }
      },

      // 错误触发测试
      errorTriggerTests: [
        {
          name: 'JavaScript运行时错误',
          level: 'high',
          safe: true,
          description: '触发一个安全的JavaScript运行时错误',
          testing: false,
          result: null,
          trigger: () => {
            // 安全的错误触发
            try {
              const obj = null
              obj.nonExistentMethod()
            } catch (error) {
              errorHandler.handleError(error, 'js_error', 'high')
              throw error
            }
          }
        },
        {
          name: 'Promise拒绝错误',
          level: 'medium',
          safe: true,
          description: '触发一个Promise拒绝错误',
          testing: false,
          result: null,
          trigger: () => {
            return new Promise((resolve, reject) => {
              setTimeout(() => {
                reject(new Error('测试Promise拒绝错误'))
              }, 100)
            })
          }
        },
        {
          name: '网络请求错误',
          level: 'medium',
          safe: true,
          description: '模拟网络请求失败',
          testing: false,
          result: null,
          trigger: async () => {
            try {
              const response = await fetch('/api/nonexistent-endpoint')
              if (!response.ok) {
                throw new Error(`网络请求失败: ${response.status}`)
              }
            } catch (error) {
              errorHandler.handleError(error, 'network_error', 'medium')
              throw error
            }
          }
        },
        {
          name: '组件渲染错误',
          level: 'high',
          safe: true,
          description: '触发Vue组件渲染错误',
          testing: false,
          result: null,
          trigger: () => {
            this.shouldTriggerComponentError = true
            setTimeout(() => {
              this.shouldTriggerComponentError = false
            }, 1000)
          }
        },
        {
          name: '自定义业务错误',
          level: 'low',
          safe: true,
          description: '触发自定义业务逻辑错误',
          testing: false,
          result: null,
          trigger: () => {
            const error = new Error('业务逻辑验证失败: 用户权限不足')
            errorHandler.handleError(error, 'custom_error', 'low')
            throw error
          }
        },
        {
          name: '资源加载错误',
          level: 'medium',
          safe: true,
          description: '模拟资源加载失败',
          testing: false,
          result: null,
          trigger: () => {
            const img = new Image()
            img.onerror = (error) => {
              errorHandler.handleError(new Error('图片加载失败'), 'resource_error', 'medium')
            }
            img.src = '/nonexistent-image.jpg'
          }
        }
      ],

      // 错误恢复测试
      recoveryTesting: false,
      boundaryTesting: false,
      recoveryResult: null,
      shouldTriggerComponentError: false,

      // 错误日志
      errorLogs: [],
      filteredErrorLogs: [],
      logFilter: {
        level: '',
        type: '',
        dateRange: []
      },
      logPagination: {
        current: 1,
        pageSize: 10
      },

      // 错误详情
      errorDetailVisible: false,
      selectedError: null,

      // 测试汇总
      testSummary: [],
      summaryColumns: [
        { title: '测试项目', dataIndex: 'name', key: 'name' },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '错误类型', dataIndex: 'errorType', key: 'errorType' },
        { title: '处理结果', dataIndex: 'handled', key: 'handled' }
      ]
    }
  },

  computed: {
    hasTestResults() {
      return this.testSummary.length > 0
    },

    totalTests() {
      return this.testSummary.length
    },

    passedTests() {
      return this.testSummary.filter(test => test.status === '通过').length
    },

    failedTests() {
      return this.testSummary.filter(test => test.status === '失败').length
    },

    errorHandlingRate() {
      if (this.totalTests === 0) return 0
      const handledTests = this.testSummary.filter(test => test.handled === '是').length
      return Math.round((handledTests / this.totalTests) * 100)
    }
  },

  mounted() {
    this.initErrorTest()
    this.refreshErrorStats()
    this.refreshErrorLogs()
  },

  methods: {
    // 初始化错误测试
    initErrorTest() {
      // 监听全局错误
      window.addEventListener('error', this.handleGlobalError)
      window.addEventListener('unhandledrejection', this.handleUnhandledRejection)

      console.log('错误测试模块已初始化')
    },

    // 运行所有错误测试
    async runAllErrorTests() {
      this.testing = true
      this.testSummary = []

      for (const errorTest of this.errorTriggerTests) {
        const startTime = performance.now()

        try {
          await this.triggerError(errorTest)
          const endTime = performance.now()

          this.testSummary.push({
            name: errorTest.name,
            status: '通过',
            errorType: this.getLevelName(errorTest.level),
            handled: errorTest.result && errorTest.result.success ? '是' : '否',
            duration: `${Math.round(endTime - startTime)}ms`
          })
        } catch (error) {
          const endTime = performance.now()

          this.testSummary.push({
            name: errorTest.name,
            status: '失败',
            errorType: this.getLevelName(errorTest.level),
            handled: '否',
            duration: `${Math.round(endTime - startTime)}ms`
          })
        }

        await this.delay(500)
      }

      this.testing = false
      this.refreshErrorStats()
      this.$message.success('所有错误测试完成')
    },

    // 触发特定错误
    async triggerError(errorTest) {
      errorTest.testing = true

      try {
        await errorTest.trigger()

        errorTest.result = {
          success: true,
          message: '错误触发成功',
          details: '错误已被正确捕获和处理'
        }

      } catch (error) {
        // 这里的catch是预期的，因为我们故意触发错误来测试错误处理
        errorTest.result = {
          success: true,
          message: '错误触发和处理成功',
          details: `错误类型: ${error.name}, 错误消息: ${error.message}`
        }
      } finally {
        errorTest.testing = false
      }
    },

    // 测试错误恢复
    async testErrorRecovery() {
      this.recoveryTesting = true

      try {
        // 模拟一个可恢复的错误场景
        const originalConsoleError = console.error
        let errorCaught = false

        // 临时替换console.error来捕获错误
        console.error = (...args) => {
          errorCaught = true
          originalConsoleError.apply(console, args)
        }

        // 触发一个错误
        try {
          throw new Error('测试错误恢复机制')
        } catch (error) {
          errorHandler.handleError(error, 'custom_error', 'medium')
        }

        // 恢复console.error
        console.error = originalConsoleError

        // 验证错误是否被正确处理
        await this.delay(100)

        this.recoveryResult = {
          success: errorCaught,
          message: errorCaught ? '错误恢复测试通过' : '错误恢复测试失败',
          details: errorCaught ? '错误被正确捕获并处理，系统成功恢复' : '错误未被正确处理'
        }

      } catch (error) {
        this.recoveryResult = {
          success: false,
          message: '错误恢复测试失败',
          details: error.message
        }
      } finally {
        this.recoveryTesting = false
      }
    },

    // 测试错误边界
    async testErrorBoundary() {
      this.boundaryTesting = true

      try {
        // 触发组件错误来测试错误边界
        this.shouldTriggerComponentError = true

        await this.delay(1000)

        this.recoveryResult = {
          success: true,
          message: '错误边界测试完成',
          details: '请查看下方的错误恢复演示组件'
        }

      } catch (error) {
        this.recoveryResult = {
          success: false,
          message: '错误边界测试失败',
          details: error.message
        }
      } finally {
        this.boundaryTesting = false
        this.shouldTriggerComponentError = false
      }
    },

    // 处理边界错误
    handleBoundaryError(errorInfo) {
      console.log('错误边界捕获到错误:', errorInfo)
      errorHandler.handleError(errorInfo.error, 'component_error', 'high')
    },

    // 处理组件恢复
    handleComponentRecovery() {
      console.log('组件已恢复')
      this.$message.success('组件错误已恢复')
    },

    // 处理全局错误
    handleGlobalError(event) {
      const error = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        timestamp: Date.now(),
        type: 'js_error',
        level: 'high'
      }

      this.addErrorLog(error)
    },

    // 处理未处理的Promise拒绝
    handleUnhandledRejection(event) {
      const error = {
        message: event.reason ? event.reason.message || event.reason : '未知Promise拒绝',
        timestamp: Date.now(),
        type: 'promise_rejection',
        level: 'medium',
        stack: event.reason ? event.reason.stack : null
      }

      this.addErrorLog(error)
    },

    // 添加错误日志
    addErrorLog(error) {
      error.id = Date.now() + Math.random()
      this.errorLogs.unshift(error)

      // 限制日志数量
      if (this.errorLogs.length > 1000) {
        this.errorLogs = this.errorLogs.slice(0, 1000)
      }

      this.filterLogs()
      this.refreshErrorStats()
    },

    // 刷新错误统计
    refreshErrorStats() {
      const logs = errorHandler.getErrors ? errorHandler.getErrors() : this.errorLogs
      const today = moment().startOf('day')

      this.errorStats = {
        total: logs.length,
        today: logs.filter(log => moment(log.timestamp).isAfter(today)).length,
        critical: logs.filter(log => log.level === 'critical').length,
        handledRate: logs.length > 0 ? Math.round((logs.filter(log => log.handled !== false).length / logs.length) * 100) : 100,
        byLevel: {
          low: logs.filter(log => log.level === 'low').length,
          medium: logs.filter(log => log.level === 'medium').length,
          high: logs.filter(log => log.level === 'high').length,
          critical: logs.filter(log => log.level === 'critical').length
        }
      }
    },

    // 刷新错误日志
    refreshErrorLogs() {
      if (errorHandler.getErrors) {
        this.errorLogs = errorHandler.getErrors()
      }
      this.filterLogs()
      this.refreshErrorStats()
    },

    // 过滤错误日志
    filterLogs() {
      let filtered = [...this.errorLogs]

      // 按级别过滤
      if (this.logFilter.level) {
        filtered = filtered.filter(log => log.level === this.logFilter.level)
      }

      // 按类型过滤
      if (this.logFilter.type) {
        filtered = filtered.filter(log => log.type === this.logFilter.type)
      }

      // 按日期范围过滤
      if (this.logFilter.dateRange && this.logFilter.dateRange.length === 2) {
        const [startDate, endDate] = this.logFilter.dateRange
        filtered = filtered.filter(log => {
          const logDate = moment(log.timestamp)
          return logDate.isBetween(startDate, endDate, 'day', '[]')
        })
      }

      this.filteredErrorLogs = filtered
    },

    // 查看错误详情
    viewErrorDetails(error) {
      this.selectedError = { ...error, showDetails: true }
      this.errorDetailVisible = true
    },

    // 清除错误日志
    clearErrorLogs() {
      this.errorLogs = []
      this.filteredErrorLogs = []

      if (errorHandler.clearErrors) {
        errorHandler.clearErrors()
      }

      this.refreshErrorStats()
      this.$message.success('错误日志已清除')
    },

    // 导出错误报告
    exportErrorReport() {
      const report = {
        exportTime: new Date().toISOString(),
        stats: this.errorStats,
        logs: this.filteredErrorLogs.map(log => ({
          id: log.id,
          timestamp: log.timestamp,
          level: log.level,
          type: log.type,
          message: log.message,
          filename: log.filename,
          lineno: log.lineno,
          colno: log.colno,
          url: log.url,
          userAgent: log.userAgent,
          stack: log.stack
        }))
      }

      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `error-report-${moment().format('YYYY-MM-DD-HH-mm-ss')}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.$message.success('错误报告已导出')
    },

    // 获取级别名称
    getLevelName(level) {
      const levelNames = {
        low: '低级',
        medium: '中级',
        high: '高级',
        critical: '严重'
      }
      return levelNames[level] || level
    },

    // 获取级别颜色
    getLevelColor(level) {
      const levelColors = {
        low: 'green',
        medium: 'orange',
        high: 'red',
        critical: 'purple'
      }
      return levelColors[level] || 'default'
    },

    // 获取类型名称
    getTypeName(type) {
      const typeNames = {
        js_error: 'JavaScript错误',
        promise_rejection: 'Promise拒绝',
        network_error: '网络错误',
        api_error: 'API错误',
        component_error: '组件错误',
        resource_error: '资源错误',
        custom_error: '自定义错误'
      }
      return typeNames[type] || type
    },

    // 获取百分比
    getPercentage(value, total) {
      return total > 0 ? Math.round((value / total) * 100) : 0
    },

    // 格式化时间
    formatTime(timestamp) {
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('error', this.handleGlobalError)
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection)
  }
}
</script>

<style lang="less" scoped>
@import '../../styles/mobile/index.less';

.error-test {
  .test-header {
    .d-flex();
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-lg;

    h2 {
      margin: 0;
      color: @text-color;
    }

    .test-controls {
      .d-flex();
      gap: @spacing-sm;
    }
  }

  .test-content {
    .test-card {
      margin-bottom: @spacing-lg;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .error-system-status {
    .error-level-distribution {
      margin-top: @spacing-lg;

      h4 {
        margin: 0 0 @spacing-md 0;
        color: @text-color;
      }

      .level-bars {
        .level-bar {
          .d-flex();
          align-items: center;
          margin-bottom: @spacing-sm;

          .level-name {
            flex: 0 0 60px;
            font-size: @font-size-sm;
            color: @text-color;
          }

          .level-progress {
            flex: 1;
            margin: 0 @spacing-sm;
          }

          .level-count {
            flex: 0 0 40px;
            text-align: right;
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }
      }
    }
  }

  .error-test-section {
    .error-trigger-list {
      .error-trigger-item {
        margin-bottom: @spacing-lg;
        padding: @spacing-md;
        border: 1px solid @border-color-light;
        border-radius: @border-radius-base;
        transition: all 0.3s ease;

        &.testing {
          border-color: @primary-color;
          background-color: fade(@primary-color, 5%);
        }

        .trigger-header {
          .d-flex();
          justify-content: space-between;
          align-items: center;
          margin-bottom: @spacing-sm;

          .trigger-info {
            .d-flex();
            align-items: center;
            gap: @spacing-sm;

            .trigger-name {
              font-weight: @font-weight-medium;
              color: @text-color;
            }
          }
        }

        .trigger-description {
          font-size: @font-size-sm;
          color: @text-color-secondary;
          margin-bottom: @spacing-sm;
        }

        .trigger-result {
          margin-top: @spacing-sm;
        }
      }
    }

    .recovery-test-controls {
      .d-flex();
      gap: @spacing-sm;
      margin-bottom: @spacing-lg;
    }

    .recovery-result {
      margin-bottom: @spacing-lg;
    }

    .recovery-demo {
      margin-top: @spacing-lg;
      padding: @spacing-lg;
      background: @background-color-light;
      border-radius: @border-radius-base;

      h4 {
        margin: 0 0 @spacing-md 0;
        color: @text-color;
      }
    }
  }

  .error-log-section {
    .log-controls {
      .d-flex();
      gap: @spacing-sm;
      margin-bottom: @spacing-lg;
      flex-wrap: wrap;
    }

    .error-log-list {
      max-height: 600px;
      overflow-y: auto;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;

      .error-log-item {
        padding: @spacing-md;
        border-bottom: 1px solid @border-color-light;

        &:last-child {
          border-bottom: none;
        }

        &.critical {
          border-left: 4px solid #722ed1;
        }

        &.high {
          border-left: 4px solid #f5222d;
        }

        &.medium {
          border-left: 4px solid #fa8c16;
        }

        &.low {
          border-left: 4px solid #52c41a;
        }

        .log-header {
          .d-flex();
          justify-content: space-between;
          align-items: center;
          margin-bottom: @spacing-sm;

          .log-info {
            .d-flex();
            align-items: center;
            gap: @spacing-sm;

            .log-time {
              font-size: @font-size-sm;
              color: @text-color-secondary;
              font-family: monospace;
            }
          }
        }

        .log-message {
          font-size: @font-size-sm;
          color: @text-color;
          margin-bottom: @spacing-sm;
          word-break: break-word;
        }

        .log-details {
          background: @background-color-light;
          padding: @spacing-sm;
          border-radius: @border-radius-sm;
          font-size: @font-size-sm;

          .detail-item {
            margin-bottom: @spacing-xs;

            &:last-child {
              margin-bottom: 0;
            }

            strong {
              color: @text-color;
            }

            .stack-trace {
              background: white;
              padding: @spacing-sm;
              border-radius: @border-radius-sm;
              font-family: monospace;
              font-size: 12px;
              max-height: 200px;
              overflow-y: auto;
              margin-top: @spacing-xs;
              border: 1px solid @border-color-light;
            }
          }
        }
      }
    }

    .log-pagination {
      margin-top: @spacing-md;
      text-align: center;
    }
  }

  .test-summary {
    .summary-stats {
      .d-flex();
      justify-content: space-around;
      margin-bottom: @spacing-lg;
    }

    .summary-details {
      .ant-table {
        font-size: @font-size-sm;
      }
    }
  }

  .error-boundary-fallback {
    padding: @spacing-lg;
    text-align: center;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: @border-radius-base;

    h3 {
      color: #cf1322;
      margin-bottom: @spacing-sm;
    }

    p {
      color: @text-color-secondary;
      margin-bottom: @spacing-md;
    }
  }

  .error-prone-component {
    padding: @spacing-md;
    border: 1px solid @border-color-light;
    border-radius: @border-radius-base;
    background: white;

    h4 {
      margin: 0 0 @spacing-sm 0;
      color: @text-color;
    }

    p {
      margin: @spacing-sm 0;
      color: @text-color-secondary;
    }

    .ant-btn {
      margin-right: @spacing-sm;
      margin-bottom: @spacing-xs;
    }
  }
}

.error-detail-content {
  .stack-trace-section {
    margin-top: @spacing-lg;

    h4 {
      margin: 0 0 @spacing-sm 0;
      color: @text-color;
    }

    .stack-trace {
      background: @background-color-light;
      padding: @spacing-md;
      border-radius: @border-radius-base;
      font-family: monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid @border-color-light;
    }
  }
}
</style>
