<template>
  <div class="test-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>测试中心</h1>
      <div class="header-actions">
        <a-button @click="toggleDebugPanel">
          <a-icon type="bug" />
          调试面板
        </a-button>
        <a-button type="primary" @click="runQuickTest">
          <a-icon type="play-circle" />
          快速测试
        </a-button>
      </div>
    </div>

    <!-- 测试导航 -->
    <div class="test-navigation">
      <a-menu mode="horizontal" v-model="activeTest" @click="switchTest">
        <a-menu-item key="performance">
          <a-icon type="dashboard" />
          性能测试
        </a-menu-item>
        <a-menu-item key="component">
          <a-icon type="appstore" />
          组件测试
        </a-menu-item>
        <a-menu-item key="api">
          <a-icon type="api" />
          API测试
        </a-menu-item>
        <a-menu-item key="cache">
          <a-icon type="database" />
          缓存测试
        </a-menu-item>
        <a-menu-item key="error">
          <a-icon type="warning" />
          错误测试
        </a-menu-item>
      </a-menu>
    </div>

    <!-- 测试内容区域 -->
    <div class="test-content">
      <!-- 性能测试 -->
      <div v-show="activeTest === 'performance'" class="test-section">
        <performance-test ref="performanceTest" />
      </div>

      <!-- 组件测试 -->
      <div v-show="activeTest === 'component'" class="test-section">
        <component-test />
      </div>

      <!-- API测试 -->
      <div v-show="activeTest === 'api'" class="test-section">
        <api-test />
      </div>

      <!-- 缓存测试 -->
      <div v-show="activeTest === 'cache'" class="test-section">
        <cache-test />
      </div>

      <!-- 错误测试 -->
      <div v-show="activeTest === 'error'" class="test-section">
        <error-test />
      </div>
    </div>

    <!-- 调试面板 -->
    <debug-panel ref="debugPanel" />

    <!-- 快速测试结果弹窗 -->
    <a-modal
      title="快速测试结果"
      :visible="quickTestVisible"
      @ok="quickTestVisible = false"
      @cancel="quickTestVisible = false"
      width="600px"
    >
      <div class="quick-test-results">
        <a-spin :spinning="quickTestRunning">
          <div v-if="quickTestResults.length > 0">
            <div 
              v-for="(result, index) in quickTestResults" 
              :key="index"
              class="test-result-item"
              :class="result.status"
            >
              <div class="result-header">
                <a-icon :type="result.status === 'success' ? 'check-circle' : 'close-circle'" />
                <span class="result-name">{{ result.name }}</span>
                <span class="result-time">{{ result.time }}ms</span>
              </div>
              <div class="result-message">{{ result.message }}</div>
            </div>
          </div>
          <a-empty v-else-if="!quickTestRunning" description="暂无测试结果" />
        </a-spin>
      </div>
    </a-modal>
  </div>
</template>

<script>
import PerformanceTest from '@/components/debug/PerformanceTest.vue'
import DebugPanel from '@/components/debug/DebugPanel.vue'
import ComponentTest from './ComponentTest.vue'
import ApiTest from './ApiTest.vue'
import CacheTest from './CacheTest.vue'
import ErrorTest from './ErrorTest.vue'

import errorHandler from '@/utils/errorHandler'
import performanceMonitor from '@/utils/performance'
import cacheManager from '@/utils/cache'
import { generatePerformanceTestData } from '@/utils/testDataGenerator'

export default {
  name: 'TestCenter',
  
  components: {
    PerformanceTest,
    DebugPanel,
    ComponentTest,
    ApiTest,
    CacheTest,
    ErrorTest
  },
  
  data() {
    return {
      activeTest: 'performance',
      quickTestVisible: false,
      quickTestRunning: false,
      quickTestResults: []
    }
  },
  
  mounted() {
    this.initTestCenter()
  },
  
  methods: {
    // 初始化测试中心
    initTestCenter() {
      // 初始化错误处理
      errorHandler.init()
      
      // 初始化性能监控
      performanceMonitor.init()
      
      // 记录页面加载性能
      performanceMonitor.recordPageLoad('test_center')
      
      console.log('测试中心已初始化')
    },
    
    // 切换测试类型
    switchTest({ key }) {
      this.activeTest = key
      
      // 记录测试切换
      performanceMonitor.recordMetric('test_switch', {
        from: this.activeTest,
        to: key,
        timestamp: Date.now()
      })
    },
    
    // 切换调试面板
    toggleDebugPanel() {
      if (this.$refs.debugPanel) {
        this.$refs.debugPanel.togglePanel()
      }
    },
    
    // 运行快速测试
    async runQuickTest() {
      this.quickTestVisible = true
      this.quickTestRunning = true
      this.quickTestResults = []
      
      const tests = [
        {
          name: '性能监控',
          test: this.testPerformanceMonitor
        },
        {
          name: '缓存系统',
          test: this.testCacheSystem
        },
        {
          name: '错误处理',
          test: this.testErrorHandler
        },
        {
          name: '虚拟滚动',
          test: this.testVirtualScroll
        },
        {
          name: '网络请求',
          test: this.testNetworkRequest
        }
      ]
      
      for (const testItem of tests) {
        const startTime = performance.now()
        
        try {
          await testItem.test()
          const endTime = performance.now()
          
          this.quickTestResults.push({
            name: testItem.name,
            status: 'success',
            message: '测试通过',
            time: Math.round(endTime - startTime)
          })
        } catch (error) {
          const endTime = performance.now()
          
          this.quickTestResults.push({
            name: testItem.name,
            status: 'error',
            message: error.message,
            time: Math.round(endTime - startTime)
          })
        }
        
        // 添加延迟以便观察测试过程
        await this.delay(500)
      }
      
      this.quickTestRunning = false
    },
    
    // 测试性能监控
    async testPerformanceMonitor() {
      // 记录一些测试指标
      performanceMonitor.recordMetric('test_metric', {
        value: Math.random() * 100,
        timestamp: Date.now()
      })
      
      // 模拟长任务
      const start = performance.now()
      while (performance.now() - start < 60) {
        // 模拟CPU密集任务
      }
      
      // 检查性能监控是否正常工作
      const stats = performanceMonitor.getStats()
      if (!stats) {
        throw new Error('性能监控未正常工作')
      }
    },
    
    // 测试缓存系统
    async testCacheSystem() {
      const testKey = 'quick_test_cache'
      const testValue = { data: 'test', timestamp: Date.now() }
      
      // 测试设置缓存
      cacheManager.set(testKey, testValue)
      
      // 测试获取缓存
      const cachedValue = cacheManager.get(testKey)
      if (!cachedValue || cachedValue.data !== testValue.data) {
        throw new Error('缓存设置或获取失败')
      }
      
      // 测试缓存删除
      cacheManager.delete(testKey)
      const deletedValue = cacheManager.get(testKey)
      if (deletedValue !== null) {
        throw new Error('缓存删除失败')
      }
    },
    
    // 测试错误处理
    async testErrorHandler() {
      // 测试自定义错误报告
      errorHandler.reportCustomError('测试错误', {
        level: errorHandler.errorLevels.LOW,
        testMode: true
      })
      
      // 检查错误是否被正确记录
      const errors = errorHandler.getErrors({ type: errorHandler.errorTypes.CUSTOM_ERROR })
      if (errors.length === 0) {
        throw new Error('错误处理系统未正常工作')
      }
    },
    
    // 测试虚拟滚动
    async testVirtualScroll() {
      // 生成测试数据
      const testData = generatePerformanceTestData()
      const largeDataSet = testData.large.materials
      
      if (largeDataSet.length < 1000) {
        throw new Error('测试数据生成失败')
      }
      
      // 模拟虚拟滚动渲染
      const startTime = performance.now()
      
      // 模拟渲染前20项（虚拟滚动的可见区域）
      const visibleItems = largeDataSet.slice(0, 20)
      for (const item of visibleItems) {
        // 模拟DOM操作
        const element = document.createElement('div')
        element.textContent = item.name
        document.body.appendChild(element)
        document.body.removeChild(element)
      }
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      if (renderTime > 100) {
        throw new Error(`虚拟滚动渲染时间过长: ${renderTime}ms`)
      }
    },
    
    // 测试网络请求
    async testNetworkRequest() {
      try {
        // 测试一个简单的网络请求
        const response = await fetch('/api/health', {
          method: 'GET',
          timeout: 5000
        })
        
        // 如果请求失败但没有抛出错误，手动检查状态
        if (!response.ok && response.status !== 404) {
          throw new Error(`网络请求失败: ${response.status}`)
        }
        
        // 404是预期的，因为这个端点可能不存在
        // 但这表明网络请求机制是正常的
      } catch (error) {
        // 如果是网络错误（而不是404），则抛出错误
        if (error.message.includes('fetch')) {
          throw new Error('网络请求机制异常')
        }
        // 其他错误（如404）是可以接受的
      }
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="less" scoped>

.test-center {
  min-height: 100vh;
  background: @background-color-light;
  
  .page-header {
    .d-flex();
    justify-content: space-between;
    align-items: center;
    padding: @spacing-lg;
    background: white;
    border-bottom: 1px solid @border-color-light;
    
    h1 {
      margin: 0;
      color: @text-color;
      font-size: @font-size-xl;
    }
    
    .header-actions {
      .d-flex();
      gap: @spacing-sm;
    }
  }
  
  .test-navigation {
    background: white;
    border-bottom: 1px solid @border-color-light;
    
    .ant-menu {
      border-bottom: none;
    }
  }
  
  .test-content {
    padding: @spacing-lg;
    
    .test-section {
      background: white;
      border-radius: @border-radius-base;
      box-shadow: @box-shadow-light;
      min-height: 600px;
    }
  }
  
  .quick-test-results {
    .test-result-item {
      margin-bottom: @spacing-md;
      padding: @spacing-md;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
      
      &.success {
        border-left: 4px solid @success-color;
        background: fade(@success-color, 5%);
      }
      
      &.error {
        border-left: 4px solid @error-color;
        background: fade(@error-color, 5%);
      }
      
      .result-header {
        .d-flex();
        justify-content: space-between;
        align-items: center;
        margin-bottom: @spacing-xs;
        
        .anticon {
          margin-right: @spacing-xs;
          
          &.anticon-check-circle {
            color: @success-color;
          }
          
          &.anticon-close-circle {
            color: @error-color;
          }
        }
        
        .result-name {
          flex: 1;
          font-weight: @font-weight-medium;
        }
        
        .result-time {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
      
      .result-message {
        font-size: @font-size-sm;
        color: @text-color-secondary;
      }
    }
  }
}
</style>
