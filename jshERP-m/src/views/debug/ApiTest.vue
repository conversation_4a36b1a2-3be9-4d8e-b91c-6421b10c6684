<template>
  <div class="api-test">
    <div class="test-header">
      <h2>API测试</h2>
      <div class="test-controls">
        <a-button type="primary" @click="runAllApiTests" :loading="testing">
          <a-icon type="play-circle" />
          运行所有测试
        </a-button>
        <a-button @click="clearResults">
          <a-icon type="clear" />
          清除结果
        </a-button>
      </div>
    </div>

    <div class="test-content">
      <!-- API连接测试 -->
      <a-card title="API连接测试" class="test-card">
        <div class="api-test-section">
          <div class="test-controls-inline">
            <a-input 
              v-model="apiConfig.baseUrl" 
              placeholder="API基础URL"
              style="width: 300px"
            />
            <a-button @click="testApiConnection" :loading="connectionTesting">
              测试连接
            </a-button>
          </div>
          
          <div class="test-result" v-if="connectionResult">
            <a-alert 
              :type="connectionResult.success ? 'success' : 'error'"
              :message="connectionResult.message"
              :description="connectionResult.details"
              show-icon
            />
          </div>
        </div>
      </a-card>

      <!-- 业务API测试 -->
      <a-card title="业务API测试" class="test-card">
        <div class="api-test-section">
          <div class="api-list">
            <div 
              v-for="(api, index) in businessApis" 
              :key="index"
              class="api-item"
              :class="{ testing: api.testing, success: api.result?.success, error: api.result && !api.result.success }"
            >
              <div class="api-header">
                <div class="api-info">
                  <span class="api-method" :class="api.method.toLowerCase()">{{ api.method }}</span>
                  <span class="api-path">{{ api.path }}</span>
                  <span class="api-name">{{ api.name }}</span>
                </div>
                <div class="api-actions">
                  <a-button size="small" @click="testSingleApi(api)" :loading="api.testing">
                    {{ api.testing ? '测试中' : '测试' }}
                  </a-button>
                </div>
              </div>
              
              <div class="api-result" v-if="api.result">
                <div class="result-status">
                  <a-tag :color="api.result.success ? 'green' : 'red'">
                    {{ api.result.success ? '成功' : '失败' }}
                  </a-tag>
                  <span class="result-time">{{ api.result.responseTime }}ms</span>
                  <span class="result-status-code">{{ api.result.statusCode }}</span>
                </div>
                
                <div class="result-details" v-if="api.result.details">
                  <pre>{{ JSON.stringify(api.result.details, null, 2) }}</pre>
                </div>
                
                <div class="result-error" v-if="api.result.error">
                  <a-alert type="error" :message="api.result.error" show-icon />
                </div>
              </div>
            </div>
          </div>
          
          <div class="batch-actions">
            <a-button @click="testAllBusinessApis" :loading="businessApiTesting">
              测试所有业务API
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 性能测试 -->
      <a-card title="API性能测试" class="test-card">
        <div class="api-test-section">
          <div class="performance-config">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="并发数">
                  <a-input-number v-model="performanceConfig.concurrent" :min="1" :max="50" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="请求次数">
                  <a-input-number v-model="performanceConfig.requests" :min="1" :max="1000" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="超时时间(ms)">
                  <a-input-number v-model="performanceConfig.timeout" :min="1000" :max="30000" :step="1000" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          
          <div class="test-controls-inline">
            <a-select v-model="performanceConfig.testApi" style="width: 200px">
              <a-select-option value="/material/list">商品列表</a-select-option>
              <a-select-option value="/order/list">订单列表</a-select-option>
              <a-select-option value="/inventory/list">库存列表</a-select-option>
            </a-select>
            <a-button @click="testApiPerformance" :loading="performanceTesting">
              性能测试
            </a-button>
          </div>
          
          <div class="performance-result" v-if="performanceResult">
            <div class="performance-stats">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic title="平均响应时间" :value="performanceResult.avgResponseTime" suffix="ms" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="最大响应时间" :value="performanceResult.maxResponseTime" suffix="ms" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="成功率" :value="performanceResult.successRate" suffix="%" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="QPS" :value="performanceResult.qps" />
                </a-col>
              </a-row>
            </div>
            
            <div class="performance-chart">
              <div ref="performanceChart" class="chart-container"></div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 错误处理测试 -->
      <a-card title="错误处理测试" class="test-card">
        <div class="api-test-section">
          <div class="error-test-list">
            <div 
              v-for="(errorTest, index) in errorTests" 
              :key="index"
              class="error-test-item"
            >
              <div class="error-test-header">
                <span class="error-test-name">{{ errorTest.name }}</span>
                <a-button size="small" @click="testErrorHandling(errorTest)" :loading="errorTest.testing">
                  测试
                </a-button>
              </div>
              
              <div class="error-test-description">{{ errorTest.description }}</div>
              
              <div class="error-test-result" v-if="errorTest.result">
                <a-alert 
                  :type="errorTest.result.success ? 'success' : 'error'"
                  :message="errorTest.result.message"
                  :description="errorTest.result.details"
                  show-icon
                />
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 测试结果汇总 -->
    <a-card title="API测试结果汇总" v-if="hasTestResults" class="test-summary">
      <div class="summary-stats">
        <a-statistic title="总API数" :value="totalApis" />
        <a-statistic title="测试通过" :value="passedApis" />
        <a-statistic title="测试失败" :value="failedApis" />
        <a-statistic title="平均响应时间" :value="avgResponseTime" suffix="ms" />
      </div>
      
      <div class="summary-chart">
        <div ref="summaryChart" class="chart-container"></div>
      </div>
    </a-card>
  </div>
</template>

<script>
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
import errorHandler from '@/utils/errorHandler'
import performanceMonitor from '@/utils/performance'

export default {
  name: 'ApiTest',
  
  data() {
    return {
      testing: false,
      
      // API配置
      apiConfig: {
        baseUrl: process.env.VUE_APP_API_BASE_URL || 'http://localhost:9999'
      },
      
      // 连接测试
      connectionTesting: false,
      connectionResult: null,
      
      // 业务API测试
      businessApiTesting: false,
      businessApis: [
        {
          name: '商品列表',
          method: 'GET',
          path: '/material/list',
          params: { pageNo: 1, pageSize: 10 },
          testing: false,
          result: null
        },
        {
          name: '商品详情',
          method: 'GET',
          path: '/material/detail',
          params: { id: 1 },
          testing: false,
          result: null
        },
        {
          name: '添加商品',
          method: 'POST',
          path: '/material/add',
          data: {
            name: '测试商品',
            code: 'TEST001',
            categoryId: 1,
            unit: '个',
            purchasePrice: 10.00,
            salePrice: 15.00
          },
          testing: false,
          result: null
        },
        {
          name: '订单列表',
          method: 'GET',
          path: '/order/list',
          params: { pageNo: 1, pageSize: 10 },
          testing: false,
          result: null
        },
        {
          name: '库存列表',
          method: 'GET',
          path: '/inventory/list',
          params: { pageNo: 1, pageSize: 10 },
          testing: false,
          result: null
        },
        {
          name: '用户信息',
          method: 'GET',
          path: '/user/info',
          testing: false,
          result: null
        }
      ],
      
      // 性能测试
      performanceTesting: false,
      performanceResult: null,
      performanceConfig: {
        concurrent: 5,
        requests: 50,
        timeout: 5000,
        testApi: '/material/list'
      },
      
      // 错误处理测试
      errorTests: [
        {
          name: '404错误处理',
          description: '测试访问不存在的API端点',
          url: '/api/nonexistent',
          expectedStatus: 404,
          testing: false,
          result: null
        },
        {
          name: '401未授权处理',
          description: '测试未授权访问的错误处理',
          url: '/api/protected',
          expectedStatus: 401,
          testing: false,
          result: null
        },
        {
          name: '500服务器错误处理',
          description: '测试服务器内部错误的处理',
          url: '/api/error',
          expectedStatus: 500,
          testing: false,
          result: null
        },
        {
          name: '网络超时处理',
          description: '测试网络请求超时的处理',
          url: '/api/timeout',
          timeout: 1000,
          testing: false,
          result: null
        }
      ]
    }
  },
  
  computed: {
    hasTestResults() {
      return this.businessApis.some(api => api.result !== null)
    },
    
    totalApis() {
      return this.businessApis.length
    },
    
    passedApis() {
      return this.businessApis.filter(api => api.result && api.result.success).length
    },
    
    failedApis() {
      return this.businessApis.filter(api => api.result && !api.result.success).length
    },
    
    avgResponseTime() {
      const testedApis = this.businessApis.filter(api => api.result && api.result.responseTime)
      if (testedApis.length === 0) return 0
      
      const totalTime = testedApis.reduce((sum, api) => sum + api.result.responseTime, 0)
      return Math.round(totalTime / testedApis.length)
    }
  },
  
  methods: {
    // 运行所有API测试
    async runAllApiTests() {
      this.testing = true
      this.clearResults()
      
      try {
        // 测试API连接
        await this.testApiConnection()
        
        // 测试业务API
        await this.testAllBusinessApis()
        
        // 测试错误处理
        for (const errorTest of this.errorTests) {
          await this.testErrorHandling(errorTest)
          await this.delay(200)
        }
        
        this.$message.success('所有API测试完成')
      } catch (error) {
        this.$message.error('API测试过程中出现错误')
        console.error('API测试错误:', error)
      } finally {
        this.testing = false
      }
    },
    
    // 测试API连接
    async testApiConnection() {
      this.connectionTesting = true
      
      try {
        const startTime = performance.now()
        
        // 尝试访问健康检查端点
        const response = await fetch(`${this.apiConfig.baseUrl}/health`, {
          method: 'GET',
          timeout: 5000
        })
        
        const endTime = performance.now()
        const responseTime = Math.round(endTime - startTime)
        
        if (response.ok) {
          this.connectionResult = {
            success: true,
            message: 'API连接成功',
            details: `响应时间: ${responseTime}ms, 状态码: ${response.status}`
          }
        } else {
          this.connectionResult = {
            success: false,
            message: 'API连接失败',
            details: `状态码: ${response.status}, 响应时间: ${responseTime}ms`
          }
        }
        
      } catch (error) {
        this.connectionResult = {
          success: false,
          message: 'API连接失败',
          details: error.message
        }
      } finally {
        this.connectionTesting = false
      }
    },
    
    // 测试所有业务API
    async testAllBusinessApis() {
      this.businessApiTesting = true
      
      for (const api of this.businessApis) {
        await this.testSingleApi(api)
        await this.delay(300)
      }
      
      this.businessApiTesting = false
    },
    
    // 测试单个API
    async testSingleApi(api) {
      api.testing = true
      api.result = null
      
      try {
        const startTime = performance.now()
        let response
        
        // 根据方法类型调用不同的API
        switch (api.method) {
          case 'GET':
            response = await getAction(api.path, api.params)
            break
          case 'POST':
            response = await postAction(api.path, api.data)
            break
          case 'PUT':
            response = await putAction(api.path, api.data)
            break
          case 'DELETE':
            response = await deleteAction(api.path, api.params)
            break
          default:
            throw new Error(`不支持的HTTP方法: ${api.method}`)
        }
        
        const endTime = performance.now()
        const responseTime = Math.round(endTime - startTime)
        
        // 记录性能指标
        performanceMonitor.recordMetric('api_request', {
          path: api.path,
          method: api.method,
          responseTime,
          success: response.success,
          timestamp: Date.now()
        })
        
        api.result = {
          success: response.success,
          responseTime,
          statusCode: response.code || 200,
          details: response.success ? response.result : response.message,
          error: response.success ? null : response.message
        }
        
      } catch (error) {
        const endTime = performance.now()
        const responseTime = Math.round(endTime - startTime)
        
        // 报告API错误
        errorHandler.reportApiError(error, {
          url: api.path,
          method: api.method,
          data: api.data || api.params
        })
        
        api.result = {
          success: false,
          responseTime,
          statusCode: error.status || 0,
          details: null,
          error: error.message
        }
      } finally {
        api.testing = false
      }
    },
    
    // 测试API性能
    async testApiPerformance() {
      this.performanceTesting = true
      
      try {
        const { concurrent, requests, timeout, testApi } = this.performanceConfig
        const responseTimes = []
        const errors = []
        
        // 创建并发请求
        const batches = Math.ceil(requests / concurrent)
        
        for (let batch = 0; batch < batches; batch++) {
          const batchRequests = []
          const batchSize = Math.min(concurrent, requests - batch * concurrent)
          
          for (let i = 0; i < batchSize; i++) {
            batchRequests.push(this.performanceSingleRequest(testApi, timeout))
          }
          
          const batchResults = await Promise.allSettled(batchRequests)
          
          batchResults.forEach(result => {
            if (result.status === 'fulfilled') {
              responseTimes.push(result.value)
            } else {
              errors.push(result.reason)
            }
          })
          
          // 批次间延迟
          if (batch < batches - 1) {
            await this.delay(100)
          }
        }
        
        // 计算性能指标
        const avgResponseTime = responseTimes.length > 0 ? 
          Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length) : 0
        const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0
        const successRate = Math.round((responseTimes.length / requests) * 100)
        const qps = responseTimes.length > 0 ? 
          Math.round(responseTimes.length / (maxResponseTime / 1000)) : 0
        
        this.performanceResult = {
          avgResponseTime,
          maxResponseTime,
          successRate,
          qps,
          totalRequests: requests,
          successfulRequests: responseTimes.length,
          failedRequests: errors.length,
          responseTimes: responseTimes.slice(0, 100) // 只保留前100个用于图表
        }
        
        // 渲染性能图表
        this.$nextTick(() => {
          this.renderPerformanceChart()
        })
        
      } catch (error) {
        this.$message.error('性能测试失败: ' + error.message)
      } finally {
        this.performanceTesting = false
      }
    },
    
    // 单个性能请求
    async performanceSingleRequest(apiPath, timeout) {
      const startTime = performance.now()
      
      try {
        const response = await Promise.race([
          getAction(apiPath, { pageNo: 1, pageSize: 10 }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), timeout)
          )
        ])
        
        const endTime = performance.now()
        return Math.round(endTime - startTime)
      } catch (error) {
        throw error
      }
    },
    
    // 测试错误处理
    async testErrorHandling(errorTest) {
      errorTest.testing = true
      errorTest.result = null
      
      try {
        const startTime = performance.now()
        
        // 模拟错误请求
        let response
        try {
          response = await fetch(this.apiConfig.baseUrl + errorTest.url, {
            method: 'GET',
            timeout: errorTest.timeout || 5000
          })
        } catch (error) {
          // 网络错误
          if (errorTest.name.includes('超时')) {
            errorTest.result = {
              success: true,
              message: '超时错误处理测试通过',
              details: '成功捕获网络超时错误'
            }
            return
          }
          throw error
        }
        
        const endTime = performance.now()
        const responseTime = Math.round(endTime - startTime)
        
        // 检查是否返回了预期的错误状态码
        if (response.status === errorTest.expectedStatus) {
          errorTest.result = {
            success: true,
            message: '错误处理测试通过',
            details: `正确返回状态码 ${response.status}, 响应时间: ${responseTime}ms`
          }
        } else {
          errorTest.result = {
            success: false,
            message: '错误处理测试失败',
            details: `期望状态码 ${errorTest.expectedStatus}, 实际状态码 ${response.status}`
          }
        }
        
      } catch (error) {
        errorTest.result = {
          success: false,
          message: '错误处理测试异常',
          details: error.message
        }
      } finally {
        errorTest.testing = false
      }
    },
    
    // 渲染性能图表
    renderPerformanceChart() {
      if (!this.performanceResult || !this.$refs.performanceChart) return
      
      // 这里可以使用ECharts等图表库渲染响应时间分布图
      console.log('渲染性能图表:', this.performanceResult.responseTimes)
    },
    
    // 清除结果
    clearResults() {
      this.connectionResult = null
      this.performanceResult = null
      
      this.businessApis.forEach(api => {
        api.result = null
        api.testing = false
      })
      
      this.errorTests.forEach(test => {
        test.result = null
        test.testing = false
      })
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="less" scoped>

.api-test {
  .test-header {
    .d-flex();
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-lg;
    
    h2 {
      margin: 0;
      color: @text-color;
    }
    
    .test-controls {
      .d-flex();
      gap: @spacing-sm;
    }
  }
  
  .test-content {
    .test-card {
      margin-bottom: @spacing-lg;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .api-test-section {
    .test-controls-inline {
      .d-flex();
      gap: @spacing-sm;
      margin-bottom: @spacing-lg;
    }
    
    .test-result {
      margin-bottom: @spacing-lg;
    }
  }
  
  .api-list {
    .api-item {
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
      margin-bottom: @spacing-md;
      transition: all 0.3s;
      
      &.testing {
        border-color: @primary-color;
        box-shadow: 0 0 8px fade(@primary-color, 20%);
      }
      
      &.success {
        border-left: 4px solid @success-color;
      }
      
      &.error {
        border-left: 4px solid @error-color;
      }
      
      .api-header {
        .d-flex();
        justify-content: space-between;
        align-items: center;
        padding: @spacing-md;
        
        .api-info {
          .d-flex();
          align-items: center;
          gap: @spacing-sm;
          
          .api-method {
            padding: 2px 8px;
            border-radius: @border-radius-sm;
            font-size: @font-size-sm;
            font-weight: @font-weight-medium;
            
            &.get { background: @success-color; color: white; }
            &.post { background: @primary-color; color: white; }
            &.put { background: @warning-color; color: white; }
            &.delete { background: @error-color; color: white; }
          }
          
          .api-path {
            font-family: monospace;
            color: @text-color-secondary;
          }
          
          .api-name {
            font-weight: @font-weight-medium;
          }
        }
      }
      
      .api-result {
        padding: 0 @spacing-md @spacing-md;
        
        .result-status {
          .d-flex();
          align-items: center;
          gap: @spacing-sm;
          margin-bottom: @spacing-sm;
          
          .result-time {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
          
          .result-status-code {
            font-size: @font-size-sm;
            color: @text-color-secondary;
          }
        }
        
        .result-details {
          pre {
            background: @background-color-light;
            padding: @spacing-sm;
            border-radius: @border-radius-sm;
            font-size: @font-size-sm;
            max-height: 200px;
            overflow-y: auto;
          }
        }
      }
    }
  }
  
  .batch-actions {
    text-align: center;
    margin-top: @spacing-lg;
  }
  
  .performance-config {
    margin-bottom: @spacing-lg;
  }
  
  .performance-result {
    .performance-stats {
      margin-bottom: @spacing-lg;
    }
    
    .chart-container {
      height: 300px;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
    }
  }
  
  .error-test-list {
    .error-test-item {
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
      padding: @spacing-md;
      margin-bottom: @spacing-md;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .error-test-header {
        .d-flex();
        justify-content: space-between;
        align-items: center;
        margin-bottom: @spacing-xs;
        
        .error-test-name {
          font-weight: @font-weight-medium;
        }
      }
      
      .error-test-description {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-bottom: @spacing-sm;
      }
      
      .error-test-result {
        margin-top: @spacing-sm;
      }
    }
  }
  
  .test-summary {
    .summary-stats {
      .d-flex();
      justify-content: space-around;
      margin-bottom: @spacing-lg;
    }
    
    .chart-container {
      height: 300px;
      border: 1px solid @border-color-light;
      border-radius: @border-radius-base;
    }
  }
}
</style>
