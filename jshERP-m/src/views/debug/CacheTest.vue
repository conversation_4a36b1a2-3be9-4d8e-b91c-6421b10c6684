<template>
  <div class="cache-test">
    <div class="test-header">
      <h2>缓存测试</h2>
      <div class="test-controls">
        <a-button type="primary" @click="runAllCacheTests" :loading="testing">
          <a-icon type="play-circle" />
          运行所有测试
        </a-button>
        <a-button @click="clearAllCaches">
          <a-icon type="clear" />
          清除所有缓存
        </a-button>
        <a-button @click="refreshCacheStatus">
          <a-icon type="reload" />
          刷新状态
        </a-button>
      </div>
    </div>

    <div class="test-content">
      <!-- 缓存状态概览 -->
      <a-card title="缓存状态概览" class="test-card">
        <div class="cache-overview">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="内存缓存项数" :value="cacheStats.memory.count" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="LocalStorage使用" :value="cacheStats.localStorage.usage" suffix="KB" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="SessionStorage使用" :value="cacheStats.sessionStorage.usage" suffix="KB" />
            </a-col>
          </a-row>

          <div class="cache-details">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="cache-detail-card">
                  <h4>内存缓存</h4>
                  <p>命中率: {{ cacheStats.memory.hitRate }}%</p>
                  <p>总大小: {{ cacheStats.memory.size }}KB</p>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="cache-detail-card">
                  <h4>LocalStorage</h4>
                  <p>项目数: {{ cacheStats.localStorage.count }}</p>
                  <p>可用空间: {{ cacheStats.localStorage.available }}KB</p>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="cache-detail-card">
                  <h4>SessionStorage</h4>
                  <p>项目数: {{ cacheStats.sessionStorage.count }}</p>
                  <p>可用空间: {{ cacheStats.sessionStorage.available }}KB</p>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-card>

      <!-- 内存缓存测试 -->
      <a-card title="内存缓存测试" class="test-card">
        <div class="cache-test-section">
          <div class="test-controls-inline">
            <a-input-number
              v-model="memoryTestConfig.itemCount"
              :min="10"
              :max="10000"
              placeholder="测试项目数量"
            />
            <a-input-number
              v-model="memoryTestConfig.itemSize"
              :min="1"
              :max="1000"
              placeholder="每项大小(KB)"
            />
            <a-button @click="testMemoryCache" :loading="memoryTesting">
              测试内存缓存
            </a-button>
          </div>

          <div class="test-result" v-if="memoryTestResult">
            <a-alert
              :type="memoryTestResult.success ? 'success' : 'error'"
              :message="memoryTestResult.message"
              :description="memoryTestResult.details"
              show-icon
            />
          </div>

          <!-- 内存缓存操作演示 -->
          <div class="cache-operations">
            <h4>缓存操作演示</h4>
            <div class="operation-controls">
              <a-input
                v-model="memoryOperation.key"
                placeholder="缓存键"
                style="width: 150px"
              />
              <a-input
                v-model="memoryOperation.value"
                placeholder="缓存值"
                style="width: 200px"
              />
              <a-input-number
                v-model="memoryOperation.ttl"
                placeholder="过期时间(秒)"
                style="width: 120px"
              />
              <a-button @click="setMemoryCache">设置</a-button>
              <a-button @click="getMemoryCache">获取</a-button>
              <a-button @click="deleteMemoryCache">删除</a-button>
            </div>

            <div class="operation-result" v-if="memoryOperation.result">
              <pre>{{ JSON.stringify(memoryOperation.result, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </a-card>

      <!-- LocalStorage测试 -->
      <a-card title="LocalStorage测试" class="test-card">
        <div class="cache-test-section">
          <div class="test-controls-inline">
            <a-input-number
              v-model="localStorageTestConfig.itemCount"
              :min="10"
              :max="1000"
              placeholder="测试项目数量"
            />
            <a-button @click="testLocalStorage" :loading="localStorageTesting">
              测试LocalStorage
            </a-button>
            <a-button @click="clearLocalStorageTest">
              清除测试数据
            </a-button>
          </div>

          <div class="test-result" v-if="localStorageTestResult">
            <a-alert
              :type="localStorageTestResult.success ? 'success' : 'error'"
              :message="localStorageTestResult.message"
              :description="localStorageTestResult.details"
              show-icon
            />
          </div>

          <!-- LocalStorage内容查看 -->
          <div class="storage-viewer">
            <h4>LocalStorage内容</h4>
            <div class="storage-list">
              <div
                v-for="(item, key) in localStorageItems"
                :key="key"
                class="storage-item"
              >
                <div class="storage-key">{{ key }}</div>
                <div class="storage-value">{{ item.substring(0, 100) }}{{ item.length > 100 ? '...' : '' }}</div>
                <div class="storage-size">{{ Math.round(item.length / 1024 * 100) / 100 }}KB</div>
                <a-button size="small" @click="deleteLocalStorageItem(key)">删除</a-button>
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- SessionStorage测试 -->
      <a-card title="SessionStorage测试" class="test-card">
        <div class="cache-test-section">
          <div class="test-controls-inline">
            <a-input-number
              v-model="sessionStorageTestConfig.itemCount"
              :min="10"
              :max="1000"
              placeholder="测试项目数量"
            />
            <a-button @click="testSessionStorage" :loading="sessionStorageTesting">
              测试SessionStorage
            </a-button>
            <a-button @click="clearSessionStorageTest">
              清除测试数据
            </a-button>
          </div>

          <div class="test-result" v-if="sessionStorageTestResult">
            <a-alert
              :type="sessionStorageTestResult.success ? 'success' : 'error'"
              :message="sessionStorageTestResult.message"
              :description="sessionStorageTestResult.details"
              show-icon
            />
          </div>

          <!-- SessionStorage内容查看 -->
          <div class="storage-viewer">
            <h4>SessionStorage内容</h4>
            <div class="storage-list">
              <div
                v-for="(item, key) in sessionStorageItems"
                :key="key"
                class="storage-item"
              >
                <div class="storage-key">{{ key }}</div>
                <div class="storage-value">{{ item.substring(0, 100) }}{{ item.length > 100 ? '...' : '' }}</div>
                <div class="storage-size">{{ Math.round(item.length / 1024 * 100) / 100 }}KB</div>
                <a-button size="small" @click="deleteSessionStorageItem(key)">删除</a-button>
              </div>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 缓存性能测试 -->
      <a-card title="缓存性能测试" class="test-card">
        <div class="cache-test-section">
          <div class="performance-config">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="操作次数">
                  <a-input-number v-model="performanceConfig.operations" :min="100" :max="10000" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="数据大小(KB)">
                  <a-input-number v-model="performanceConfig.dataSize" :min="1" :max="100" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="并发数">
                  <a-input-number v-model="performanceConfig.concurrent" :min="1" :max="10" />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="测试类型">
                  <a-select v-model="performanceConfig.testType">
                    <a-select-option value="read">读取测试</a-select-option>
                    <a-select-option value="write">写入测试</a-select-option>
                    <a-select-option value="mixed">混合测试</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <div class="test-controls-inline">
            <a-button @click="testCachePerformance" :loading="performanceTesting">
              性能测试
            </a-button>
          </div>

          <div class="performance-result" v-if="performanceResult">
            <div class="performance-stats">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic title="平均操作时间" :value="performanceResult.avgTime" suffix="ms" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="最大操作时间" :value="performanceResult.maxTime" suffix="ms" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="操作成功率" :value="performanceResult.successRate" suffix="%" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="OPS" :value="performanceResult.ops" />
                </a-col>
              </a-row>
            </div>

            <div class="performance-chart">
              <div ref="performanceChart" class="chart-container"></div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 测试结果汇总 -->
    <a-card title="缓存测试结果汇总" v-if="hasTestResults" class="test-summary">
      <div class="summary-stats">
        <a-statistic title="总测试数" :value="totalTests" />
        <a-statistic title="通过测试" :value="passedTests" />
        <a-statistic title="失败测试" :value="failedTests" />
        <a-statistic title="缓存效率" :value="cacheEfficiency" suffix="%" />
      </div>

      <div class="summary-details">
        <a-table
          :columns="summaryColumns"
          :dataSource="testSummary"
          :pagination="false"
          size="small"
        />
      </div>
    </a-card>
  </div>
</template>

<script>
import cacheManager from '@/utils/cache'
import performanceMonitor from '@/utils/performance'
import { generateCacheTestData } from '@/utils/testDataGenerator'

export default {
  name: 'CacheTest',

  data() {
    return {
      testing: false,

      // 缓存统计
      cacheStats: {
        memory: {
          count: 0,
          size: 0,
          hitRate: 0
        },
        localStorage: {
          count: 0,
          usage: 0,
          available: 0
        },
        sessionStorage: {
          count: 0,
          usage: 0,
          available: 0
        }
      },

      // 内存缓存测试
      memoryTesting: false,
      memoryTestResult: null,
      memoryTestConfig: {
        itemCount: 100,
        itemSize: 10
      },
      memoryOperation: {
        key: '',
        value: '',
        ttl: 60,
        result: null
      },

      // LocalStorage测试
      localStorageTesting: false,
      localStorageTestResult: null,
      localStorageTestConfig: {
        itemCount: 50
      },
      localStorageItems: {},

      // SessionStorage测试
      sessionStorageTesting: false,
      sessionStorageTestResult: null,
      sessionStorageTestConfig: {
        itemCount: 50
      },
      sessionStorageItems: {},

      // 性能测试
      performanceTesting: false,
      performanceResult: null,
      performanceConfig: {
        operations: 1000,
        dataSize: 10,
        concurrent: 5,
        testType: 'mixed'
      },

      // 测试汇总
      testSummary: [],
      summaryColumns: [
        { title: '测试项目', dataIndex: 'name', key: 'name' },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '耗时', dataIndex: 'duration', key: 'duration' },
        { title: '详情', dataIndex: 'details', key: 'details' }
      ]
    }
  },

  computed: {
    hasTestResults() {
      return this.testSummary.length > 0
    },

    totalTests() {
      return this.testSummary.length
    },

    passedTests() {
      return this.testSummary.filter(test => test.status === '通过').length
    },

    failedTests() {
      return this.testSummary.filter(test => test.status === '失败').length
    },

    cacheEfficiency() {
      if (this.totalTests === 0) return 0
      return Math.round((this.passedTests / this.totalTests) * 100)
    }
  },

  mounted() {
    this.initCacheTest()
    this.refreshCacheStatus()
  },

  methods: {
    // 初始化缓存测试
    initCacheTest() {
      // 初始化缓存管理器
      if (typeof cacheManager.init === 'function') {
        cacheManager.init()
      }

      console.log('缓存测试模块已初始化')
    },

    // 运行所有缓存测试
    async runAllCacheTests() {
      this.testing = true
      this.testSummary = []

      const tests = [
        { name: '内存缓存测试', method: this.testMemoryCache },
        { name: 'LocalStorage测试', method: this.testLocalStorage },
        { name: 'SessionStorage测试', method: this.testSessionStorage },
        { name: '缓存性能测试', method: this.testCachePerformance }
      ]

      for (const test of tests) {
        const startTime = performance.now()

        try {
          await test.method()
          const endTime = performance.now()

          this.testSummary.push({
            name: test.name,
            status: '通过',
            duration: `${Math.round(endTime - startTime)}ms`,
            details: '测试通过'
          })
        } catch (error) {
          const endTime = performance.now()

          this.testSummary.push({
            name: test.name,
            status: '失败',
            duration: `${Math.round(endTime - startTime)}ms`,
            details: error.message
          })
        }

        await this.delay(500)
      }

      this.testing = false
      this.refreshCacheStatus()
      this.$message.success('所有缓存测试完成')
    },

    // 测试内存缓存
    async testMemoryCache() {
      this.memoryTesting = true

      try {
        const { itemCount, itemSize } = this.memoryTestConfig
        const testData = generateCacheTestData(itemCount, itemSize)

        let successCount = 0
        let failCount = 0
        const startTime = performance.now()

        // 测试设置缓存
        for (let i = 0; i < testData.length; i++) {
          const item = testData[i]
          try {
            cacheManager.set(`test_memory_${i}`, item, 300) // 5分钟过期
            successCount++
          } catch (error) {
            failCount++
          }
        }

        // 测试获取缓存
        let hitCount = 0
        let missCount = 0

        for (let i = 0; i < testData.length; i++) {
          const cached = cacheManager.get(`test_memory_${i}`)
          if (cached !== null) {
            hitCount++
          } else {
            missCount++
          }
        }

        const endTime = performance.now()
        const totalTime = Math.round(endTime - startTime)
        const hitRate = Math.round((hitCount / testData.length) * 100)

        // 清理测试数据
        for (let i = 0; i < testData.length; i++) {
          cacheManager.delete(`test_memory_${i}`)
        }

        this.memoryTestResult = {
          success: true,
          message: '内存缓存测试通过',
          details: `设置成功: ${successCount}, 设置失败: ${failCount}, 命中率: ${hitRate}%, 总耗时: ${totalTime}ms`
        }

        // 记录性能指标
        performanceMonitor.recordMetric('memory_cache_test', {
          itemCount,
          itemSize,
          successCount,
          hitRate,
          totalTime,
          timestamp: Date.now()
        })

      } catch (error) {
        this.memoryTestResult = {
          success: false,
          message: '内存缓存测试失败',
          details: error.message
        }
        throw error
      } finally {
        this.memoryTesting = false
      }
    },

    // 内存缓存操作
    setMemoryCache() {
      if (!this.memoryOperation.key || !this.memoryOperation.value) {
        this.$message.error('请输入缓存键和值')
        return
      }

      try {
        cacheManager.set(
          this.memoryOperation.key,
          this.memoryOperation.value,
          this.memoryOperation.ttl
        )
        this.memoryOperation.result = {
          operation: 'set',
          success: true,
          message: '缓存设置成功'
        }
        this.$message.success('缓存设置成功')
      } catch (error) {
        this.memoryOperation.result = {
          operation: 'set',
          success: false,
          error: error.message
        }
        this.$message.error('缓存设置失败: ' + error.message)
      }
    },

    getMemoryCache() {
      if (!this.memoryOperation.key) {
        this.$message.error('请输入缓存键')
        return
      }

      try {
        const value = cacheManager.get(this.memoryOperation.key)
        this.memoryOperation.result = {
          operation: 'get',
          success: value !== null,
          value: value,
          message: value !== null ? '缓存获取成功' : '缓存不存在或已过期'
        }
      } catch (error) {
        this.memoryOperation.result = {
          operation: 'get',
          success: false,
          error: error.message
        }
      }
    },

    deleteMemoryCache() {
      if (!this.memoryOperation.key) {
        this.$message.error('请输入缓存键')
        return
      }

      try {
        cacheManager.delete(this.memoryOperation.key)
        this.memoryOperation.result = {
          operation: 'delete',
          success: true,
          message: '缓存删除成功'
        }
        this.$message.success('缓存删除成功')
      } catch (error) {
        this.memoryOperation.result = {
          operation: 'delete',
          success: false,
          error: error.message
        }
        this.$message.error('缓存删除失败: ' + error.message)
      }
    },

    // 测试LocalStorage
    async testLocalStorage() {
      this.localStorageTesting = true

      try {
        const { itemCount } = this.localStorageTestConfig
        const testData = generateCacheTestData(itemCount, 5) // 5KB per item

        let successCount = 0
        let failCount = 0
        const startTime = performance.now()

        // 测试设置LocalStorage
        for (let i = 0; i < testData.length; i++) {
          const item = testData[i]
          try {
            localStorage.setItem(`test_local_${i}`, JSON.stringify(item))
            successCount++
          } catch (error) {
            failCount++
            if (error.name === 'QuotaExceededError') {
              break // 存储空间不足，停止测试
            }
          }
        }

        // 测试获取LocalStorage
        let hitCount = 0
        let missCount = 0

        for (let i = 0; i < successCount; i++) {
          try {
            const cached = localStorage.getItem(`test_local_${i}`)
            if (cached !== null) {
              JSON.parse(cached) // 验证数据完整性
              hitCount++
            } else {
              missCount++
            }
          } catch (error) {
            missCount++
          }
        }

        const endTime = performance.now()
        const totalTime = Math.round(endTime - startTime)
        const hitRate = successCount > 0 ? Math.round((hitCount / successCount) * 100) : 0

        this.localStorageTestResult = {
          success: true,
          message: 'LocalStorage测试通过',
          details: `设置成功: ${successCount}, 设置失败: ${failCount}, 命中率: ${hitRate}%, 总耗时: ${totalTime}ms`
        }

        // 更新LocalStorage显示
        this.refreshLocalStorageItems()

      } catch (error) {
        this.localStorageTestResult = {
          success: false,
          message: 'LocalStorage测试失败',
          details: error.message
        }
        throw error
      } finally {
        this.localStorageTesting = false
      }
    },

    // 测试SessionStorage
    async testSessionStorage() {
      this.sessionStorageTesting = true

      try {
        const { itemCount } = this.sessionStorageTestConfig
        const testData = generateCacheTestData(itemCount, 5) // 5KB per item

        let successCount = 0
        let failCount = 0
        const startTime = performance.now()

        // 测试设置SessionStorage
        for (let i = 0; i < testData.length; i++) {
          const item = testData[i]
          try {
            sessionStorage.setItem(`test_session_${i}`, JSON.stringify(item))
            successCount++
          } catch (error) {
            failCount++
            if (error.name === 'QuotaExceededError') {
              break // 存储空间不足，停止测试
            }
          }
        }

        // 测试获取SessionStorage
        let hitCount = 0
        let missCount = 0

        for (let i = 0; i < successCount; i++) {
          try {
            const cached = sessionStorage.getItem(`test_session_${i}`)
            if (cached !== null) {
              JSON.parse(cached) // 验证数据完整性
              hitCount++
            } else {
              missCount++
            }
          } catch (error) {
            missCount++
          }
        }

        const endTime = performance.now()
        const totalTime = Math.round(endTime - startTime)
        const hitRate = successCount > 0 ? Math.round((hitCount / successCount) * 100) : 0

        this.sessionStorageTestResult = {
          success: true,
          message: 'SessionStorage测试通过',
          details: `设置成功: ${successCount}, 设置失败: ${failCount}, 命中率: ${hitRate}%, 总耗时: ${totalTime}ms`
        }

        // 更新SessionStorage显示
        this.refreshSessionStorageItems()

      } catch (error) {
        this.sessionStorageTestResult = {
          success: false,
          message: 'SessionStorage测试失败',
          details: error.message
        }
        throw error
      } finally {
        this.sessionStorageTesting = false
      }
    },

    // 测试缓存性能
    async testCachePerformance() {
      this.performanceTesting = true

      try {
        const { operations, dataSize, concurrent, testType } = this.performanceConfig
        const testData = generateCacheTestData(operations, dataSize)

        const results = []
        const startTime = performance.now()

        // 根据测试类型执行不同的操作
        if (testType === 'write' || testType === 'mixed') {
          // 写入测试
          for (let i = 0; i < operations; i++) {
            const opStartTime = performance.now()
            try {
              cacheManager.set(`perf_test_${i}`, testData[i % testData.length])
              const opEndTime = performance.now()
              results.push(opEndTime - opStartTime)
            } catch (error) {
              results.push(-1) // 标记失败
            }
          }
        }

        if (testType === 'read' || testType === 'mixed') {
          // 读取测试
          for (let i = 0; i < operations; i++) {
            const opStartTime = performance.now()
            try {
              cacheManager.get(`perf_test_${i}`)
              const opEndTime = performance.now()
              results.push(opEndTime - opStartTime)
            } catch (error) {
              results.push(-1) // 标记失败
            }
          }
        }

        const endTime = performance.now()
        const totalTime = endTime - startTime

        // 计算性能指标
        const validResults = results.filter(time => time >= 0)
        const avgTime = validResults.length > 0 ?
          Math.round(validResults.reduce((sum, time) => sum + time, 0) / validResults.length * 100) / 100 : 0
        const maxTime = validResults.length > 0 ? Math.max(...validResults) : 0
        const successRate = Math.round((validResults.length / results.length) * 100)
        const ops = Math.round(validResults.length / (totalTime / 1000))

        this.performanceResult = {
          avgTime,
          maxTime: Math.round(maxTime * 100) / 100,
          successRate,
          ops,
          totalOperations: operations,
          successfulOperations: validResults.length,
          failedOperations: results.length - validResults.length,
          results: validResults.slice(0, 100) // 只保留前100个用于图表
        }

        // 清理测试数据
        for (let i = 0; i < operations; i++) {
          cacheManager.delete(`perf_test_${i}`)
        }

        // 渲染性能图表
        this.$nextTick(() => {
          this.renderPerformanceChart()
        })

      } catch (error) {
        this.$message.error('缓存性能测试失败: ' + error.message)
        throw error
      } finally {
        this.performanceTesting = false
      }
    },

    // 刷新缓存状态
    refreshCacheStatus() {
      // 内存缓存统计
      if (cacheManager.getStats) {
        const memoryStats = cacheManager.getStats()
        this.cacheStats.memory = {
          count: memoryStats.count || 0,
          size: Math.round((memoryStats.size || 0) / 1024),
          hitRate: Math.round((memoryStats.hitRate || 0) * 100)
        }
      }

      // LocalStorage统计
      this.refreshLocalStorageItems()
      let localStorageSize = 0
      Object.values(this.localStorageItems).forEach(item => {
        localStorageSize += item.length
      })

      this.cacheStats.localStorage = {
        count: Object.keys(this.localStorageItems).length,
        usage: Math.round(localStorageSize / 1024),
        available: this.getStorageAvailable('localStorage')
      }

      // SessionStorage统计
      this.refreshSessionStorageItems()
      let sessionStorageSize = 0
      Object.values(this.sessionStorageItems).forEach(item => {
        sessionStorageSize += item.length
      })

      this.cacheStats.sessionStorage = {
        count: Object.keys(this.sessionStorageItems).length,
        usage: Math.round(sessionStorageSize / 1024),
        available: this.getStorageAvailable('sessionStorage')
      }
    },

    // 刷新LocalStorage项目
    refreshLocalStorageItems() {
      this.localStorageItems = {}
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key) {
          this.localStorageItems[key] = localStorage.getItem(key) || ''
        }
      }
    },

    // 刷新SessionStorage项目
    refreshSessionStorageItems() {
      this.sessionStorageItems = {}
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i)
        if (key) {
          this.sessionStorageItems[key] = sessionStorage.getItem(key) || ''
        }
      }
    },

    // 获取存储可用空间
    getStorageAvailable(storageType) {
      try {
        const storage = storageType === 'localStorage' ? localStorage : sessionStorage
        const testKey = '__storage_test__'
        const testData = '0'.repeat(1024) // 1KB
        let available = 0

        // 尝试写入数据直到达到限制
        for (let i = 0; i < 10240; i++) { // 最多测试10MB
          try {
            storage.setItem(testKey, testData.repeat(i))
            available = i
          } catch (e) {
            break
          }
        }

        // 清理测试数据
        storage.removeItem(testKey)
        return available
      } catch (error) {
        return 0
      }
    },

    // 删除LocalStorage项目
    deleteLocalStorageItem(key) {
      localStorage.removeItem(key)
      this.refreshLocalStorageItems()
      this.refreshCacheStatus()
      this.$message.success('项目已删除')
    },

    // 删除SessionStorage项目
    deleteSessionStorageItem(key) {
      sessionStorage.removeItem(key)
      this.refreshSessionStorageItems()
      this.refreshCacheStatus()
      this.$message.success('项目已删除')
    },

    // 清除LocalStorage测试数据
    clearLocalStorageTest() {
      const keys = Object.keys(this.localStorageItems).filter(key => key.startsWith('test_local_'))
      keys.forEach(key => localStorage.removeItem(key))
      this.refreshLocalStorageItems()
      this.refreshCacheStatus()
      this.$message.success(`清除了 ${keys.length} 个测试项目`)
    },

    // 清除SessionStorage测试数据
    clearSessionStorageTest() {
      const keys = Object.keys(this.sessionStorageItems).filter(key => key.startsWith('test_session_'))
      keys.forEach(key => sessionStorage.removeItem(key))
      this.refreshSessionStorageItems()
      this.refreshCacheStatus()
      this.$message.success(`清除了 ${keys.length} 个测试项目`)
    },

    // 清除所有缓存
    clearAllCaches() {
      // 清除内存缓存
      if (cacheManager.clear) {
        cacheManager.clear()
      }

      // 清除测试相关的LocalStorage
      const localKeys = Object.keys(this.localStorageItems).filter(key => key.startsWith('test_'))
      localKeys.forEach(key => localStorage.removeItem(key))

      // 清除测试相关的SessionStorage
      const sessionKeys = Object.keys(this.sessionStorageItems).filter(key => key.startsWith('test_'))
      sessionKeys.forEach(key => sessionStorage.removeItem(key))

      this.refreshCacheStatus()
      this.$message.success('所有测试缓存已清除')
    },

    // 渲染性能图表
    renderPerformanceChart() {
      if (!this.performanceResult || !this.$refs.performanceChart) return

      // 这里可以使用ECharts等图表库渲染性能分布图
      console.log('渲染缓存性能图表:', this.performanceResult.results)
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../styles/mobile/index.less';

.cache-test {
  .test-header {
    .d-flex();
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-lg;

    h2 {
      margin: 0;
      color: @text-color;
    }

    .test-controls {
      .d-flex();
      gap: @spacing-sm;
    }
  }

  .test-content {
    .test-card {
      margin-bottom: @spacing-lg;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .cache-overview {
    .cache-details {
      margin-top: @spacing-lg;

      .cache-detail-card {
        padding: @spacing-md;
        background: @background-color-light;
        border-radius: @border-radius-base;
        text-align: center;

        h4 {
          margin: 0 0 @spacing-sm 0;
          color: @text-color;
        }

        p {
          margin: @spacing-xs 0;
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }

  .cache-test-section {
    .test-controls-inline {
      .d-flex();
      gap: @spacing-sm;
      margin-bottom: @spacing-lg;
      flex-wrap: wrap;
    }

    .test-result {
      margin-bottom: @spacing-lg;
    }

    .cache-operations {
      margin-top: @spacing-lg;
      padding: @spacing-lg;
      background: @background-color-light;
      border-radius: @border-radius-base;

      h4 {
        margin: 0 0 @spacing-md 0;
        color: @text-color;
      }

      .operation-controls {
        .d-flex();
        gap: @spacing-sm;
        margin-bottom: @spacing-md;
        flex-wrap: wrap;
      }

      .operation-result {
        pre {
          background: white;
          padding: @spacing-sm;
          border-radius: @border-radius-sm;
          font-size: @font-size-sm;
          max-height: 200px;
          overflow-y: auto;
          border: 1px solid @border-color-light;
        }
      }
    }

    .storage-viewer {
      margin-top: @spacing-lg;

      h4 {
        margin: 0 0 @spacing-md 0;
        color: @text-color;
      }

      .storage-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid @border-color-light;
        border-radius: @border-radius-base;

        .storage-item {
          .d-flex();
          align-items: center;
          padding: @spacing-sm @spacing-md;
          border-bottom: 1px solid @border-color-light;

          &:last-child {
            border-bottom: none;
          }

          .storage-key {
            flex: 0 0 150px;
            font-weight: @font-weight-medium;
            color: @text-color;
            font-family: monospace;
          }

          .storage-value {
            flex: 1;
            font-size: @font-size-sm;
            color: @text-color-secondary;
            margin: 0 @spacing-sm;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .storage-size {
            flex: 0 0 60px;
            font-size: @font-size-sm;
            color: @text-color-secondary;
            text-align: right;
          }
        }
      }
    }

    .performance-config {
      margin-bottom: @spacing-lg;
      padding: @spacing-lg;
      background: @background-color-light;
      border-radius: @border-radius-base;
    }

    .performance-result {
      margin-top: @spacing-lg;

      .performance-stats {
        margin-bottom: @spacing-lg;
      }

      .chart-container {
        height: 300px;
        border: 1px solid @border-color-light;
        border-radius: @border-radius-base;
        background: white;
      }
    }
  }

  .test-summary {
    .summary-stats {
      .d-flex();
      justify-content: space-around;
      margin-bottom: @spacing-lg;
    }

    .summary-details {
      .ant-table {
        font-size: @font-size-sm;
      }
    }
  }
}
</style>