/**
 * 响应式指令 (Responsive Directives)
 * 提供基于断点的显示/隐藏和样式控制指令
 */

import mobileAdapter from '@/utils/mobile-adapter'

/**
 * v-show-on 指令
 * 在指定断点显示元素
 * 用法: v-show-on="'md+'" 或 v-show-on="['md', 'lg']"
 */
export const showOn = {
  bind(el, binding) {
    el._showOnUpdate = () => {
      const condition = binding.value
      const shouldShow = checkBreakpointCondition(condition)
      
      if (shouldShow) {
        el.style.display = el._originalDisplay || ''
      } else {
        if (!el._originalDisplay) {
          el._originalDisplay = el.style.display || ''
        }
        el.style.display = 'none'
      }
    }
    
    // 初始检查
    el._showOnUpdate()
    
    // 监听变化
    window.addEventListener('resize', el._showOnUpdate)
    window.addEventListener('mobileadapter:refresh', el._showOnUpdate)
  },
  
  update(el, binding) {
    if (binding.value !== binding.oldValue) {
      el._showOnUpdate()
    }
  },
  
  unbind(el) {
    if (el._showOnUpdate) {
      window.removeEventListener('resize', el._showOnUpdate)
      window.removeEventListener('mobileadapter:refresh', el._showOnUpdate)
    }
  }
}

/**
 * v-hide-on 指令
 * 在指定断点隐藏元素
 * 用法: v-hide-on="'xs'" 或 v-hide-on="['xs', 'sm']"
 */
export const hideOn = {
  bind(el, binding) {
    el._hideOnUpdate = () => {
      const condition = binding.value
      const shouldHide = checkBreakpointCondition(condition)
      
      if (shouldHide) {
        if (!el._originalDisplay) {
          el._originalDisplay = el.style.display || ''
        }
        el.style.display = 'none'
      } else {
        el.style.display = el._originalDisplay || ''
      }
    }
    
    // 初始检查
    el._hideOnUpdate()
    
    // 监听变化
    window.addEventListener('resize', el._hideOnUpdate)
    window.addEventListener('mobileadapter:refresh', el._hideOnUpdate)
  },
  
  update(el, binding) {
    if (binding.value !== binding.oldValue) {
      el._hideOnUpdate()
    }
  },
  
  unbind(el) {
    if (el._hideOnUpdate) {
      window.removeEventListener('resize', el._hideOnUpdate)
      window.removeEventListener('mobileadapter:refresh', el._hideOnUpdate)
    }
  }
}

/**
 * v-responsive-class 指令
 * 根据断点动态添加CSS类
 * 用法: v-responsive-class="{ 'mobile-style': 'xs-sm', 'desktop-style': 'md+' }"
 */
export const responsiveClass = {
  bind(el, binding) {
    el._responsiveClassUpdate = () => {
      const config = binding.value || {}
      
      // 移除之前添加的类
      if (el._addedClasses) {
        el._addedClasses.forEach(className => {
          el.classList.remove(className)
        })
      }
      
      el._addedClasses = []
      
      // 检查每个类的条件
      Object.keys(config).forEach(className => {
        const condition = config[className]
        if (checkBreakpointCondition(condition)) {
          el.classList.add(className)
          el._addedClasses.push(className)
        }
      })
    }
    
    // 初始检查
    el._responsiveClassUpdate()
    
    // 监听变化
    window.addEventListener('resize', el._responsiveClassUpdate)
    window.addEventListener('mobileadapter:refresh', el._responsiveClassUpdate)
  },
  
  update(el, binding) {
    if (JSON.stringify(binding.value) !== JSON.stringify(binding.oldValue)) {
      el._responsiveClassUpdate()
    }
  },
  
  unbind(el) {
    if (el._responsiveClassUpdate) {
      window.removeEventListener('resize', el._responsiveClassUpdate)
      window.removeEventListener('mobileadapter:refresh', el._responsiveClassUpdate)
    }
    
    // 清理添加的类
    if (el._addedClasses) {
      el._addedClasses.forEach(className => {
        el.classList.remove(className)
      })
    }
  }
}

/**
 * v-responsive-style 指令
 * 根据断点动态应用样式
 * 用法: v-responsive-style="{ fontSize: { xs: '12px', md: '14px', lg: '16px' } }"
 */
export const responsiveStyle = {
  bind(el, binding) {
    el._responsiveStyleUpdate = () => {
      const config = binding.value || {}
      const currentBreakpoint = mobileAdapter.getCurrentBreakpoint()
      const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']
      const currentIndex = breakpoints.indexOf(currentBreakpoint)
      
      // 应用样式
      Object.keys(config).forEach(property => {
        const value = config[property]
        
        if (typeof value === 'object' && value !== null) {
          // 响应式值对象
          let appliedValue = null
          
          // 从当前断点向下查找可用值
          for (let i = currentIndex; i >= 0; i--) {
            const bp = breakpoints[i]
            if (value[bp] !== undefined) {
              appliedValue = value[bp]
              break
            }
          }
          
          if (appliedValue !== null) {
            el.style[property] = appliedValue
          }
        } else {
          // 普通值
          el.style[property] = value
        }
      })
    }
    
    // 初始应用
    el._responsiveStyleUpdate()
    
    // 监听变化
    window.addEventListener('resize', el._responsiveStyleUpdate)
    window.addEventListener('mobileadapter:refresh', el._responsiveStyleUpdate)
  },
  
  update(el, binding) {
    if (JSON.stringify(binding.value) !== JSON.stringify(binding.oldValue)) {
      el._responsiveStyleUpdate()
    }
  },
  
  unbind(el) {
    if (el._responsiveStyleUpdate) {
      window.removeEventListener('resize', el._responsiveStyleUpdate)
      window.removeEventListener('mobileadapter:refresh', el._responsiveStyleUpdate)
    }
  }
}

/**
 * v-mobile-only 指令
 * 仅在移动设备显示
 */
export const mobileOnly = {
  bind(el) {
    el._mobileOnlyUpdate = () => {
      if (mobileAdapter.isMobile()) {
        el.style.display = el._originalDisplay || ''
      } else {
        if (!el._originalDisplay) {
          el._originalDisplay = el.style.display || ''
        }
        el.style.display = 'none'
      }
    }
    
    el._mobileOnlyUpdate()
    window.addEventListener('resize', el._mobileOnlyUpdate)
    window.addEventListener('mobileadapter:refresh', el._mobileOnlyUpdate)
  },
  
  unbind(el) {
    if (el._mobileOnlyUpdate) {
      window.removeEventListener('resize', el._mobileOnlyUpdate)
      window.removeEventListener('mobileadapter:refresh', el._mobileOnlyUpdate)
    }
  }
}

/**
 * v-desktop-only 指令
 * 仅在桌面设备显示
 */
export const desktopOnly = {
  bind(el) {
    el._desktopOnlyUpdate = () => {
      if (mobileAdapter.isDesktop()) {
        el.style.display = el._originalDisplay || ''
      } else {
        if (!el._originalDisplay) {
          el._originalDisplay = el.style.display || ''
        }
        el.style.display = 'none'
      }
    }
    
    el._desktopOnlyUpdate()
    window.addEventListener('resize', el._desktopOnlyUpdate)
    window.addEventListener('mobileadapter:refresh', el._desktopOnlyUpdate)
  },
  
  unbind(el) {
    if (el._desktopOnlyUpdate) {
      window.removeEventListener('resize', el._desktopOnlyUpdate)
      window.removeEventListener('mobileadapter:refresh', el._desktopOnlyUpdate)
    }
  }
}

/**
 * 检查断点条件
 */
function checkBreakpointCondition(condition) {
  if (!condition) return false
  
  const currentBreakpoint = mobileAdapter.getCurrentBreakpoint()
  const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']
  const currentIndex = breakpoints.indexOf(currentBreakpoint)
  
  if (Array.isArray(condition)) {
    // 数组形式: ['xs', 'sm', 'md']
    return condition.includes(currentBreakpoint)
  } else if (typeof condition === 'string') {
    if (condition.includes('+')) {
      // 'md+' 表示 md 及以上
      const bp = condition.replace('+', '')
      const targetIndex = breakpoints.indexOf(bp)
      return currentIndex >= targetIndex
    } else if (condition.includes('-') && !condition.includes('+')) {
      if (condition.endsWith('-')) {
        // 'lg-' 表示 lg 及以下
        const bp = condition.replace('-', '')
        const targetIndex = breakpoints.indexOf(bp)
        return currentIndex <= targetIndex
      } else {
        // 'sm-md' 表示 sm 到 md 之间
        const [start, end] = condition.split('-')
        const startIndex = breakpoints.indexOf(start)
        const endIndex = breakpoints.indexOf(end)
        return currentIndex >= startIndex && currentIndex <= endIndex
      }
    } else {
      // 精确匹配
      return currentBreakpoint === condition
    }
  }
  
  return false
}

// 安装所有指令的函数
export function install(Vue) {
  Vue.directive('show-on', showOn)
  Vue.directive('hide-on', hideOn)
  Vue.directive('responsive-class', responsiveClass)
  Vue.directive('responsive-style', responsiveStyle)
  Vue.directive('mobile-only', mobileOnly)
  Vue.directive('desktop-only', desktopOnly)
}

// 默认导出
export default {
  install,
  showOn,
  hideOn,
  responsiveClass,
  responsiveStyle,
  mobileOnly,
  desktopOnly
}
