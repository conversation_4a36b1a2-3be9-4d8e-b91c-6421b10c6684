/**
 * 代码分割配置
 * 用于优化打包体积和加载性能
 */

/**
 * 路由级别的代码分割配置
 * 将不同的页面分割到不同的chunk中
 */
export const routeChunks = {
  // 认证相关页面
  auth: [
    'mobile-login',
    'mobile-register', 
    'mobile-forgot-password'
  ],
  
  // 核心业务页面
  dashboard: ['mobile-dashboard'],
  material: ['mobile-material'],
  orders: ['mobile-orders'],
  inventory: ['mobile-inventory'],
  
  // 管理页面
  management: [
    'mobile-customer',
    'mobile-supplier',
    'mobile-finance',
    'mobile-report',
    'mobile-settings'
  ],
  
  // 详情页面
  detail: [
    'mobile-material-detail',
    'mobile-order-detail',
    'mobile-customer-detail'
  ],
  
  // 表单页面
  form: [
    'mobile-material-form',
    'mobile-order-form'
  ]
}

/**
 * 组件级别的代码分割配置
 * 将大型组件分割到独立的chunk中
 */
export const componentChunks = {
  // 图表组件
  charts: [
    'ECharts',
    'Chart',
    'LineChart',
    'BarChart',
    'PieChart'
  ],
  
  // 编辑器组件
  editors: [
    'RichTextEditor',
    'CodeEditor',
    'MarkdownEditor'
  ],
  
  // 上传组件
  upload: [
    'FileUpload',
    'ImageUpload',
    'VideoUpload'
  ],
  
  // 选择器组件
  selectors: [
    'MaterialSelector',
    'CustomerSelector',
    'SupplierSelector',
    'WarehouseSelector'
  ],
  
  // 表单组件
  forms: [
    'MaterialForm',
    'OrderForm',
    'InventoryForm',
    'BatchOperationForm'
  ]
}

/**
 * 第三方库的代码分割配置
 * 将第三方库分割到vendor chunk中
 */
export const vendorChunks = {
  // Vue生态系统
  vue: [
    'vue',
    'vue-router',
    'vuex'
  ],
  
  // UI组件库
  antd: [
    'ant-design-vue'
  ],
  
  // 工具库
  utils: [
    'lodash',
    'moment',
    'dayjs',
    'axios'
  ],
  
  // 图表库
  charts: [
    'echarts',
    'echarts/lib'
  ],
  
  // 其他大型库
  others: [
    'crypto-js',
    'jszip',
    'xlsx'
  ]
}

/**
 * 动态导入配置
 * 用于配置动态导入的选项
 */
export const dynamicImportConfig = {
  // 预加载配置
  preload: {
    // 预加载核心页面
    core: ['dashboard', 'material', 'orders'],
    
    // 预加载常用组件
    components: ['MaterialSelector', 'MobileForm']
  },
  
  // 预获取配置
  prefetch: {
    // 预获取管理页面
    management: ['customer', 'supplier', 'finance'],
    
    // 预获取详情页面
    detail: ['material-detail', 'order-detail']
  },
  
  // 延迟加载配置
  lazy: {
    // 延迟加载的页面
    pages: ['report', 'settings'],
    
    // 延迟加载的组件
    components: ['RichTextEditor', 'CodeEditor']
  }
}

/**
 * Webpack代码分割配置生成器
 */
export function generateWebpackSplitChunks() {
  return {
    chunks: 'all',
    minSize: 20000,
    maxSize: 244000,
    minChunks: 1,
    maxAsyncRequests: 30,
    maxInitialRequests: 30,
    cacheGroups: {
      // 默认配置
      default: {
        minChunks: 2,
        priority: -20,
        reuseExistingChunk: true
      },
      
      // Vue核心库
      vue: {
        test: /[\\/]node_modules[\\/](vue|vue-router|vuex)[\\/]/,
        name: 'chunk-vue',
        priority: 20,
        chunks: 'all'
      },
      
      // Ant Design Vue
      antd: {
        test: /[\\/]node_modules[\\/]ant-design-vue[\\/]/,
        name: 'chunk-antd',
        priority: 15,
        chunks: 'all'
      },
      
      // 图表库
      charts: {
        test: /[\\/]node_modules[\\/]echarts[\\/]/,
        name: 'chunk-charts',
        priority: 15,
        chunks: 'all'
      },
      
      // 工具库
      utils: {
        test: /[\\/]node_modules[\\/](lodash|moment|dayjs|axios)[\\/]/,
        name: 'chunk-utils',
        priority: 10,
        chunks: 'all'
      },
      
      // 其他第三方库
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'chunk-vendor',
        priority: 5,
        chunks: 'all'
      },
      
      // 公共组件
      common: {
        test: /[\\/]src[\\/]components[\\/]/,
        name: 'chunk-common',
        priority: 5,
        chunks: 'all',
        minChunks: 2
      }
    }
  }
}

/**
 * 路由懒加载配置生成器
 */
export function generateLazyRoutes() {
  const lazyRoutes = {}
  
  Object.entries(routeChunks).forEach(([chunkName, routes]) => {
    routes.forEach(route => {
      lazyRoutes[route] = {
        component: () => import(
          /* webpackChunkName: "[request]" */
          /* webpackPreload: true */
          `@/views/mobile/${getRouteComponent(route)}`
        ),
        chunkName: `chunk-${chunkName}`
      }
    })
  })
  
  return lazyRoutes
}

/**
 * 组件懒加载配置生成器
 */
export function generateLazyComponents() {
  const lazyComponents = {}
  
  Object.entries(componentChunks).forEach(([chunkName, components]) => {
    components.forEach(component => {
      lazyComponents[component] = {
        component: () => import(
          /* webpackChunkName: "[request]" */
          `@/components/${getComponentPath(component)}`
        ),
        chunkName: `chunk-${chunkName}`
      }
    })
  })
  
  return lazyComponents
}

/**
 * 获取路由组件路径
 */
function getRouteComponent(routeName) {
  const routeMap = {
    'mobile-dashboard': 'Dashboard',
    'mobile-material': 'Material',
    'mobile-orders': 'Orders',
    'mobile-inventory': 'Inventory',
    'mobile-customer': 'Customer',
    'mobile-supplier': 'Supplier',
    'mobile-finance': 'Finance',
    'mobile-report': 'Report',
    'mobile-settings': 'Settings',
    'mobile-login': 'auth/Login',
    'mobile-register': 'auth/Register',
    'mobile-forgot-password': 'auth/ForgotPassword',
    'mobile-material-detail': 'detail/MaterialDetail',
    'mobile-order-detail': 'detail/OrderDetail',
    'mobile-customer-detail': 'detail/CustomerDetail',
    'mobile-material-form': 'form/MaterialForm',
    'mobile-order-form': 'form/OrderForm'
  }
  
  return routeMap[routeName] || routeName
}

/**
 * 获取组件路径
 */
function getComponentPath(componentName) {
  const componentMap = {
    'MaterialSelector': 'mobile/MaterialSelector',
    'MaterialForm': 'mobile/MaterialForm',
    'OrderForm': 'mobile/OrderForm',
    'InventoryForm': 'mobile/InventoryForm',
    'BatchOperationForm': 'mobile/BatchOperationForm',
    'ECharts': 'charts/ECharts',
    'Chart': 'charts/Chart',
    'LineChart': 'charts/LineChart',
    'BarChart': 'charts/BarChart',
    'PieChart': 'charts/PieChart',
    'RichTextEditor': 'editor/RichTextEditor',
    'CodeEditor': 'editor/CodeEditor',
    'MarkdownEditor': 'editor/MarkdownEditor',
    'FileUpload': 'upload/FileUpload',
    'ImageUpload': 'upload/ImageUpload',
    'VideoUpload': 'upload/VideoUpload'
  }
  
  return componentMap[componentName] || componentName
}

/**
 * 性能预算配置
 */
export const performanceBudget = {
  // 初始加载预算
  initial: {
    js: 250000,      // 250KB
    css: 50000,      // 50KB
    total: 300000    // 300KB
  },
  
  // 异步加载预算
  async: {
    js: 100000,      // 100KB
    css: 20000,      // 20KB
    total: 120000    // 120KB
  },
  
  // 单个chunk预算
  chunk: {
    vendor: 200000,  // 200KB
    common: 50000,   // 50KB
    page: 30000      // 30KB
  }
}

/**
 * 缓存配置
 */
export const cacheConfig = {
  // 长期缓存的资源
  longTerm: [
    'chunk-vue',
    'chunk-antd',
    'chunk-vendor',
    'chunk-utils'
  ],
  
  // 短期缓存的资源
  shortTerm: [
    'chunk-common',
    'chunk-dashboard',
    'chunk-material'
  ],
  
  // 不缓存的资源
  noCache: [
    'chunk-auth',
    'chunk-settings'
  ]
}
