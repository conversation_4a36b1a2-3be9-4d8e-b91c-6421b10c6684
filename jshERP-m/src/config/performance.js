/**
 * 性能优化配置
 * 整合所有性能优化策略和配置
 */

import { generateWebpackSplitChunks, performanceBudget } from './code-splitting'

/**
 * 懒加载配置
 */
export const lazyLoadConfig = {
  // 图片懒加载配置
  image: {
    // 预加载距离（像素）
    preloadDistance: 100,
    
    // 占位图
    placeholder: '/static/images/placeholder.png',
    
    // 错误图片
    errorImage: '/static/images/error.png',
    
    // 加载动画
    loading: true,
    
    // 淡入效果
    fadeIn: true,
    
    // 支持的图片格式
    supportedFormats: ['jpg', 'jpeg', 'png', 'webp', 'gif']
  },
  
  // 组件懒加载配置
  component: {
    // 延迟时间（毫秒）
    delay: 200,
    
    // 加载状态组件
    loading: () => import('@/components/common/Loading'),
    
    // 错误状态组件
    error: () => import('@/components/common/Error'),
    
    // 超时时间
    timeout: 10000
  },
  
  // 路由懒加载配置
  route: {
    // 预加载策略
    preload: ['dashboard', 'material', 'orders'],
    
    // 预获取策略
    prefetch: ['inventory', 'customer', 'supplier'],
    
    // 延迟加载策略
    lazy: ['report', 'settings', 'finance']
  }
}

/**
 * 虚拟滚动配置
 */
export const virtualScrollConfig = {
  // 默认配置
  default: {
    itemHeight: 80,
    containerHeight: 400,
    bufferSize: 5,
    threshold: 100 // 超过100项启用虚拟滚动
  },
  
  // 不同场景的配置
  scenarios: {
    // 商品列表
    material: {
      itemHeight: 120,
      containerHeight: 500,
      bufferSize: 8
    },
    
    // 订单列表
    order: {
      itemHeight: 100,
      containerHeight: 450,
      bufferSize: 6
    },
    
    // 库存列表
    inventory: {
      itemHeight: 90,
      containerHeight: 400,
      bufferSize: 5
    },
    
    // 客户列表
    customer: {
      itemHeight: 80,
      containerHeight: 350,
      bufferSize: 4
    }
  }
}

/**
 * 缓存策略配置
 */
export const cacheStrategyConfig = {
  // API缓存配置
  api: {
    // 短期缓存（5分钟）
    shortTerm: {
      ttl: 300000,
      keys: [
        'material_list',
        'order_list',
        'inventory_list'
      ]
    },
    
    // 中期缓存（30分钟）
    mediumTerm: {
      ttl: 1800000,
      keys: [
        'customer_list',
        'supplier_list',
        'warehouse_list'
      ]
    },
    
    // 长期缓存（2小时）
    longTerm: {
      ttl: 7200000,
      keys: [
        'system_config',
        'user_permissions',
        'menu_data'
      ]
    }
  },
  
  // 页面状态缓存配置
  pageState: {
    ttl: 1800000, // 30分钟
    keys: [
      'search_filters',
      'sort_options',
      'pagination_state',
      'selected_items'
    ]
  },
  
  // 静态资源缓存配置
  staticResource: {
    ttl: 86400000, // 24小时
    keys: [
      'images',
      'icons',
      'fonts',
      'css',
      'js'
    ]
  }
}

/**
 * 网络优化配置
 */
export const networkOptimizationConfig = {
  // 请求合并配置
  requestBatching: {
    enabled: true,
    batchSize: 10,
    delay: 100 // 100ms内的请求合并
  },
  
  // 请求去重配置
  requestDeduplication: {
    enabled: true,
    ttl: 5000 // 5秒内相同请求去重
  },
  
  // 请求重试配置
  requestRetry: {
    enabled: true,
    maxRetries: 3,
    retryDelay: 1000,
    retryCondition: (error) => {
      return error.code === 'NETWORK_ERROR' || error.status >= 500
    }
  },
  
  // 请求超时配置
  requestTimeout: {
    default: 10000, // 10秒
    upload: 60000,  // 上传60秒
    download: 30000 // 下载30秒
  }
}

/**
 * 渲染优化配置
 */
export const renderOptimizationConfig = {
  // 防抖配置
  debounce: {
    search: 300,
    resize: 100,
    scroll: 16
  },
  
  // 节流配置
  throttle: {
    scroll: 16,
    mousemove: 16,
    touchmove: 16
  },
  
  // 虚拟DOM优化
  virtualDOM: {
    // 使用key优化列表渲染
    useKey: true,
    
    // 避免内联对象和函数
    avoidInlineObjects: true,
    
    // 使用v-show替代v-if（频繁切换）
    preferVShow: true
  },
  
  // 组件优化
  component: {
    // 使用函数式组件
    functional: ['Icon', 'Tag', 'Badge'],
    
    // 使用异步组件
    async: ['Chart', 'Editor', 'Upload'],
    
    // 使用keep-alive缓存
    keepAlive: ['Dashboard', 'Material', 'Order']
  }
}

/**
 * 内存优化配置
 */
export const memoryOptimizationConfig = {
  // 内存监控配置
  monitoring: {
    enabled: true,
    interval: 30000, // 30秒检查一次
    threshold: 0.8   // 80%内存使用率告警
  },
  
  // 垃圾回收配置
  garbageCollection: {
    // 定时清理
    interval: 300000, // 5分钟
    
    // 清理策略
    strategies: [
      'unused_cache',
      'expired_data',
      'detached_listeners'
    ]
  },
  
  // 内存泄漏预防
  leakPrevention: {
    // 自动解绑事件监听器
    autoUnbindListeners: true,
    
    // 清理定时器
    clearTimers: true,
    
    // 清理观察者
    clearObservers: true
  }
}

/**
 * 移动端特定优化配置
 */
export const mobileOptimizationConfig = {
  // 触摸优化
  touch: {
    // 快速点击处理
    fastClick: true,
    
    // 触摸反馈
    touchFeedback: true,
    
    // 滑动优化
    smoothScroll: true
  },
  
  // 视口优化
  viewport: {
    // 禁用缩放
    disableZoom: true,
    
    // 固定视口
    fixedViewport: true,
    
    // 安全区域适配
    safeArea: true
  },
  
  // 电池优化
  battery: {
    // 降低动画频率
    reducedAnimation: true,
    
    // 暂停后台任务
    pauseBackgroundTasks: true,
    
    // 优化网络请求
    optimizeNetworkRequests: true
  }
}

/**
 * 性能监控配置
 */
export const performanceMonitoringConfig = {
  // 核心指标监控
  coreMetrics: {
    // First Contentful Paint
    fcp: { threshold: 2000 },
    
    // Largest Contentful Paint
    lcp: { threshold: 4000 },
    
    // First Input Delay
    fid: { threshold: 100 },
    
    // Cumulative Layout Shift
    cls: { threshold: 0.1 }
  },
  
  // 自定义指标监控
  customMetrics: {
    // 页面加载时间
    pageLoadTime: { threshold: 3000 },
    
    // API响应时间
    apiResponseTime: { threshold: 1000 },
    
    // 组件渲染时间
    componentRenderTime: { threshold: 100 }
  },
  
  // 报告配置
  reporting: {
    // 报告间隔
    interval: 60000, // 1分钟
    
    // 批量大小
    batchSize: 50,
    
    // 发送端点
    endpoint: '/api/performance/report'
  }
}

/**
 * 获取完整的性能优化配置
 */
export function getPerformanceConfig() {
  return {
    lazyLoad: lazyLoadConfig,
    virtualScroll: virtualScrollConfig,
    cache: cacheStrategyConfig,
    network: networkOptimizationConfig,
    render: renderOptimizationConfig,
    memory: memoryOptimizationConfig,
    mobile: mobileOptimizationConfig,
    monitoring: performanceMonitoringConfig,
    webpack: {
      splitChunks: generateWebpackSplitChunks(),
      performanceBudget
    }
  }
}

/**
 * 应用性能优化配置
 */
export function applyPerformanceOptimizations(app) {
  const config = getPerformanceConfig()
  
  // 应用懒加载配置
  if (config.lazyLoad.component.loading) {
    app.config.globalProperties.$loadingComponent = config.lazyLoad.component.loading
  }
  
  // 应用缓存配置
  if (app.config.globalProperties.$cache) {
    app.config.globalProperties.$cache.configure(config.cache)
  }
  
  // 应用性能监控
  if (app.config.globalProperties.$performance) {
    app.config.globalProperties.$performance.configure(config.monitoring)
  }
  
  return config
}

export default {
  getPerformanceConfig,
  applyPerformanceOptimizations,
  lazyLoadConfig,
  virtualScrollConfig,
  cacheStrategyConfig,
  networkOptimizationConfig,
  renderOptimizationConfig,
  memoryOptimizationConfig,
  mobileOptimizationConfig,
  performanceMonitoringConfig
}
