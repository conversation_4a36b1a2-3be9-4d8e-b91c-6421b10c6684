/**
 * 移动端专用路由配置
 * 针对移动端优化的路由结构
 */

import { MobileLayout, MobileTabLayout } from '@/components/layouts/mobile'

/**
 * 移动端专用路由
 * 这些路由专门为移动端设计，提供更好的移动体验
 */
export const mobileRouterMap = [
  {
    path: '/mobile',
    name: 'mobile',
    component: MobileLayout,
    meta: { title: '移动端', requiresAuth: true },
    redirect: '/mobile/dashboard',
    children: [
      // 移动端仪表板
      {
        path: 'dashboard',
        name: 'mobile-dashboard',
        component: () => import(/* webpackChunkName: "mobile-dashboard" */ '@/views/mobile/Dashboard'),
        meta: { 
          title: '首页',
          icon: 'home',
          keepAlive: true
        }
      },
      
      // 移动端商品管理
      {
        path: 'material',
        name: 'mobile-material',
        component: () => import(/* webpackChunkName: "mobile-material" */ '@/views/mobile/Material'),
        meta: { 
          title: '商品管理',
          icon: 'shopping',
          keepAlive: true
        }
      },
      
      // 移动端订单管理
      {
        path: 'orders',
        name: 'mobile-orders',
        component: () => import(/* webpackChunkName: "mobile-orders" */ '@/views/mobile/Orders'),
        meta: {
          title: '订单管理',
          icon: 'file-text',
          keepAlive: true
        }
      },

      // 移动端库存管理
      {
        path: 'inventory',
        name: 'mobile-inventory',
        component: () => import(/* webpackChunkName: "mobile-inventory" */ '@/views/mobile/Inventory'),
        meta: {
          title: '库存管理',
          icon: 'database',
          keepAlive: true
        }
      },
      
      // 移动端客户管理
      {
        path: 'customer',
        name: 'mobile-customer',
        component: () => import(/* webpackChunkName: "mobile-customer" */ '@/views/mobile/Customer'),
        meta: { 
          title: '客户管理',
          icon: 'team',
          keepAlive: true
        }
      },
      
      // 移动端供应商管理
      {
        path: 'supplier',
        name: 'mobile-supplier',
        component: () => import(/* webpackChunkName: "mobile-supplier" */ '@/views/mobile/Supplier'),
        meta: { 
          title: '供应商管理',
          icon: 'contacts',
          keepAlive: true
        }
      },
      
      // 移动端财务管理
      {
        path: 'finance',
        name: 'mobile-finance',
        component: () => import(/* webpackChunkName: "mobile-finance" */ '@/views/mobile/Finance'),
        meta: { 
          title: '财务管理',
          icon: 'dollar',
          keepAlive: true
        }
      },
      
      // 移动端报表中心
      {
        path: 'report',
        name: 'mobile-report',
        component: () => import(/* webpackChunkName: "mobile-report" */ '@/views/mobile/Report'),
        meta: { 
          title: '报表中心',
          icon: 'bar-chart',
          keepAlive: true
        }
      },
      
      // 移动端设置
      {
        path: 'settings',
        name: 'mobile-settings',
        component: () => import(/* webpackChunkName: "mobile-settings" */ '@/views/mobile/Settings'),
        meta: {
          title: '设置',
          icon: 'setting',
          keepAlive: false
        }
      },

      // 测试中心（仅开发环境）
      {
        path: 'debug',
        name: 'mobile-debug',
        component: () => import(/* webpackChunkName: "mobile-debug" */ '@/views/debug/TestCenter'),
        meta: {
          title: '测试中心',
          icon: 'bug',
          keepAlive: false,
          hidden: process.env.NODE_ENV === 'production' // 生产环境隐藏
        }
      }
    ]
  },
  
  // 移动端用户认证路由
  {
    path: '/mobile-auth',
    name: 'mobile-auth',
    component: () => import(/* webpackChunkName: "mobile-auth" */ '@/components/layouts/mobile/MobileAuthLayout'),
    redirect: '/mobile-auth/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'mobile-login',
        component: () => import(/* webpackChunkName: "mobile-login" */ '@/views/mobile/auth/Login'),
        meta: { 
          title: '登录',
          hideHeader: true
        }
      },
      {
        path: 'register',
        name: 'mobile-register',
        component: () => import(/* webpackChunkName: "mobile-register" */ '@/views/mobile/auth/Register'),
        meta: { 
          title: '注册',
          hideHeader: true
        }
      },
      {
        path: 'forgot-password',
        name: 'mobile-forgot-password',
        component: () => import(/* webpackChunkName: "mobile-forgot" */ '@/views/mobile/auth/ForgotPassword'),
        meta: { 
          title: '忘记密码',
          hideHeader: true
        }
      }
    ]
  },
  
  // 移动端详情页路由
  {
    path: '/mobile-detail',
    name: 'mobile-detail',
    component: () => import(/* webpackChunkName: "mobile-detail" */ '@/components/layouts/mobile/MobileDetailLayout'),
    hidden: true,
    children: [
      // 商品详情
      {
        path: 'material/:id',
        name: 'mobile-material-detail',
        component: () => import(/* webpackChunkName: "mobile-material-detail" */ '@/views/mobile/detail/MaterialDetail'),
        meta: { 
          title: '商品详情',
          showBack: true
        }
      },
      
      // 订单详情
      {
        path: 'order/:id',
        name: 'mobile-order-detail',
        component: () => import(/* webpackChunkName: "mobile-order-detail" */ '@/views/mobile/detail/OrderDetail'),
        meta: { 
          title: '订单详情',
          showBack: true
        }
      },
      
      // 客户详情
      {
        path: 'customer/:id',
        name: 'mobile-customer-detail',
        component: () => import(/* webpackChunkName: "mobile-customer-detail" */ '@/views/mobile/detail/CustomerDetail'),
        meta: { 
          title: '客户详情',
          showBack: true
        }
      }
    ]
  },
  
  // 移动端表单页路由
  {
    path: '/mobile-form',
    name: 'mobile-form',
    component: () => import(/* webpackChunkName: "mobile-form" */ '@/components/layouts/mobile/MobileFormLayout'),
    hidden: true,
    children: [
      // 新增商品
      {
        path: 'material/add',
        name: 'mobile-material-add',
        component: () => import(/* webpackChunkName: "mobile-material-form" */ '@/views/mobile/form/MaterialForm'),
        meta: { 
          title: '新增商品',
          showBack: true,
          mode: 'add'
        }
      },
      
      // 编辑商品
      {
        path: 'material/edit/:id',
        name: 'mobile-material-edit',
        component: () => import(/* webpackChunkName: "mobile-material-form" */ '@/views/mobile/form/MaterialForm'),
        meta: { 
          title: '编辑商品',
          showBack: true,
          mode: 'edit'
        }
      },
      
      // 新增订单
      {
        path: 'order/add',
        name: 'mobile-order-add',
        component: () => import(/* webpackChunkName: "mobile-order-form" */ '@/views/mobile/form/OrderForm'),
        meta: { 
          title: '新增订单',
          showBack: true,
          mode: 'add'
        }
      },
      
      // 编辑订单
      {
        path: 'order/edit/:id',
        name: 'mobile-order-edit',
        component: () => import(/* webpackChunkName: "mobile-order-form" */ '@/views/mobile/form/OrderForm'),
        meta: { 
          title: '编辑订单',
          showBack: true,
          mode: 'edit'
        }
      }
    ]
  }
]

/**
 * 移动端路由配置选项
 */
export const mobileRouterOptions = {
  // 移动端专用的滚动行为
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    } else {
      // 移动端默认滚动到顶部
      return { x: 0, y: 0 }
    }
  },
  
  // 移动端路由模式
  mode: 'history',
  
  // 移动端基础路径
  base: process.env.BASE_URL || '/'
}

/**
 * 移动端导航菜单配置
 * 用于底部导航栏
 */
export const mobileNavigation = [
  {
    path: '/mobile/dashboard',
    name: 'dashboard',
    title: '首页',
    icon: 'home',
    badge: null
  },
  {
    path: '/mobile/material',
    name: 'material',
    title: '商品',
    icon: 'shopping',
    badge: null
  },
  {
    path: '/mobile/orders',
    name: 'orders',
    title: '订单',
    icon: 'file-text',
    badge: 'new'
  },
  {
    path: '/mobile/inventory',
    name: 'inventory',
    title: '库存',
    icon: 'database',
    badge: null
  },
  {
    path: '/mobile/settings',
    name: 'settings',
    title: '设置',
    icon: 'setting',
    badge: null
  }
]
