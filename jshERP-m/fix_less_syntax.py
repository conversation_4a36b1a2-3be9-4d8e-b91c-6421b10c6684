#!/usr/bin/env python3
"""
批量修复 responsive.less 文件中的语法错误
将所有的 @media 查询中的 }); 替换为 }
"""

import re

def fix_less_syntax():
    file_path = 'src/styles/mobile/responsive.less'
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 记录原始内容长度
        original_length = len(content)
        
        # 替换所有的 }); 为 }，但只在 @media 查询上下文中
        # 这个正则表达式会匹配 @media 查询中的 });
        pattern = r'(\s+@media[^{]*\{[^}]*)\}\);'
        content = re.sub(pattern, r'\1}', content)
        
        # 也处理普通的 }); 结尾（在行尾）
        content = re.sub(r'\s*\}\);\s*$', '}', content, flags=re.MULTILINE)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成！")
        print(f"📄 文件: {file_path}")
        print(f"📊 原始长度: {original_length} 字符")
        print(f"📊 修复后长度: {len(content)} 字符")
        
        # 验证是否还有 });
        remaining_errors = content.count('});')
        if remaining_errors > 0:
            print(f"⚠️  仍有 {remaining_errors} 个 '}}); ' 需要手动检查")
        else:
            print("🎉 所有 '}}); ' 语法错误已修复！")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    fix_less_syntax()
