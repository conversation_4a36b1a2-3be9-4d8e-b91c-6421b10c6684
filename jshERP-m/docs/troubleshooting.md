# jshERP移动端问题排查指南

## 概述

本文档提供了jshERP移动端常见问题的排查方法、解决方案和预防措施，帮助开发者和运维人员快速定位和解决问题。

## 问题分类

### 🚀 启动和构建问题
- [项目启动失败](#项目启动失败)
- [构建失败](#构建失败)
- [依赖安装问题](#依赖安装问题)

### 🌐 网络和API问题
- [API请求失败](#api请求失败)
- [跨域问题](#跨域问题)
- [认证失败](#认证失败)

### 📱 移动端适配问题
- [样式显示异常](#样式显示异常)
- [触摸事件问题](#触摸事件问题)
- [兼容性问题](#兼容性问题)

### ⚡ 性能问题
- [页面加载缓慢](#页面加载缓慢)
- [内存泄漏](#内存泄漏)
- [滚动卡顿](#滚动卡顿)

### 🐛 运行时错误
- [JavaScript错误](#javascript错误)
- [Vue组件错误](#vue组件错误)
- [路由问题](#路由问题)

## 启动和构建问题

### 项目启动失败

#### 问题描述
运行`npm run dev`时项目无法启动

#### 常见原因和解决方案

**1. Node.js版本不兼容**
```bash
# 检查Node.js版本
node --version

# 推荐使用Node.js 16.x或更高版本
# 使用nvm切换版本
nvm install 16
nvm use 16
```

**2. 端口被占用**
```bash
# 检查端口占用
lsof -i :8899

# 杀死占用进程
kill -9 <PID>

# 或者修改端口
# 在.env.development中修改
VUE_APP_PORT=8900
```

**3. 依赖缺失或版本冲突**
```bash
# 清除依赖缓存
rm -rf node_modules
rm package-lock.json

# 重新安装依赖
npm install

# 或使用yarn
yarn install
```

**4. 环境变量配置错误**
```bash
# 检查环境变量文件
cat .env.development

# 确保必要的环境变量存在
VUE_APP_API_BASE_URL=http://localhost:9999
VUE_APP_TITLE=jshERP移动端
```

### 构建失败

#### 问题描述
运行`npm run build`时构建失败

#### 常见错误和解决方案

**1. 内存不足**
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

# 或在package.json中配置
"build": "NODE_OPTIONS=--max-old-space-size=4096 vue-cli-service build"
```

**2. 语法错误**
```bash
# 运行ESLint检查
npm run lint

# 自动修复可修复的问题
npm run lint:fix
```

**3. 类型错误（TypeScript）**
```bash
# 检查TypeScript错误
npx tsc --noEmit

# 查看详细错误信息
npx vue-cli-service build --report
```

### 依赖安装问题

#### 问题描述
`npm install`或`yarn install`失败

#### 解决方案

**1. 网络问题**
```bash
# 使用国内镜像
npm config set registry https://registry.npmmirror.com/

# 或使用yarn
yarn config set registry https://registry.npmmirror.com/
```

**2. 权限问题**
```bash
# 修复npm权限
sudo chown -R $(whoami) ~/.npm

# 或使用yarn
sudo chown -R $(whoami) ~/.yarn
```

**3. 缓存问题**
```bash
# 清除npm缓存
npm cache clean --force

# 清除yarn缓存
yarn cache clean
```

## 网络和API问题

### API请求失败

#### 问题描述
前端无法正常调用后端API

#### 排查步骤

**1. 检查网络连接**
```bash
# 测试后端服务是否可达
curl -I http://localhost:9999/api/health

# 检查DNS解析
nslookup your-api-domain.com
```

**2. 检查API配置**
```javascript
// 检查axios配置
// src/utils/request.js
console.log('API Base URL:', process.env.VUE_APP_API_BASE_URL)
console.log('Request Config:', config)
```

**3. 查看网络请求**
```javascript
// 在浏览器开发者工具中查看Network面板
// 或在代码中添加调试信息
axios.interceptors.request.use(config => {
  console.log('Request:', config)
  return config
})

axios.interceptors.response.use(
  response => {
    console.log('Response:', response)
    return response
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)
```

#### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 404 | 接口不存在 | 检查API路径是否正确 |
| 401 | 未授权 | 检查Token是否有效 |
| 403 | 权限不足 | 检查用户权限配置 |
| 500 | 服务器错误 | 查看后端日志 |
| CORS | 跨域错误 | 配置代理或后端CORS |

### 跨域问题

#### 问题描述
浏览器控制台显示CORS错误

#### 解决方案

**1. 开发环境代理配置**
```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:9999',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  }
}
```

**2. 生产环境Nginx配置**
```nginx
# nginx.conf
location /api {
    proxy_pass http://backend-server;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    # CORS配置
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
}
```

### 认证失败

#### 问题描述
用户登录后仍然提示未授权

#### 排查步骤

**1. 检查Token存储**
```javascript
// 检查localStorage中的token
console.log('Token:', localStorage.getItem('token'))

// 检查Token格式
const token = localStorage.getItem('token')
if (token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    console.log('Token Payload:', payload)
    console.log('Token Expires:', new Date(payload.exp * 1000))
  } catch (e) {
    console.error('Invalid Token Format')
  }
}
```

**2. 检查请求头**
```javascript
// 检查Authorization头是否正确设置
axios.interceptors.request.use(config => {
  console.log('Authorization Header:', config.headers.Authorization)
  return config
})
```

**3. 检查Token刷新机制**
```javascript
// src/utils/auth.js
export function refreshToken() {
  const refreshToken = localStorage.getItem('refreshToken')
  if (!refreshToken) {
    // 跳转到登录页
    router.push('/login')
    return
  }
  
  // 调用刷新接口
  return api.refreshToken(refreshToken)
}
```

## 移动端适配问题

### 样式显示异常

#### 问题描述
在移动设备上样式显示不正常

#### 常见问题和解决方案

**1. 视口配置问题**
```html
<!-- 确保index.html中有正确的viewport配置 -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

**2. CSS单位问题**
```less
// 使用相对单位而不是固定像素
.component {
  // 错误：使用固定像素
  font-size: 14px;
  padding: 10px;
  
  // 正确：使用相对单位
  font-size: 0.875rem; // 14px
  padding: 0.625rem;   // 10px
  
  // 或使用vw/vh单位
  width: 90vw;
  height: 50vh;
}
```

**3. 响应式断点问题**
```less
// 确保断点顺序正确
.component {
  // 基础样式
  padding: 16px;
  
  // 平板样式
  @media (max-width: 1024px) {
    padding: 12px;
  }
  
  // 手机样式
  @media (max-width: 768px) {
    padding: 8px;
  }
}
```

### 触摸事件问题

#### 问题描述
触摸事件不响应或响应异常

#### 解决方案

**1. 添加触摸事件监听**
```javascript
// 使用passive事件监听器
element.addEventListener('touchstart', handler, { passive: true })
element.addEventListener('touchmove', handler, { passive: true })
element.addEventListener('touchend', handler, { passive: true })
```

**2. 防止默认行为**
```javascript
// 阻止页面滚动
document.addEventListener('touchmove', (e) => {
  if (shouldPreventDefault) {
    e.preventDefault()
  }
}, { passive: false })
```

**3. 处理点击延迟**
```css
/* 消除300ms点击延迟 */
* {
  touch-action: manipulation;
}

/* 或使用FastClick库 */
```

### 兼容性问题

#### 问题描述
在某些移动浏览器中功能异常

#### 解决方案

**1. 添加Polyfill**
```javascript
// babel.config.js
module.exports = {
  presets: [
    ['@babel/preset-env', {
      useBuiltIns: 'usage',
      corejs: 3
    }]
  ]
}
```

**2. 检查浏览器支持**
```javascript
// 检查特定API支持
if ('serviceWorker' in navigator) {
  // 支持Service Worker
}

if ('IntersectionObserver' in window) {
  // 支持Intersection Observer
}
```

## 性能问题

### 页面加载缓慢

#### 问题描述
首屏加载时间过长

#### 排查和优化

**1. 分析构建产物**
```bash
# 生成构建分析报告
npm run build -- --report

# 查看bundle大小
npm run build:analyze
```

**2. 代码分割优化**
```javascript
// 路由懒加载
const routes = [
  {
    path: '/user',
    component: () => import(/* webpackChunkName: "user" */ '@/pages/user/UserManagement.vue')
  }
]

// 组件懒加载
export default {
  components: {
    HeavyComponent: () => import('@/components/HeavyComponent.vue')
  }
}
```

**3. 资源优化**
```javascript
// 图片懒加载
<img v-lazy="imageUrl" alt="description">

// 预加载关键资源
<link rel="preload" href="/critical.css" as="style">
<link rel="preload" href="/critical.js" as="script">
```

### 内存泄漏

#### 问题描述
长时间使用后页面卡顿，内存占用过高

#### 排查方法

**1. 使用Chrome DevTools**
```javascript
// 在代码中添加性能标记
performance.mark('component-start')
// ... 组件逻辑
performance.mark('component-end')
performance.measure('component-duration', 'component-start', 'component-end')
```

**2. 检查事件监听器**
```javascript
// 组件销毁时清理事件监听器
export default {
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  }
}
```

**3. 清理定时器**
```javascript
export default {
  data() {
    return {
      timer: null
    }
  },
  
  mounted() {
    this.timer = setInterval(() => {
      // 定时任务
    }, 1000)
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  }
}
```

## 调试工具和技巧

### 移动端调试

#### 1. 远程调试
```bash
# Chrome远程调试
# 1. 手机开启USB调试
# 2. 连接电脑
# 3. Chrome访问 chrome://inspect
# 4. 选择设备进行调试
```

#### 2. vConsole调试
```javascript
// main.js
import VConsole from 'vconsole'

if (process.env.NODE_ENV === 'development') {
  new VConsole()
}
```

#### 3. Eruda调试工具
```javascript
// 动态加载Eruda
if (process.env.NODE_ENV === 'development') {
  import('eruda').then(eruda => eruda.default.init())
}
```

### 性能监控

#### 1. 性能指标收集
```javascript
// 收集关键性能指标
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.entryType === 'navigation') {
      console.log('页面加载时间:', entry.loadEventEnd - entry.fetchStart)
    }
    
    if (entry.entryType === 'paint') {
      console.log(`${entry.name}:`, entry.startTime)
    }
  }
})

observer.observe({ entryTypes: ['navigation', 'paint'] })
```

#### 2. 错误监控
```javascript
// 全局错误捕获
window.addEventListener('error', (event) => {
  console.error('JavaScript Error:', event.error)
  // 上报错误信息
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason)
  // 上报错误信息
})
```

## 日志分析

### 前端日志

#### 1. 控制台日志
```javascript
// 分级日志
console.log('Info:', info)
console.warn('Warning:', warning)
console.error('Error:', error)

// 分组日志
console.group('API Request')
console.log('URL:', url)
console.log('Method:', method)
console.log('Data:', data)
console.groupEnd()
```

#### 2. 网络日志
```javascript
// 记录API请求
axios.interceptors.request.use(config => {
  console.log(`[${new Date().toISOString()}] API Request:`, {
    url: config.url,
    method: config.method,
    data: config.data
  })
  return config
})
```

### 后端日志

#### 查看后端日志
```bash
# Docker环境
docker logs jsherp-backend

# 实时查看日志
docker logs -f jsherp-backend

# 查看最近100行日志
docker logs --tail 100 jsherp-backend
```

## 常用排查命令

### 系统信息
```bash
# 查看系统信息
uname -a

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看进程
ps aux | grep node
```

### 网络诊断
```bash
# 测试网络连通性
ping api.example.com

# 测试端口连通性
telnet api.example.com 80

# 查看网络连接
netstat -an | grep :8899
```

### 应用诊断
```bash
# 查看Node.js进程
ps aux | grep node

# 查看端口占用
lsof -i :8899

# 查看文件句柄
lsof -p <pid>
```

## 预防措施

### 1. 代码质量
- 使用ESLint和Prettier保证代码质量
- 编写单元测试和集成测试
- 进行代码审查

### 2. 监控告警
- 设置性能监控
- 配置错误告警
- 定期检查系统状态

### 3. 文档维护
- 保持文档更新
- 记录已知问题和解决方案
- 分享最佳实践

## 联系支持

### 技术支持
- **紧急问题**: <EMAIL>
- **技术咨询**: <EMAIL>
- **Bug反馈**: <EMAIL>

### 相关资源
- **问题追踪**: https://issues.company.com
- **技术文档**: https://docs.company.com
- **社区论坛**: https://forum.company.com
