# jshERP移动端部署指南

## 概述

本文档详细介绍了jshERP移动端的部署配置和流程，包括开发环境、测试环境和生产环境的部署方案。

## 环境要求

### 基础环境
- Node.js 16.x 或更高版本
- npm 8.x 或 yarn 1.22.x
- Docker 20.x 或更高版本（可选）
- Nginx 1.20.x 或更高版本（生产环境）

### 系统要求
- 内存：最少 2GB，推荐 4GB
- 磁盘：最少 10GB 可用空间
- 网络：稳定的互联网连接

## 环境配置

### 1. 环境变量配置

项目支持多环境配置，通过不同的 `.env` 文件管理：

```bash
# 开发环境
.env.development

# 测试环境  
.env.test

# 生产环境
.env.production
```

#### 主要配置项

```bash
# API配置
VUE_APP_API_BASE_URL=https://your-api-domain.com
VUE_APP_API_TIMEOUT=30000

# 缓存配置
VUE_APP_CACHE_ENABLE=true
VUE_APP_CACHE_PREFIX=jsh_mobile_
VUE_APP_CACHE_EXPIRE=3600000

# 性能监控配置
VUE_APP_PERFORMANCE_ENABLE=true
VUE_APP_PERFORMANCE_SAMPLE_RATE=0.1
VUE_APP_PERFORMANCE_REPORT_URL=https://your-monitor-api.com/performance

# 错误监控配置
VUE_APP_ERROR_MONITORING_ENABLE=true
VUE_APP_ERROR_REPORT_URL=https://your-monitor-api.com/errors

# 告警配置
VUE_APP_ALERT_ENABLE=true
VUE_APP_ALERT_WEBHOOK_URL=https://your-webhook-url.com
VUE_APP_ALERT_EMAIL_URL=https://your-email-api.com
```

### 2. 构建配置

#### 开发环境构建
```bash
npm run build:dev
```

#### 测试环境构建
```bash
npm run build:test
```

#### 生产环境构建
```bash
npm run build:prod
```

#### 构建分析
```bash
npm run build:analyze
```

## 部署方案

### 方案一：传统部署

#### 1. 构建项目
```bash
# 安装依赖
npm install

# 构建生产版本
npm run build:prod
```

#### 2. 配置Nginx
```nginx
server {
    listen 80;
    server_name your-mobile-domain.com;
    root /var/www/jsh-erp-mobile/dist;
    index index.html;

    # 移动端优化
    add_header X-Device-Type "mobile";
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Cache-Status "HIT";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
    }

    # API代理
    location /api/ {
        proxy_pass http://your-backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移动端特殊头部
        proxy_set_header X-Device-Type "mobile";
        proxy_set_header X-Mobile-Version "1.0.0";
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

#### 3. 部署脚本
```bash
# 使用项目提供的部署脚本
./scripts/deploy.sh prod --build
```

### 方案二：Docker部署

#### 1. 构建Docker镜像
```bash
# 构建镜像
docker build -t jsh-erp-mobile:latest .

# 或使用npm脚本
npm run docker:build
```

#### 2. 运行容器
```bash
# 单容器运行
docker run -d -p 8899:80 --name jsh-erp-mobile jsh-erp-mobile:latest

# 或使用npm脚本
npm run docker:run
```

#### 3. Docker Compose部署
```bash
# 生产环境
docker-compose up -d

# 开发环境
docker-compose -f docker-compose.dev.yml up -d

# 或使用npm脚本
npm run docker:prod  # 生产环境
npm run docker:dev   # 开发环境
```

## CI/CD配置

### GitHub Actions

项目已配置GitHub Actions自动化流程：

1. **代码质量检查** - ESLint检查和代码格式化
2. **单元测试** - 运行测试套件
3. **多环境构建** - 构建开发、测试、生产版本
4. **Docker构建** - 构建和推送Docker镜像
5. **自动部署** - 部署到测试和生产环境

#### 触发条件
- Push到 `main` 分支：触发生产环境部署
- Push到 `develop` 分支：触发测试环境部署
- Pull Request：触发代码检查和构建测试

### 部署环境配置

#### 测试环境
- 分支：`develop`
- 域名：`test-mobile.your-domain.com`
- 自动部署：是
- 监控：启用

#### 生产环境
- 分支：`main`
- 域名：`mobile.your-domain.com`
- 自动部署：需要审批
- 监控：完整启用

## 监控和告警

### 性能监控

系统自动收集以下性能指标：
- 页面加载时间
- 资源加载性能
- 用户交互响应时间
- 内存使用情况
- 网络状态

### 错误监控

自动捕获和上报：
- JavaScript错误
- Promise拒绝
- 网络错误
- API错误
- 组件错误

### 告警配置

支持多种告警方式：
- Webhook通知
- 邮件通知
- 浏览器通知
- 控制台日志

#### 告警阈值
- 错误率 > 10%
- 响应时间 > 3秒
- 内存使用率 > 80%
- 严重错误立即告警

## 运维脚本

### 构建脚本
```bash
# 环境构建
./scripts/build.sh dev    # 开发环境
./scripts/build.sh test   # 测试环境
./scripts/build.sh prod   # 生产环境
```

### 部署脚本
```bash
# 环境部署
./scripts/deploy.sh dev --build    # 开发环境
./scripts/deploy.sh test --build   # 测试环境
./scripts/deploy.sh prod --build   # 生产环境
```

### 监控脚本
```bash
# 系统监控
./scripts/monitor.sh --check   # 健康检查
./scripts/monitor.sh --logs    # 查看日志
./scripts/monitor.sh --stats   # 系统统计
```

### 预览脚本
```bash
# 本地预览构建产物
npm run preview
```

## 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
npm run clean
rm -rf node_modules package-lock.json
npm install

# 重新构建
npm run build:prod
```

#### 2. 部署后页面空白
- 检查nginx配置中的root路径
- 确认SPA路由配置正确
- 检查静态资源路径

#### 3. API请求失败
- 检查API代理配置
- 确认后端服务状态
- 检查CORS配置

#### 4. 性能问题
- 检查资源加载时间
- 分析bundle大小
- 检查缓存配置

### 日志查看

#### 应用日志
```bash
# 查看应用日志
./scripts/monitor.sh --logs

# 查看错误日志
./scripts/monitor.sh --logs --level error
```

#### 系统日志
```bash
# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Docker日志
docker logs jsh-erp-mobile
```

## 安全配置

### HTTPS配置
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头部
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
}
```

### 内容安全策略
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:";
```

## 备份和恢复

### 备份策略
- 代码备份：Git仓库
- 配置备份：定期备份配置文件
- 数据备份：监控数据和日志

### 恢复流程
1. 从Git仓库拉取代码
2. 恢复环境配置
3. 重新构建和部署
4. 验证服务状态

## 联系支持

如遇到部署问题，请联系：
- 技术支持：<EMAIL>
- 运维团队：<EMAIL>
- 项目负责人：<EMAIL>
