# jshERP移动端API文档

## 概述

本文档详细介绍了jshERP移动端的API接口规范、认证机制、请求响应格式等。

## 基础信息

### 接口地址
- **开发环境**: http://localhost:9999
- **测试环境**: https://test-api.your-domain.com
- **生产环境**: https://api.your-domain.com

### 接口版本
- **当前版本**: v1.0
- **版本前缀**: /api/v1

### 请求格式
- **Content-Type**: application/json
- **字符编码**: UTF-8
- **请求方法**: GET, POST, PUT, DELETE

## 认证机制

### JWT Token认证

#### 1. 获取Token
```http
POST /api/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "deviceType": "mobile",
  "deviceId": "unique-device-id"
}
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200,
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理员",
      "roles": ["admin"],
      "permissions": ["user:read", "user:write"]
    }
  },
  "timestamp": 1640995200000
}
```

#### 2. 使用Token
```http
GET /api/user/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-Device-Type: mobile
```

#### 3. 刷新Token
```http
POST /api/refresh-token
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 响应格式

### 统一响应结构
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 分页响应结构
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "rows": [],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  },
  "timestamp": 1640995200000
}
```

### 错误响应结构
```json
{
  "success": false,
  "code": 400,
  "message": "参数错误",
  "data": null,
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": 1640995200000
}
```

## 状态码说明

| 状态码 | 说明 | 描述 |
|--------|------|------|
| 200 | 成功 | 请求成功 |
| 201 | 创建成功 | 资源创建成功 |
| 400 | 参数错误 | 请求参数有误 |
| 401 | 未授权 | 需要登录认证 |
| 403 | 权限不足 | 没有访问权限 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 409 | 资源冲突 | 资源已存在或冲突 |
| 422 | 验证失败 | 数据验证失败 |
| 500 | 服务器错误 | 内部服务器错误 |
| 502 | 网关错误 | 网关或代理错误 |
| 503 | 服务不可用 | 服务暂时不可用 |

## 核心API接口

### 用户管理

#### 用户登录
```http
POST /api/login
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| deviceType | string | 否 | 设备类型(mobile/desktop) |
| deviceId | string | 否 | 设备唯一标识 |

#### 用户登出
```http
POST /api/logout
```

#### 获取用户信息
```http
GET /api/user/profile
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "name": "管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "https://example.com/avatar.jpg",
    "roles": ["admin"],
    "permissions": ["user:read", "user:write"],
    "lastLoginTime": "2023-01-01 10:00:00"
  }
}
```

#### 修改密码
```http
PUT /api/user/password
```

**请求参数**:
```json
{
  "oldPassword": "123456",
  "newPassword": "654321",
  "confirmPassword": "654321"
}
```

### 商品管理

#### 获取商品列表
```http
GET /api/material/list
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| size | number | 否 | 每页数量，默认20 |
| name | string | 否 | 商品名称(模糊查询) |
| categoryId | number | 否 | 分类ID |
| status | string | 否 | 状态(active/inactive) |

**响应示例**:
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 1,
        "name": "商品名称",
        "model": "型号",
        "standard": "规格",
        "color": "颜色",
        "unit": "件",
        "purchasePrice": 100.00,
        "retailPrice": 150.00,
        "wholesalePrice": 120.00,
        "stock": 100,
        "safetyStock": 10,
        "categoryId": 1,
        "categoryName": "分类名称",
        "status": "active",
        "createTime": "2023-01-01 10:00:00"
      }
    ],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  }
}
```

#### 获取商品详情
```http
GET /api/material/{id}
```

#### 创建商品
```http
POST /api/material/add
```

**请求参数**:
```json
{
  "name": "商品名称",
  "model": "型号",
  "standard": "规格",
  "color": "颜色",
  "unit": "件",
  "purchasePrice": 100.00,
  "retailPrice": 150.00,
  "wholesalePrice": 120.00,
  "safetyStock": 10,
  "categoryId": 1,
  "remark": "备注"
}
```

#### 更新商品
```http
PUT /api/material/update
```

#### 删除商品
```http
DELETE /api/material/delete
```

**请求参数**:
```json
{
  "ids": [1, 2, 3]
}
```

### 订单管理

#### 获取订单列表
```http
GET /api/depotHead/list
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码 |
| size | number | 否 | 每页数量 |
| type | string | 否 | 单据类型 |
| subType | string | 否 | 单据子类型 |
| status | string | 否 | 状态 |
| beginTime | string | 否 | 开始时间 |
| endTime | string | 否 | 结束时间 |

#### 获取订单详情
```http
GET /api/depotHead/{id}
```

#### 创建订单
```http
POST /api/depotHead/add
```

**请求参数**:
```json
{
  "type": "出库",
  "subType": "销售",
  "supplierId": 1,
  "accountId": 1,
  "totalPrice": 1000.00,
  "discount": 50.00,
  "discountMoney": 50.00,
  "discountLastMoney": 950.00,
  "otherMoney": 0.00,
  "accountIdValue": 950.00,
  "remark": "备注",
  "items": [
    {
      "materialId": 1,
      "operNumber": 10,
      "unitPrice": 100.00,
      "allPrice": 1000.00,
      "remark": "商品备注"
    }
  ]
}
```

### 库存管理

#### 获取库存列表
```http
GET /api/materialCurrentStock/list
```

#### 库存盘点
```http
POST /api/depotHead/add
```

**请求参数**:
```json
{
  "type": "盘点",
  "subType": "盘点复盘",
  "items": [
    {
      "materialId": 1,
      "operNumber": 100,
      "remark": "盘点数量"
    }
  ]
}
```

### 报表统计

#### 销售统计
```http
GET /api/reports/sale
```

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| beginTime | string | 是 | 开始时间 |
| endTime | string | 是 | 结束时间 |
| type | string | 否 | 统计类型(day/month/year) |

#### 库存统计
```http
GET /api/reports/stock
```

#### 财务统计
```http
GET /api/reports/financial
```

## 移动端专用接口

### 设备管理

#### 设备注册
```http
POST /api/mobile/device/register
```

**请求参数**:
```json
{
  "deviceId": "unique-device-id",
  "deviceType": "mobile",
  "deviceModel": "iPhone 13",
  "systemVersion": "iOS 15.0",
  "appVersion": "1.0.0"
}
```

#### 设备状态同步
```http
POST /api/mobile/device/sync
```

### 离线数据

#### 获取离线数据
```http
GET /api/mobile/offline/data
```

#### 上传离线数据
```http
POST /api/mobile/offline/upload
```

### 推送通知

#### 注册推送Token
```http
POST /api/mobile/push/register
```

**请求参数**:
```json
{
  "deviceId": "unique-device-id",
  "pushToken": "push-token",
  "platform": "ios"
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 10001 | 参数缺失 | 检查必填参数 |
| 10002 | 参数格式错误 | 检查参数类型和格式 |
| 10003 | 参数值无效 | 检查参数值范围 |
| 20001 | 用户不存在 | 检查用户名 |
| 20002 | 密码错误 | 检查密码 |
| 20003 | 账户被锁定 | 联系管理员 |
| 20004 | Token无效 | 重新登录 |
| 20005 | Token过期 | 刷新Token |
| 30001 | 权限不足 | 联系管理员分配权限 |
| 40001 | 资源不存在 | 检查资源ID |
| 40002 | 资源已存在 | 检查重复数据 |
| 50001 | 数据库错误 | 联系技术支持 |
| 50002 | 服务不可用 | 稍后重试 |

### 错误处理示例

```javascript
// API调用错误处理
try {
  const response = await this.$api.getUserList()
  this.userList = response.data.rows
} catch (error) {
  if (error.response) {
    // 服务器响应错误
    const { code, message } = error.response.data
    
    switch (code) {
      case 401:
        // Token过期，重新登录
        this.$router.push('/login')
        break
      case 403:
        // 权限不足
        this.$message.error('权限不足')
        break
      default:
        this.$message.error(message || '请求失败')
    }
  } else if (error.request) {
    // 网络错误
    this.$message.error('网络连接失败')
  } else {
    // 其他错误
    this.$message.error('请求异常')
  }
}
```

## 接口测试

### 测试工具推荐
- **Postman**: API接口测试
- **Insomnia**: REST客户端
- **curl**: 命令行工具

### 测试环境
- **测试地址**: https://test-api.your-domain.com
- **测试账号**: test/123456
- **测试数据**: 提供完整的测试数据集

### 接口文档更新
- **更新频率**: 每次版本发布
- **变更通知**: 邮件通知开发团队
- **版本控制**: Git管理文档版本

## 联系支持

### API相关问题
- **技术支持**: <EMAIL>
- **文档问题**: <EMAIL>
- **Bug反馈**: <EMAIL>

### 相关资源
- **在线文档**: https://api-docs.company.com
- **SDK下载**: https://sdk.company.com
- **示例代码**: https://examples.company.com
