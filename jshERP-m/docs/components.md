# jshERP移动端组件文档

## 概述

jshERP移动端基于Ant Design Vue构建，并针对移动端使用场景进行了优化和扩展。本文档详细介绍了移动端组件库的使用方法、API接口和最佳实践。

## 组件分类

### 基础组件
- [按钮 Button](#按钮-button)
- [图标 Icon](#图标-icon)
- [输入框 Input](#输入框-input)
- [选择器 Select](#选择器-select)

### 布局组件
- [栅格 Grid](#栅格-grid)
- [布局 Layout](#布局-layout)
- [卡片 Card](#卡片-card)

### 导航组件
- [菜单 Menu](#菜单-menu)
- [标签页 Tabs](#标签页-tabs)
- [面包屑 Breadcrumb](#面包屑-breadcrumb)

### 数据展示组件
- [移动端表格 MobileTable](#移动端表格-mobiletable)
- [列表 List](#列表-list)
- [描述列表 Descriptions](#描述列表-descriptions)

### 反馈组件
- [消息 Message](#消息-message)
- [通知 Notification](#通知-notification)
- [加载 Loading](#加载-loading)

### 移动端专用组件
- [下拉刷新 PullRefresh](#下拉刷新-pullrefresh)
- [虚拟滚动 VirtualList](#虚拟滚动-virtuallist)
- [触摸手势 TouchGesture](#触摸手势-touchgesture)

## 基础组件

### 按钮 Button

移动端按钮针对触摸操作进行了优化，提供更大的点击区域和触觉反馈。

#### 基本用法

```vue
<template>
  <div class="button-demo">
    <!-- 基础按钮 -->
    <a-button type="primary">主要按钮</a-button>
    <a-button>默认按钮</a-button>
    <a-button type="dashed">虚线按钮</a-button>
    <a-button type="link">链接按钮</a-button>
    
    <!-- 移动端优化按钮 -->
    <a-button 
      type="primary" 
      size="large" 
      block
      :class="$mobileAdapter.getAdaptiveClass()"
    >
      移动端大按钮
    </a-button>
  </div>
</template>

<style lang="less" scoped>
.button-demo {
  .ant-btn {
    margin: 8px;
    
    // 移动端适配
    @media (max-width: 768px) {
      min-height: 44px; // 符合移动端点击区域标准
      font-size: 16px;
    }
  }
}
</style>
```

#### API

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| type | 按钮类型 | `primary` \| `default` \| `dashed` \| `link` | `default` |
| size | 按钮大小 | `large` \| `default` \| `small` | `default` |
| block | 将按钮宽度调整为其父宽度 | boolean | false |
| loading | 设置按钮载入状态 | boolean | false |
| disabled | 按钮失效状态 | boolean | false |

### 输入框 Input

移动端输入框优化了键盘交互和输入体验。

#### 基本用法

```vue
<template>
  <div class="input-demo">
    <!-- 基础输入框 -->
    <a-input 
      v-model="value" 
      placeholder="请输入内容"
      :class="$mobileAdapter.getAdaptiveClass()"
    />
    
    <!-- 搜索框 -->
    <a-input-search
      v-model="searchValue"
      placeholder="搜索商品"
      enter-button="搜索"
      @search="onSearch"
    />
    
    <!-- 数字输入框 -->
    <a-input-number
      v-model="numberValue"
      :min="0"
      :max="999999"
      :precision="2"
      placeholder="请输入数量"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: '',
      searchValue: '',
      numberValue: null
    }
  },
  
  methods: {
    onSearch(value) {
      console.log('搜索:', value)
    }
  }
}
</script>

<style lang="less" scoped>
.input-demo {
  .ant-input {
    margin-bottom: 16px;
    
    // 移动端适配
    @media (max-width: 768px) {
      height: 44px;
      font-size: 16px; // 防止iOS缩放
    }
  }
}
</style>
```

## 数据展示组件

### 移动端表格 MobileTable

专为移动端设计的表格组件，支持卡片式展示和滑动操作。

#### 基本用法

```vue
<template>
  <mobile-table
    :data="tableData"
    :columns="columns"
    :loading="loading"
    @refresh="handleRefresh"
    @loadMore="handleLoadMore"
  />
</template>

<script>
import MobileTable from '@/components/mobile/MobileTable.vue'

export default {
  components: {
    MobileTable
  },
  
  data() {
    return {
      loading: false,
      tableData: [
        {
          id: 1,
          name: '商品A',
          price: 100.00,
          stock: 50,
          status: 'active'
        }
      ],
      columns: [
        {
          title: '商品名称',
          dataIndex: 'name',
          key: 'name',
          primary: true // 主要显示字段
        },
        {
          title: '价格',
          dataIndex: 'price',
          key: 'price',
          render: (text) => `¥${text}`
        },
        {
          title: '库存',
          dataIndex: 'stock',
          key: 'stock'
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          render: (text) => text === 'active' ? '启用' : '禁用'
        }
      ]
    }
  },
  
  methods: {
    handleRefresh() {
      this.loadData()
    },
    
    handleLoadMore() {
      this.loadMoreData()
    }
  }
}
</script>
```

#### API

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| data | 数据源 | Array | [] |
| columns | 列配置 | Array | [] |
| loading | 加载状态 | boolean | false |
| showHeader | 显示表头 | boolean | true |
| cardMode | 卡片模式 | boolean | true |
| pullRefresh | 下拉刷新 | boolean | true |
| infiniteScroll | 无限滚动 | boolean | true |

#### 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| refresh | 下拉刷新触发 | - |
| loadMore | 加载更多触发 | - |
| rowClick | 行点击事件 | (record, index) |
| rowAction | 行操作事件 | (action, record, index) |

## 移动端专用组件

### 下拉刷新 PullRefresh

提供下拉刷新功能，适用于列表和表格组件。

#### 基本用法

```vue
<template>
  <pull-refresh
    v-model="refreshing"
    @refresh="onRefresh"
  >
    <div class="content">
      <div v-for="item in list" :key="item.id" class="item">
        {{ item.name }}
      </div>
    </div>
  </pull-refresh>
</template>

<script>
import PullRefresh from '@/components/mobile/PullRefresh.vue'

export default {
  components: {
    PullRefresh
  },
  
  data() {
    return {
      refreshing: false,
      list: []
    }
  },
  
  methods: {
    async onRefresh() {
      try {
        const response = await this.$api.getList()
        this.list = response.data
      } finally {
        this.refreshing = false
      }
    }
  }
}
</script>
```

### 虚拟滚动 VirtualList

处理大量数据的虚拟滚动组件，提高渲染性能。

#### 基本用法

```vue
<template>
  <virtual-list
    :data-key="'id'"
    :data-sources="dataList"
    :data-component="itemComponent"
    :estimate-size="60"
    :item-class="'list-item'"
    :wrap-class="'virtual-list-wrap'"
  />
</template>

<script>
import VirtualList from '@/components/mobile/VirtualList.vue'
import ListItem from './ListItem.vue'

export default {
  components: {
    VirtualList
  },
  
  data() {
    return {
      dataList: [], // 大量数据
      itemComponent: ListItem
    }
  }
}
</script>
```

#### ListItem组件示例

```vue
<!-- ListItem.vue -->
<template>
  <div class="list-item">
    <div class="item-title">{{ source.name }}</div>
    <div class="item-desc">{{ source.description }}</div>
    <div class="item-price">¥{{ source.price }}</div>
  </div>
</template>

<script>
export default {
  props: {
    source: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    }
  }
}
</script>

<style lang="less" scoped>
.list-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  .item-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
  
  .item-desc {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
  }
  
  .item-price {
    font-size: 16px;
    color: #ff4d4f;
    font-weight: 500;
    margin-top: 8px;
  }
}
</style>
```

## 样式定制

### 主题配置

#### 1. Less变量定制

```less
// src/assets/styles/variables.less

// 主色调
@primary-color: #3B82F6;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// 移动端专用变量
@mobile-header-height: 56px;
@mobile-footer-height: 60px;
@mobile-padding: 16px;
@mobile-border-radius: 6px;

// 字体大小
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-xl: 18px;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;
```

#### 2. CSS变量定制

```css
/* src/assets/styles/theme.css */

:root {
  /* 颜色变量 */
  --primary-color: #3B82F6;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  
  /* 移动端变量 */
  --mobile-safe-area-top: env(safe-area-inset-top);
  --mobile-safe-area-bottom: env(safe-area-inset-bottom);
  --mobile-header-height: 56px;
  --mobile-footer-height: 60px;
  
  /* 字体变量 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-base: 14px;
  --line-height-base: 1.5;
}
```

### 响应式设计

#### 断点系统

```less
// 断点定义
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 媒体查询混合
.respond-to(@media) {
  @media @media {
    @content;
  }
}

// 使用示例
.component {
  padding: 16px;
  
  .respond-to(@media-sm) {
    padding: 12px;
  }
  
  .respond-to(@media-xs) {
    padding: 8px;
  }
}
```

## 最佳实践

### 1. 组件使用原则

#### 性能优化
```vue
<template>
  <div class="page">
    <!-- 使用v-show而不是v-if来切换显示状态 -->
    <mobile-table 
      v-show="showTable"
      :data="tableData"
    />
    
    <!-- 大列表使用虚拟滚动 -->
    <virtual-list
      v-if="dataList.length > 100"
      :data-sources="dataList"
    />
    
    <!-- 小列表使用普通渲染 -->
    <div v-else>
      <div v-for="item in dataList" :key="item.id">
        {{ item.name }}
      </div>
    </div>
  </div>
</template>
```

#### 错误边界
```vue
<script>
export default {
  errorCaptured(err, instance, info) {
    // 捕获子组件错误
    console.error('组件错误:', err)
    this.$errorHandler.captureException(err, {
      component: this.$options.name,
      info: info
    })
    
    // 返回false阻止错误继续传播
    return false
  }
}
</script>
```

### 2. 移动端适配

#### 触摸友好设计
```less
.touch-friendly {
  // 最小点击区域44px
  min-height: 44px;
  min-width: 44px;
  
  // 触摸反馈
  &:active {
    opacity: 0.7;
    transform: scale(0.98);
  }
  
  // 禁用文本选择
  user-select: none;
  -webkit-touch-callout: none;
}
```

#### 安全区域适配
```less
.safe-area {
  // 顶部安全区域
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  
  // 底部安全区域
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
```

### 3. 性能监控

#### 组件性能监控
```vue
<script>
export default {
  created() {
    this.$performanceMonitor.startTimer('component_load')
  },
  
  mounted() {
    this.$performanceMonitor.endTimer('component_load')
  },
  
  methods: {
    async loadData() {
      this.$performanceMonitor.startTimer('data_load')
      
      try {
        const response = await this.$api.getData()
        this.data = response.data
      } finally {
        this.$performanceMonitor.endTimer('data_load')
      }
    }
  }
}
</script>
```

## 常见问题

### Q: 如何处理移动端表格数据过多的问题？
A: 使用MobileTable组件的卡片模式，配合虚拟滚动和分页加载。

### Q: 如何实现移动端友好的表单验证？
A: 使用实时验证，在用户输入时提供即时反馈，避免提交时才显示错误。

### Q: 如何优化移动端的加载性能？
A: 使用懒加载、虚拟滚动、图片压缩和缓存策略。

### Q: 如何处理不同屏幕尺寸的适配？
A: 使用响应式设计和移动端适配器，根据屏幕尺寸动态调整布局。

## 更新日志

### v1.0.0 (2023-01-01)
- 初始版本发布
- 基础组件库
- 移动端适配系统

### v1.1.0 (2023-02-01)
- 新增虚拟滚动组件
- 优化表格组件性能
- 增加触摸手势支持

### v1.2.0 (2023-03-01)
- 新增下拉刷新组件
- 优化样式系统
- 增加主题定制功能

## 贡献指南

### 组件开发规范
1. 遵循Vue.js最佳实践
2. 提供完整的TypeScript类型定义
3. 编写单元测试
4. 提供使用文档和示例

### 提交流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

## 联系支持

- **组件问题**: <EMAIL>
- **文档问题**: <EMAIL>
- **Bug反馈**: <EMAIL>
