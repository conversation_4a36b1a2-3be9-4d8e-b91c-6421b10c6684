# jshERP移动端开发指南

## 概述

本文档详细介绍了jshERP移动端的开发环境搭建、项目结构、开发规范和最佳实践。

## 项目架构

### 技术栈
- **前端框架**: Vue.js 2.7.16
- **UI组件库**: Ant Design Vue 1.5.2
- **路由管理**: Vue Router 3.0.1
- **状态管理**: Vuex 3.1.0
- **HTTP客户端**: Axios 0.18.0
- **构建工具**: Vue CLI 4.x + Webpack 5.x
- **样式预处理**: Less
- **移动端适配**: 自研适配系统

### 项目结构
```
jshERP-m/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── api/               # API接口
│   ├── assets/            # 资源文件
│   │   ├── images/        # 图片资源
│   │   └── styles/        # 样式文件
│   ├── components/        # 公共组件
│   │   ├── mobile/        # 移动端专用组件
│   │   └── common/        # 通用组件
│   ├── layouts/           # 布局组件
│   ├── pages/             # 页面组件
│   ├── router/            # 路由配置
│   ├── store/             # Vuex状态管理
│   ├── utils/             # 工具函数
│   │   ├── mobile-adapter.js    # 移动端适配器
│   │   ├── performance.js       # 性能监控
│   │   ├── performanceMonitor.js # 新版性能监控
│   │   ├── errorHandler.js      # 错误处理
│   │   ├── alertManager.js      # 告警管理
│   │   └── cache.js             # 缓存管理
│   └── main.js            # 应用入口
├── scripts/               # 构建和部署脚本
├── config/                # 配置文件
├── docs/                  # 文档
├── docker/                # Docker配置
└── .github/               # GitHub Actions配置
```

## 开发环境搭建

### 环境要求
- Node.js 16.x 或更高版本
- npm 8.x 或 yarn 1.22.x
- Git 2.x

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd jshERP-m
```

#### 2. 安装依赖
```bash
npm install
# 或
yarn install
```

#### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env.development

# 编辑配置文件
vim .env.development
```

#### 4. 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:8899 查看应用。

### 开发工具推荐

#### IDE配置
- **推荐IDE**: VS Code
- **必装插件**:
  - Vetur (Vue语法支持)
  - ESLint (代码检查)
  - Prettier (代码格式化)
  - Auto Rename Tag (标签重命名)
  - Bracket Pair Colorizer (括号配对)

#### 浏览器工具
- **Chrome DevTools**: 调试和性能分析
- **Vue DevTools**: Vue组件调试
- **移动端调试**: Chrome DevTools Device Mode

## 移动端适配系统

### 核心概念

#### 1. 设备检测
```javascript
import mobileAdapter from '@/utils/mobile-adapter'

// 检测设备类型
const isMobile = mobileAdapter.isMobile()
const isTablet = mobileAdapter.isTablet()
const deviceType = mobileAdapter.getDeviceType()

// 获取屏幕信息
const screenInfo = mobileAdapter.getScreenInfo()
```

#### 2. 响应式适配
```javascript
// 组件中使用适配器
export default {
  computed: {
    adaptiveClass() {
      return this.$mobileAdapter.getAdaptiveClass()
    },
    
    adaptiveStyle() {
      return this.$mobileAdapter.getAdaptiveStyle()
    }
  }
}
```

#### 3. 组件适配
```vue
<template>
  <div :class="['component', adaptiveClass]">
    <!-- 移动端内容 -->
    <div v-if="isMobile" class="mobile-content">
      <mobile-table :data="tableData" />
    </div>
    
    <!-- 桌面端内容 -->
    <div v-else class="desktop-content">
      <a-table :data-source="tableData" />
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    isMobile() {
      return this.$mobileAdapter.isMobile()
    },
    
    adaptiveClass() {
      return this.$mobileAdapter.getAdaptiveClass()
    }
  }
}
</script>
```

### 样式适配

#### 1. 响应式断点
```less
// 移动端断点
@mobile-breakpoint: 768px;
@tablet-breakpoint: 1024px;

// 媒体查询混合
.mobile-only() {
  @media (max-width: @mobile-breakpoint) {
    @content;
  }
}

.tablet-up() {
  @media (min-width: @mobile-breakpoint + 1px) {
    @content;
  }
}
```

#### 2. 适配样式
```less
.adaptive-component {
  // 基础样式
  padding: 16px;
  
  // 移动端适配
  .mobile-only({
    padding: 12px;
    font-size: 14px;
  });
  
  // 平板适配
  .tablet-up({
    padding: 20px;
    font-size: 16px;
  });
}
```

## 开发规范

### 代码规范

#### 1. 命名规范
```javascript
// 组件命名：PascalCase
export default {
  name: 'MobileTable'
}

// 文件命名：kebab-case
// mobile-table.vue
// user-management.vue

// 变量命名：camelCase
const userName = 'admin'
const userList = []

// 常量命名：UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3
```

#### 2. 组件规范
```vue
<template>
  <!-- 使用语义化标签 -->
  <section class="mobile-page">
    <header class="page-header">
      <h1>{{ title }}</h1>
    </header>
    
    <main class="page-content">
      <!-- 内容区域 -->
    </main>
    
    <footer class="page-footer">
      <!-- 底部操作 -->
    </footer>
  </section>
</template>

<script>
export default {
  name: 'MobilePage',
  
  // 组件属性
  props: {
    title: {
      type: String,
      required: true,
      default: ''
    }
  },
  
  // 组件数据
  data() {
    return {
      loading: false,
      dataList: []
    }
  },
  
  // 计算属性
  computed: {
    isEmpty() {
      return this.dataList.length === 0
    }
  },
  
  // 生命周期
  created() {
    this.loadData()
  },
  
  // 方法
  methods: {
    async loadData() {
      try {
        this.loading = true
        const response = await this.$api.getData()
        this.dataList = response.data
      } catch (error) {
        this.$errorHandler.handle(error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mobile-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .page-header {
    flex-shrink: 0;
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .page-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
  
  .page-footer {
    flex-shrink: 0;
    padding: 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
```

#### 3. API调用规范
```javascript
// api/user.js
import request from '@/utils/request'

export default {
  // 获取用户列表
  getUserList(params) {
    return request({
      url: '/api/user/list',
      method: 'get',
      params
    })
  },
  
  // 创建用户
  createUser(data) {
    return request({
      url: '/api/user/create',
      method: 'post',
      data
    })
  },
  
  // 更新用户
  updateUser(id, data) {
    return request({
      url: `/api/user/${id}`,
      method: 'put',
      data
    })
  },
  
  // 删除用户
  deleteUser(id) {
    return request({
      url: `/api/user/${id}`,
      method: 'delete'
    })
  }
}
```

### 性能优化

#### 1. 懒加载
```javascript
// 路由懒加载
const routes = [
  {
    path: '/user',
    component: () => import('@/pages/user/UserManagement.vue')
  }
]

// 组件懒加载
export default {
  components: {
    MobileTable: () => import('@/components/mobile/MobileTable.vue')
  }
}
```

#### 2. 虚拟滚动
```vue
<template>
  <virtual-list
    :data-key="'id'"
    :data-sources="dataList"
    :data-component="itemComponent"
    :estimate-size="60"
    :item-class="'list-item'"
  />
</template>

<script>
import VirtualList from '@/components/mobile/VirtualList.vue'
import ListItem from './ListItem.vue'

export default {
  components: {
    VirtualList
  },
  
  data() {
    return {
      dataList: [],
      itemComponent: ListItem
    }
  }
}
</script>
```

#### 3. 缓存策略
```javascript
// 使用缓存管理器
export default {
  async loadData() {
    const cacheKey = 'user_list'
    
    // 尝试从缓存获取
    let data = this.$cache.get(cacheKey)
    
    if (!data) {
      // 缓存不存在，从API获取
      const response = await this.$api.getUserList()
      data = response.data
      
      // 存入缓存
      this.$cache.set(cacheKey, data, 300000) // 5分钟
    }
    
    this.dataList = data
  }
}
```

## 调试和测试

### 调试技巧

#### 1. 移动端调试
```javascript
// 移动端控制台
import VConsole from 'vconsole'

if (process.env.NODE_ENV === 'development') {
  new VConsole()
}

// 性能监控
this.$performanceMonitor.startTimer('page_load')
// ... 页面加载逻辑
this.$performanceMonitor.endTimer('page_load')
```

#### 2. 错误调试
```javascript
// 错误边界
export default {
  errorCaptured(err, instance, info) {
    console.error('组件错误:', err)
    console.error('错误实例:', instance)
    console.error('错误信息:', info)
    
    // 上报错误
    this.$errorHandler.captureException(err, {
      component: this.$options.name,
      info: info
    })
    
    return false
  }
}
```

### 测试规范

#### 1. 单元测试
```javascript
// tests/unit/components/MobileTable.spec.js
import { shallowMount } from '@vue/test-utils'
import MobileTable from '@/components/mobile/MobileTable.vue'

describe('MobileTable.vue', () => {
  it('renders correctly', () => {
    const wrapper = shallowMount(MobileTable, {
      propsData: {
        data: [
          { id: 1, name: 'Test' }
        ]
      }
    })
    
    expect(wrapper.find('.mobile-table').exists()).toBe(true)
  })
  
  it('handles empty data', () => {
    const wrapper = shallowMount(MobileTable, {
      propsData: {
        data: []
      }
    })
    
    expect(wrapper.find('.empty-state').exists()).toBe(true)
  })
})
```

#### 2. 集成测试
```javascript
// tests/e2e/user-management.spec.js
describe('User Management', () => {
  it('should load user list', () => {
    cy.visit('/user')
    cy.get('[data-testid="user-list"]').should('be.visible')
    cy.get('[data-testid="user-item"]').should('have.length.greaterThan', 0)
  })
  
  it('should create new user', () => {
    cy.visit('/user')
    cy.get('[data-testid="add-user-btn"]').click()
    cy.get('[data-testid="user-form"]').should('be.visible')
    
    cy.get('[data-testid="name-input"]').type('Test User')
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="submit-btn"]').click()
    
    cy.get('[data-testid="success-message"]').should('be.visible')
  })
})
```

## 最佳实践

### 1. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 设计通用的组件接口
- **可测试性**: 便于编写单元测试
- **可维护性**: 代码清晰，注释完整

### 2. 状态管理
- **合理使用Vuex**: 只存储需要共享的状态
- **模块化管理**: 按功能模块组织store
- **避免直接修改**: 通过mutations修改状态

### 3. 错误处理
- **统一错误处理**: 使用全局错误处理器
- **用户友好提示**: 提供清晰的错误信息
- **错误上报**: 收集错误信息用于改进

### 4. 性能优化
- **按需加载**: 使用路由和组件懒加载
- **缓存策略**: 合理使用缓存减少请求
- **虚拟滚动**: 处理大量数据列表
- **图片优化**: 使用适当的图片格式和大小

## 常用工具和命令

### 开发命令
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build:prod

# 代码检查
npm run lint

# 代码格式化
npm run lint:fix

# 运行测试
npm run test:unit

# 预览构建产物
npm run preview
```

### 调试工具
```bash
# 分析构建产物
npm run build:analyze

# 性能分析
npm run build:prod --report

# 查看依赖关系
npm ls --depth=0
```

### 部署命令
```bash
# 部署到测试环境
npm run deploy:test

# 部署到生产环境
npm run deploy:prod

# Docker构建
npm run docker:build

# 系统监控
npm run monitor
```

## 同步系统

### 代码同步
项目提供了完整的代码同步系统，用于与桌面端保持同步：

```bash
# 启动同步服务
npm run sync:start

# 执行同步
npm run sync

# 完整同步
npm run sync:full

# 查看同步状态
npm run sync:status

# 查看同步历史
npm run sync:history
```

### 同步配置
```javascript
// scripts/sync-config.js
export default {
  // 同步源目录
  sourceDir: '../jshERP',

  // 同步规则
  syncRules: {
    // 需要同步的文件
    include: [
      'src/api/**/*.js',
      'src/utils/request.js',
      'src/store/modules/**/*.js'
    ],

    // 排除的文件
    exclude: [
      'src/components/**/*.vue',
      'src/pages/**/*.vue',
      'src/assets/**/*'
    ]
  },

  // 转换规则
  transformRules: {
    // 移动端特殊处理
    mobile: true,

    // 组件适配
    componentAdapter: true
  }
}
```

## 联系和支持

### 开发团队
- **项目负责人**: <EMAIL>
- **前端团队**: <EMAIL>
- **技术支持**: <EMAIL>

### 相关资源
- **项目仓库**: https://github.com/company/jshERP-m
- **API文档**: [api.md](./api.md)
- **组件文档**: [components.md](./components.md)
- **部署文档**: [deployment.md](./deployment.md)
- **问题排查**: [troubleshooting.md](./troubleshooting.md)
