#!/usr/bin/env python3
"""
修复responsive.less文件中的语法错误
主要问题：
1. .media-*({...}); 应该是 .media-*({...})
2. @media查询后的 }); 应该是 }
"""

import re

def fix_responsive_syntax():
    file_path = 'src/styles/mobile/responsive.less'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复1: .media-*({...}); -> .media-*({...})
        # 匹配 .media-xxx({...}); 模式
        content = re.sub(r'(\.media-\w+\([^)]*\{[^}]*\})\);', r'\1)', content)
        
        # 修复2: @media查询后的多余 });
        # 匹配 @media (...) { ... }); 模式
        content = re.sub(r'(@media[^{]*\{[^}]*\})\);', r'\1', content)
        
        # 修复3: 处理连续的语法错误，如 });.media-lg({ -> }) .media-lg({
        content = re.sub(r'\}\);\.(media-\w+\(\{)', r'}) .\1', content)
        
        # 修复4: 处理 });} -> })
        content = re.sub(r'\}\);\}', r'})', content)
        
        # 修复5: 处理 });& -> }) &
        content = re.sub(r'\}\);&', r'}) &', content)
        
        # 统计修复数量
        changes = len(re.findall(r'\}\);', original_content))
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成！共修复了 {changes} 个语法错误")
        print(f"📁 文件: {file_path}")
        
        # 验证修复结果
        remaining_errors = len(re.findall(r'\}\);', content))
        if remaining_errors > 0:
            print(f"⚠️  仍有 {remaining_errors} 个 }}); 需要手动检查")
        else:
            print("✅ 所有 }}); 语法错误已修复")
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    fix_responsive_syntax()
