# 测试环境配置
NODE_ENV=test
VUE_APP_ENV=test

# 应用配置
VUE_APP_TITLE=jshERP移动端(测试)
VUE_APP_VERSION=1.0.0-test

# API配置
VUE_APP_API_BASE_URL=http://test-api.your-domain.com
VUE_APP_API_TIMEOUT=30000
VUE_APP_API_PREFIX=/jshERP-boot

# 公共路径
VUE_APP_PUBLIC_PATH=/

# 存储配置
VUE_APP_STORAGE_PREFIX=jshERP_mobile_test_
VUE_APP_STORAGE_EXPIRE=7200000

# 缓存配置
VUE_APP_CACHE_ENABLE=true
VUE_APP_CACHE_MAX_SIZE=50
VUE_APP_CACHE_TTL=3600000

# 性能监控(测试环境启用)
VUE_APP_PERFORMANCE_ENABLE=true
VUE_APP_PERFORMANCE_SAMPLE_RATE=0.5

# 错误监控(测试环境启用)
VUE_APP_ERROR_MONITORING_ENABLE=true
VUE_APP_ERROR_REPORT_URL=http://test-error-report.your-domain.com

# 调试配置(测试环境部分启用)
VUE_APP_DEBUG_ENABLE=true
VUE_APP_CONSOLE_ENABLE=false

# 移动端配置
VUE_APP_MOBILE_VIEWPORT_WIDTH=375
VUE_APP_MOBILE_DESIGN_WIDTH=750
VUE_APP_MOBILE_SCALE_ENABLE=true

# 构建配置
ANALYZE=false
GENERATE_SOURCEMAP=true
