# jshERP移动端环境配置模板
# 复制此文件为 .env.local 并根据实际情况修改配置

# 应用基础配置
VUE_APP_TITLE=jshERP移动端
VUE_APP_VERSION=1.0.0
VUE_APP_DESCRIPTION=jshERP企业资源规划系统移动端

# 环境标识
NODE_ENV=development
VUE_APP_ENV=development

# API配置
VUE_APP_API_BASE_URL=http://localhost:9999
VUE_APP_API_TIMEOUT=30000
VUE_APP_API_PREFIX=/jshERP-boot

# 公共路径配置
VUE_APP_PUBLIC_PATH=/

# 存储配置
VUE_APP_STORAGE_PREFIX=jshERP_mobile_
VUE_APP_STORAGE_EXPIRE=7200000

# 缓存配置
VUE_APP_CACHE_ENABLE=true
VUE_APP_CACHE_MAX_SIZE=50
VUE_APP_CACHE_TTL=3600000

# 性能监控配置
VUE_APP_PERFORMANCE_ENABLE=true
VUE_APP_PERFORMANCE_SAMPLE_RATE=0.1

# 错误监控配置
VUE_APP_ERROR_MONITORING_ENABLE=true
VUE_APP_ERROR_REPORT_URL=

# 调试配置
VUE_APP_DEBUG_ENABLE=true
VUE_APP_CONSOLE_ENABLE=true

# 移动端特殊配置
VUE_APP_MOBILE_VIEWPORT_WIDTH=375
VUE_APP_MOBILE_DESIGN_WIDTH=750
VUE_APP_MOBILE_SCALE_ENABLE=true

# 安全配置
VUE_APP_ENCRYPT_ENABLE=false
VUE_APP_ENCRYPT_KEY=

# 第三方服务配置
VUE_APP_MAP_KEY=
VUE_APP_ANALYTICS_ID=

# 构建配置
ANALYZE=false
GENERATE_SOURCEMAP=false
