const fs = require('fs');

// 需要修复的文件
const filePath = 'src/styles/mobile/components.less';

try {
  // 读取文件内容
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换所有的 .mobile-only({ 为 @media (max-width: @screen-md-max) {
  content = content.replace(/\.mobile-only\(\{/g, '@media (max-width: @screen-md-max) {');
  
  // 替换对应的结束括号 }); 为 }
  // 这个需要更精确的处理，因为可能有嵌套
  let lines = content.split('\n');
  let result = [];
  let mobileOnlyDepth = 0;
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    // 检查是否是 mobile-only 开始
    if (line.includes('@media (max-width: @screen-md-max) {')) {
      mobileOnlyDepth++;
      result.push(line);
      continue;
    }
    
    // 检查是否是 mobile-only 结束
    if (mobileOnlyDepth > 0 && line.trim() === '});') {
      mobileOnlyDepth--;
      result.push(line.replace('});', '}'));
      continue;
    }
    
    result.push(line);
  }
  
  // 写回文件
  fs.writeFileSync(filePath, result.join('\n'), 'utf8');
  console.log('✅ 已修复 mobile-only 语法');
  
} catch (error) {
  console.error('❌ 修复失败:', error.message);
}
