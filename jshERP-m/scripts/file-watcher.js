#!/usr/bin/env node

/**
 * 文件监控系统
 * 监控桌面端文件变化，自动触发同步
 */

const fs = require('fs');
const path = require('path');
// const chokidar = require('chokidar'); // 需要安装: npm install chokidar
const EventEmitter = require('events');

class FileWatcher extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      // 监控的桌面端路径
      desktopPath: config.desktopPath || '../jshERP-web',
      
      // 监控的目录
      watchDirs: config.watchDirs || ['src/api', 'src/utils', 'src/mixins'],
      
      // 忽略的文件模式
      ignored: config.ignored || [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/*.log',
        '**/*.tmp',
        '**/.*'
      ],
      
      // 监控选项
      watchOptions: {
        persistent: true,
        ignoreInitial: true,
        followSymlinks: false,
        depth: 10,
        awaitWriteFinish: {
          stabilityThreshold: 1000,
          pollInterval: 100
        }
      },
      
      // 同步延迟（毫秒）
      syncDelay: config.syncDelay || 2000,
      
      // 批量同步配置
      batchSync: {
        enabled: true,
        maxWaitTime: 5000,
        maxBatchSize: 50
      }
    };
    
    this.watchers = new Map();
    this.pendingChanges = new Map();
    this.syncTimer = null;
    this.isWatching = false;
  }

  /**
   * 开始监控
   */
  start() {
    if (this.isWatching) {
      console.log('⚠️  文件监控已在运行中');
      return;
    }

    console.log('🔍 启动文件监控系统...');
    
    this.config.watchDirs.forEach(dir => {
      this.startWatchingDirectory(dir);
    });
    
    this.isWatching = true;
    console.log('✅ 文件监控系统已启动');
    
    // 发送启动事件
    this.emit('started');
  }

  /**
   * 停止监控
   */
  stop() {
    if (!this.isWatching) {
      console.log('⚠️  文件监控未在运行');
      return;
    }

    console.log('🛑 停止文件监控系统...');
    
    // 停止所有监控器
    this.watchers.forEach((watcher, dir) => {
      watcher.close();
      console.log(`  停止监控: ${dir}`);
    });
    
    this.watchers.clear();
    this.pendingChanges.clear();
    
    // 清除定时器
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
      this.syncTimer = null;
    }
    
    this.isWatching = false;
    console.log('✅ 文件监控系统已停止');
    
    // 发送停止事件
    this.emit('stopped');
  }

  /**
   * 监控指定目录
   */
  startWatchingDirectory(dir) {
    const watchPath = path.join(this.config.desktopPath, dir);
    
    if (!fs.existsSync(watchPath)) {
      console.warn(`⚠️  监控目录不存在: ${watchPath}`);
      return;
    }

    console.log(`🔍 开始监控: ${dir}`);
    
    const watcher = chokidar.watch(watchPath, {
      ...this.config.watchOptions,
      ignored: this.config.ignored
    });

    // 监听文件变化事件
    watcher
      .on('add', (filePath) => this.handleFileChange('add', filePath, dir))
      .on('change', (filePath) => this.handleFileChange('change', filePath, dir))
      .on('unlink', (filePath) => this.handleFileChange('unlink', filePath, dir))
      .on('addDir', (dirPath) => this.handleFileChange('addDir', dirPath, dir))
      .on('unlinkDir', (dirPath) => this.handleFileChange('unlinkDir', dirPath, dir))
      .on('error', (error) => {
        console.error(`❌ 监控错误 (${dir}):`, error);
        this.emit('error', { dir, error });
      })
      .on('ready', () => {
        console.log(`✅ 监控就绪: ${dir}`);
        this.emit('ready', { dir });
      });

    this.watchers.set(dir, watcher);
  }

  /**
   * 处理文件变化
   */
  handleFileChange(event, filePath, watchDir) {
    const relativePath = path.relative(path.join(this.config.desktopPath, watchDir), filePath);
    const changeInfo = {
      event,
      filePath,
      relativePath,
      watchDir,
      timestamp: Date.now()
    };

    console.log(`📝 文件变化: ${event} ${relativePath} (${watchDir})`);
    
    // 添加到待处理变化列表
    const changeKey = `${watchDir}/${relativePath}`;
    this.pendingChanges.set(changeKey, changeInfo);
    
    // 发送变化事件
    this.emit('change', changeInfo);
    
    // 触发批量同步
    if (this.config.batchSync.enabled) {
      this.scheduleBatchSync();
    } else {
      this.scheduleSync();
    }
  }

  /**
   * 调度单次同步
   */
  scheduleSync() {
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
    }

    this.syncTimer = setTimeout(() => {
      this.triggerSync();
    }, this.config.syncDelay);
  }

  /**
   * 调度批量同步
   */
  scheduleBatchSync() {
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
    }

    // 检查是否达到批量大小限制
    if (this.pendingChanges.size >= this.config.batchSync.maxBatchSize) {
      this.triggerSync();
      return;
    }

    this.syncTimer = setTimeout(() => {
      this.triggerSync();
    }, Math.min(this.config.syncDelay, this.config.batchSync.maxWaitTime));
  }

  /**
   * 触发同步
   */
  triggerSync() {
    if (this.pendingChanges.size === 0) {
      return;
    }

    const changes = Array.from(this.pendingChanges.values());
    this.pendingChanges.clear();
    
    console.log(`🔄 触发同步: ${changes.length} 个变化`);
    
    // 发送同步事件
    this.emit('sync', {
      changes,
      timestamp: Date.now()
    });
    
    // 清除定时器
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * 获取监控状态
   */
  getStatus() {
    return {
      isWatching: this.isWatching,
      watchedDirs: Array.from(this.watchers.keys()),
      pendingChanges: this.pendingChanges.size,
      config: this.config
    };
  }

  /**
   * 重新加载配置
   */
  reloadConfig(newConfig) {
    const wasWatching = this.isWatching;
    
    if (wasWatching) {
      this.stop();
    }
    
    this.config = { ...this.config, ...newConfig };
    
    if (wasWatching) {
      this.start();
    }
    
    console.log('🔄 配置已重新加载');
    this.emit('configReloaded', this.config);
  }
}

// 命令行接口
if (require.main === module) {
  const watcher = new FileWatcher();
  
  // 监听事件
  watcher.on('change', (changeInfo) => {
    console.log(`📝 检测到变化: ${changeInfo.event} ${changeInfo.relativePath}`);
  });
  
  watcher.on('sync', (syncInfo) => {
    console.log(`🔄 需要同步 ${syncInfo.changes.length} 个文件`);
    
    // 这里可以调用同步脚本
    const DesktopSyncManager = require('./sync-from-desktop');
    const syncManager = new DesktopSyncManager();
    
    syncManager.sync().catch(error => {
      console.error('❌ 同步失败:', error);
    });
  });
  
  watcher.on('error', (errorInfo) => {
    console.error(`❌ 监控错误: ${errorInfo.error.message}`);
  });
  
  // 处理命令行参数
  const command = process.argv[2];
  
  switch (command) {
    case 'start':
      watcher.start();
      
      // 保持进程运行
      process.on('SIGINT', () => {
        console.log('\n🛑 收到中断信号，正在停止监控...');
        watcher.stop();
        process.exit(0);
      });
      
      break;
      
    case 'status':
      console.log('📊 监控状态:', watcher.getStatus());
      break;
      
    default:
      console.log('用法:');
      console.log('  node file-watcher.js start   - 启动文件监控');
      console.log('  node file-watcher.js status  - 查看监控状态');
      break;
  }
}

module.exports = FileWatcher;
