#!/bin/bash

# jshERP移动端监控脚本
# 用法: ./scripts/monitor.sh [选项]
# 选项: --check (检查服务状态) --logs (查看日志) --stats (显示统计信息)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_service_status() {
    log_info "检查服务状态..."
    
    # 检查Docker容器状态
    if command -v docker &> /dev/null; then
        log_info "Docker容器状态:"
        docker ps --filter "name=jsh-erp" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    fi
    
    # 检查端口占用
    log_info "端口占用情况:"
    netstat -tlnp | grep -E ":(8899|9999|3306|6379)" || log_warning "未发现相关端口占用"
    
    # 健康检查
    log_info "执行健康检查..."
    
    # 检查移动端服务
    if curl -f http://localhost:8899/health > /dev/null 2>&1; then
        log_success "移动端服务正常"
    else
        log_error "移动端服务异常"
    fi
    
    # 检查后端服务
    if curl -f http://localhost:9999/jshERP-boot/sys/common/health > /dev/null 2>&1; then
        log_success "后端服务正常"
    else
        log_warning "后端服务可能异常"
    fi
}

# 查看日志
view_logs() {
    log_info "查看服务日志..."
    
    echo "选择要查看的日志:"
    echo "1) 移动端容器日志"
    echo "2) 后端容器日志"
    echo "3) Nginx访问日志"
    echo "4) Nginx错误日志"
    echo "5) 系统日志"
    
    read -p "请选择 (1-5): " choice
    
    case $choice in
        1)
            docker logs -f jsh-erp-mobile
            ;;
        2)
            docker logs -f jsh-erp-backend
            ;;
        3)
            tail -f logs/nginx/access.log
            ;;
        4)
            tail -f logs/nginx/error.log
            ;;
        5)
            journalctl -u nginx -f
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 显示统计信息
show_stats() {
    log_info "系统统计信息..."
    
    # 系统资源使用情况
    echo "=== 系统资源 ==="
    echo "CPU使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
    
    echo "内存使用情况:"
    free -h
    
    echo "磁盘使用情况:"
    df -h
    
    # Docker容器资源使用
    if command -v docker &> /dev/null; then
        echo "=== Docker容器资源 ==="
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    fi
    
    # 网络连接统计
    echo "=== 网络连接 ==="
    netstat -an | grep -E ":(8899|9999)" | wc -l | xargs echo "活跃连接数:"
    
    # 日志文件大小
    echo "=== 日志文件大小 ==="
    if [ -d "logs" ]; then
        du -sh logs/*
    fi
}

# 主函数
main() {
    local option=${1:-"--check"}
    
    case $option in
        "--check")
            check_service_status
            ;;
        "--logs")
            view_logs
            ;;
        "--stats")
            show_stats
            ;;
        "--all")
            check_service_status
            echo ""
            show_stats
            ;;
        *)
            log_info "jshERP移动端监控脚本"
            log_info "用法: $0 [选项]"
            log_info "选项:"
            log_info "  --check  检查服务状态 (默认)"
            log_info "  --logs   查看日志"
            log_info "  --stats  显示统计信息"
            log_info "  --all    显示所有信息"
            ;;
    esac
}

# 执行主函数
main "$@"
