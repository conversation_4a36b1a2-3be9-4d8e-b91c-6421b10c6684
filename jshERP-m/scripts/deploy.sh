#!/bin/bash

# jshERP移动端部署脚本
# 用法: ./scripts/deploy.sh [环境] [选项]
# 环境: dev|test|prod (默认: prod)
# 选项: --build (先构建再部署)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取参数
ENV=${1:-prod}
BUILD_OPTION=${2}

# 验证环境参数
if [[ ! "$ENV" =~ ^(dev|test|prod)$ ]]; then
    log_error "无效的环境参数: $ENV"
    log_info "支持的环境: dev, test, prod"
    exit 1
fi

log_info "开始部署 jshERP 移动端..."
log_info "部署环境: $ENV"

# 如果指定了构建选项，先执行构建
if [[ "$BUILD_OPTION" == "--build" ]]; then
    log_info "执行构建..."
    ./scripts/build.sh $ENV
fi

# 检查构建产物
if [ ! -d "dist" ]; then
    log_error "构建产物不存在，请先执行构建"
    log_info "使用 ./scripts/build.sh $ENV 进行构建"
    exit 1
fi

# 根据环境选择部署方式
case $ENV in
    "dev")
        log_info "部署到开发环境..."
        docker-compose -f docker-compose.dev.yml down
        docker-compose -f docker-compose.dev.yml up -d --build
        ;;
    "test")
        log_info "部署到测试环境..."
        # 这里可以添加测试环境的部署逻辑
        log_warning "测试环境部署逻辑待实现"
        ;;
    "prod")
        log_info "部署到生产环境..."
        
        # 备份当前版本
        if [ -d "/var/www/jsh-erp-mobile" ]; then
            BACKUP_DIR="/var/www/jsh-erp-mobile-backup-$(date +%Y%m%d_%H%M%S)"
            log_info "备份当前版本到: $BACKUP_DIR"
            cp -r /var/www/jsh-erp-mobile $BACKUP_DIR
        fi
        
        # 部署新版本
        log_info "部署新版本..."
        sudo mkdir -p /var/www/jsh-erp-mobile
        sudo cp -r dist/* /var/www/jsh-erp-mobile/
        sudo chown -R www-data:www-data /var/www/jsh-erp-mobile
        
        # 重启nginx
        log_info "重启 Nginx..."
        sudo systemctl reload nginx
        ;;
esac

# 健康检查
log_info "执行健康检查..."
sleep 5

case $ENV in
    "dev")
        HEALTH_URL="http://localhost:8899/health"
        ;;
    "test")
        HEALTH_URL="http://test.your-domain.com/health"
        ;;
    "prod")
        HEALTH_URL="http://your-domain.com/health"
        ;;
esac

if curl -f $HEALTH_URL > /dev/null 2>&1; then
    log_success "健康检查通过!"
    log_success "部署完成!"
else
    log_warning "健康检查失败，请检查服务状态"
fi

log_info "部署脚本执行完成!"
