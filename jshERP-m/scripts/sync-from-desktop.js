#!/usr/bin/env node

/**
 * 桌面端代码同步脚本
 * 用于将桌面端的代码同步到移动端项目
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 同步配置
const SYNC_CONFIG = {
  // 桌面端项目路径
  desktopPath: '../jshERP-web',
  
  // 需要同步的目录
  syncDirs: ['src/api', 'src/utils', 'src/mixins'],
  
  // 排除的文件
  excludeFiles: ['.DS_Store', 'node_modules', '*.log', '*.tmp'],
  
  // 备份配置
  backup: {
    enabled: true,
    maxBackups: 10,
    backupDir: './backups/sync'
  }
};

class DesktopSyncManager {
  constructor() {
    this.config = SYNC_CONFIG;
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  }

  /**
   * 执行同步
   */
  async sync() {
    try {
      console.log('🚀 开始同步桌面端代码...');
      
      // 1. 创建备份
      if (this.config.backup.enabled) {
        await this.createBackup();
      }
      
      // 2. 执行同步
      await this.performSync();
      
      // 3. 清理旧备份
      await this.cleanupOldBackups();
      
      console.log('✅ 同步完成！');
      
    } catch (error) {
      console.error('❌ 同步失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 创建备份
   */
  async createBackup() {
    console.log('📦 创建备份...');
    
    const backupDir = path.join(this.config.backup.backupDir, `backup-${this.timestamp}`);
    
    // 确保备份目录存在
    if (!fs.existsSync(this.config.backup.backupDir)) {
      fs.mkdirSync(this.config.backup.backupDir, { recursive: true });
    }
    
    // 备份需要同步的目录
    for (const dir of this.config.syncDirs) {
      const srcDir = path.join(process.cwd(), dir);
      const destDir = path.join(backupDir, dir);

      if (fs.existsSync(srcDir)) {
        console.log(`  备份 ${dir}...`);

        // 确保目标目录存在
        const destParentDir = path.dirname(destDir);
        if (!fs.existsSync(destParentDir)) {
          fs.mkdirSync(destParentDir, { recursive: true });
        }

        execSync(`cp -r "${srcDir}" "${destDir}"`, { stdio: 'inherit' });
      }
    }
    
    console.log(`✅ 备份完成: ${backupDir}`);
  }

  /**
   * 执行同步
   */
  async performSync() {
    console.log('🔄 执行同步...');
    
    for (const dir of this.config.syncDirs) {
      const srcDir = path.join(this.config.desktopPath, dir);
      const destDir = path.join(process.cwd(), dir);
      
      if (fs.existsSync(srcDir)) {
        console.log(`  同步 ${dir}...`);
        
        // 删除目标目录
        if (fs.existsSync(destDir)) {
          execSync(`rm -rf "${destDir}"`, { stdio: 'inherit' });
        }
        
        // 复制源目录
        execSync(`cp -r "${srcDir}" "${destDir}"`, { stdio: 'inherit' });
        
        console.log(`  ✅ ${dir} 同步完成`);
      } else {
        console.warn(`  ⚠️  源目录不存在: ${srcDir}`);
      }
    }
  }

  /**
   * 清理旧备份
   */
  async cleanupOldBackups() {
    const backupDir = this.config.backup.backupDir;
    
    if (!fs.existsSync(backupDir)) {
      return;
    }
    
    const backups = fs.readdirSync(backupDir)
      .filter(name => name.startsWith('backup-'))
      .map(name => ({
        name,
        path: path.join(backupDir, name),
        time: fs.statSync(path.join(backupDir, name)).mtime
      }))
      .sort((a, b) => b.time - a.time);
    
    // 保留最新的备份，删除多余的
    if (backups.length > this.config.backup.maxBackups) {
      const toDelete = backups.slice(this.config.backup.maxBackups);
      
      for (const backup of toDelete) {
        console.log(`🗑️  删除旧备份: ${backup.name}`);
        execSync(`rm -rf "${backup.path}"`, { stdio: 'inherit' });
      }
    }
  }

  /**
   * 检查同步状态
   */
  async checkStatus() {
    console.log('📊 检查同步状态...');
    
    for (const dir of this.config.syncDirs) {
      const srcDir = path.join(this.config.desktopPath, dir);
      const destDir = path.join(process.cwd(), dir);
      
      const srcExists = fs.existsSync(srcDir);
      const destExists = fs.existsSync(destDir);
      
      console.log(`  ${dir}:`);
      console.log(`    桌面端: ${srcExists ? '✅ 存在' : '❌ 不存在'}`);
      console.log(`    移动端: ${destExists ? '✅ 存在' : '❌ 不存在'}`);
      
      if (srcExists && destExists) {
        // 比较修改时间
        const srcStat = fs.statSync(srcDir);
        const destStat = fs.statSync(destDir);
        
        if (srcStat.mtime > destStat.mtime) {
          console.log(`    状态: ⚠️  桌面端更新，需要同步`);
        } else {
          console.log(`    状态: ✅ 已同步`);
        }
      }
    }
  }

  /**
   * 回滚到指定备份
   */
  async rollback(backupId) {
    console.log(`🔄 回滚到备份: ${backupId}`);
    
    const backupDir = path.join(this.config.backup.backupDir, `backup-${backupId}`);
    
    if (!fs.existsSync(backupDir)) {
      throw new Error(`备份不存在: ${backupDir}`);
    }
    
    for (const dir of this.config.syncDirs) {
      const srcDir = path.join(backupDir, dir);
      const destDir = path.join(process.cwd(), dir);
      
      if (fs.existsSync(srcDir)) {
        console.log(`  回滚 ${dir}...`);
        
        // 删除当前目录
        if (fs.existsSync(destDir)) {
          execSync(`rm -rf "${destDir}"`, { stdio: 'inherit' });
        }
        
        // 恢复备份
        execSync(`cp -r "${srcDir}" "${destDir}"`, { stdio: 'inherit' });
        
        console.log(`  ✅ ${dir} 回滚完成`);
      }
    }
    
    console.log('✅ 回滚完成！');
  }
}

// 命令行接口
if (require.main === module) {
  const syncManager = new DesktopSyncManager();
  const command = process.argv[2];
  
  switch (command) {
    case 'sync':
      syncManager.sync();
      break;
    case 'check':
      syncManager.checkStatus();
      break;
    case 'rollback':
      const backupId = process.argv[3];
      if (!backupId) {
        console.error('请指定备份ID');
        process.exit(1);
      }
      syncManager.rollback(backupId);
      break;
    default:
      console.log('用法:');
      console.log('  node sync-from-desktop.js sync     - 执行同步');
      console.log('  node sync-from-desktop.js check    - 检查状态');
      console.log('  node sync-from-desktop.js rollback <backup-id> - 回滚备份');
      break;
  }
}

module.exports = DesktopSyncManager;
