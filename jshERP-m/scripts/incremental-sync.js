#!/usr/bin/env node

/**
 * 增量同步系统
 * 只同步变化的文件，提高同步效率
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

class IncrementalSyncManager {
  constructor(config = {}) {
    this.config = {
      // 桌面端项目路径
      desktopPath: config.desktopPath || '../jshERP-web',
      
      // 需要同步的目录
      syncDirs: config.syncDirs || ['src/api', 'src/utils', 'src/mixins'],
      
      // 状态文件路径
      stateFile: config.stateFile || './scripts/.sync-state.json',
      
      // 排除的文件
      excludeFiles: config.excludeFiles || ['.DS_Store', 'node_modules', '*.log', '*.tmp'],
      
      // 备份配置
      backup: {
        enabled: true,
        maxBackups: 10,
        backupDir: './backups/incremental'
      }
    };
    
    this.syncState = this.loadSyncState();
  }

  /**
   * 加载同步状态
   */
  loadSyncState() {
    try {
      if (fs.existsSync(this.config.stateFile)) {
        const stateData = fs.readFileSync(this.config.stateFile, 'utf8');
        return JSON.parse(stateData);
      }
    } catch (error) {
      console.warn('⚠️  无法加载同步状态文件:', error.message);
    }
    
    return {
      lastSync: null,
      fileHashes: {},
      version: '1.0.0'
    };
  }

  /**
   * 保存同步状态
   */
  saveSyncState() {
    try {
      const stateDir = path.dirname(this.config.stateFile);
      if (!fs.existsSync(stateDir)) {
        fs.mkdirSync(stateDir, { recursive: true });
      }
      
      fs.writeFileSync(this.config.stateFile, JSON.stringify(this.syncState, null, 2));
    } catch (error) {
      console.error('❌ 无法保存同步状态:', error.message);
    }
  }

  /**
   * 计算文件哈希
   */
  calculateFileHash(filePath) {
    try {
      const content = fs.readFileSync(filePath);
      return crypto.createHash('md5').update(content).digest('hex');
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取目录中的所有文件
   */
  getAllFiles(dirPath, baseDir = '') {
    const files = [];
    
    if (!fs.existsSync(dirPath)) {
      return files;
    }
    
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const relativePath = path.join(baseDir, item);
      
      // 检查是否应该排除
      if (this.shouldExclude(relativePath)) {
        continue;
      }
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.getAllFiles(fullPath, relativePath));
      } else if (stat.isFile()) {
        files.push({
          fullPath,
          relativePath,
          size: stat.size,
          mtime: stat.mtime.getTime()
        });
      }
    }
    
    return files;
  }

  /**
   * 检查文件是否应该排除
   */
  shouldExclude(filePath) {
    return this.config.excludeFiles.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(filePath);
      }
      return filePath.includes(pattern);
    });
  }

  /**
   * 检测变化的文件
   */
  detectChanges() {
    const changes = {
      added: [],
      modified: [],
      deleted: [],
      unchanged: []
    };
    
    const currentFiles = new Map();
    
    // 扫描所有同步目录
    for (const dir of this.config.syncDirs) {
      const desktopDir = path.join(this.config.desktopPath, dir);
      const files = this.getAllFiles(desktopDir, dir);
      
      for (const file of files) {
        const hash = this.calculateFileHash(file.fullPath);
        if (hash) {
          currentFiles.set(file.relativePath, {
            ...file,
            hash
          });
        }
      }
    }
    
    // 检查新增和修改的文件
    for (const [relativePath, fileInfo] of currentFiles) {
      const oldHash = this.syncState.fileHashes[relativePath];
      
      if (!oldHash) {
        changes.added.push(fileInfo);
      } else if (oldHash !== fileInfo.hash) {
        changes.modified.push(fileInfo);
      } else {
        changes.unchanged.push(fileInfo);
      }
    }
    
    // 检查删除的文件
    for (const relativePath of Object.keys(this.syncState.fileHashes)) {
      if (!currentFiles.has(relativePath)) {
        changes.deleted.push({ relativePath });
      }
    }
    
    return changes;
  }

  /**
   * 执行增量同步
   */
  async performIncrementalSync() {
    console.log('🔍 检测文件变化...');
    
    const changes = this.detectChanges();
    const totalChanges = changes.added.length + changes.modified.length + changes.deleted.length;
    
    if (totalChanges === 0) {
      console.log('✅ 没有检测到文件变化，无需同步');
      return { success: true, changes: 0 };
    }
    
    console.log(`📊 检测到 ${totalChanges} 个文件变化:`);
    console.log(`  新增: ${changes.added.length} 个文件`);
    console.log(`  修改: ${changes.modified.length} 个文件`);
    console.log(`  删除: ${changes.deleted.length} 个文件`);
    
    // 创建备份
    if (this.config.backup.enabled) {
      await this.createIncrementalBackup(changes);
    }
    
    // 执行同步操作
    await this.applySyncChanges(changes);
    
    // 更新同步状态
    this.updateSyncState(changes);
    
    console.log('✅ 增量同步完成');
    return { success: true, changes: totalChanges };
  }

  /**
   * 创建增量备份
   */
  async createIncrementalBackup(changes) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(this.config.backup.backupDir, `incremental-${timestamp}`);
    
    console.log('📦 创建增量备份...');
    
    // 确保备份目录存在
    if (!fs.existsSync(this.config.backup.backupDir)) {
      fs.mkdirSync(this.config.backup.backupDir, { recursive: true });
    }
    
    // 备份将要修改的文件
    const filesToBackup = [...changes.modified, ...changes.deleted];
    
    for (const fileInfo of filesToBackup) {
      const srcPath = path.join(process.cwd(), fileInfo.relativePath);
      const destPath = path.join(backupDir, fileInfo.relativePath);
      
      if (fs.existsSync(srcPath)) {
        const destDir = path.dirname(destPath);
        if (!fs.existsSync(destDir)) {
          fs.mkdirSync(destDir, { recursive: true });
        }
        
        execSync(`cp "${srcPath}" "${destPath}"`, { stdio: 'inherit' });
      }
    }
    
    // 保存变化信息
    const changeInfo = {
      timestamp,
      changes,
      totalFiles: filesToBackup.length
    };
    
    fs.writeFileSync(
      path.join(backupDir, 'changes.json'),
      JSON.stringify(changeInfo, null, 2)
    );
    
    console.log(`✅ 增量备份完成: ${backupDir}`);
  }

  /**
   * 应用同步变化
   */
  async applySyncChanges(changes) {
    console.log('🔄 应用同步变化...');
    
    // 处理删除的文件
    for (const fileInfo of changes.deleted) {
      const targetPath = path.join(process.cwd(), fileInfo.relativePath);
      if (fs.existsSync(targetPath)) {
        console.log(`  删除: ${fileInfo.relativePath}`);
        fs.unlinkSync(targetPath);
      }
    }
    
    // 处理新增和修改的文件
    const filesToCopy = [...changes.added, ...changes.modified];
    
    for (const fileInfo of filesToCopy) {
      const srcPath = fileInfo.fullPath;
      const destPath = path.join(process.cwd(), fileInfo.relativePath);
      
      console.log(`  ${changes.added.includes(fileInfo) ? '新增' : '修改'}: ${fileInfo.relativePath}`);
      
      // 确保目标目录存在
      const destDir = path.dirname(destPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      // 复制文件
      execSync(`cp "${srcPath}" "${destPath}"`, { stdio: 'inherit' });
    }
  }

  /**
   * 更新同步状态
   */
  updateSyncState(changes) {
    // 更新文件哈希
    for (const fileInfo of [...changes.added, ...changes.modified]) {
      this.syncState.fileHashes[fileInfo.relativePath] = fileInfo.hash;
    }
    
    // 删除已删除文件的哈希
    for (const fileInfo of changes.deleted) {
      delete this.syncState.fileHashes[fileInfo.relativePath];
    }
    
    // 更新同步时间
    this.syncState.lastSync = new Date().toISOString();
    
    // 保存状态
    this.saveSyncState();
  }

  /**
   * 强制全量同步
   */
  async forceFullSync() {
    console.log('🔄 执行强制全量同步...');
    
    // 清除同步状态
    this.syncState = {
      lastSync: null,
      fileHashes: {},
      version: '1.0.0'
    };
    
    // 执行增量同步（此时所有文件都会被视为新增）
    return await this.performIncrementalSync();
  }

  /**
   * 获取同步统计信息
   */
  getSyncStats() {
    const totalFiles = Object.keys(this.syncState.fileHashes).length;
    
    return {
      lastSync: this.syncState.lastSync,
      totalFiles,
      version: this.syncState.version,
      stateFile: this.config.stateFile
    };
  }
}

// 命令行接口
if (require.main === module) {
  const syncManager = new IncrementalSyncManager();
  const command = process.argv[2];
  
  switch (command) {
    case 'sync':
      syncManager.performIncrementalSync();
      break;
      
    case 'force':
      syncManager.forceFullSync();
      break;
      
    case 'check':
      const changes = syncManager.detectChanges();
      const totalChanges = changes.added.length + changes.modified.length + changes.deleted.length;
      
      console.log('📊 文件变化检测结果:');
      console.log(`  新增: ${changes.added.length} 个文件`);
      console.log(`  修改: ${changes.modified.length} 个文件`);
      console.log(`  删除: ${changes.deleted.length} 个文件`);
      console.log(`  总计: ${totalChanges} 个变化`);
      break;
      
    case 'stats':
      console.log('📊 同步统计信息:', syncManager.getSyncStats());
      break;
      
    default:
      console.log('用法:');
      console.log('  node incremental-sync.js sync   - 执行增量同步');
      console.log('  node incremental-sync.js force  - 强制全量同步');
      console.log('  node incremental-sync.js check  - 检查文件变化');
      console.log('  node incremental-sync.js stats  - 查看同步统计');
      break;
  }
}

module.exports = IncrementalSyncManager;
