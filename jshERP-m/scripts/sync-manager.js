#!/usr/bin/env node

/**
 * 统一同步管理器
 * 整合文件监控和增量同步功能
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

// 导入同步组件
const FileWatcher = require('./file-watcher');
const IncrementalSyncManager = require('./incremental-sync');
const DesktopSyncManager = require('./sync-from-desktop');

class SyncManager extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      // 自动同步配置
      autoSync: config.autoSync !== false,
      
      // 同步模式: 'incremental' | 'full'
      syncMode: config.syncMode || 'incremental',
      
      // 监控配置
      watch: {
        enabled: config.watch?.enabled !== false,
        delay: config.watch?.delay || 2000
      },
      
      // 日志配置
      logging: {
        enabled: config.logging?.enabled !== false,
        logFile: config.logging?.logFile || './scripts/sync.log',
        maxLogSize: config.logging?.maxLogSize || 10 * 1024 * 1024 // 10MB
      }
    };
    
    // 初始化组件
    this.fileWatcher = new FileWatcher();
    this.incrementalSync = new IncrementalSyncManager();
    this.fullSync = new DesktopSyncManager();
    
    // 同步状态
    this.syncStatus = {
      isRunning: false,
      lastSync: null,
      totalSyncs: 0,
      errors: 0,
      mode: this.config.syncMode
    };
    
    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // 文件监控事件
    this.fileWatcher.on('change', (changeInfo) => {
      this.log('info', `文件变化: ${changeInfo.event} ${changeInfo.relativePath}`);
      
      if (this.config.autoSync) {
        this.scheduleSync();
      }
    });
    
    this.fileWatcher.on('sync', (syncInfo) => {
      this.log('info', `触发同步: ${syncInfo.changes.length} 个变化`);
      
      if (this.config.autoSync) {
        this.performSync();
      }
    });
    
    this.fileWatcher.on('error', (errorInfo) => {
      this.log('error', `监控错误: ${errorInfo.error.message}`);
      this.syncStatus.errors++;
      this.emit('error', errorInfo);
    });
  }

  /**
   * 启动同步管理器
   */
  async start() {
    if (this.syncStatus.isRunning) {
      this.log('warn', '同步管理器已在运行中');
      return;
    }

    this.log('info', '启动同步管理器...');
    
    try {
      // 启动文件监控
      if (this.config.watch.enabled) {
        this.fileWatcher.start();
      }
      
      // 执行初始同步
      if (this.config.autoSync) {
        await this.performSync();
      }
      
      this.syncStatus.isRunning = true;
      this.log('info', '同步管理器已启动');
      this.emit('started');
      
    } catch (error) {
      this.log('error', `启动失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 停止同步管理器
   */
  stop() {
    if (!this.syncStatus.isRunning) {
      this.log('warn', '同步管理器未在运行');
      return;
    }

    this.log('info', '停止同步管理器...');
    
    // 停止文件监控
    this.fileWatcher.stop();
    
    this.syncStatus.isRunning = false;
    this.log('info', '同步管理器已停止');
    this.emit('stopped');
  }

  /**
   * 执行同步
   */
  async performSync() {
    if (this.syncInProgress) {
      this.log('warn', '同步正在进行中，跳过此次同步');
      return;
    }

    this.syncInProgress = true;
    const startTime = Date.now();
    
    try {
      this.log('info', `开始${this.config.syncMode}同步...`);
      this.emit('syncStart', { mode: this.config.syncMode });
      
      let result;
      
      if (this.config.syncMode === 'incremental') {
        result = await this.incrementalSync.performIncrementalSync();
      } else {
        result = await this.fullSync.sync();
      }
      
      const duration = Date.now() - startTime;
      
      this.syncStatus.lastSync = new Date().toISOString();
      this.syncStatus.totalSyncs++;
      
      this.log('info', `同步完成，耗时 ${duration}ms，变化 ${result.changes || 0} 个文件`);
      this.emit('syncComplete', { 
        result, 
        duration, 
        mode: this.config.syncMode 
      });
      
    } catch (error) {
      this.syncStatus.errors++;
      this.log('error', `同步失败: ${error.message}`);
      this.emit('syncError', { error, mode: this.config.syncMode });
      throw error;
      
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 调度同步
   */
  scheduleSync() {
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
    }

    this.syncTimer = setTimeout(() => {
      this.performSync().catch(error => {
        this.log('error', `调度同步失败: ${error.message}`);
      });
    }, this.config.watch.delay);
  }

  /**
   * 切换同步模式
   */
  setSyncMode(mode) {
    if (!['incremental', 'full'].includes(mode)) {
      throw new Error(`无效的同步模式: ${mode}`);
    }
    
    this.config.syncMode = mode;
    this.syncStatus.mode = mode;
    this.log('info', `同步模式已切换为: ${mode}`);
    this.emit('modeChanged', { mode });
  }

  /**
   * 手动触发同步
   */
  async triggerSync(mode = null) {
    const originalMode = this.config.syncMode;
    
    if (mode) {
      this.setSyncMode(mode);
    }
    
    try {
      await this.performSync();
    } finally {
      if (mode) {
        this.setSyncMode(originalMode);
      }
    }
  }

  /**
   * 获取同步状态
   */
  getStatus() {
    return {
      ...this.syncStatus,
      watcherStatus: this.fileWatcher.getStatus(),
      incrementalStats: this.incrementalSync.getSyncStats(),
      config: this.config
    };
  }

  /**
   * 获取同步历史
   */
  getSyncHistory(limit = 50) {
    try {
      if (!fs.existsSync(this.config.logging.logFile)) {
        return [];
      }
      
      const logContent = fs.readFileSync(this.config.logging.logFile, 'utf8');
      const lines = logContent.split('\n').filter(line => line.trim());
      
      return lines
        .slice(-limit)
        .map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return { message: line, timestamp: null };
          }
        })
        .reverse();
        
    } catch (error) {
      this.log('error', `获取同步历史失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 记录日志
   */
  log(level, message, data = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      ...data
    };
    
    // 控制台输出
    const prefix = {
      info: '📝',
      warn: '⚠️ ',
      error: '❌'
    }[level] || '📝';
    
    console.log(`${prefix} ${message}`);
    
    // 文件日志
    if (this.config.logging.enabled) {
      this.writeLog(logEntry);
    }
    
    // 发送日志事件
    this.emit('log', logEntry);
  }

  /**
   * 写入日志文件
   */
  writeLog(logEntry) {
    try {
      const logDir = path.dirname(this.config.logging.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      // 检查日志文件大小
      if (fs.existsSync(this.config.logging.logFile)) {
        const stats = fs.statSync(this.config.logging.logFile);
        if (stats.size > this.config.logging.maxLogSize) {
          this.rotateLog();
        }
      }
      
      fs.appendFileSync(
        this.config.logging.logFile,
        JSON.stringify(logEntry) + '\n'
      );
      
    } catch (error) {
      console.error('写入日志失败:', error.message);
    }
  }

  /**
   * 日志轮转
   */
  rotateLog() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = this.config.logging.logFile.replace('.log', `-${timestamp}.log`);
      
      fs.renameSync(this.config.logging.logFile, backupFile);
      
      this.log('info', `日志文件已轮转: ${backupFile}`);
      
    } catch (error) {
      console.error('日志轮转失败:', error.message);
    }
  }

  /**
   * 重新加载配置
   */
  reloadConfig(newConfig) {
    const wasRunning = this.syncStatus.isRunning;
    
    if (wasRunning) {
      this.stop();
    }
    
    this.config = { ...this.config, ...newConfig };
    
    // 重新配置组件
    this.fileWatcher.reloadConfig(newConfig);
    
    if (wasRunning) {
      this.start();
    }
    
    this.log('info', '配置已重新加载');
    this.emit('configReloaded', this.config);
  }
}

// 命令行接口
if (require.main === module) {
  const syncManager = new SyncManager();
  
  // 监听事件
  syncManager.on('syncComplete', (info) => {
    console.log(`✅ 同步完成: ${info.result.changes || 0} 个文件变化，耗时 ${info.duration}ms`);
  });
  
  syncManager.on('syncError', (info) => {
    console.error(`❌ 同步失败: ${info.error.message}`);
  });
  
  const command = process.argv[2];
  
  switch (command) {
    case 'start':
      syncManager.start();
      
      // 保持进程运行
      process.on('SIGINT', () => {
        console.log('\n🛑 收到中断信号，正在停止同步管理器...');
        syncManager.stop();
        process.exit(0);
      });
      
      break;
      
    case 'sync':
      const mode = process.argv[3] || 'incremental';
      syncManager.triggerSync(mode).then(() => {
        console.log('✅ 手动同步完成');
        process.exit(0);
      }).catch(error => {
        console.error('❌ 手动同步失败:', error.message);
        process.exit(1);
      });
      break;
      
    case 'status':
      console.log('📊 同步状态:', JSON.stringify(syncManager.getStatus(), null, 2));
      break;
      
    case 'history':
      const history = syncManager.getSyncHistory(20);
      console.log('📜 同步历史:');
      history.forEach(entry => {
        console.log(`  ${entry.timestamp} [${entry.level}] ${entry.message}`);
      });
      break;
      
    default:
      console.log('用法:');
      console.log('  node sync-manager.js start              - 启动同步管理器');
      console.log('  node sync-manager.js sync [mode]        - 手动同步 (incremental|full)');
      console.log('  node sync-manager.js status             - 查看同步状态');
      console.log('  node sync-manager.js history            - 查看同步历史');
      break;
  }
}

module.exports = SyncManager;
