# jshERP 移动端智能同步系统

## 概述

智能同步系统是 jshERP 移动端项目的核心组件，负责自动同步桌面端代码变化到移动端项目，确保两个项目保持一致性。

## 系统架构

### 核心组件

1. **文件监控系统** (`file-watcher.js`)
   - 实时监控桌面端文件变化
   - 支持批量变化检测
   - 可配置监控目录和排除规则

2. **增量同步系统** (`incremental-sync.js`)
   - 基于文件哈希的增量同步
   - 只同步变化的文件，提高效率
   - 自动备份和状态管理

3. **完整同步系统** (`sync-from-desktop.js`)
   - 全量同步桌面端代码
   - 支持备份和回滚功能
   - 适用于初始化和重大更新

4. **统一管理器** (`sync-manager.js`)
   - 整合所有同步功能
   - 提供统一的API接口
   - 支持自动同步和手动触发

## 使用方法

### NPM 脚本

```bash
# 执行增量同步
npm run sync

# 执行完整同步
npm run sync:full

# 启动自动同步监控
npm run sync:start

# 查看同步状态
npm run sync:status

# 查看同步历史
npm run sync:history
```

### 直接使用脚本

```bash
# 统一管理器
node scripts/sync-manager.js start    # 启动自动同步
node scripts/sync-manager.js sync     # 手动增量同步
node scripts/sync-manager.js status   # 查看状态

# 增量同步
node scripts/incremental-sync.js sync   # 执行增量同步
node scripts/incremental-sync.js check  # 检查文件变化
node scripts/incremental-sync.js force  # 强制全量同步

# 文件监控
node scripts/file-watcher.js start    # 启动文件监控
node scripts/file-watcher.js status   # 查看监控状态

# 完整同步
node scripts/sync-from-desktop.js sync     # 执行同步
node scripts/sync-from-desktop.js check    # 检查状态
node scripts/sync-from-desktop.js rollback # 回滚到备份
```

## 配置说明

### 默认配置

```javascript
{
  // 桌面端项目路径
  desktopPath: '../jshERP-web',
  
  // 同步目录
  syncDirs: ['src/api', 'src/utils', 'src/mixins'],
  
  // 自动同步
  autoSync: true,
  
  // 同步模式: 'incremental' | 'full'
  syncMode: 'incremental',
  
  // 文件监控
  watch: {
    enabled: true,
    delay: 2000
  },
  
  // 备份配置
  backup: {
    enabled: true,
    maxBackups: 10
  }
}
```

### 自定义配置

可以通过创建 `scripts/sync-config.json` 文件来自定义配置：

```json
{
  "desktopPath": "../jshERP-web",
  "syncDirs": ["src/api", "src/utils", "src/mixins", "src/components"],
  "autoSync": true,
  "syncMode": "incremental",
  "watch": {
    "enabled": true,
    "delay": 1000
  },
  "excludeFiles": [".DS_Store", "node_modules", "*.log", "*.tmp"]
}
```

## 工作流程

### 自动同步流程

1. **启动监控**: 文件监控系统开始监控桌面端指定目录
2. **检测变化**: 当文件发生变化时，触发变化事件
3. **批量处理**: 收集一定时间内的所有变化，进行批量处理
4. **增量同步**: 只同步实际变化的文件
5. **状态更新**: 更新同步状态和文件哈希记录

### 手动同步流程

1. **检测变化**: 扫描所有监控目录，计算文件哈希
2. **对比状态**: 与上次同步状态进行对比
3. **执行同步**: 复制变化的文件到移动端项目
4. **创建备份**: 自动备份被覆盖的文件
5. **更新状态**: 保存新的同步状态

## 备份和恢复

### 自动备份

- 每次同步前自动创建备份
- 备份文件保存在 `backups/` 目录
- 支持最大备份数量限制
- 备份包含变化信息和时间戳

### 手动恢复

```bash
# 查看可用备份
ls backups/

# 恢复到指定备份
node scripts/sync-from-desktop.js rollback backup-2024-06-26T10-30-00-000Z
```

## 监控和日志

### 日志系统

- 自动记录所有同步操作
- 支持日志轮转和大小限制
- 日志文件: `scripts/sync.log`

### 状态监控

```bash
# 查看详细状态
npm run sync:status

# 查看同步历史
npm run sync:history
```

## 故障排除

### 常见问题

1. **同步失败**
   - 检查桌面端项目路径是否正确
   - 确认文件权限是否足够
   - 查看错误日志获取详细信息

2. **文件监控不工作**
   - 确认 chokidar 依赖已安装
   - 检查监控目录是否存在
   - 验证文件系统权限

3. **备份空间不足**
   - 清理旧的备份文件
   - 调整最大备份数量配置
   - 使用增量同步减少备份大小

### 调试模式

```bash
# 启用详细日志
DEBUG=sync:* npm run sync:start

# 检查文件变化（不执行同步）
npm run sync:check
```

## 性能优化

### 建议配置

- 使用增量同步模式
- 合理设置监控延迟时间
- 排除不必要的文件和目录
- 定期清理备份文件

### 监控指标

- 同步耗时
- 文件变化数量
- 错误率
- 备份大小

## 扩展开发

### 添加新的同步目录

```javascript
// 在配置中添加新目录
{
  "syncDirs": ["src/api", "src/utils", "src/mixins", "src/new-module"]
}
```

### 自定义同步规则

```javascript
// 继承 IncrementalSyncManager 类
class CustomSyncManager extends IncrementalSyncManager {
  shouldExclude(filePath) {
    // 自定义排除规则
    return super.shouldExclude(filePath) || filePath.includes('custom-exclude');
  }
}
```

### 添加同步钩子

```javascript
syncManager.on('syncStart', (info) => {
  console.log('同步开始:', info);
});

syncManager.on('syncComplete', (info) => {
  console.log('同步完成:', info);
});
```

## 版本历史

- v1.0.0: 基础同步功能
- v1.1.0: 增加文件监控
- v1.2.0: 增量同步支持
- v1.3.0: 统一管理器和可视化界面

## 许可证

本项目遵循 jshERP 项目的许可证协议。
