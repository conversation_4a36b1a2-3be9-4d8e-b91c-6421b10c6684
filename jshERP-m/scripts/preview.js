#!/usr/bin/env node

/**
 * jshERP移动端预览服务器
 * 用于预览构建后的静态文件
 */

const express = require('express')
const path = require('path')
const history = require('connect-history-api-fallback')
const compression = require('compression')
const chalk = require('chalk')

const app = express()
const PORT = process.env.PREVIEW_PORT || 8900
const DIST_DIR = path.join(__dirname, '../dist')

// 检查dist目录是否存在
const fs = require('fs')
if (!fs.existsSync(DIST_DIR)) {
    console.log(chalk.red('❌ 构建产物不存在!'))
    console.log(chalk.yellow('请先执行构建命令: npm run build'))
    process.exit(1)
}

// 启用gzip压缩
app.use(compression())

// 设置安全头
app.use((req, res, next) => {
    res.setHeader('X-Frame-Options', 'SAMEORIGIN')
    res.setHeader('X-Content-Type-Options', 'nosniff')
    res.setHeader('X-XSS-Protection', '1; mode=block')
    next()
})

// 静态资源缓存
app.use('/static', express.static(path.join(DIST_DIR, 'static'), {
    maxAge: '1y',
    etag: true,
    lastModified: true
}))

// SPA路由支持
app.use(history({
    index: '/index.html',
    rewrites: [
        { from: /^\/api\/.*$/, to: function(context) {
            return context.parsedUrl.pathname
        }}
    ]
}))

// 静态文件服务
app.use(express.static(DIST_DIR, {
    maxAge: '1d',
    etag: true,
    lastModified: true
}))

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: require('../package.json').version
    })
})

// 错误处理
app.use((err, req, res, next) => {
    console.error(chalk.red('服务器错误:'), err)
    res.status(500).json({
        error: '内部服务器错误',
        message: process.env.NODE_ENV === 'development' ? err.message : '服务器错误'
    })
})

// 404处理
app.use((req, res) => {
    res.status(404).sendFile(path.join(DIST_DIR, 'index.html'))
})

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
    console.log()
    console.log(chalk.green('🚀 jshERP移动端预览服务器启动成功!'))
    console.log()
    console.log(chalk.cyan('  本地访问:'), `http://localhost:${PORT}`)
    console.log(chalk.cyan('  网络访问:'), `http://0.0.0.0:${PORT}`)
    console.log()
    console.log(chalk.yellow('  按 Ctrl+C 停止服务器'))
    console.log()
    
    // 显示构建信息
    const stats = fs.statSync(DIST_DIR)
    console.log(chalk.gray('  构建时间:'), stats.mtime.toLocaleString())
    
    // 显示主要文件
    const files = fs.readdirSync(DIST_DIR)
    const jsFiles = files.filter(f => f.endsWith('.js')).slice(0, 3)
    const cssFiles = files.filter(f => f.endsWith('.css')).slice(0, 3)
    
    if (jsFiles.length > 0) {
        console.log(chalk.gray('  JS文件:'), jsFiles.join(', '))
    }
    if (cssFiles.length > 0) {
        console.log(chalk.gray('  CSS文件:'), cssFiles.join(', '))
    }
    console.log()
})

// 优雅关闭
process.on('SIGTERM', () => {
    console.log(chalk.yellow('\n📦 正在关闭预览服务器...'))
    process.exit(0)
})

process.on('SIGINT', () => {
    console.log(chalk.yellow('\n📦 正在关闭预览服务器...'))
    process.exit(0)
})
