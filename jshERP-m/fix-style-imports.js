const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const files = [
  'src/components/mobile/MobileList.vue',
  'src/components/mobile/VirtualMobileList.vue',
  'src/components/mobile/MobileTable.vue',
  'src/components/mobile/MobileModal.vue',
  'src/components/mobile/MobileForm.vue',
  'src/components/mobile/MobileDrawer.vue',
  'src/components/mobile/MobileFormItem.vue',
  'src/components/debug/DebugPanel.vue',
  'src/components/debug/PerformanceTest.vue',
  'src/views/mobile/Inventory.vue',
  'src/views/mobile/Material.vue',
  'src/views/mobile/Dashboard.vue',
  'src/views/mobile/components/OrderForm.vue',
  'src/views/mobile/components/MaterialForm.vue',
  'src/views/mobile/components/BatchOperationForm.vue',
  'src/views/mobile/components/InventoryForm.vue',
  'src/views/mobile/components/MaterialSelector.vue',
  'src/views/mobile/Orders.vue',
  'src/views/debug/CacheTest.vue',
  'src/views/debug/ErrorTest.vue',
  'src/views/debug/ComponentTest.vue',
  'src/views/debug/ApiTest.vue',
  'src/views/debug/TestCenter.vue'
];

// 计算相对路径的函数
function getRelativePath(fromFile, toFile) {
  const fromDir = path.dirname(fromFile);
  const relativePath = path.relative(fromDir, toFile);
  return relativePath.replace(/\\/g, '/'); // 确保使用正斜杠
}

// 处理每个文件
files.forEach(filePath => {
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 计算到样式文件的相对路径
    const relativePath = getRelativePath(filePath, 'src/styles/mobile/index.less');
    
    // 替换导入语句
    const newContent = content.replace(
      /@import '@\/styles\/mobile\/index\.less';/g,
      `@import '${relativePath}';`
    );
    
    // 如果内容有变化，写回文件
    if (newContent !== content) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 已修复: ${filePath} -> ${relativePath}`);
    } else {
      console.log(`⚠️  未找到需要修复的导入: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
  }
});

console.log('\n🎉 样式导入路径修复完成！');
