-- 聆花文化商品价格配置脚本
-- 为现有商品配置完整的价格体系
-- tenant_id: 63 (聆花文化)

-- =====================================================
-- 第一部分：查询现有商品ID
-- =====================================================

-- 1.1 查询手作体验套件商品ID
SELECT '=== 手作体验套件商品ID查询 ===' as info;
SELECT id, name, standard FROM jsh_material 
WHERE name LIKE '%手作体验套件%' AND tenant_id = 63 AND delete_flag = '0';

-- 1.2 查询团建课程包商品ID
SELECT '=== 团建课程包商品ID查询 ===' as info;
SELECT id, name, standard FROM jsh_material 
WHERE name LIKE '%团建课程包%' AND tenant_id = 63 AND delete_flag = '0';

-- 1.3 查询咖啡饮品商品ID
SELECT '=== 咖啡饮品商品ID查询 ===' as info;
SELECT id, name, standard FROM jsh_material 
WHERE name LIKE '%印象%' AND tenant_id = 63 AND delete_flag = '0';

-- =====================================================
-- 第二部分：为手作体验套件配置价格
-- =====================================================

-- 2.1 手作体验套件-入门款价格配置
-- 成本价：80元，零售价：150元，体验价：120元
INSERT INTO jsh_material_extend (material_id, bar_code, unit, purchase_decimal, commodity_decimal, wholesale_decimal, low_decimal, default_flag, create_time, creator, tenant_id, delete_flag)
SELECT 
    id as material_id,
    'LH-SZ-TJ-001' as bar_code,
    '份' as unit,
    80.00 as purchase_decimal,
    150.00 as commodity_decimal,
    120.00 as wholesale_decimal,
    120.00 as low_decimal,
    '1' as default_flag,
    NOW() as create_time,
    'admin' as creator,
    63 as tenant_id,
    '0' as delete_flag
FROM jsh_material 
WHERE standard = 'LH-SZ-TJ-001' AND tenant_id = 63 AND delete_flag = '0'
AND NOT EXISTS (
    SELECT 1 FROM jsh_material_extend me 
    WHERE me.material_id = jsh_material.id AND me.default_flag = '1'
);

-- 2.2 手作体验套件-进阶款价格配置
-- 成本价：120元，零售价：220元，体验价：180元
INSERT INTO jsh_material_extend (material_id, bar_code, unit, purchase_decimal, commodity_decimal, wholesale_decimal, low_decimal, default_flag, create_time, creator, tenant_id, delete_flag)
SELECT 
    id as material_id,
    'LH-SZ-TJ-002' as bar_code,
    '份' as unit,
    120.00 as purchase_decimal,
    220.00 as commodity_decimal,
    180.00 as wholesale_decimal,
    180.00 as low_decimal,
    '1' as default_flag,
    NOW() as create_time,
    'admin' as creator,
    63 as tenant_id,
    '0' as delete_flag
FROM jsh_material 
WHERE standard = 'LH-SZ-TJ-002' AND tenant_id = 63 AND delete_flag = '0'
AND NOT EXISTS (
    SELECT 1 FROM jsh_material_extend me 
    WHERE me.material_id = jsh_material.id AND me.default_flag = '1'
);

-- =====================================================
-- 第三部分：为团建课程包配置价格
-- =====================================================

-- 3.1 团建课程包-企业定制价格配置
-- 成本价：1500元，零售价：3000元，团建价：2500元
INSERT INTO jsh_material_extend (material_id, bar_code, unit, purchase_decimal, commodity_decimal, wholesale_decimal, low_decimal, default_flag, create_time, creator, tenant_id, delete_flag)
SELECT 
    id as material_id,
    'LH-SZ-TJ-101' as bar_code,
    '场' as unit,
    1500.00 as purchase_decimal,
    3000.00 as commodity_decimal,
    2500.00 as wholesale_decimal,
    2500.00 as low_decimal,
    '1' as default_flag,
    NOW() as create_time,
    'admin' as creator,
    63 as tenant_id,
    '0' as delete_flag
FROM jsh_material 
WHERE standard = 'LH-SZ-TJ-101' AND tenant_id = 63 AND delete_flag = '0'
AND NOT EXISTS (
    SELECT 1 FROM jsh_material_extend me 
    WHERE me.material_id = jsh_material.id AND me.default_flag = '1'
);

-- 3.2 团建课程包-学校定制价格配置
-- 成本价：1800元，零售价：3600元，团建价：3000元
INSERT INTO jsh_material_extend (material_id, bar_code, unit, purchase_decimal, commodity_decimal, wholesale_decimal, low_decimal, default_flag, create_time, creator, tenant_id, delete_flag)
SELECT 
    id as material_id,
    'LH-SZ-TJ-102' as bar_code,
    '场' as unit,
    1800.00 as purchase_decimal,
    3600.00 as commodity_decimal,
    3000.00 as wholesale_decimal,
    3000.00 as low_decimal,
    '1' as default_flag,
    NOW() as create_time,
    'admin' as creator,
    63 as tenant_id,
    '0' as delete_flag
FROM jsh_material 
WHERE standard = 'LH-SZ-TJ-102' AND tenant_id = 63 AND delete_flag = '0'
AND NOT EXISTS (
    SELECT 1 FROM jsh_material_extend me 
    WHERE me.material_id = jsh_material.id AND me.default_flag = '1'
);

-- =====================================================
-- 第四部分：更新现有商品价格（如需要）
-- =====================================================

-- 4.1 更新掐丝珐琅唐卡价格（如果价格不合理）
UPDATE jsh_material_extend me
JOIN jsh_material m ON me.material_id = m.id
SET 
    me.purchase_decimal = CASE 
        WHEN m.standard = 'QSFL-TK-JX8B' THEN 1800.00
        WHEN m.standard = 'QSFL-TK-JGC' THEN 2000.00
        ELSE me.purchase_decimal
    END,
    me.commodity_decimal = CASE 
        WHEN m.standard = 'QSFL-TK-JX8B' THEN 3600.00
        WHEN m.standard = 'QSFL-TK-JGC' THEN 4200.00
        ELSE me.commodity_decimal
    END,
    me.wholesale_decimal = CASE 
        WHEN m.standard = 'QSFL-TK-JX8B' THEN 2880.00
        WHEN m.standard = 'QSFL-TK-JGC' THEN 3360.00
        ELSE me.wholesale_decimal
    END,
    me.low_decimal = CASE 
        WHEN m.standard = 'QSFL-TK-JX8B' THEN 2160.00
        WHEN m.standard = 'QSFL-TK-JGC' THEN 2520.00
        ELSE me.low_decimal
    END
WHERE m.tenant_id = 63 AND m.delete_flag = '0' 
AND m.standard IN ('QSFL-TK-JX8B', 'QSFL-TK-JGC')
AND me.default_flag = '1';

-- 4.2 更新珐琅彩饰品价格（如果价格不合理）
UPDATE jsh_material_extend me
JOIN jsh_material m ON me.material_id = m.id
SET 
    me.purchase_decimal = CASE 
        WHEN m.standard = 'FLCSP-XY-XL' THEN 380.00
        WHEN m.standard = 'FLCSP-DLH-EZ' THEN 320.00
        WHEN m.standard = 'FLCSP-HH-BJ' THEN 580.00
        WHEN m.standard = 'FLCSP-XL-BJ' THEN 680.00
        ELSE me.purchase_decimal
    END,
    me.commodity_decimal = CASE 
        WHEN m.standard = 'FLCSP-XY-XL' THEN 780.00
        WHEN m.standard = 'FLCSP-DLH-EZ' THEN 680.00
        WHEN m.standard = 'FLCSP-HH-BJ' THEN 1180.00
        WHEN m.standard = 'FLCSP-XL-BJ' THEN 1380.00
        ELSE me.commodity_decimal
    END,
    me.wholesale_decimal = CASE 
        WHEN m.standard = 'FLCSP-XY-XL' THEN 624.00
        WHEN m.standard = 'FLCSP-DLH-EZ' THEN 544.00
        WHEN m.standard = 'FLCSP-HH-BJ' THEN 944.00
        WHEN m.standard = 'FLCSP-XL-BJ' THEN 1104.00
        ELSE me.wholesale_decimal
    END,
    me.low_decimal = CASE 
        WHEN m.standard = 'FLCSP-XY-XL' THEN 468.00
        WHEN m.standard = 'FLCSP-DLH-EZ' THEN 408.00
        WHEN m.standard = 'FLCSP-HH-BJ' THEN 708.00
        WHEN m.standard = 'FLCSP-XL-BJ' THEN 828.00
        ELSE me.low_decimal
    END
WHERE m.tenant_id = 63 AND m.delete_flag = '0' 
AND m.standard IN ('FLCSP-XY-XL', 'FLCSP-DLH-EZ', 'FLCSP-HH-BJ', 'FLCSP-XL-BJ')
AND me.default_flag = '1';

-- =====================================================
-- 第五部分：验证价格配置
-- =====================================================

-- 5.1 验证所有商品价格配置
SELECT '=== 验证商品价格配置 ===' as info;
SELECT 
    m.name,
    m.standard,
    me.bar_code,
    me.purchase_decimal as 成本价,
    me.commodity_decimal as 零售价,
    me.wholesale_decimal as 渠道价,
    me.low_decimal as 景之蓝价,
    ROUND((me.commodity_decimal - me.purchase_decimal) / me.purchase_decimal * 100, 2) as 毛利率
FROM jsh_material m
LEFT JOIN jsh_material_extend me ON m.id = me.material_id AND me.default_flag = '1'
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
ORDER BY m.name;

-- 5.2 检查价格异常的商品
SELECT '=== 检查价格异常商品 ===' as info;
SELECT 
    m.name,
    m.standard,
    me.purchase_decimal,
    me.commodity_decimal,
    '零售价低于成本价' as 异常类型
FROM jsh_material m
JOIN jsh_material_extend me ON m.id = me.material_id AND me.default_flag = '1'
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
AND me.commodity_decimal <= me.purchase_decimal

UNION ALL

SELECT 
    m.name,
    m.standard,
    me.purchase_decimal,
    me.commodity_decimal,
    '毛利率过低(<20%)' as 异常类型
FROM jsh_material m
JOIN jsh_material_extend me ON m.id = me.material_id AND me.default_flag = '1'
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
AND me.purchase_decimal > 0
AND (me.commodity_decimal - me.purchase_decimal) / me.purchase_decimal < 0.2;

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT '=== 价格配置完成 ===' as info;
SELECT '请检查价格配置结果，确认价格合理性' as instruction;
