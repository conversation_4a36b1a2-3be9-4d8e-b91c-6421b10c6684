# 聆花文化ERP系统配置指南

## 配置概述

本指南基于jshERP现有功能，通过配置优化的方式实现聆花文化ERP系统需求，无需修改代码，仅通过数据配置和业务流程设置即可满足80%的业务需求。

## 配置前准备

### 1. 环境要求
- jshERP系统已正常运行
- 数据库连接正常
- 管理员权限账户

### 2. 租户信息确认
- 确认聆花文化的tenant_id（示例中使用63，请根据实际情况修改）
- 确认管理员用户权限

## 第一阶段：基础数据配置

### 1.1 执行配置脚本

1. **执行SQL配置脚本**
   ```bash
   # 连接到MySQL数据库
   mysql -u username -p database_name
   
   # 执行配置脚本
   source /path/to/聆花文化ERP系统配置脚本.sql
   ```

2. **验证配置结果**
   ```sql
   -- 检查商品分类
   SELECT * FROM jsh_material_category WHERE tenant_id = 63;
   
   -- 检查仓库配置
   SELECT * FROM jsh_depot WHERE tenant_id = 63;
   
   -- 检查供应商客户
   SELECT * FROM jsh_supplier WHERE tenant_id = 63;
   ```

### 1.2 商品信息配置

#### 商品字段使用规范
- **name**: 产品名称（如：掐丝珐琅画-荷花图）
- **model**: 规格尺寸（如：30cm×40cm）
- **standard**: 标准/型号（如：LH-001）
- **other_field1**: 工艺类型（掐丝/点蓝）
- **other_field2**: 设计师（如：张大师）
- **other_field3**: 制作难度（1-5级）

#### 价格体系配置
- **purchase_decimal**: 成本价（原料+人工+制作费）
- **commodity_decimal**: 零售价（聆花掐丝珐琅馆）
- **wholesale_decimal**: 渠道价（美术馆、纪念堂等）
- **low_decimal**: 景之蓝供货价/制作费

#### 示例商品配置
```sql
INSERT INTO jsh_material (name, model, standard, other_field1, other_field2, other_field3, 
                         purchase_decimal, commodity_decimal, wholesale_decimal, low_decimal,
                         category_id, unit_id, tenant_id, delete_flag) VALUES
('掐丝珐琅画-荷花图', '30cm×40cm', 'LH-001', '掐丝', '张大师', '3', 
 800.00, 1500.00, 1200.00, 1000.00,
 (SELECT id FROM jsh_material_category WHERE name='掐丝珐琅画'), 
 (SELECT id FROM jsh_unit WHERE name='幅'), 63, '0');
```

### 1.3 BOM管理配置

利用jshERP的组装拆卸功能实现BOM管理：

1. **创建组装单**
   - 单据类型：选择"组装单"
   - 成品：选择要组装的产品
   - 原料：添加底胎、釉料、配件、包装等

2. **BOM标准化**
   ```
   掐丝珐琅画BOM示例：
   ├── 底胎：铝塑板 1片
   ├── 珐琅釉料：各色釉料 若干
   ├── 辅料：固釉剂、铝镀金丝
   └── 包装：画框、包装箱
   ```

## 第二阶段：业务流程配置

### 2.1 委外生产流程

#### 流程设计
1. **委外订单创建**
   - 模块：采购管理 → 采购订单
   - 供应商：选择"广西生产基地"
   - 备注：标注委外加工要求

2. **底胎出库**
   - 模块：库存管理 → 其他出库
   - 出库仓库：广州原料仓
   - 入库仓库：广西生产基地仓（虚拟）

3. **完工入库**
   - 模块：库存管理 → 其他入库
   - 出库仓库：广西生产基地仓（虚拟）
   - 入库仓库：广州半成品仓/成品仓

#### 操作步骤
```
1. 创建委外采购订单
   采购管理 → 采购订单 → 新增
   供应商：广西生产基地
   商品：选择需要加工的产品
   数量：加工数量
   单价：加工费用
   备注：制作要求、交货仓库、紧急程度

2. 底胎出库到广西
   库存管理 → 其他出库 → 新增
   出库仓库：广州原料仓
   商品：底胎材料
   关联单据：委外采购订单号

3. 完工产品入库
   库存管理 → 其他入库 → 新增
   入库仓库：广州半成品仓/成品仓
   商品：完工产品
   关联单据：委外采购订单号
```

### 2.2 多渠道销售流程

#### 渠道配置
1. **线下零售**
   - 模块：销售管理 → 零售出库
   - 客户：散客
   - 价格：零售价（commodity_decimal）

2. **渠道销售**
   - 模块：销售管理 → 销售订单
   - 客户：选择对应渠道（美术馆、纪念堂等）
   - 价格：渠道价（wholesale_decimal）

3. **景之蓝供货**
   - 模块：销售管理 → 销售订单
   - 客户：深圳景之蓝
   - 价格：景之蓝供货价（low_decimal）

4. **手作体验**
   - 模块：销售管理 → 销售订单
   - 客户：体验客户
   - 商品：手作体验套件
   - 价格：体验价格

### 2.3 库存管理流程

#### 多仓库协同
```
库存流转路径：
1. 采购入库 → 广州原料仓
2. 委外发料 → 广州原料仓 → 广西生产基地仓
3. 委外完工 → 广西生产基地仓 → 广州半成品仓/成品仓
4. 内部加工 → 广州半成品仓 → 广州成品仓
5. 销售出库 → 广州成品仓 → 客户
6. 渠道发货 → 广州成品仓 → 渠道虚拟仓
```

#### 库存预警设置
1. **设置最低库存量**
   - 商品管理 → 商品档案 → 编辑
   - 设置最低库存量
   - 系统自动预警

2. **库存盘点**
   - 库存管理 → 库存盘点
   - 定期盘点：月度/季度
   - 抽样盘点：随机抽查

## 第三阶段：权限配置

### 3.1 用户角色配置

#### 角色设计
1. **聆花管理员**：全部权限
2. **销售人员**：销售订单、客户管理、库存查询
3. **仓库管理员**：库存管理、出入库操作
4. **财务人员**：财务管理、报表查询
5. **生产管理员**：采购管理、委外管理

#### 权限分配
```
系统管理 → 用户管理 → 角色管理
1. 创建角色
2. 分配功能权限
3. 设置数据权限（仓库权限、客户权限等）
4. 用户分配角色
```

### 3.2 数据权限配置

#### 仓库权限
- 销售人员：成品仓查询权限
- 仓库管理员：所有仓库操作权限
- 生产管理员：原料仓、半成品仓权限

#### 客户权限
- 销售人员：分配对应客户权限
- 渠道专员：对应渠道客户权限

## 配置验证

### 验证清单
- [ ] 商品分类体系完整
- [ ] 仓库架构配置正确
- [ ] 供应商客户信息完整
- [ ] 价格体系设置合理
- [ ] 业务流程测试通过
- [ ] 权限配置正确
- [ ] 库存数据准确

### 测试流程
1. **完整业务流程测试**
   - 采购 → 入库 → 委外 → 完工 → 销售 → 出库
2. **多渠道销售测试**
   - 零售、渠道、景之蓝、手作体验
3. **财务流程测试**
   - 应收应付、收支管理

## 注意事项

1. **tenant_id修改**：所有SQL脚本中的tenant_id需要根据实际情况修改
2. **数据备份**：配置前请备份数据库
3. **权限测试**：配置完成后务必测试各角色权限
4. **业务培训**：为用户提供业务流程培训

## 技术支持

如遇到配置问题，请检查：
1. 数据库连接是否正常
2. tenant_id是否正确
3. 用户权限是否充足
4. 基础数据是否完整
