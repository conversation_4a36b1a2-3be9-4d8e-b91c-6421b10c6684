-- 聆花文化ERP系统数据检查和补充脚本
-- 基于现有数据进行检查和补充
-- tenant_id: 63 (聆花文化)

-- =====================================================
-- 第一部分：数据检查查询
-- =====================================================

-- 1.1 检查现有商品分类
SELECT '=== 商品分类检查 ===' as info;
SELECT id, name, parent_id, category_level, sort, remark 
FROM jsh_material_category 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY category_level, sort;

-- 1.2 检查现有仓库
SELECT '=== 仓库配置检查 ===' as info;
SELECT id, name, address, warehousing, tradeType, remark 
FROM jsh_depot 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY sort;

-- 1.3 检查现有供应商客户
SELECT '=== 供应商客户检查 ===' as info;
SELECT id, supplier, contacts, phonenum, type, remark 
FROM jsh_supplier 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY type, supplier;

-- 1.4 检查现有商品信息
SELECT '=== 商品信息检查 ===' as info;
SELECT m.id, m.name, m.model, m.standard, m.other_field1, m.other_field2, m.other_field3,
       mc.name as category_name, u.name as unit_name
FROM jsh_material m
LEFT JOIN jsh_material_category mc ON m.category_id = mc.id
LEFT JOIN jsh_unit u ON m.unit_id = u.id
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
ORDER BY mc.name, m.name;

-- 1.5 检查现有价格信息
SELECT '=== 价格信息检查 ===' as info;
SELECT m.name, me.bar_code, me.purchase_decimal, me.commodity_decimal, 
       me.wholesale_decimal, me.low_decimal
FROM jsh_material m
LEFT JOIN jsh_material_extend me ON m.id = me.material_id AND me.default_flag = '1'
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
ORDER BY m.name;

-- 1.6 检查计量单位
SELECT '=== 计量单位检查 ===' as info;
SELECT id, name, basic_unit, other_unit, other_unit_two, other_unit_three
FROM jsh_unit 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY name;

-- 1.7 检查收支项目
SELECT '=== 收支项目检查 ===' as info;
SELECT id, name, type, remark 
FROM jsh_in_out_item 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY type, name;

-- 1.8 检查账户信息
SELECT '=== 账户信息检查 ===' as info;
SELECT id, name, serial_no, initial_amount, current_amount, remark 
FROM jsh_account 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY name;

-- =====================================================
-- 第二部分：补充缺失的基础数据
-- =====================================================

-- 2.1 补充缺失的计量单位
INSERT IGNORE INTO jsh_unit (name, basic_unit, remark, tenant_id, delete_flag) VALUES
('份', '份', '手作体验套件计量单位', 63, '0'),
('场', '场', '团建活动计量单位', 63, '0'),
('人次', '人次', '体验活动计量单位', 63, '0'),
('杯', '杯', '饮品计量单位', 63, '0'),
('套', '套', '套装产品计量单位', 63, '0');

-- 2.2 补充缺失的收支项目
INSERT IGNORE INTO jsh_in_out_item (name, type, remark, tenant_id, delete_flag) VALUES
('手作体验收入', '收入', '手作体验活动收入', 63, '0'),
('场地租赁费', '支出', '团建场地租赁费用', 63, '0'),
('包装运输费', '支出', '产品包装和物流费用', 63, '0'),
('设备维护费', '支出', '生产设备维护费用', 63, '0'),
('培训费用', '支出', '员工培训费用', 63, '0');

-- 2.3 补充缺失的账户信息
INSERT IGNORE INTO jsh_account (name, serial_no, initial_amount, current_amount, remark, tenant_id, delete_flag) VALUES
('聆花文化基本户', 'LH-BASIC-001', 0.00, 0.00, '聆花文化企业基本账户', 63, '0'),
('聆花文化一般户', 'LH-GENERAL-001', 0.00, 0.00, '聆花文化一般存款账户', 63, '0'),
('团建项目专用账户', 'LH-TEAM-001', 0.00, 0.00, '团建项目收支专用账户', 63, '0');

-- =====================================================
-- 第三部分：补充商品信息数据
-- =====================================================

-- 3.1 补充手作体验套件商品
INSERT IGNORE INTO jsh_material (category_id, name, model, standard, other_field1, other_field2, other_field3, 
                                 unit, remark, tenant_id, delete_flag) VALUES
((SELECT id FROM jsh_material_category WHERE name='聆花手作馆' AND tenant_id=63), 
 '手作体验套件-入门款', '15cm×20cm', 'LH-SZ-TJ-001', '体验', '聆花老师', '1', 
 '份', '包含底胎、颜料、工具、说明书', 63, '0'),
((SELECT id FROM jsh_material_category WHERE name='聆花手作馆' AND tenant_id=63), 
 '手作体验套件-进阶款', '20cm×25cm', 'LH-SZ-TJ-002', '体验', '聆花老师', '2', 
 '份', '包含底胎、颜料、工具、说明书、装裱框', 63, '0'),
((SELECT id FROM jsh_material_category WHERE name='聆花手作馆' AND tenant_id=63), 
 '团建课程包-企业定制', '20人份', 'LH-SZ-TJ-101', '团建', '聆花老师', '2', 
 '场', '含材料、讲师、助理、场地服务', 63, '0'),
((SELECT id FROM jsh_material_category WHERE name='聆花手作馆' AND tenant_id=63), 
 '团建课程包-学校定制', '30人份', 'LH-SZ-TJ-102', '团建', '聆花老师', '1', 
 '场', '含材料、讲师、助理、场地服务', 63, '0');

-- 3.2 为新增商品添加价格信息
-- 注意：需要先获取商品ID，然后添加价格信息
-- 这部分需要在商品创建后手动执行

-- =====================================================
-- 第四部分：补充供应商客户信息
-- =====================================================

-- 4.1 补充缺失的供应商
INSERT IGNORE INTO jsh_supplier (supplier, contacts, phonenum, email, address, type, remark, tenant_id, delete_flag) VALUES
('唐卡铜胎供应商', '张师傅', '13800138001', '<EMAIL>', '西藏拉萨市城关区', '供应商', '专业铜胎制作，质量稳定', 63, '0'),
('阿里巴巴铜底胎商', '李经理', '13800138002', '<EMAIL>', '浙江杭州市余杭区', '供应商', '网络采购平台，品种丰富', 63, '0'),
('玉石底胎供应商', '王师傅', '13800138003', '<EMAIL>', '新疆和田市', '供应商', '天然玉石底胎，高端定制', 63, '0'),
('黑檀木底胎供应商', '赵师傅', '13800138004', '<EMAIL>', '海南海口市', '供应商', '黑檀木底胎，文创系列', 63, '0'),
('珐琅釉料供应商', '陈经理', '13800138005', '<EMAIL>', '景德镇市', '供应商', '各色珐琅釉料，品质优良', 63, '0');

-- 4.2 补充缺失的客户
INSERT IGNORE INTO jsh_supplier (supplier, contacts, phonenum, email, address, type, remark, tenant_id, delete_flag) VALUES
('华南理工大学', '教务处', '020-87110000', '<EMAIL>', '广州市天河区五山路', '客户', '高校团建客户，定期合作', 63, '0'),
('广州市第一中学', '德育处', '020-83177500', '<EMAIL>', '广州市荔湾区', '客户', '中学团建客户，传统文化教育', 63, '0'),
('腾讯广州分公司', 'HR部门', '020-38888888', '<EMAIL>', '广州市海珠区', '客户', '企业团建客户，创新文化体验', 63, '0'),
('广汽集团', '工会', '020-83151888', '<EMAIL>', '广州市越秀区', '客户', '大型企业团建客户，员工福利', 63, '0');

-- =====================================================
-- 第五部分：验证数据完整性
-- =====================================================

-- 5.1 验证商品分类完整性
SELECT '=== 验证商品分类完整性 ===' as info;
SELECT 
    COUNT(*) as total_categories,
    SUM(CASE WHEN category_level = 1 THEN 1 ELSE 0 END) as level1_count,
    SUM(CASE WHEN category_level = 2 THEN 1 ELSE 0 END) as level2_count
FROM jsh_material_category 
WHERE tenant_id = 63 AND delete_flag = '0';

-- 5.2 验证仓库配置完整性
SELECT '=== 验证仓库配置完整性 ===' as info;
SELECT 
    COUNT(*) as total_depots,
    SUM(CASE WHEN warehousing = 1 THEN 1 ELSE 0 END) as physical_depots,
    SUM(CASE WHEN warehousing = 0 THEN 1 ELSE 0 END) as virtual_depots
FROM jsh_depot 
WHERE tenant_id = 63 AND delete_flag = '0';

-- 5.3 验证供应商客户完整性
SELECT '=== 验证供应商客户完整性 ===' as info;
SELECT 
    type,
    COUNT(*) as count
FROM jsh_supplier 
WHERE tenant_id = 63 AND delete_flag = '0'
GROUP BY type
ORDER BY type;

-- 5.4 验证商品信息完整性
SELECT '=== 验证商品信息完整性 ===' as info;
SELECT 
    mc.name as category_name,
    COUNT(m.id) as product_count
FROM jsh_material_category mc
LEFT JOIN jsh_material m ON mc.id = m.category_id AND m.tenant_id = 63 AND m.delete_flag = '0'
WHERE mc.tenant_id = 63 AND mc.delete_flag = '0'
GROUP BY mc.id, mc.name
ORDER BY mc.category_level, mc.sort;

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT '=== 数据检查和补充完成 ===' as info;
SELECT '请检查上述查询结果，确认数据完整性' as instruction;
SELECT '如有缺失数据，请手动补充或联系管理员' as note;
