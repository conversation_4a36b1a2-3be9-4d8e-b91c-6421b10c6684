-- 聆花文化业务流程验证脚本
-- 验证各业务流程的数据完整性和可操作性
-- tenant_id: 63 (聆花文化)

-- =====================================================
-- 第一部分：委外生产流程验证
-- =====================================================

-- 1.1 验证委外供应商配置
SELECT '=== 委外供应商验证 ===' as info;
SELECT id, supplier, contacts, phonenum, type, remark
FROM jsh_supplier 
WHERE tenant_id = 63 AND delete_flag = '0' 
AND (supplier LIKE '%广西%' OR supplier LIKE '%生产基地%' OR type LIKE '%委外%')
ORDER BY supplier;

-- 1.2 验证生产相关仓库配置
SELECT '=== 生产仓库验证 ===' as info;
SELECT id, name, address, warehousing, tradeType, remark
FROM jsh_depot 
WHERE tenant_id = 63 AND delete_flag = '0' 
AND (name LIKE '%原料%' OR name LIKE '%半成品%' OR name LIKE '%成品%' OR name LIKE '%广西%')
ORDER BY name;

-- 1.3 验证可委外加工的产品
SELECT '=== 可委外产品验证 ===' as info;
SELECT m.id, m.name, m.standard, m.other_field1, mc.name as category_name
FROM jsh_material m
LEFT JOIN jsh_material_category mc ON m.category_id = mc.id
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
AND (m.other_field1 = '掐丝' OR m.other_field1 = '点蓝' OR mc.name LIKE '%珐琅%')
ORDER BY mc.name, m.name;

-- 1.4 验证底胎原料配置
SELECT '=== 底胎原料验证 ===' as info;
SELECT m.id, m.name, m.standard, mc.name as category_name
FROM jsh_material m
LEFT JOIN jsh_material_category mc ON m.category_id = mc.id
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
AND (m.name LIKE '%底胎%' OR m.name LIKE '%底料%' OR mc.name LIKE '%底胎%')
ORDER BY m.name;

-- =====================================================
-- 第二部分：多渠道销售流程验证
-- =====================================================

-- 2.1 验证销售客户配置
SELECT '=== 销售客户验证 ===' as info;
SELECT id, supplier as customer_name, contacts, phonenum, type, remark
FROM jsh_supplier 
WHERE tenant_id = 63 AND delete_flag = '0' 
AND type IN ('客户', '会员')
ORDER BY type, supplier;

-- 2.2 验证代销渠道配置
SELECT '=== 代销渠道验证 ===' as info;
SELECT s.id, s.supplier as channel_name, s.contacts, s.phonenum, d.name as depot_name
FROM jsh_supplier s
LEFT JOIN jsh_depot d ON d.name LIKE CONCAT('%', s.supplier, '%') AND d.tenant_id = 63
WHERE s.tenant_id = 63 AND s.delete_flag = '0' 
AND (s.supplier LIKE '%美术馆%' OR s.supplier LIKE '%纪念堂%' OR s.supplier LIKE '%书店%' OR s.remark LIKE '%代销%')
ORDER BY s.supplier;

-- 2.3 验证景之蓝合作配置
SELECT '=== 景之蓝合作验证 ===' as info;
SELECT s.id, s.supplier, s.contacts, s.type, d.name as depot_name
FROM jsh_supplier s
LEFT JOIN jsh_depot d ON d.name LIKE '%景之蓝%' AND d.tenant_id = 63
WHERE s.tenant_id = 63 AND s.delete_flag = '0' 
AND s.supplier LIKE '%景之蓝%'
ORDER BY s.supplier;

-- 2.4 验证销售产品价格配置
SELECT '=== 销售产品价格验证 ===' as info;
SELECT 
    m.name,
    m.standard,
    me.purchase_decimal as 成本价,
    me.commodity_decimal as 零售价,
    me.wholesale_decimal as 渠道价,
    me.low_decimal as 景之蓝价,
    CASE 
        WHEN me.commodity_decimal > 0 THEN '可零售'
        ELSE '价格未配置'
    END as 零售状态,
    CASE 
        WHEN me.wholesale_decimal > 0 THEN '可渠道销售'
        ELSE '渠道价未配置'
    END as 渠道状态
FROM jsh_material m
LEFT JOIN jsh_material_extend me ON m.id = me.material_id AND me.default_flag = '1'
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
AND m.category_id IN (
    SELECT id FROM jsh_material_category 
    WHERE tenant_id = 63 AND name IN ('聆花艺术臻品', '聆花非遗文创', '聆花手作馆')
)
ORDER BY m.name;

-- =====================================================
-- 第三部分：库存管理流程验证
-- =====================================================

-- 3.1 验证仓库权限配置
SELECT '=== 仓库权限验证 ===' as info;
SELECT 
    d.id as depot_id,
    d.name as depot_name,
    d.warehousing,
    d.tradeType,
    COUNT(ub.id) as user_count
FROM jsh_depot d
LEFT JOIN jsh_user_business ub ON ub.type = 'UserDepot' 
    AND FIND_IN_SET(d.id, REPLACE(REPLACE(ub.value, '[', ''), ']', '')) > 0
    AND ub.tenant_id = 63
WHERE d.tenant_id = 63 AND d.delete_flag = '0'
GROUP BY d.id, d.name, d.warehousing, d.tradeType
ORDER BY d.name;

-- 3.2 验证库存初始化状态
SELECT '=== 库存初始化验证 ===' as info;
SELECT 
    d.name as depot_name,
    COUNT(mcs.id) as stock_records,
    SUM(CASE WHEN mcs.current_number > 0 THEN 1 ELSE 0 END) as positive_stock_count,
    SUM(CASE WHEN mcs.current_number = 0 THEN 1 ELSE 0 END) as zero_stock_count
FROM jsh_depot d
LEFT JOIN jsh_material_current_stock mcs ON d.id = mcs.depot_id AND mcs.tenant_id = 63
WHERE d.tenant_id = 63 AND d.delete_flag = '0'
GROUP BY d.id, d.name
ORDER BY d.name;

-- 3.3 验证商品库存分布
SELECT '=== 商品库存分布验证 ===' as info;
SELECT 
    m.name as product_name,
    d.name as depot_name,
    COALESCE(mcs.current_number, 0) as current_stock
FROM jsh_material m
CROSS JOIN jsh_depot d
LEFT JOIN jsh_material_current_stock mcs ON m.id = mcs.material_id AND d.id = mcs.depot_id
WHERE m.tenant_id = 63 AND m.delete_flag = '0'
AND d.tenant_id = 63 AND d.delete_flag = '0'
AND m.category_id IN (
    SELECT id FROM jsh_material_category 
    WHERE tenant_id = 63 AND name IN ('聆花艺术臻品', '聆花非遗文创')
)
ORDER BY m.name, d.name;

-- =====================================================
-- 第四部分：财务管理流程验证
-- =====================================================

-- 4.1 验证账户配置
SELECT '=== 账户配置验证 ===' as info;
SELECT id, name, serial_no, initial_amount, current_amount, remark
FROM jsh_account 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY name;

-- 4.2 验证收支项目配置
SELECT '=== 收支项目验证 ===' as info;
SELECT 
    type,
    COUNT(*) as item_count,
    GROUP_CONCAT(name SEPARATOR ', ') as items
FROM jsh_in_out_item 
WHERE tenant_id = 63 AND delete_flag = '0'
GROUP BY type
ORDER BY type;

-- 4.3 验证客户信用配置
SELECT '=== 客户信用配置验证 ===' as info;
SELECT 
    supplier as customer_name,
    type,
    advance_in as 预收款,
    advance_back as 应收款,
    all_back as 累计应收,
    CASE 
        WHEN type = '客户' AND all_back IS NULL THEN '需要设置信用额度'
        WHEN type = '客户' AND all_back = 0 THEN '现金客户'
        WHEN type = '客户' AND all_back > 0 THEN '信用客户'
        ELSE '非客户'
    END as 信用状态
FROM jsh_supplier 
WHERE tenant_id = 63 AND delete_flag = '0'
AND type IN ('客户', '会员')
ORDER BY type, supplier;

-- =====================================================
-- 第五部分：权限配置验证
-- =====================================================

-- 5.1 验证用户角色配置
SELECT '=== 用户角色验证 ===' as info;
SELECT 
    u.id as user_id,
    u.username,
    u.login_name,
    u.position,
    r.name as role_name,
    r.type as role_type
FROM jsh_user u
LEFT JOIN jsh_user_business ub ON u.id = ub.key_id AND ub.type = 'UserRole' AND ub.tenant_id = 63
LEFT JOIN jsh_role r ON FIND_IN_SET(r.id, REPLACE(REPLACE(ub.value, '[', ''), ']', '')) > 0
WHERE u.tenant_id = 63 AND u.delete_flag = '0'
ORDER BY u.username, r.name;

-- 5.2 验证功能权限配置
SELECT '=== 功能权限验证 ===' as info;
SELECT 
    r.name as role_name,
    COUNT(DISTINCT SUBSTRING_INDEX(SUBSTRING_INDEX(REPLACE(REPLACE(ub.value, '[', ''), ']', ''), ',', numbers.n), ',', -1)) as function_count
FROM jsh_role r
LEFT JOIN jsh_user_business ub ON r.id = ub.key_id AND ub.type = 'RoleFunctions' AND ub.tenant_id = 63
CROSS JOIN (
    SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 
    UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10
    UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15
    UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION SELECT 20
) numbers
WHERE r.tenant_id = 63 AND r.delete_flag = '0'
AND numbers.n <= (LENGTH(REPLACE(REPLACE(ub.value, '[', ''), ']', '')) - LENGTH(REPLACE(REPLACE(REPLACE(ub.value, '[', ''), ']', ''), ',', '')) + 1)
GROUP BY r.id, r.name
ORDER BY r.name;

-- =====================================================
-- 第六部分：系统配置验证
-- =====================================================

-- 6.1 验证系统基础配置
SELECT '=== 系统配置验证 ===' as info;
SELECT company_name, company_contacts, company_address, company_tel, tenant_id
FROM jsh_system_config 
WHERE tenant_id = 63;

-- 6.2 验证计量单位配置
SELECT '=== 计量单位验证 ===' as info;
SELECT name, basic_unit, other_unit, other_unit_two, other_unit_three, ratio
FROM jsh_unit 
WHERE tenant_id = 63 AND delete_flag = '0'
ORDER BY name;

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT '=== 业务流程验证完成 ===' as info;
SELECT '请检查上述验证结果，确认各业务流程配置完整' as instruction;
SELECT '如发现配置缺失或异常，请及时补充和修正' as note;
